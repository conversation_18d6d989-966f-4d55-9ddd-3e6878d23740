"""
Advanced Tank Control Algorithms
Implements various control strategies for industrial tank systems including:
- PID Control
- Model Predictive Control (MPC)
- Adaptive Control
- Fuzzy Logic Control
- Neural Network Control
"""

import asyncio
import logging
import numpy as np
from typing import Dict, List, Optional, Any, Callable, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
import math

from .tank_models import (
    TankConfiguration, TankStateData, TankControlParameters,
    ControlMode, ActuatorType, SensorType, TankPhysicsModel
)

logger = logging.getLogger(__name__)


class ControlAlgorithmType(Enum):
    """Types of control algorithms"""
    PID = "pid"
    MODEL_PREDICTIVE = "mpc"
    ADAPTIVE = "adaptive"
    FUZZY_LOGIC = "fuzzy"
    NEURAL_NETWORK = "neural"
    HYBRID = "hybrid"


class ControllerState(Enum):
    """Controller states"""
    IDLE = "idle"
    MANUAL = "manual"
    AUTOMATIC = "automatic"
    TUNING = "tuning"
    FAULT = "fault"


@dataclass
class ControllerConfiguration:
    """Configuration for control algorithms"""
    algorithm_type: ControlAlgorithmType
    sample_time: float = 1.0  # seconds
    output_limits: Tuple[float, float] = (0.0, 100.0)  # min, max percentage
    enable_anti_windup: bool = True
    enable_derivative_kick_prevention: bool = True
    enable_setpoint_ramping: bool = True
    ramp_rate: float = 10.0  # units per minute
    
    # PID specific parameters
    pid_gains: Dict[str, float] = field(default_factory=lambda: {
        'kp': 1.0, 'ki': 0.1, 'kd': 0.01
    })
    
    # MPC specific parameters
    prediction_horizon: int = 10
    control_horizon: int = 3
    constraints: Dict[str, Tuple[float, float]] = field(default_factory=dict)
    
    # Adaptive control parameters
    adaptation_rate: float = 0.01
    forgetting_factor: float = 0.95
    
    # Fuzzy logic parameters
    membership_functions: Dict[str, Any] = field(default_factory=dict)
    rule_base: List[Dict[str, Any]] = field(default_factory=list)


class ControlAlgorithm(ABC):
    """Abstract base class for control algorithms"""
    
    @abstractmethod
    async def initialize(self, config: ControllerConfiguration, tank_config: TankConfiguration):
        """Initialize the control algorithm"""
        pass
    
    @abstractmethod
    async def compute_control_action(self, 
                                   setpoint: float, 
                                   process_variable: float, 
                                   dt: float,
                                   additional_inputs: Dict[str, Any] = None) -> float:
        """Compute control action"""
        pass
    
    @abstractmethod
    async def reset(self):
        """Reset controller state"""
        pass
    
    @abstractmethod
    def get_parameters(self) -> Dict[str, Any]:
        """Get controller parameters"""
        pass
    
    @abstractmethod
    async def update_parameters(self, parameters: Dict[str, Any]):
        """Update controller parameters"""
        pass


class PIDController(ControlAlgorithm):
    """Enhanced PID controller with advanced features"""
    
    def __init__(self):
        self.config: Optional[ControllerConfiguration] = None
        self.tank_config: Optional[TankConfiguration] = None
        
        # PID state variables
        self.integral_term = 0.0
        self.previous_error = 0.0
        self.previous_pv = 0.0
        self.previous_setpoint = 0.0
        self.last_time = None
        
        # Anti-windup
        self.integral_min = -100.0
        self.integral_max = 100.0
        
        # Setpoint ramping
        self.ramped_setpoint = 0.0
        
        # Performance tracking
        self.error_history: List[float] = []
        self.output_history: List[float] = []
        self.performance_metrics = {
            'ise': 0.0,  # Integral of Squared Error
            'iae': 0.0,  # Integral of Absolute Error
            'settling_time': 0.0,
            'overshoot': 0.0
        }
        
    async def initialize(self, config: ControllerConfiguration, tank_config: TankConfiguration):
        """Initialize PID controller"""
        self.config = config
        self.tank_config = tank_config
        self.ramped_setpoint = tank_config.control_parameters.temperature_setpoint
        
        # Set integral limits based on output limits
        output_range = config.output_limits[1] - config.output_limits[0]
        self.integral_min = -output_range
        self.integral_max = output_range
        
        logger.info(f"PID controller initialized with gains: {config.pid_gains}")
    
    async def compute_control_action(self, 
                                   setpoint: float, 
                                   process_variable: float, 
                                   dt: float,
                                   additional_inputs: Dict[str, Any] = None) -> float:
        """Compute PID control action"""
        try:
            current_time = datetime.now()
            
            # Initialize timing
            if self.last_time is None:
                self.last_time = current_time
                self.previous_pv = process_variable
                self.previous_setpoint = setpoint
                return 0.0
            
            # Setpoint ramping
            if self.config.enable_setpoint_ramping:
                self.ramped_setpoint = self._apply_setpoint_ramping(setpoint, dt)
            else:
                self.ramped_setpoint = setpoint
            
            # Calculate error
            error = self.ramped_setpoint - process_variable
            
            # PID gains
            kp = self.config.pid_gains.get('kp', 1.0)
            ki = self.config.pid_gains.get('ki', 0.1)
            kd = self.config.pid_gains.get('kd', 0.01)
            
            # Proportional term
            proportional = kp * error
            
            # Integral term
            self.integral_term += error * dt
            
            # Anti-windup
            if self.config.enable_anti_windup:
                self.integral_term = max(self.integral_min, 
                                       min(self.integral_max, self.integral_term))
            
            integral = ki * self.integral_term
            
            # Derivative term with kick prevention
            if self.config.enable_derivative_kick_prevention:
                # Derivative on measurement instead of error
                derivative = -kd * (process_variable - self.previous_pv) / dt
            else:
                derivative = kd * (error - self.previous_error) / dt
            
            # Calculate output
            output = proportional + integral + derivative
            
            # Apply output limits
            output = max(self.config.output_limits[0], 
                        min(self.config.output_limits[1], output))
            
            # Update state variables
            self.previous_error = error
            self.previous_pv = process_variable
            self.previous_setpoint = setpoint
            self.last_time = current_time
            
            # Track performance
            self._update_performance_metrics(error, output)
            
            return output
            
        except Exception as e:
            logger.error(f"Error in PID computation: {e}")
            return 0.0
    
    async def reset(self):
        """Reset PID controller state"""
        self.integral_term = 0.0
        self.previous_error = 0.0
        self.previous_pv = 0.0
        self.last_time = None
        self.error_history.clear()
        self.output_history.clear()
        
        # Reset performance metrics
        for key in self.performance_metrics:
            self.performance_metrics[key] = 0.0
    
    def get_parameters(self) -> Dict[str, Any]:
        """Get PID parameters"""
        return {
            'gains': self.config.pid_gains.copy(),
            'integral_term': self.integral_term,
            'ramped_setpoint': self.ramped_setpoint,
            'performance_metrics': self.performance_metrics.copy()
        }
    
    async def update_parameters(self, parameters: Dict[str, Any]):
        """Update PID parameters"""
        if 'gains' in parameters:
            self.config.pid_gains.update(parameters['gains'])
            logger.info(f"Updated PID gains: {self.config.pid_gains}")
    
    def _apply_setpoint_ramping(self, target_setpoint: float, dt: float) -> float:
        """Apply setpoint ramping to prevent sudden changes"""
        ramp_rate_per_second = self.config.ramp_rate / 60.0  # Convert from per minute
        max_change = ramp_rate_per_second * dt
        
        difference = target_setpoint - self.ramped_setpoint
        
        if abs(difference) <= max_change:
            return target_setpoint
        else:
            return self.ramped_setpoint + math.copysign(max_change, difference)
    
    def _update_performance_metrics(self, error: float, output: float):
        """Update performance metrics"""
        # Keep limited history
        self.error_history.append(error)
        self.output_history.append(output)
        
        max_history = 1000
        if len(self.error_history) > max_history:
            self.error_history.pop(0)
            self.output_history.pop(0)
        
        # Calculate metrics
        if len(self.error_history) > 1:
            dt = self.config.sample_time
            
            # Integral of Squared Error
            self.performance_metrics['ise'] += error**2 * dt
            
            # Integral of Absolute Error
            self.performance_metrics['iae'] += abs(error) * dt


class ModelPredictiveController(ControlAlgorithm):
    """Model Predictive Controller for tank systems"""
    
    def __init__(self):
        self.config: Optional[ControllerConfiguration] = None
        self.tank_config: Optional[TankConfiguration] = None
        self.system_model: Optional[TankPhysicsModel] = None
        
        # MPC state
        self.state_history: List[np.ndarray] = []
        self.input_history: List[np.ndarray] = []
        self.prediction_model = None
        
        # Optimization variables
        self.control_sequence: np.ndarray = None
        
    async def initialize(self, config: ControllerConfiguration, tank_config: TankConfiguration):
        """Initialize MPC controller"""
        self.config = config
        self.tank_config = tank_config
        
        # Initialize system model
        self.system_model = TankPhysicsModel()
        
        # Initialize control sequence
        self.control_sequence = np.zeros(config.control_horizon)
        
        logger.info("MPC controller initialized")
    
    async def compute_control_action(self, 
                                   setpoint: float, 
                                   process_variable: float, 
                                   dt: float,
                                   additional_inputs: Dict[str, Any] = None) -> float:
        """Compute MPC control action"""
        try:
            # Update state history
            current_state = np.array([process_variable])
            self.state_history.append(current_state)
            
            # Limit history size
            if len(self.state_history) > 100:
                self.state_history.pop(0)
                self.input_history.pop(0)
            
            # Solve optimization problem
            optimal_input = await self._solve_mpc_optimization(setpoint, current_state)
            
            # Store input history
            self.input_history.append(np.array([optimal_input]))
            
            return optimal_input
            
        except Exception as e:
            logger.error(f"Error in MPC computation: {e}")
            return 0.0
    
    async def _solve_mpc_optimization(self, setpoint: float, current_state: np.ndarray) -> float:
        """Solve MPC optimization problem (simplified)"""
        # Simplified MPC implementation
        # In practice, this would use a proper optimization solver
        
        error = setpoint - current_state[0]
        
        # Simple predictive control based on error and trend
        if len(self.state_history) > 1:
            trend = current_state[0] - self.state_history[-2][0]
            predicted_error = error - trend * self.config.prediction_horizon
        else:
            predicted_error = error
        
        # Control action proportional to predicted error
        control_gain = 2.0
        control_action = control_gain * predicted_error
        
        # Apply constraints
        control_action = max(self.config.output_limits[0], 
                           min(self.config.output_limits[1], control_action))
        
        return control_action
    
    async def reset(self):
        """Reset MPC controller state"""
        self.state_history.clear()
        self.input_history.clear()
        self.control_sequence = np.zeros(self.config.control_horizon)
    
    def get_parameters(self) -> Dict[str, Any]:
        """Get MPC parameters"""
        return {
            'prediction_horizon': self.config.prediction_horizon,
            'control_horizon': self.config.control_horizon,
            'constraints': self.config.constraints
        }
    
    async def update_parameters(self, parameters: Dict[str, Any]):
        """Update MPC parameters"""
        if 'prediction_horizon' in parameters:
            self.config.prediction_horizon = parameters['prediction_horizon']
        if 'control_horizon' in parameters:
            self.config.control_horizon = parameters['control_horizon']


class AdaptiveController(ControlAlgorithm):
    """Adaptive controller that adjusts parameters based on system behavior"""
    
    def __init__(self):
        self.config: Optional[ControllerConfiguration] = None
        self.tank_config: Optional[TankConfiguration] = None
        
        # Base PID controller
        self.base_controller = PIDController()
        
        # Adaptive parameters
        self.adaptive_gains = {'kp': 1.0, 'ki': 0.1, 'kd': 0.01}
        self.parameter_estimator = None
        self.system_identification_data = []
        
    async def initialize(self, config: ControllerConfiguration, tank_config: TankConfiguration):
        """Initialize adaptive controller"""
        self.config = config
        self.tank_config = tank_config
        
        # Initialize base PID controller
        await self.base_controller.initialize(config, tank_config)
        
        # Initialize adaptive gains
        self.adaptive_gains.update(config.pid_gains)
        
        logger.info("Adaptive controller initialized")
    
    async def compute_control_action(self, 
                                   setpoint: float, 
                                   process_variable: float, 
                                   dt: float,
                                   additional_inputs: Dict[str, Any] = None) -> float:
        """Compute adaptive control action"""
        try:
            # Collect data for system identification
            self._collect_identification_data(setpoint, process_variable)
            
            # Update parameter estimates
            await self._update_parameter_estimates()
            
            # Update base controller gains
            self.config.pid_gains.update(self.adaptive_gains)
            
            # Compute control action using adapted parameters
            control_action = await self.base_controller.compute_control_action(
                setpoint, process_variable, dt, additional_inputs
            )
            
            return control_action
            
        except Exception as e:
            logger.error(f"Error in adaptive control computation: {e}")
            return 0.0
    
    def _collect_identification_data(self, setpoint: float, process_variable: float):
        """Collect data for system identification"""
        data_point = {
            'timestamp': datetime.now(),
            'setpoint': setpoint,
            'process_variable': process_variable,
            'error': setpoint - process_variable
        }
        
        self.system_identification_data.append(data_point)
        
        # Limit data size
        if len(self.system_identification_data) > 1000:
            self.system_identification_data.pop(0)
    
    async def _update_parameter_estimates(self):
        """Update parameter estimates using recursive least squares"""
        if len(self.system_identification_data) < 10:
            return
        
        # Simplified parameter adaptation
        # In practice, this would use proper system identification techniques
        
        recent_data = self.system_identification_data[-10:]
        errors = [d['error'] for d in recent_data]
        
        # Adjust gains based on error characteristics
        error_variance = np.var(errors)
        error_mean = np.mean(np.abs(errors))
        
        adaptation_rate = self.config.adaptation_rate
        
        if error_variance > 10.0:  # High variance - reduce derivative gain
            self.adaptive_gains['kd'] *= (1 - adaptation_rate)
        elif error_mean > 5.0:  # High steady-state error - increase integral gain
            self.adaptive_gains['ki'] *= (1 + adaptation_rate)
        elif error_mean < 1.0:  # Low error - can increase proportional gain
            self.adaptive_gains['kp'] *= (1 + adaptation_rate * 0.5)
        
        # Apply bounds to gains
        self.adaptive_gains['kp'] = max(0.1, min(10.0, self.adaptive_gains['kp']))
        self.adaptive_gains['ki'] = max(0.01, min(2.0, self.adaptive_gains['ki']))
        self.adaptive_gains['kd'] = max(0.001, min(1.0, self.adaptive_gains['kd']))
    
    async def reset(self):
        """Reset adaptive controller state"""
        await self.base_controller.reset()
        self.system_identification_data.clear()
        self.adaptive_gains.update(self.config.pid_gains)
    
    def get_parameters(self) -> Dict[str, Any]:
        """Get adaptive controller parameters"""
        base_params = self.base_controller.get_parameters()
        base_params['adaptive_gains'] = self.adaptive_gains.copy()
        base_params['adaptation_data_points'] = len(self.system_identification_data)
        return base_params
    
    async def update_parameters(self, parameters: Dict[str, Any]):
        """Update adaptive controller parameters"""
        await self.base_controller.update_parameters(parameters)
        
        if 'adaptation_rate' in parameters:
            self.config.adaptation_rate = parameters['adaptation_rate']


class TankControllerManager:
    """Manager for tank control algorithms and coordination"""
    
    def __init__(self):
        self.controllers: Dict[str, Dict[str, ControlAlgorithm]] = {}
        self.tank_configs: Dict[str, TankConfiguration] = {}
        self.control_modes: Dict[str, ControlMode] = {}
        self.active_setpoints: Dict[str, Dict[str, float]] = {}
        
        # Control coordination
        self.cascade_controllers: Dict[str, List[str]] = {}
        self.feedforward_controllers: Dict[str, List[str]] = {}
        
        # Performance monitoring
        self.control_performance: Dict[str, Dict[str, Any]] = {}
        
    async def add_tank(self, tank_config: TankConfiguration):
        """Add tank to control manager"""
        tank_id = tank_config.tank_id
        self.tank_configs[tank_id] = tank_config
        self.control_modes[tank_id] = tank_config.control_parameters.control_mode
        self.controllers[tank_id] = {}
        self.active_setpoints[tank_id] = {}
        self.control_performance[tank_id] = {}
        
        # Initialize controllers for different control loops
        await self._initialize_tank_controllers(tank_config)
        
        logger.info(f"Added tank {tank_id} to control manager")
    
    async def _initialize_tank_controllers(self, tank_config: TankConfiguration):
        """Initialize controllers for a tank"""
        tank_id = tank_config.tank_id
        
        # Temperature controllers for each heating zone
        temp_sensors = tank_config.get_sensors_by_type(SensorType.TEMPERATURE)
        heater_actuators = tank_config.get_actuators_by_type(ActuatorType.HEATER)
        
        for i, (sensor, actuator) in enumerate(zip(temp_sensors, heater_actuators)):
            controller_id = f"temp_zone_{i+1}"
            
            # Create controller configuration
            config = ControllerConfiguration(
                algorithm_type=ControlAlgorithmType.PID,
                sample_time=1.0,
                output_limits=(0.0, 100.0),
                pid_gains={'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
            )
            
            # Create and initialize controller
            controller = PIDController()
            await controller.initialize(config, tank_config)
            
            self.controllers[tank_id][controller_id] = controller
            self.active_setpoints[tank_id][controller_id] = tank_config.control_parameters.temperature_setpoint
        
        # Level controller
        level_sensors = tank_config.get_sensors_by_type(SensorType.LEVEL)
        pump_actuators = tank_config.get_actuators_by_type(ActuatorType.PUMP)
        
        if level_sensors and pump_actuators:
            controller_id = "level_control"
            
            config = ControllerConfiguration(
                algorithm_type=ControlAlgorithmType.PID,
                pid_gains={'kp': 1.5, 'ki': 0.05, 'kd': 0.02}
            )
            
            controller = PIDController()
            await controller.initialize(config, tank_config)
            
            self.controllers[tank_id][controller_id] = controller
            self.active_setpoints[tank_id][controller_id] = tank_config.control_parameters.level_setpoint
        
        # Pressure controller
        pressure_sensors = tank_config.get_sensors_by_type(SensorType.PRESSURE)
        
        if pressure_sensors:
            controller_id = "pressure_control"
            
            config = ControllerConfiguration(
                algorithm_type=ControlAlgorithmType.PID,
                pid_gains={'kp': 1.0, 'ki': 0.1, 'kd': 0.01}
            )
            
            controller = PIDController()
            await controller.initialize(config, tank_config)
            
            self.controllers[tank_id][controller_id] = controller
            self.active_setpoints[tank_id][controller_id] = tank_config.control_parameters.pressure_setpoint
    
    async def compute_control_actions(self, tank_id: str, tank_state: TankStateData) -> Dict[str, float]:
        """Compute control actions for all controllers of a tank"""
        if tank_id not in self.controllers:
            logger.error(f"Tank {tank_id} not found in control manager")
            return {}
        
        control_actions = {}
        
        try:
            # Check control mode
            if self.control_modes[tank_id] != ControlMode.AUTO:
                return control_actions
            
            tank_controllers = self.controllers[tank_id]
            tank_setpoints = self.active_setpoints[tank_id]
            
            # Temperature control
            for controller_id, controller in tank_controllers.items():
                if controller_id.startswith('temp_zone_'):
                    zone_num = int(controller_id.split('_')[-1])
                    zone_key = f"zone_{zone_num}"
                    
                    if zone_key in tank_state.temperature:
                        current_temp = tank_state.temperature[zone_key]
                        setpoint = tank_setpoints.get(controller_id, 160.0)
                        
                        control_output = await controller.compute_control_action(
                            setpoint, current_temp, 1.0
                        )
                        
                        control_actions[f"heater_zone_{zone_num}"] = control_output
                
                elif controller_id == "level_control":
                    if tank_state.level:
                        current_level = list(tank_state.level.values())[0]
                        setpoint = tank_setpoints.get(controller_id, 50.0)
                        
                        control_output = await controller.compute_control_action(
                            setpoint, current_level, 1.0
                        )
                        
                        control_actions["pump_speed"] = control_output
                
                elif controller_id == "pressure_control":
                    if tank_state.pressure:
                        current_pressure = list(tank_state.pressure.values())[0]
                        setpoint = tank_setpoints.get(controller_id, 1.0)
                        
                        control_output = await controller.compute_control_action(
                            setpoint, current_pressure, 1.0
                        )
                        
                        control_actions["pressure_control"] = control_output
            
            # Update performance metrics
            await self._update_control_performance(tank_id, control_actions)
            
            return control_actions
            
        except Exception as e:
            logger.error(f"Error computing control actions for tank {tank_id}: {e}")
            return {}
    
    async def set_control_mode(self, tank_id: str, mode: ControlMode):
        """Set control mode for a tank"""
        if tank_id in self.control_modes:
            old_mode = self.control_modes[tank_id]
            self.control_modes[tank_id] = mode
            
            if mode == ControlMode.AUTO and old_mode != ControlMode.AUTO:
                # Reset controllers when switching to auto
                for controller in self.controllers[tank_id].values():
                    await controller.reset()
            
            logger.info(f"Control mode for tank {tank_id} changed from {old_mode.value} to {mode.value}")
    
    async def set_setpoint(self, tank_id: str, controller_id: str, setpoint: float):
        """Set setpoint for a specific controller"""
        if tank_id in self.active_setpoints and controller_id in self.active_setpoints[tank_id]:
            self.active_setpoints[tank_id][controller_id] = setpoint
            logger.info(f"Setpoint for {tank_id}.{controller_id} set to {setpoint}")
    
    async def tune_controller(self, tank_id: str, controller_id: str, parameters: Dict[str, Any]):
        """Tune controller parameters"""
        if (tank_id in self.controllers and 
            controller_id in self.controllers[tank_id]):
            
            controller = self.controllers[tank_id][controller_id]
            await controller.update_parameters(parameters)
            
            logger.info(f"Tuned controller {tank_id}.{controller_id} with parameters: {parameters}")
    
    def get_control_status(self, tank_id: str) -> Dict[str, Any]:
        """Get control status for a tank"""
        if tank_id not in self.controllers:
            return {}
        
        status = {
            'tank_id': tank_id,
            'control_mode': self.control_modes[tank_id].value,
            'setpoints': self.active_setpoints[tank_id].copy(),
            'controllers': {},
            'performance': self.control_performance.get(tank_id, {})
        }
        
        for controller_id, controller in self.controllers[tank_id].items():
            status['controllers'][controller_id] = controller.get_parameters()
        
        return status
    
    async def _update_control_performance(self, tank_id: str, control_actions: Dict[str, float]):
        """Update control performance metrics"""
        if tank_id not in self.control_performance:
            self.control_performance[tank_id] = {}
        
        performance = self.control_performance[tank_id]
        
        # Update timestamp
        performance['last_update'] = datetime.now().isoformat()
        
        # Track control activity
        if 'control_actions_count' not in performance:
            performance['control_actions_count'] = 0
        performance['control_actions_count'] += 1
        
        # Track average control outputs
        for action, value in control_actions.items():
            avg_key = f"avg_{action}"
            if avg_key not in performance:
                performance[avg_key] = value
            else:
                # Exponential moving average
                alpha = 0.1
                performance[avg_key] = alpha * value + (1 - alpha) * performance[avg_key]


# Factory function for creating controllers
def create_controller(algorithm_type: ControlAlgorithmType, 
                     config: ControllerConfiguration) -> ControlAlgorithm:
    """Factory function to create control algorithms"""
    
    if algorithm_type == ControlAlgorithmType.PID:
        return PIDController()
    elif algorithm_type == ControlAlgorithmType.MODEL_PREDICTIVE:
        return ModelPredictiveController()
    elif algorithm_type == ControlAlgorithmType.ADAPTIVE:
        return AdaptiveController()
    else:
        raise ValueError(f"Unsupported control algorithm type: {algorithm_type}")


# Utility functions for controller tuning
def calculate_pid_gains_ziegler_nichols(ultimate_gain: float, ultimate_period: float) -> Dict[str, float]:
    """Calculate PID gains using Ziegler-Nichols method"""
    kp = 0.6 * ultimate_gain
    ki = 2 * kp / ultimate_period
    kd = kp * ultimate_period / 8
    
    return {'kp': kp, 'ki': ki, 'kd': kd}


def calculate_pid_gains_cohen_coon(process_gain: float, time_constant: float, dead_time: float) -> Dict[str, float]:
    """Calculate PID gains using Cohen-Coon method"""
    tau_over_theta = time_constant / dead_time
    
    kp = (1.35 / process_gain) * (tau_over_theta + 0.25) / tau_over_theta
    ki = kp / (time_constant * (2.5 + dead_time / time_constant) / (1 + dead_time / time_constant))
    kd = kp * time_constant * 0.37 / (1 + 0.37 * dead_time / time_constant)
    
    return {'kp': kp, 'ki': ki, 'kd': kd}