# Enhanced Kubernetes namespace for MLOps Digital Twin Platform
apiVersion: v1
kind: Namespace
metadata:
  name: digital-twin
  labels:
    name: digital-twin
    app.kubernetes.io/name: digital-twin
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: platform
    app.kubernetes.io/part-of: mlops-digital-twin
    kubernetes.io/managed-by: "kubectl"
  annotations:
    description: "MLOps Digital Twin Platform for Predictive Asphalt Tank Control"
    contact: "<EMAIL>"
    
---
# Resource quotas for the namespace
apiVersion: v1
kind: ResourceQuota
metadata:
  name: digital-twin-quota
  namespace: digital-twin
spec:
  hard:
    requests.cpu: "4"
    requests.memory: "8Gi"
    limits.cpu: "8"
    limits.memory: "16Gi"
    persistentvolumeclaims: "10"
    services: "20"
    secrets: "20"
    configmaps: "20"

---
# Network policies for security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: digital-twin-network-policy
  namespace: digital-twin
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: digital-twin
    - namespaceSelector:
        matchLabels:
          name: monitoring
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: digital-twin
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
  - to: []
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 443