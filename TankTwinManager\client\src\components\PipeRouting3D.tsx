import React, { useRef, useMemo } from 'react';
import { useFrame } from '@react-three/fiber';
import { Mesh, Vector3, CatmullRomCurve3, TubeGeometry } from 'three';
import { Line } from '@react-three/drei';

interface PipeSegment {
  id: string;
  type: 'hot_oil' | 'asphalt' | 'fuel' | 'steam';
  startPosition: [number, number, number];
  endPosition: [number, number, number];
  controlPoints?: [number, number, number][];
  diameter: number;
  isActive: boolean;
  flowRate: number;
  temperature: number;
  pressure: number;
}

interface PipeRouting3DProps {
  pipeSegments: PipeSegment[];
  showFlow: boolean;
  showLabels: boolean;
}

export function PipeRouting3D({ pipeSegments, showFlow, showLabels }: PipeRouting3DProps) {
  const flowParticlesRef = useRef<Mesh[]>([]);

  // Animate flow particles
  useFrame((state) => {
    if (showFlow) {
      flowParticlesRef.current.forEach((particle, index) => {
        if (particle) {
          // Move particles along pipe direction
          particle.position.x += Math.sin(state.clock.elapsedTime + index) * 0.01;
          particle.position.z += Math.cos(state.clock.elapsedTime + index) * 0.01;
        }
      });
    }
  });

  const getPipeColor = (type: string, isActive: boolean, temperature: number) => {
    if (!isActive) return '#666666';
    
    switch (type) {
      case 'hot_oil':
        // Color based on temperature
        if (temperature > 280) return '#FF6600'; // Hot orange
        if (temperature > 250) return '#FFD700'; // Gold
        return '#FFFF00'; // Yellow
      case 'asphalt':
        return '#2a2a2a'; // Black
      case 'fuel':
        return '#8B4513'; // Brown
      case 'steam':
        return '#E0E0E0'; // Light gray
      default:
        return '#666666';
    }
  };

  const createPipePath = (segment: PipeSegment) => {
    const start = new Vector3(...segment.startPosition);
    const end = new Vector3(...segment.endPosition);
    
    if (segment.controlPoints && segment.controlPoints.length > 0) {
      // Create curved pipe using control points
      const points = [
        start,
        ...segment.controlPoints.map(p => new Vector3(...p)),
        end
      ];
      return new CatmullRomCurve3(points);
    } else {
      // Straight pipe
      return new CatmullRomCurve3([start, end]);
    }
  };

  return (
    <group>
      {pipeSegments.map((segment) => {
        const curve = createPipePath(segment);
        const points = curve.getPoints(50);
        const color = getPipeColor(segment.type, segment.isActive, segment.temperature);
        
        return (
          <group key={segment.id}>
            {/* Main pipe */}
            <mesh>
              <tubeGeometry args={[curve, 20, segment.diameter / 2, 8, false]} />
              <meshStandardMaterial 
                color={color}
                emissive={segment.isActive ? color : '#000000'}
                emissiveIntensity={segment.isActive ? 0.1 : 0}
                roughness={0.3}
                metalness={0.7}
              />
            </mesh>

            {/* Pipe insulation (for hot-oil pipes) */}
            {segment.type === 'hot_oil' && (
              <mesh>
                <tubeGeometry args={[curve, 20, (segment.diameter / 2) + 0.05, 8, false]} />
                <meshStandardMaterial 
                  color="#888888"
                  transparent
                  opacity={0.3}
                  roughness={0.8}
                  metalness={0.2}
                />
              </mesh>
            )}

            {/* Flow direction indicators */}
            {segment.isActive && showFlow && (
              <group>
                {points.slice(0, -1).map((point, index) => {
                  if (index % 5 === 0) { // Show every 5th point
                    const nextPoint = points[index + 1];
                    const direction = nextPoint.clone().sub(point).normalize();
                    
                    return (
                      <mesh 
                        key={`flow-${index}`}
                        position={[point.x, point.y, point.z]}
                        lookAt={[
                          point.x + direction.x,
                          point.y + direction.y,
                          point.z + direction.z
                        ]}
                      >
                        <coneGeometry args={[0.02, 0.1, 4]} />
                        <meshStandardMaterial 
                          color={color}
                          emissive={color}
                          emissiveIntensity={0.5}
                        />
                      </mesh>
                    );
                  }
                  return null;
                })}
              </group>
            )}

            {/* Flow particles for active pipes */}
            {segment.isActive && showFlow && segment.flowRate > 0 && (
              <group>
                {[0, 1, 2].map((particleIndex) => {
                  const t = (particleIndex / 3) + (Date.now() / 5000) % 1;
                  const position = curve.getPoint(t % 1);
                  
                  return (
                    <mesh 
                      key={`particle-${particleIndex}`}
                      position={[position.x, position.y, position.z]}
                      ref={(ref) => {
                        if (ref) flowParticlesRef.current[particleIndex] = ref;
                      }}
                    >
                      <sphereGeometry args={[0.03, 6, 6]} />
                      <meshStandardMaterial 
                        color={color}
                        emissive={color}
                        emissiveIntensity={0.8}
                        transparent
                        opacity={0.8}
                      />
                    </mesh>
                  );
                })}
              </group>
            )}

            {/* Pipe supports */}
            {points.slice(0, -1).map((point, index) => {
              if (index % 10 === 0 && point.y > -1.5) { // Support every 10th point above ground
                return (
                  <mesh 
                    key={`support-${index}`}
                    position={[point.x, point.y - 0.3, point.z]}
                  >
                    <cylinderGeometry args={[0.05, 0.05, 0.6, 6]} />
                    <meshStandardMaterial 
                      color="#444444"
                      roughness={0.6}
                      metalness={0.8}
                    />
                  </mesh>
                );
              }
              return null;
            })}

            {/* Pipe labels */}
            {showLabels && (
              <group>
                {/* Pipe ID label */}
                <mesh position={[
                  (segment.startPosition[0] + segment.endPosition[0]) / 2,
                  (segment.startPosition[1] + segment.endPosition[1]) / 2 + 0.3,
                  (segment.startPosition[2] + segment.endPosition[2]) / 2
                ]}>
                  <planeGeometry args={[1, 0.2]} />
                  <meshStandardMaterial 
                    color="#000000"
                    transparent
                    opacity={0.7}
                  />
                </mesh>
                
                {/* Flow rate indicator */}
                {segment.isActive && segment.flowRate > 0 && (
                  <mesh position={[
                    (segment.startPosition[0] + segment.endPosition[0]) / 2,
                    (segment.startPosition[1] + segment.endPosition[1]) / 2 + 0.5,
                    (segment.startPosition[2] + segment.endPosition[2]) / 2
                  ]}>
                    <planeGeometry args={[0.8, 0.15]} />
                    <meshStandardMaterial 
                      color={segment.flowRate > 500 ? '#00ff00' : '#ffaa00'}
                      transparent
                      opacity={0.8}
                    />
                  </mesh>
                )}
              </group>
            )}

            {/* Valves and fittings */}
            {segment.controlPoints && segment.controlPoints.map((controlPoint, cpIndex) => (
              <group key={`valve-${cpIndex}`} position={controlPoint}>
                {/* Valve body */}
                <mesh>
                  <boxGeometry args={[0.2, 0.2, 0.2]} />
                  <meshStandardMaterial 
                    color="#666666"
                    roughness={0.2}
                    metalness={0.9}
                  />
                </mesh>
                
                {/* Valve handle */}
                <mesh position={[0, 0.15, 0]}>
                  <cylinderGeometry args={[0.03, 0.03, 0.1, 6]} />
                  <meshStandardMaterial 
                    color="#333333"
                    roughness={0.4}
                    metalness={0.8}
                  />
                </mesh>
                
                {/* Valve status indicator */}
                <mesh position={[0, 0.25, 0]}>
                  <sphereGeometry args={[0.02, 6, 6]} />
                  <meshStandardMaterial 
                    color={segment.isActive ? '#00ff00' : '#ff0000'}
                    emissive={segment.isActive ? '#00ff00' : '#ff0000'}
                    emissiveIntensity={0.8}
                  />
                </mesh>
              </group>
            ))}
          </group>
        );
      })}

      {/* Pipe legend */}
      {showLabels && (
        <group position={[20, 2, -20]}>
          <mesh>
            <planeGeometry args={[4, 3]} />
            <meshStandardMaterial 
              color="#000000"
              transparent
              opacity={0.8}
            />
          </mesh>
          
          {/* Legend items */}
          <mesh position={[-1.5, 1, 0.01]}>
            <cylinderGeometry args={[0.05, 0.05, 0.5, 8]} />
            <meshStandardMaterial color="#FFD700" />
          </mesh>
          
          <mesh position={[-1.5, 0.5, 0.01]}>
            <cylinderGeometry args={[0.05, 0.05, 0.5, 8]} />
            <meshStandardMaterial color="#2a2a2a" />
          </mesh>
          
          <mesh position={[-1.5, 0, 0.01]}>
            <cylinderGeometry args={[0.05, 0.05, 0.5, 8]} />
            <meshStandardMaterial color="#8B4513" />
          </mesh>
          
          <mesh position={[-1.5, -0.5, 0.01]}>
            <cylinderGeometry args={[0.05, 0.05, 0.5, 8]} />
            <meshStandardMaterial color="#E0E0E0" />
          </mesh>
        </group>
      )}
    </group>
  );
}
