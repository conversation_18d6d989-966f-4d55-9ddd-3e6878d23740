"""
Tank System Data Models
Comprehensive data models for industrial tank control systems
"""

import asyncio
import logging
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import numpy as np
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class TankState(Enum):
    """Tank operational states"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    MAINTENANCE = "maintenance"
    ERROR = "error"
    EMERGENCY_STOP = "emergency_stop"


class TankType(Enum):
    """Tank types"""
    STORAGE = "storage"
    PROCESS = "process"
    MIXING = "mixing"
    HEATING = "heating"
    COOLING = "cooling"
    REACTION = "reaction"


class SensorType(Enum):
    """Sensor types"""
    TEMPERATURE = "temperature"
    PRESSURE = "pressure"
    LEVEL = "level"
    FLOW = "flow"
    PH = "ph"
    CONDUCTIVITY = "conductivity"
    DENSITY = "density"
    VISCOSITY = "viscosity"


class ActuatorType(Enum):
    """Actuator types"""
    HEATER = "heater"
    COOLER = "cooler"
    PUMP = "pump"
    VALVE = "valve"
    MIXER = "mixer"
    AGITATOR = "agitator"
    BLOWER = "blower"


class AlarmSeverity(Enum):
    """Alarm severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ControlMode(Enum):
    """Control modes"""
    MANUAL = "manual"
    AUTO = "auto"
    SEMI_AUTO = "semi_auto"
    PROGRAM = "program"
    REMOTE = "remote"


@dataclass
class TankGeometry:
    """Tank physical geometry"""
    tank_type: TankType
    capacity: float  # liters
    diameter: float  # meters
    height: float  # meters
    volume: float  # cubic meters
    surface_area: float  # square meters
    insulation_thickness: float = 0.0  # meters
    material: str = "steel"
    design_pressure: float = 0.0  # bar
    design_temperature: float = 100.0  # celsius


@dataclass
class TankSensor:
    """Tank sensor configuration"""
    sensor_id: str
    sensor_type: SensorType
    description: str
    unit: str
    min_value: float
    max_value: float
    accuracy: float
    location: str  # physical location in tank
    node_id: str  # OPC UA node ID
    alarm_low: Optional[float] = None
    alarm_high: Optional[float] = None
    alarm_low_low: Optional[float] = None
    alarm_high_high: Optional[float] = None
    current_value: Optional[float] = None
    last_update: Optional[datetime] = None
    quality: str = "GOOD"
    is_active: bool = True


@dataclass
class TankActuator:
    """Tank actuator configuration"""
    actuator_id: str
    actuator_type: ActuatorType
    description: str
    unit: str
    min_value: float
    max_value: float
    location: str
    node_id: str  # OPC UA node ID
    current_value: Optional[float] = None
    setpoint: Optional[float] = None
    last_update: Optional[datetime] = None
    is_active: bool = True
    is_enabled: bool = True
    control_mode: ControlMode = ControlMode.MANUAL


@dataclass
class TankControlParameters:
    """Tank control parameters"""
    temperature_setpoint: float = 25.0  # celsius
    temperature_deadband: float = 2.0  # celsius
    pressure_setpoint: float = 1.0  # bar
    pressure_deadband: float = 0.1  # bar
    level_setpoint: float = 50.0  # percent
    level_deadband: float = 5.0  # percent
    flow_setpoint: float = 0.0  # liters/hour
    flow_deadband: float = 10.0  # liters/hour
    heating_power_limit: float = 100.0  # percent
    cooling_power_limit: float = 100.0  # percent
    mixing_speed_limit: float = 100.0  # rpm
    pump_speed_limit: float = 100.0  # rpm
    control_mode: ControlMode = ControlMode.MANUAL
    auto_start_enabled: bool = False
    safety_interlock_enabled: bool = True


@dataclass
class TankAlarm:
    """Tank alarm definition"""
    alarm_id: str
    description: str
    severity: AlarmSeverity
    condition: str  # alarm condition expression
    enabled: bool = True
    acknowledged: bool = False
    active: bool = False
    first_occurrence: Optional[datetime] = None
    last_occurrence: Optional[datetime] = None
    acknowledgment_time: Optional[datetime] = None
    acknowledged_by: Optional[str] = None


@dataclass
class TankStateData:
    """Real-time tank state data"""
    timestamp: datetime
    tank_id: str
    state: TankState
    
    # Process variables
    temperature: Dict[str, float] = field(default_factory=dict)
    pressure: Dict[str, float] = field(default_factory=dict)
    level: Dict[str, float] = field(default_factory=dict)
    flow: Dict[str, float] = field(default_factory=dict)
    ph: Dict[str, float] = field(default_factory=dict)
    conductivity: Dict[str, float] = field(default_factory=dict)
    density: Dict[str, float] = field(default_factory=dict)
    viscosity: Dict[str, float] = field(default_factory=dict)
    
    # Control outputs
    heater_output: Dict[str, float] = field(default_factory=dict)
    cooler_output: Dict[str, float] = field(default_factory=dict)
    pump_output: Dict[str, float] = field(default_factory=dict)
    valve_position: Dict[str, float] = field(default_factory=dict)
    mixer_speed: Dict[str, float] = field(default_factory=dict)
    
    # Calculated values
    volume_current: float = 0.0  # liters
    mass_current: float = 0.0  # kg
    energy_content: float = 0.0  # kJ
    heat_loss_rate: float = 0.0  # kW
    
    # Quality indicators
    data_quality: float = 1.0  # 0-1 scale
    model_confidence: float = 1.0  # 0-1 scale
    synchronization_error: float = 0.0  # deviation from physical
    
    # Alarms
    active_alarms: List[str] = field(default_factory=list)
    alarm_count: int = 0


@dataclass
class TankConfiguration:
    """Complete tank configuration"""
    tank_id: str
    name: str
    description: str
    geometry: TankGeometry
    sensors: List[TankSensor]
    actuators: List[TankActuator]
    control_parameters: TankControlParameters
    alarms: List[TankAlarm]
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    is_active: bool = True
    
    # Digital twin specific
    twin_id: str = ""
    update_frequency: float = 1.0  # Hz
    synchronization_threshold: float = 0.1
    physics_model_version: str = "v1.0"
    
    # Process parameters
    fluid_properties: Dict[str, float] = field(default_factory=dict)
    thermal_properties: Dict[str, float] = field(default_factory=dict)
    
    def get_sensor_by_id(self, sensor_id: str) -> Optional[TankSensor]:
        """Get sensor by ID"""
        for sensor in self.sensors:
            if sensor.sensor_id == sensor_id:
                return sensor
        return None
    
    def get_actuator_by_id(self, actuator_id: str) -> Optional[TankActuator]:
        """Get actuator by ID"""
        for actuator in self.actuators:
            if actuator.actuator_id == actuator_id:
                return actuator
        return None
    
    def get_sensors_by_type(self, sensor_type: SensorType) -> List[TankSensor]:
        """Get sensors by type"""
        return [s for s in self.sensors if s.sensor_type == sensor_type]
    
    def get_actuators_by_type(self, actuator_type: ActuatorType) -> List[TankActuator]:
        """Get actuators by type"""
        return [a for a in self.actuators if a.actuator_type == actuator_type]


@dataclass
class TankGroupConfiguration:
    """Configuration for a group of tanks"""
    group_id: str
    name: str
    description: str
    tanks: List[TankConfiguration]
    inter_tank_connections: List[Dict[str, Any]] = field(default_factory=list)
    shared_utilities: List[Dict[str, Any]] = field(default_factory=list)
    group_control_parameters: Dict[str, Any] = field(default_factory=dict)
    sequence_programs: List[Dict[str, Any]] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    is_active: bool = True
    
    def get_tank_by_id(self, tank_id: str) -> Optional[TankConfiguration]:
        """Get tank by ID"""
        for tank in self.tanks:
            if tank.tank_id == tank_id:
                return tank
        return None
    
    def get_tanks_by_type(self, tank_type: TankType) -> List[TankConfiguration]:
        """Get tanks by type"""
        return [t for t in self.tanks if t.geometry.tank_type == tank_type]


class TankControlStrategy(ABC):
    """Abstract base class for tank control strategies"""
    
    @abstractmethod
    async def calculate_control_output(self, 
                                     tank_state: TankStateData,
                                     setpoint: float,
                                     control_params: TankControlParameters) -> float:
        """Calculate control output"""
        pass


class PIDController(TankControlStrategy):
    """PID controller for tank control"""
    
    def __init__(self, kp: float = 1.0, ki: float = 0.0, kd: float = 0.0):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.integral = 0.0
        self.previous_error = 0.0
        self.last_time = None
    
    async def calculate_control_output(self, 
                                     tank_state: TankStateData,
                                     setpoint: float,
                                     control_params: TankControlParameters) -> float:
        """Calculate PID control output"""
        current_time = datetime.now()
        
        if self.last_time is None:
            self.last_time = current_time
            return 0.0
        
        # Get current process variable (example: temperature)
        current_value = 0.0
        if tank_state.temperature:
            current_value = next(iter(tank_state.temperature.values()))
        
        # Calculate error
        error = setpoint - current_value
        
        # Calculate time delta
        dt = (current_time - self.last_time).total_seconds()
        
        # Calculate PID terms
        proportional = self.kp * error
        
        self.integral += error * dt
        integral_term = self.ki * self.integral
        
        derivative = (error - self.previous_error) / dt if dt > 0 else 0.0
        derivative_term = self.kd * derivative
        
        # Calculate output
        output = proportional + integral_term + derivative_term
        
        # Update for next iteration
        self.previous_error = error
        self.last_time = current_time
        
        # Clamp output to limits
        output = max(-100.0, min(100.0, output))
        
        return output


@dataclass
class TankPhysicsModel:
    """Physics model parameters for tank simulation"""
    
    # Thermal properties
    thermal_conductivity: float = 0.6  # W/m/K
    specific_heat: float = 4186.0  # J/kg/K (water)
    density: float = 1000.0  # kg/m³
    convection_coefficient: float = 10.0  # W/m²/K
    
    # Heat transfer
    ambient_temperature: float = 20.0  # celsius
    insulation_r_value: float = 1.0  # m²K/W
    
    # Fluid dynamics
    viscosity: float = 0.001  # Pa·s
    reynolds_number: float = 10000.0
    prandtl_number: float = 7.0
    
    # Mass transfer
    diffusion_coefficient: float = 1e-9  # m²/s
    mass_transfer_coefficient: float = 1e-5  # m/s
    
    # Reaction kinetics (if applicable)
    reaction_rate_constant: float = 0.0  # depends on reaction
    activation_energy: float = 0.0  # J/mol
    
    # Mixing
    mixing_time_constant: float = 60.0  # seconds
    mixing_efficiency: float = 0.8  # 0-1 scale
    
    def calculate_heat_transfer_coefficient(self, flow_rate: float) -> float:
        """Calculate heat transfer coefficient based on flow"""
        # Simplified correlation
        if flow_rate > 0:
            return self.convection_coefficient * (flow_rate / 1000.0) ** 0.8
        return self.convection_coefficient
    
    def calculate_mixing_time(self, volume: float, agitator_speed: float) -> float:
        """Calculate mixing time"""
        if agitator_speed > 0:
            return self.mixing_time_constant * (volume / 1000.0) / (agitator_speed / 100.0)
        return float('inf')


@dataclass
class TankPerformanceMetrics:
    """Tank performance metrics"""
    timestamp: datetime
    tank_id: str
    
    # Energy metrics
    energy_consumption: float = 0.0  # kWh
    energy_efficiency: float = 0.0  # percent
    heat_loss_rate: float = 0.0  # kW
    
    # Process metrics
    temperature_stability: float = 0.0  # std deviation
    pressure_stability: float = 0.0  # std deviation
    level_stability: float = 0.0  # std deviation
    
    # Control metrics
    control_accuracy: float = 0.0  # percent
    settling_time: float = 0.0  # seconds
    overshoot: float = 0.0  # percent
    
    # Reliability metrics
    uptime: float = 0.0  # percent
    mtbf: float = 0.0  # mean time between failures (hours)
    mttr: float = 0.0  # mean time to repair (hours)
    
    # Quality metrics
    product_quality_index: float = 0.0  # 0-1 scale
    batch_success_rate: float = 0.0  # percent
    
    # Maintenance metrics
    maintenance_cost: float = 0.0  # currency
    maintenance_frequency: float = 0.0  # per month
    
    # Alarm metrics
    alarm_rate: float = 0.0  # alarms per hour
    false_alarm_rate: float = 0.0  # percent
    
    # Environmental metrics
    emissions: float = 0.0  # kg CO2 equivalent
    waste_generated: float = 0.0  # kg
    water_usage: float = 0.0  # liters