"""
Application Handling Layer
Multi-device user interfaces for researchers, device graphics, and AI applications
Based on the architecture diagram's Application Handling component
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
from typing import Dict, List, Optional, Any, Union, Callable
import json
import numpy as np
import torch
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
from pydantic import BaseModel

from ..core.architecture_framework import (
    PipelineComponent, ComponentType, ProcessingStage, PipelineData,
    pipeline_orchestrator, create_pipeline_data
)

logger = logging.getLogger(__name__)


class UserRole(Enum):
    """User roles in the system"""
    RESEARCHER = "researcher"
    ENGINEER = "engineer"
    OPERATOR = "operator"
    ADMIN = "admin"
    AI_DEVELOPER = "ai_developer"
    DEVICE_GRAPHICS = "device_graphics"


class InterfaceType(Enum):
    """Types of user interfaces"""
    WEB_DASHBOARD = "web_dashboard"
    MOBILE_APP = "mobile_app"
    VR_INTERFACE = "vr_interface"
    AR_INTERFACE = "ar_interface"
    API_CLIENT = "api_client"
    JUPYTER_NOTEBOOK = "jupyter_notebook"
    DESKTOP_APP = "desktop_app"


@dataclass
class UserSession:
    """User session information"""
    session_id: str
    user_id: str
    user_role: UserRole
    interface_type: InterfaceType
    connected_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    last_activity: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    preferences: Dict[str, Any] = field(default_factory=dict)
    permissions: List[str] = field(default_factory=list)


class UserRequest(BaseModel):
    """User request model"""
    request_id: str
    user_id: str
    session_id: str
    action: str
    parameters: Dict[str, Any] = {}
    timestamp: datetime = datetime.now(timezone.utc)


class UserResponse(BaseModel):
    """User response model"""
    response_id: str
    request_id: str
    status: str
    data: Dict[str, Any] = {}
    timestamp: datetime = datetime.now(timezone.utc)
    error_message: Optional[str] = None


class ApplicationInterface(ABC):
    """Abstract base class for application interfaces"""
    
    def __init__(self, interface_id: str, interface_type: InterfaceType):
        self.interface_id = interface_id
        self.interface_type = interface_type
        self.active_sessions: Dict[str, UserSession] = {}
        self.request_handlers: Dict[str, Callable] = {}
        
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the interface"""
        pass
    
    @abstractmethod
    async def handle_user_request(self, request: UserRequest) -> UserResponse:
        """Handle user request"""
        pass
    
    @abstractmethod
    async def send_update(self, session_id: str, update_data: Dict[str, Any]):
        """Send update to user session"""
        pass
    
    def register_handler(self, action: str, handler: Callable):
        """Register a handler for a specific action"""
        self.request_handlers[action] = handler
    
    def add_session(self, session: UserSession):
        """Add user session"""
        self.active_sessions[session.session_id] = session
        logger.info(f"Added session {session.session_id} for user {session.user_id}")
    
    def remove_session(self, session_id: str):
        """Remove user session"""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
            logger.info(f"Removed session {session_id}")


class WebDashboardInterface(ApplicationInterface):
    """Web dashboard interface for researchers and engineers"""
    
    def __init__(self):
        super().__init__("web_dashboard", InterfaceType.WEB_DASHBOARD)
        self.app = FastAPI(title="AI Pipeline Dashboard")
        self.websocket_connections: Dict[str, WebSocket] = {}
        self._setup_routes()
    
    async def initialize(self) -> bool:
        """Initialize web dashboard"""
        try:
            # Add CORS middleware
            self.app.add_middleware(
                CORSMiddleware,
                allow_origins=["*"],
                allow_credentials=True,
                allow_methods=["*"],
                allow_headers=["*"],
            )
            
            # Mount static files if directory exists
            static_path = "static"
            try:
                self.app.mount("/static", StaticFiles(directory=static_path), name="static")
            except Exception:
                logger.warning("Static files directory not found")
            
            logger.info("Web dashboard interface initialized")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize web dashboard: {e}")
            return False
    
    def _setup_routes(self):
        """Setup FastAPI routes"""
        
        @self.app.get("/")
        async def dashboard_home():
            return {"message": "AI Pipeline Dashboard", "status": "active"}
        
        @self.app.get("/api/pipeline/status")
        async def get_pipeline_status():
            """Get pipeline status"""
            return pipeline_orchestrator.get_pipeline_status()
        
        @self.app.post("/api/pipeline/process")
        async def process_data(request: UserRequest):
            """Process data through pipeline"""
            try:
                # Create pipeline data from request
                pipeline_data = create_pipeline_data(
                    data=request.parameters,
                    component_type=ComponentType.APPLICATION_HANDLING
                )
                
                # Process through pipeline
                result = await pipeline_orchestrator.process_data(
                    pipeline_data, "application_handling"
                )
                
                return UserResponse(
                    response_id=f"resp_{request.request_id}",
                    request_id=request.request_id,
                    status="success",
                    data=result.data
                )
            except Exception as e:
                logger.error(f"Error processing request: {e}")
                return UserResponse(
                    response_id=f"resp_{request.request_id}",
                    request_id=request.request_id,
                    status="error",
                    error_message=str(e)
                )
        
        @self.app.websocket("/ws/{session_id}")
        async def websocket_endpoint(websocket: WebSocket, session_id: str):
            """WebSocket endpoint for real-time updates"""
            await websocket.accept()
            self.websocket_connections[session_id] = websocket
            
            try:
                while True:
                    # Receive message from client
                    data = await websocket.receive_json()
                    
                    # Process message
                    await self._handle_websocket_message(session_id, data)
                    
            except WebSocketDisconnect:
                if session_id in self.websocket_connections:
                    del self.websocket_connections[session_id]
                logger.info(f"WebSocket disconnected: {session_id}")
        
        @self.app.get("/api/models")
        async def list_models():
            """List available models"""
            from ..core.architecture_framework import model_registry
            return {"models": model_registry.list_models()}
        
        @self.app.get("/api/devices")
        async def get_device_info():
            """Get device information"""
            from ..core.architecture_framework import device_manager
            return device_manager.get_device_info()
    
    async def _handle_websocket_message(self, session_id: str, message: Dict[str, Any]):
        """Handle WebSocket message"""
        try:
            action = message.get("action")
            data = message.get("data", {})
            
            if action == "simulation_update":
                # Handle simulation update request
                await self._handle_simulation_update(session_id, data)
            elif action == "model_inference":
                # Handle model inference request
                await self._handle_model_inference(session_id, data)
            elif action == "training_status":
                # Handle training status request
                await self._handle_training_status(session_id)
            
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {e}")
            await self.send_error(session_id, str(e))
    
    async def _handle_simulation_update(self, session_id: str, data: Dict[str, Any]):
        """Handle simulation update"""
        # This would integrate with the World State Controller
        response = {
            "type": "simulation_update",
            "data": {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "simulation_data": data
            }
        }
        await self.send_update(session_id, response)
    
    async def _handle_model_inference(self, session_id: str, data: Dict[str, Any]):
        """Handle model inference request"""
        # This would integrate with the AI Model Training component
        response = {
            "type": "model_inference",
            "data": {
                "prediction": "example_prediction",
                "confidence": 0.95,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        }
        await self.send_update(session_id, response)
    
    async def _handle_training_status(self, session_id: str):
        """Handle training status request"""
        response = {
            "type": "training_status",
            "data": {
                "status": "training",
                "epoch": 150,
                "loss": 0.001,
                "accuracy": 0.98
            }
        }
        await self.send_update(session_id, response)
    
    async def handle_user_request(self, request: UserRequest) -> UserResponse:
        """Handle user request"""
        try:
            action = request.action
            
            if action in self.request_handlers:
                result = await self.request_handlers[action](request)
                return UserResponse(
                    response_id=f"resp_{request.request_id}",
                    request_id=request.request_id,
                    status="success",
                    data=result
                )
            else:
                return UserResponse(
                    response_id=f"resp_{request.request_id}",
                    request_id=request.request_id,
                    status="error",
                    error_message=f"Unknown action: {action}"
                )
        except Exception as e:
            logger.error(f"Error handling request: {e}")
            return UserResponse(
                response_id=f"resp_{request.request_id}",
                request_id=request.request_id,
                status="error",
                error_message=str(e)
            )
    
    async def send_update(self, session_id: str, update_data: Dict[str, Any]):
        """Send update to user session via WebSocket"""
        if session_id in self.websocket_connections:
            try:
                await self.websocket_connections[session_id].send_json(update_data)
            except Exception as e:
                logger.error(f"Error sending update to {session_id}: {e}")
                # Remove disconnected connection
                if session_id in self.websocket_connections:
                    del self.websocket_connections[session_id]
    
    async def send_error(self, session_id: str, error_message: str):
        """Send error message to session"""
        error_data = {
            "type": "error",
            "message": error_message,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        await self.send_update(session_id, error_data)
    
    async def start_server(self, host: str = "0.0.0.0", port: int = 8000):
        """Start the web server"""
        config = uvicorn.Config(self.app, host=host, port=port, log_level="info")
        server = uvicorn.Server(config)
        await server.serve()


class MobileAppInterface(ApplicationInterface):
    """Mobile app interface for field operations"""
    
    def __init__(self):
        super().__init__("mobile_app", InterfaceType.MOBILE_APP)
        self.push_notification_service = None
    
    async def initialize(self) -> bool:
        """Initialize mobile app interface"""
        try:
            # Initialize push notification service
            # This would integrate with FCM, APNS, etc.
            logger.info("Mobile app interface initialized")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize mobile app interface: {e}")
            return False
    
    async def handle_user_request(self, request: UserRequest) -> UserResponse:
        """Handle mobile app request"""
        # Mobile-specific request handling
        return UserResponse(
            response_id=f"resp_{request.request_id}",
            request_id=request.request_id,
            status="success",
            data={"message": "Mobile request processed"}
        )
    
    async def send_update(self, session_id: str, update_data: Dict[str, Any]):
        """Send push notification or real-time update"""
        # Implementation for mobile push notifications
        logger.info(f"Sending mobile update to {session_id}: {update_data}")


class VRInterface(ApplicationInterface):
    """VR interface for immersive 3D interaction"""
    
    def __init__(self):
        super().__init__("vr_interface", InterfaceType.VR_INTERFACE)
        self.vr_engine = None
    
    async def initialize(self) -> bool:
        """Initialize VR interface"""
        try:
            # Initialize VR engine (OpenXR, SteamVR, etc.)
            logger.info("VR interface initialized")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize VR interface: {e}")
            return False
    
    async def handle_user_request(self, request: UserRequest) -> UserResponse:
        """Handle VR-specific request"""
        # VR interaction handling
        return UserResponse(
            response_id=f"resp_{request.request_id}",
            request_id=request.request_id,
            status="success",
            data={"message": "VR interaction processed"}
        )
    
    async def send_update(self, session_id: str, update_data: Dict[str, Any]):
        """Send VR scene update"""
        # Update VR scene with new data
        logger.info(f"Updating VR scene for {session_id}: {update_data}")


class JupyterNotebookInterface(ApplicationInterface):
    """Jupyter notebook interface for researchers"""
    
    def __init__(self):
        super().__init__("jupyter_notebook", InterfaceType.JUPYTER_NOTEBOOK)
        self.kernel_manager = None
    
    async def initialize(self) -> bool:
        """Initialize Jupyter interface"""
        try:
            # Initialize Jupyter kernel manager
            logger.info("Jupyter notebook interface initialized")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize Jupyter interface: {e}")
            return False
    
    async def handle_user_request(self, request: UserRequest) -> UserResponse:
        """Handle notebook request"""
        # Jupyter kernel communication
        return UserResponse(
            response_id=f"resp_{request.request_id}",
            request_id=request.request_id,
            status="success",
            data={"message": "Notebook request processed"}
        )
    
    async def send_update(self, session_id: str, update_data: Dict[str, Any]):
        """Send update to notebook"""
        # Update notebook output
        logger.info(f"Updating notebook for {session_id}: {update_data}")


class ApplicationHandlingComponent(PipelineComponent):
    """Main application handling component for the pipeline"""
    
    def __init__(self):
        super().__init__("application_handling", ComponentType.APPLICATION_HANDLING)
        self.interfaces: Dict[str, ApplicationInterface] = {}
        self.user_sessions: Dict[str, UserSession] = {}
        
    async def initialize(self) -> bool:
        """Initialize application handling component"""
        try:
            # Initialize all interfaces
            interfaces = [
                WebDashboardInterface(),
                MobileAppInterface(),
                VRInterface(),
                JupyterNotebookInterface()
            ]
            
            for interface in interfaces:
                if await interface.initialize():
                    self.interfaces[interface.interface_id] = interface
                    logger.info(f"Initialized interface: {interface.interface_id}")
                else:
                    logger.error(f"Failed to initialize interface: {interface.interface_id}")
            
            self.is_initialized = True
            logger.info("Application handling component initialized")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing application handling: {e}")
            return False
    
    async def process(self, data: PipelineData) -> PipelineData:
        """Process data through application handling"""
        try:
            # Update stage
            data.stage = ProcessingStage.PREPROCESSING
            
            # Add application handling metadata
            data.metadata['application_handling'] = {
                'processed_at': datetime.now(timezone.utc).isoformat(),
                'active_interfaces': list(self.interfaces.keys()),
                'active_sessions': len(self.user_sessions)
            }
            
            # Process through interfaces if needed
            interface_type = data.data.get('interface_type')
            if interface_type and interface_type in self.interfaces:
                interface = self.interfaces[interface_type]
                # Additional interface-specific processing
                data.data['interface_processed'] = True
            
            # Update metrics
            self.update_metrics({
                'data_processed': self.metrics.get('data_processed', 0) + 1,
                'last_processing_time': datetime.now(timezone.utc).isoformat()
            })
            
            logger.debug("Data processed through application handling")
            return data
            
        except Exception as e:
            logger.error(f"Error processing data in application handling: {e}")
            raise
    
    async def cleanup(self):
        """Clean up application handling resources"""
        for interface in self.interfaces.values():
            # Cleanup interface resources
            pass
        
        self.interfaces.clear()
        self.user_sessions.clear()
        logger.info("Application handling component cleaned up")
    
    def get_interface(self, interface_id: str) -> Optional[ApplicationInterface]:
        """Get interface by ID"""
        return self.interfaces.get(interface_id)
    
    async def create_user_session(self, user_id: str, user_role: UserRole, 
                                interface_type: InterfaceType) -> UserSession:
        """Create new user session"""
        session_id = f"session_{user_id}_{datetime.now().timestamp()}"
        
        session = UserSession(
            session_id=session_id,
            user_id=user_id,
            user_role=user_role,
            interface_type=interface_type
        )
        
        self.user_sessions[session_id] = session
        
        # Add session to appropriate interface
        interface_id = interface_type.value
        if interface_id in self.interfaces:
            self.interfaces[interface_id].add_session(session)
        
        logger.info(f"Created user session: {session_id}")
        return session
    
    async def broadcast_update(self, update_data: Dict[str, Any], 
                             interface_types: List[InterfaceType] = None):
        """Broadcast update to all or specific interfaces"""
        target_interfaces = interface_types or list(InterfaceType)
        
        for interface_type in target_interfaces:
            interface_id = interface_type.value
            if interface_id in self.interfaces:
                interface = self.interfaces[interface_id]
                
                # Send to all sessions of this interface type
                for session_id, session in self.user_sessions.items():
                    if session.interface_type == interface_type:
                        await interface.send_update(session_id, update_data)


# Global application handling component instance
application_handling = ApplicationHandlingComponent()