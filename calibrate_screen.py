"""
Screen Layout Calibration Tool
Helps identify screen regions for SCADA data extraction
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
from typing import Dict, List, Optional
import os

try:
    import mss
    import PIL.Image, PIL.ImageTk
    import cv2
    import numpy as np
    MSS_AVAILABLE = True
except ImportError:
    MSS_AVAILABLE = False
    print("Warning: Please install requirements first: pip install -r requirements-minimal.txt")


class ScreenCalibrator:
    """GUI tool for calibrating screen regions"""
    
    def __init__(self):
        if not MSS_AVAILABLE:
            messagebox.showerror("Error", "Required libraries not installed.\nPlease run: pip install -r requirements-minimal.txt")
            return
        
        self.root = tk.Tk()
        self.root.title("SCADA Screen Calibration Tool")
        self.root.geometry("1200x800")
        
        self.sct = mss.mss()
        self.screenshot = None
        self.screenshot_tk = None
        self.regions = []
        self.current_region = None
        self.drawing = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Control panel
        control_frame = ttk.LabelFrame(main_frame, text="Controls")
        control_frame.pack(fill=tk.X, pady=(0, 10))\n        \n        # Buttons\n        btn_frame = ttk.Frame(control_frame)\n        btn_frame.pack(fill=tk.X, padx=10, pady=10)\n        \n        ttk.Button(btn_frame, text=\"Capture Screen\", command=self.capture_screen).pack(side=tk.LEFT, padx=(0, 5))\n        ttk.Button(btn_frame, text=\"Save Layout\", command=self.save_layout).pack(side=tk.LEFT, padx=5)\n        ttk.Button(btn_frame, text=\"Load Layout\", command=self.load_layout).pack(side=tk.LEFT, padx=5)\n        ttk.Button(btn_frame, text=\"Test OCR\", command=self.test_ocr).pack(side=tk.LEFT, padx=5)\n        ttk.Button(btn_frame, text=\"Clear All\", command=self.clear_regions).pack(side=tk.LEFT, padx=5)\n        \n        # Region info frame\n        info_frame = ttk.Frame(control_frame)\n        info_frame.pack(fill=tk.X, padx=10, pady=(0, 10))\n        \n        ttk.Label(info_frame, text=\"Region Name:\").pack(side=tk.LEFT)\n        self.region_name_var = tk.StringVar()\n        ttk.Entry(info_frame, textvariable=self.region_name_var, width=20).pack(side=tk.LEFT, padx=(5, 10))\n        \n        ttk.Label(info_frame, text=\"Type:\").pack(side=tk.LEFT)\n        self.region_type_var = tk.StringVar(value=\"number\")\n        type_combo = ttk.Combobox(info_frame, textvariable=self.region_type_var, values=[\"number\", \"text\", \"status\"], width=10)\n        type_combo.pack(side=tk.LEFT, padx=(5, 10))\n        \n        ttk.Button(info_frame, text=\"Add Region\", command=self.add_region).pack(side=tk.LEFT, padx=5)\n        \n        # Main content frame\n        content_frame = ttk.Frame(main_frame)\n        content_frame.pack(fill=tk.BOTH, expand=True)\n        \n        # Canvas for screenshot\n        canvas_frame = ttk.LabelFrame(content_frame, text=\"Screenshot (Click and drag to define regions)\")\n        canvas_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))\n        \n        self.canvas = tk.Canvas(canvas_frame, bg=\"white\")\n        canvas_scrollbar_v = ttk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)\n        canvas_scrollbar_h = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)\n        \n        self.canvas.configure(yscrollcommand=canvas_scrollbar_v.set, xscrollcommand=canvas_scrollbar_h.set)\n        \n        canvas_scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)\n        canvas_scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)\n        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)\n        \n        # Bind canvas events\n        self.canvas.bind(\"<Button-1>\", self.on_canvas_click)\n        self.canvas.bind(\"<B1-Motion>\", self.on_canvas_drag)\n        self.canvas.bind(\"<ButtonRelease-1>\", self.on_canvas_release)\n        \n        # Regions list\n        regions_frame = ttk.LabelFrame(content_frame, text=\"Defined Regions\")\n        regions_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=10)\n        \n        # Regions listbox\n        self.regions_listbox = tk.Listbox(regions_frame, width=30, height=20)\n        regions_scrollbar = ttk.Scrollbar(regions_frame, orient=tk.VERTICAL, command=self.regions_listbox.yview)\n        self.regions_listbox.configure(yscrollcommand=regions_scrollbar.set)\n        \n        regions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)\n        self.regions_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)\n        \n        # Region control buttons\n        region_btn_frame = ttk.Frame(regions_frame)\n        region_btn_frame.pack(fill=tk.X, pady=(10, 0))\n        \n        ttk.Button(region_btn_frame, text=\"Delete Selected\", command=self.delete_region).pack(fill=tk.X, pady=(0, 5))\n        ttk.Button(region_btn_frame, text=\"Edit Selected\", command=self.edit_region).pack(fill=tk.X)\n        \n        # Status bar\n        self.status_var = tk.StringVar(value=\"Ready - Click 'Capture Screen' to start\")\n        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)\n        status_bar.pack(fill=tk.X, pady=(10, 0))\n    \n    def capture_screen(self):\n        \"\"\"Capture the current screen\"\"\"\n        try:\n            self.status_var.set(\"Capturing screen...\")\n            self.root.update()\n            \n            # Capture primary monitor\n            monitor = self.sct.monitors[1]\n            screenshot = self.sct.grab(monitor)\n            \n            # Convert to PIL Image\n            img = PIL.Image.frombytes(\"RGB\", screenshot.size, screenshot.bgra, \"raw\", \"BGRX\")\n            self.screenshot = img\n            \n            # Resize for display (maintain aspect ratio)\n            display_width = 800\n            display_height = int(img.height * display_width / img.width)\n            \n            img_display = img.resize((display_width, display_height), PIL.Image.Resampling.LANCZOS)\n            self.screenshot_tk = PIL.ImageTk.PhotoImage(img_display)\n            \n            # Update canvas\n            self.canvas.delete(\"all\")\n            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.screenshot_tk)\n            self.canvas.configure(scrollregion=self.canvas.bbox(\"all\"))\n            \n            # Store scale factor for coordinate conversion\n            self.scale_x = img.width / display_width\n            self.scale_y = img.height / display_height\n            \n            self.status_var.set(\"Screen captured - Draw rectangles to define regions\")\n            \n        except Exception as e:\n            messagebox.showerror(\"Error\", f\"Failed to capture screen: {e}\")\n            self.status_var.set(\"Error capturing screen\")\n    \n    def on_canvas_click(self, event):\n        \"\"\"Handle canvas click\"\"\"\n        if self.screenshot is None:\n            messagebox.showwarning(\"Warning\", \"Please capture screen first\")\n            return\n        \n        self.drawing = True\n        self.start_x = event.x\n        self.start_y = event.y\n        \n        # Create rectangle\n        self.current_region = self.canvas.create_rectangle(\n            self.start_x, self.start_y, self.start_x, self.start_y,\n            outline=\"red\", width=2\n        )\n    \n    def on_canvas_drag(self, event):\n        \"\"\"Handle canvas drag\"\"\"\n        if self.drawing and self.current_region:\n            # Update rectangle\n            self.canvas.coords(self.current_region, self.start_x, self.start_y, event.x, event.y)\n    \n    def on_canvas_release(self, event):\n        \"\"\"Handle canvas release\"\"\"\n        if self.drawing and self.current_region:\n            self.drawing = False\n            \n            # Calculate real coordinates\n            x1 = int(min(self.start_x, event.x) * self.scale_x)\n            y1 = int(min(self.start_y, event.y) * self.scale_y)\n            x2 = int(max(self.start_x, event.x) * self.scale_x)\n            y2 = int(max(self.start_y, event.y) * self.scale_y)\n            \n            width = x2 - x1\n            height = y2 - y1\n            \n            if width > 10 and height > 10:  # Minimum size\n                self.status_var.set(f\"Region defined: ({x1}, {y1}) - {width}x{height}\")\n            else:\n                # Remove small rectangles\n                self.canvas.delete(self.current_region)\n                self.status_var.set(\"Region too small - please draw a larger area\")\n            \n            self.current_region = None\n    \n    def add_region(self):\n        \"\"\"Add the current region to the list\"\"\"\n        if not self.region_name_var.get():\n            messagebox.showwarning(\"Warning\", \"Please enter a region name\")\n            return\n        \n        # Find the last drawn rectangle\n        rectangles = [item for item in self.canvas.find_all() if self.canvas.type(item) == \"rectangle\"]\n        if not rectangles:\n            messagebox.showwarning(\"Warning\", \"Please draw a region first\")\n            return\n        \n        last_rect = rectangles[-1]\n        coords = self.canvas.coords(last_rect)\n        \n        # Convert to real coordinates\n        x1 = int(min(coords[0], coords[2]) * self.scale_x)\n        y1 = int(min(coords[1], coords[3]) * self.scale_y)\n        x2 = int(max(coords[0], coords[2]) * self.scale_x)\n        y2 = int(max(coords[1], coords[3]) * self.scale_y)\n        \n        width = x2 - x1\n        height = y2 - y1\n        \n        region = {\n            \"name\": self.region_name_var.get(),\n            \"type\": self.region_type_var.get(),\n            \"x\": x1,\n            \"y\": y1,\n            \"width\": width,\n            \"height\": height\n        }\n        \n        self.regions.append(region)\n        \n        # Update listbox\n        self.regions_listbox.insert(tk.END, f\"{region['name']} ({region['type']}) - {x1},{y1} {width}x{height}\")\n        \n        # Add label to canvas\n        label_x = (coords[0] + coords[2]) / 2\n        label_y = coords[1] - 10\n        self.canvas.create_text(label_x, label_y, text=region['name'], fill=\"red\", font=(\"Arial\", 8, \"bold\"))\n        \n        # Clear name field\n        self.region_name_var.set(\"\")\n        \n        self.status_var.set(f\"Added region: {region['name']}\")\n    \n    def delete_region(self):\n        \"\"\"Delete selected region\"\"\"\n        selection = self.regions_listbox.curselection()\n        if not selection:\n            messagebox.showwarning(\"Warning\", \"Please select a region to delete\")\n            return\n        \n        index = selection[0]\n        region = self.regions[index]\n        \n        # Remove from list\n        del self.regions[index]\n        self.regions_listbox.delete(index)\n        \n        self.status_var.set(f\"Deleted region: {region['name']}\")\n        \n        # Redraw canvas\n        self.redraw_regions()\n    \n    def edit_region(self):\n        \"\"\"Edit selected region\"\"\"\n        selection = self.regions_listbox.curselection()\n        if not selection:\n            messagebox.showwarning(\"Warning\", \"Please select a region to edit\")\n            return\n        \n        index = selection[0]\n        region = self.regions[index]\n        \n        # Fill form with region data\n        self.region_name_var.set(region['name'])\n        self.region_type_var.set(region['type'])\n        \n        # Remove from list temporarily\n        del self.regions[index]\n        self.regions_listbox.delete(index)\n        \n        self.status_var.set(f\"Editing region: {region['name']} - Draw new rectangle and click Add Region\")\n    \n    def clear_regions(self):\n        \"\"\"Clear all regions\"\"\"\n        if messagebox.askyesno(\"Confirm\", \"Clear all regions?\"):\n            self.regions.clear()\n            self.regions_listbox.delete(0, tk.END)\n            self.redraw_regions()\n            self.status_var.set(\"All regions cleared\")\n    \n    def redraw_regions(self):\n        \"\"\"Redraw all regions on canvas\"\"\"\n        if self.screenshot is None:\n            return\n        \n        # Clear and redraw screenshot\n        self.canvas.delete(\"all\")\n        self.canvas.create_image(0, 0, anchor=tk.NW, image=self.screenshot_tk)\n        \n        # Draw all regions\n        for region in self.regions:\n            # Convert to display coordinates\n            x1 = region['x'] / self.scale_x\n            y1 = region['y'] / self.scale_y\n            x2 = (region['x'] + region['width']) / self.scale_x\n            y2 = (region['y'] + region['height']) / self.scale_y\n            \n            # Draw rectangle\n            self.canvas.create_rectangle(x1, y1, x2, y2, outline=\"red\", width=2)\n            \n            # Draw label\n            label_x = (x1 + x2) / 2\n            label_y = y1 - 10\n            self.canvas.create_text(label_x, label_y, text=region['name'], fill=\"red\", font=(\"Arial\", 8, \"bold\"))\n    \n    def save_layout(self):\n        \"\"\"Save layout to JSON file\"\"\"\n        if not self.regions:\n            messagebox.showwarning(\"Warning\", \"No regions to save\")\n            return\n        \n        filename = filedialog.asksaveasfilename(\n            defaultextension=\".json\",\n            filetypes=[(\"JSON files\", \"*.json\"), (\"All files\", \"*.*\")],\n            title=\"Save Layout\"\n        )\n        \n        if filename:\n            try:\n                layout_data = {\n                    \"tank_id\": \"tank_001\",\n                    \"window_title\": \"SCADA Screen\",\n                    \"regions\": self.regions,\n                    \"screen_resolution\": {\n                        \"width\": self.screenshot.width if self.screenshot else 1920,\n                        \"height\": self.screenshot.height if self.screenshot else 1080\n                    }\n                }\n                \n                with open(filename, 'w') as f:\n                    json.dump(layout_data, f, indent=2)\n                \n                messagebox.showinfo(\"Success\", f\"Layout saved to {filename}\")\n                self.status_var.set(f\"Layout saved: {filename}\")\n                \n            except Exception as e:\n                messagebox.showerror(\"Error\", f\"Failed to save layout: {e}\")\n    \n    def load_layout(self):\n        \"\"\"Load layout from JSON file\"\"\"\n        filename = filedialog.askopenfilename(\n            filetypes=[(\"JSON files\", \"*.json\"), (\"All files\", \"*.*\")],\n            title=\"Load Layout\"\n        )\n        \n        if filename:\n            try:\n                with open(filename, 'r') as f:\n                    layout_data = json.load(f)\n                \n                self.regions = layout_data.get(\"regions\", [])\n                \n                # Update listbox\n                self.regions_listbox.delete(0, tk.END)\n                for region in self.regions:\n                    self.regions_listbox.insert(tk.END, \n                        f\"{region['name']} ({region['type']}) - {region['x']},{region['y']} {region['width']}x{region['height']}\")\n                \n                # Redraw if screenshot exists\n                self.redraw_regions()\n                \n                messagebox.showinfo(\"Success\", f\"Layout loaded from {filename}\")\n                self.status_var.set(f\"Layout loaded: {filename}\")\n                \n            except Exception as e:\n                messagebox.showerror(\"Error\", f\"Failed to load layout: {e}\")\n    \n    def test_ocr(self):\n        \"\"\"Test OCR on selected region\"\"\"\n        selection = self.regions_listbox.curselection()\n        if not selection:\n            messagebox.showwarning(\"Warning\", \"Please select a region to test\")\n            return\n        \n        if self.screenshot is None:\n            messagebox.showwarning(\"Warning\", \"Please capture screen first\")\n            return\n        \n        try:\n            index = selection[0]\n            region = self.regions[index]\n            \n            # Extract region from screenshot\n            x, y, w, h = region['x'], region['y'], region['width'], region['height']\n            region_img = self.screenshot.crop((x, y, x + w, y + h))\n            \n            # Save temporary image\n            temp_path = \"temp_region.png\"\n            region_img.save(temp_path)\n            \n            # Try OCR\n            import pytesseract\n            text = pytesseract.image_to_string(region_img, config='--psm 8')\n            \n            # Show result\n            result_window = tk.Toplevel(self.root)\n            result_window.title(f\"OCR Test - {region['name']}\")\n            result_window.geometry(\"400x300\")\n            \n            ttk.Label(result_window, text=f\"Region: {region['name']}\").pack(pady=10)\n            ttk.Label(result_window, text=f\"Coordinates: ({x}, {y}) - {w}x{h}\").pack()\n            ttk.Label(result_window, text=\"Extracted Text:\").pack(pady=(10, 0))\n            \n            text_area = tk.Text(result_window, height=10, width=50)\n            text_area.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)\n            text_area.insert(tk.END, text)\n            \n            # Clean up\n            if os.path.exists(temp_path):\n                os.remove(temp_path)\n            \n            self.status_var.set(f\"OCR test completed for {region['name']}\")\n            \n        except Exception as e:\n            messagebox.showerror(\"Error\", f\"OCR test failed: {e}\")\n    \n    def run(self):\n        \"\"\"Run the calibrator\"\"\"\n        if MSS_AVAILABLE:\n            self.root.mainloop()\n        else:\n            print(\"Required libraries not available. Please install requirements first.\")\n\n\nif __name__ == \"__main__\":\n    calibrator = ScreenCalibrator()\n    calibrator.run()"