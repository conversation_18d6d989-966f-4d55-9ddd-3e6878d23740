#!/usr/bin/env python3
"""
Simple test runner for Digital Twin System
Tests core functionality without external dependencies
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_digital_twin_core():
    """Test digital twin core functionality"""
    try:
        from digital_twin.core import DigitalTwinEngine, TwinConfiguration
        
        logger.info("Testing Digital Twin Core Engine...")
        
        # Create engine
        engine = DigitalTwinEngine()
        
        # Create twin configuration
        config = TwinConfiguration(
            twin_id="test_twin_001",
            name="Test Twin",
            description="Test digital twin for validation",
            physical_asset_id="test_asset_001",
            model_version="v1.0"
        )
        
        # Create and start twin
        twin = await engine.create_twin(config)
        logger.info(f"✅ Created twin: {config.twin_id}")
        
        success = await engine.start_twin(config.twin_id)
        if success:
            logger.info(f"✅ Started twin: {config.twin_id}")
        else:
            logger.error(f"❌ Failed to start twin: {config.twin_id}")
            return False
        
        # Get twin state
        state = await engine.get_twin_state(config.twin_id)
        if state:
            logger.info(f"✅ Retrieved twin state: step={state.simulation_step}")
        else:
            logger.error("❌ Failed to get twin state")
            return False
        
        # Stop twin
        await engine.stop_twin(config.twin_id)
        logger.info(f"✅ Stopped twin: {config.twin_id}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Digital Twin Core test failed: {e}")
        return False

async def test_physics_simulation():
    """Test physics simulation functionality"""
    try:
        from digital_twin.simulation import (
            RealTimePhysicsEngine, 
            SimulationConfig, 
            PhysicsParameters,
            BoundaryConditions
        )
        
        logger.info("Testing Physics Simulation Engine...")
        
        # Create configuration
        config = SimulationConfig(
            physics_params=PhysicsParameters(
                thermal_conductivity=0.5,
                specific_heat=920.0,
                density=2400.0,
                grid_size_x=20,  # Smaller grid for testing
                grid_size_y=10,
                time_step=0.1
            ),
            boundary_conditions=BoundaryConditions(),
            real_time_factor=10.0,  # Run faster for testing
            max_simulation_time=5.0  # Short test
        )
        
        # Create and initialize engine
        engine = RealTimePhysicsEngine(config)
        success = await engine.initialize()
        
        if not success:
            logger.error("❌ Failed to initialize physics engine")
            return False
        
        logger.info("✅ Physics engine initialized")
        
        # Start simulation
        success = await engine.start()
        if success:
            logger.info("✅ Physics simulation started")
        else:
            logger.error("❌ Failed to start physics simulation")
            return False
        
        # Let it run for a bit
        await asyncio.sleep(2.0)
        
        # Get current state
        state = await engine.get_current_state()
        logger.info(f"✅ Simulation state: time={state['simulation_time']:.2f}s, step={state['step_count']}")
        
        # Stop simulation
        await engine.stop()
        logger.info("✅ Physics simulation stopped")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Physics Simulation test failed: {e}")
        return False

async def test_api_models():
    """Test API models"""
    try:
        from src.api.models import TwinCreateRequest, TwinResponse, HealthResponse
        
        logger.info("Testing API Models...")
        
        # Test twin create request
        request = TwinCreateRequest(
            twin_id="test_api_twin",
            name="Test API Twin",
            description="Test twin for API validation",
            physical_asset_id="api_asset_001"
        )
        
        logger.info(f"✅ Created TwinCreateRequest: {request.twin_id}")
        
        # Test twin response
        response = TwinResponse(
            twin_id="test_api_twin",
            status="created",
            message="Test response"
        )
        
        logger.info(f"✅ Created TwinResponse: {response.status}")
        
        # Test health response
        health = HealthResponse(
            status="healthy",
            version="1.0.0",
            components={"test": "healthy"}
        )
        
        logger.info(f"✅ Created HealthResponse: {health.status}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ API Models test failed: {e}")
        return False

async def main():
    """Run all tests"""
    logger.info("🚀 Starting Digital Twin System Tests...")
    logger.info("=" * 60)
    
    tests = [
        ("Digital Twin Core", test_digital_twin_core),
        ("Physics Simulation", test_physics_simulation),
        ("API Models", test_api_models)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🔬 Running {test_name} Test...")
        try:
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name} Test PASSED")
            else:
                logger.error(f"❌ {test_name} Test FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name} Test ERROR: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST SUMMARY:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Digital Twin System is working correctly.")
        return True
    else:
        logger.error("💥 Some tests failed. Please check the logs above.")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}")
        sys.exit(1)