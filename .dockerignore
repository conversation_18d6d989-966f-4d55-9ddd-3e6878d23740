# Docker ignore file to optimize build context
# Development and testing files
.git
.gitignore
*.md
*.txt
README*
CHANGELOG*
LICENSE*
.vscode
.idea
*.pyc
__pycache__
.pytest_cache
.coverage
.tox
.env*
.python-version

# Virtual environments
venv/
env/
.venv/
.env/

# Data and logs
data/
logs/
*.log
*.db
*.sqlite3

# Models and cache
models/
.cache/
*.pkl
*.joblib

# Documentation
docs/
*.pdf
*.docx

# OS files
.DS_Store
Thumbs.db
desktop.ini

# Large development dependencies
node_modules/
bower_components/

# Temporary files
tmp/
temp/
*.tmp
*.swp
*.swo
*~

# Test files
test/
tests/
*_test.py
test_*.py

# Build artifacts
build/
dist/
*.egg-info/
.build/

# IDE files
*.sublime-*
*.code-workspace

# Docker files (except production)
Dockerfile.simple
docker-compose.simple.yml
docker-compose.yml

# Scripts (to avoid confusion)
setup.bat
install_dependencies.py

# Screenshots and demo files
calibrate_screen.py
run_screen_integration.py

# Development configurations
config/development/
config/testing/