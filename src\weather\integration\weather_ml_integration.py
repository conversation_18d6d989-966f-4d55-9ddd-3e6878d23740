"""
Weather Data Integration for ML Models
Integrates weather data with temperature prediction and energy optimization models
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from dataclasses import dataclass, field
from abc import ABC, abstractmethod

try:
    import requests
    import aiohttp
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    print("Warning: requests/aiohttp not available. Weather API functionality limited.")

logger = logging.getLogger(__name__)


@dataclass
class WeatherData:
    """Weather data point"""
    timestamp: datetime
    temperature: float  # °C
    humidity: float  # %
    wind_speed: float  # m/s
    wind_direction: float  # degrees
    pressure: float  # hPa
    cloud_cover: float  # %
    precipitation: float  # mm
    solar_radiation: float  # W/m²
    location: str = "unknown"
    source: str = "unknown"


@dataclass
class WeatherForecast:
    """Weather forecast data"""
    forecast_time: datetime
    horizon_hours: int
    data_points: List[WeatherData] = field(default_factory=list)
    confidence: float = 1.0
    model_version: str = "unknown"


class WeatherDataProvider(ABC):
    """Abstract base class for weather data providers"""
    
    @abstractmethod
    async def get_current_weather(self, latitude: float, longitude: float) -> Optional[WeatherData]:
        """Get current weather data"""
        pass
    
    @abstractmethod
    async def get_forecast(self, latitude: float, longitude: float, 
                          hours: int = 24) -> Optional[WeatherForecast]:
        """Get weather forecast"""
        pass
    
    @abstractmethod
    async def get_historical_data(self, latitude: float, longitude: float,
                                 start_date: datetime, end_date: datetime) -> List[WeatherData]:
        """Get historical weather data"""
        pass


class OpenWeatherMapProvider(WeatherDataProvider):
    """OpenWeatherMap API provider"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.openweathermap.org/data/2.5"
        self.session = None
    
    async def _get_session(self):
        """Get or create aiohttp session"""
        if not REQUESTS_AVAILABLE:
            raise ImportError("aiohttp required for weather API")
        
        if not self.session:
            self.session = aiohttp.ClientSession()
        return self.session
    
    async def get_current_weather(self, latitude: float, longitude: float) -> Optional[WeatherData]:
        """Get current weather from OpenWeatherMap"""
        try:
            session = await self._get_session()
            url = f"{self.base_url}/weather"
            params = {
                'lat': latitude,
                'lon': longitude,
                'appid': self.api_key,
                'units': 'metric'
            }
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_current_weather(data)
                else:
                    logger.error(f"Weather API error: {response.status}")
                    return None
        
        except Exception as e:
            logger.error(f"Error fetching current weather: {e}")
            return None
    
    async def get_forecast(self, latitude: float, longitude: float, 
                          hours: int = 24) -> Optional[WeatherForecast]:
        """Get weather forecast from OpenWeatherMap"""
        try:
            session = await self._get_session()
            url = f"{self.base_url}/forecast"
            params = {
                'lat': latitude,
                'lon': longitude,
                'appid': self.api_key,
                'units': 'metric'
            }
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_forecast(data, hours)
                else:
                    logger.error(f"Forecast API error: {response.status}")
                    return None
        
        except Exception as e:
            logger.error(f"Error fetching forecast: {e}")
            return None
    
    async def get_historical_data(self, latitude: float, longitude: float,
                                 start_date: datetime, end_date: datetime) -> List[WeatherData]:
        """Get historical weather data (limited in free tier)"""
        try:
            # OpenWeatherMap free tier has limited historical data
            # This would require a different endpoint or paid plan
            logger.warning("Historical data requires paid OpenWeatherMap subscription")
            return []
        
        except Exception as e:
            logger.error(f"Error fetching historical data: {e}")
            return []
    
    def _parse_current_weather(self, data: Dict[str, Any]) -> WeatherData:
        """Parse current weather response"""
        main = data.get('main', {})
        wind = data.get('wind', {})
        clouds = data.get('clouds', {})
        
        return WeatherData(
            timestamp=datetime.now(),
            temperature=main.get('temp', 0.0),
            humidity=main.get('humidity', 0.0),
            wind_speed=wind.get('speed', 0.0),
            wind_direction=wind.get('deg', 0.0),
            pressure=main.get('pressure', 1013.25),
            cloud_cover=clouds.get('all', 0.0),
            precipitation=0.0,  # Not in current weather
            solar_radiation=self._estimate_solar_radiation(clouds.get('all', 0.0)),
            location=data.get('name', 'unknown'),
            source='openweathermap'
        )
    
    def _parse_forecast(self, data: Dict[str, Any], hours: int) -> WeatherForecast:
        """Parse forecast response"""
        forecast_list = data.get('list', [])
        data_points = []
        
        for item in forecast_list[:hours//3]:  # 3-hour intervals
            main = item.get('main', {})
            wind = item.get('wind', {})
            clouds = item.get('clouds', {})
            rain = item.get('rain', {})
            
            weather_data = WeatherData(
                timestamp=datetime.fromtimestamp(item.get('dt', 0)),
                temperature=main.get('temp', 0.0),
                humidity=main.get('humidity', 0.0),
                wind_speed=wind.get('speed', 0.0),
                wind_direction=wind.get('deg', 0.0),
                pressure=main.get('pressure', 1013.25),
                cloud_cover=clouds.get('all', 0.0),
                precipitation=rain.get('3h', 0.0),
                solar_radiation=self._estimate_solar_radiation(clouds.get('all', 0.0)),
                source='openweathermap'
            )
            data_points.append(weather_data)
        
        return WeatherForecast(
            forecast_time=datetime.now(),
            horizon_hours=hours,
            data_points=data_points,
            confidence=0.8,  # Estimated confidence
            model_version='openweathermap_v2.5'
        )
    
    def _estimate_solar_radiation(self, cloud_cover: float) -> float:
        """Estimate solar radiation based on cloud cover"""
        # Simplified solar radiation estimation
        max_radiation = 1000.0  # W/m² on clear day
        cloud_factor = 1.0 - (cloud_cover / 100.0) * 0.8
        return max_radiation * cloud_factor
    
    async def cleanup(self):
        """Clean up resources"""
        if self.session:
            await self.session.close()


class WeatherFeatureEngineer:
    """Weather feature engineering for ML models"""
    
    def __init__(self):
        self.feature_history = []
        self.seasonal_patterns = {}
        self.trend_window = 24  # hours
    
    def extract_features(self, weather_data: List[WeatherData], 
                        forecast_data: Optional[WeatherForecast] = None) -> Dict[str, Any]:
        """Extract weather features for ML models"""
        if not weather_data:
            return {}
        
        try:
            # Convert to DataFrame for easier processing
            df = self._create_weather_dataframe(weather_data)
            
            features = {}
            
            # Current weather features
            if len(df) > 0:
                latest = df.iloc[-1]
                features.update({
                    'current_temp': latest['temperature'],
                    'current_humidity': latest['humidity'],
                    'current_wind_speed': latest['wind_speed'],
                    'current_pressure': latest['pressure'],
                    'current_solar_radiation': latest['solar_radiation']
                })
            
            # Statistical features
            if len(df) >= 3:
                features.update({
                    'temp_mean_3h': df['temperature'].tail(3).mean(),
                    'temp_std_3h': df['temperature'].tail(3).std(),
                    'temp_trend_3h': self._calculate_trend(df['temperature'].tail(3)),
                    'humidity_mean_3h': df['humidity'].tail(3).mean(),
                    'wind_speed_mean_3h': df['wind_speed'].tail(3).mean()
                })
            
            # Longer term features
            if len(df) >= 24:
                features.update({
                    'temp_mean_24h': df['temperature'].tail(24).mean(),
                    'temp_max_24h': df['temperature'].tail(24).max(),
                    'temp_min_24h': df['temperature'].tail(24).min(),
                    'temp_range_24h': df['temperature'].tail(24).max() - df['temperature'].tail(24).min(),
                    'humidity_mean_24h': df['humidity'].tail(24).mean(),
                    'precipitation_24h': df['precipitation'].tail(24).sum()
                })
            
            # Forecast features
            if forecast_data and forecast_data.data_points:
                forecast_features = self._extract_forecast_features(forecast_data)
                features.update(forecast_features)
            
            # Seasonal and temporal features
            if len(df) > 0:
                temporal_features = self._extract_temporal_features(df.iloc[-1]['timestamp'])
                features.update(temporal_features)
            
            # Derived features
            features.update(self._calculate_derived_features(features))
            
            return features
        
        except Exception as e:
            logger.error(f"Error extracting weather features: {e}")
            return {}
    
    def _create_weather_dataframe(self, weather_data: List[WeatherData]) -> pd.DataFrame:
        """Create DataFrame from weather data"""
        data = []
        for wd in weather_data:
            data.append({
                'timestamp': wd.timestamp,
                'temperature': wd.temperature,
                'humidity': wd.humidity,
                'wind_speed': wd.wind_speed,
                'wind_direction': wd.wind_direction,
                'pressure': wd.pressure,
                'cloud_cover': wd.cloud_cover,
                'precipitation': wd.precipitation,
                'solar_radiation': wd.solar_radiation
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df.sort_index()
    
    def _calculate_trend(self, series: pd.Series) -> float:
        """Calculate trend in time series"""
        if len(series) < 2:
            return 0.0
        
        try:
            x = np.arange(len(series))
            coeffs = np.polyfit(x, series.values, 1)
            return coeffs[0]  # Slope
        except:
            return 0.0
    
    def _extract_forecast_features(self, forecast: WeatherForecast) -> Dict[str, Any]:
        """Extract features from weather forecast"""
        features = {}
        
        if not forecast.data_points:
            return features
        
        # Convert forecast to arrays
        temps = [wd.temperature for wd in forecast.data_points]
        humidity = [wd.humidity for wd in forecast.data_points]
        wind_speed = [wd.wind_speed for wd in forecast.data_points]
        
        # Short-term forecast (next 6 hours)
        short_term = min(6, len(temps))
        if short_term > 0:
            features.update({
                f'forecast_temp_mean_{short_term}h': np.mean(temps[:short_term]),
                f'forecast_temp_max_{short_term}h': np.max(temps[:short_term]),
                f'forecast_temp_min_{short_term}h': np.min(temps[:short_term]),
                f'forecast_humidity_mean_{short_term}h': np.mean(humidity[:short_term]),
                f'forecast_wind_mean_{short_term}h': np.mean(wind_speed[:short_term])
            })
        
        # Medium-term forecast (next 24 hours)
        medium_term = min(24, len(temps))
        if medium_term > 6:
            features.update({
                f'forecast_temp_mean_{medium_term}h': np.mean(temps[:medium_term]),
                f'forecast_temp_trend_{medium_term}h': self._calculate_trend(pd.Series(temps[:medium_term]))
            })
        
        return features
    
    def _extract_temporal_features(self, timestamp: datetime) -> Dict[str, Any]:
        """Extract temporal features"""
        return {
            'hour_of_day': timestamp.hour,
            'day_of_week': timestamp.weekday(),
            'day_of_year': timestamp.timetuple().tm_yday,
            'month': timestamp.month,
            'season': self._get_season(timestamp),
            'is_weekend': 1 if timestamp.weekday() >= 5 else 0,
            'is_business_hours': 1 if 8 <= timestamp.hour <= 17 else 0,
            # Cyclical encoding
            'hour_sin': np.sin(2 * np.pi * timestamp.hour / 24),
            'hour_cos': np.cos(2 * np.pi * timestamp.hour / 24),
            'day_sin': np.sin(2 * np.pi * timestamp.weekday() / 7),
            'day_cos': np.cos(2 * np.pi * timestamp.weekday() / 7),
            'month_sin': np.sin(2 * np.pi * timestamp.month / 12),
            'month_cos': np.cos(2 * np.pi * timestamp.month / 12)
        }
    
    def _get_season(self, timestamp: datetime) -> int:
        """Get season (0=winter, 1=spring, 2=summer, 3=autumn)"""
        month = timestamp.month
        if month in [12, 1, 2]:
            return 0  # Winter
        elif month in [3, 4, 5]:
            return 1  # Spring
        elif month in [6, 7, 8]:
            return 2  # Summer
        else:
            return 3  # Autumn
    
    def _calculate_derived_features(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate derived weather features"""
        derived = {}
        
        try:
            # Heat index (simplified)
            if 'current_temp' in features and 'current_humidity' in features:
                temp = features['current_temp']
                humidity = features['current_humidity']
                derived['heat_index'] = self._calculate_heat_index(temp, humidity)
            
            # Wind chill (simplified)
            if 'current_temp' in features and 'current_wind_speed' in features:
                temp = features['current_temp']
                wind = features['current_wind_speed']
                derived['wind_chill'] = self._calculate_wind_chill(temp, wind)
            
            # Comfort index
            if 'current_temp' in features and 'current_humidity' in features:
                derived['comfort_index'] = self._calculate_comfort_index(
                    features['current_temp'], features['current_humidity']
                )
            
            # Temperature stability
            if 'temp_std_3h' in features:
                derived['temp_stability'] = 1.0 / (1.0 + features['temp_std_3h'])
            
        except Exception as e:
            logger.error(f"Error calculating derived features: {e}")
        
        return derived
    
    def _calculate_heat_index(self, temp: float, humidity: float) -> float:
        """Calculate heat index (simplified formula)"""
        if temp < 27:  # Only applicable for high temperatures
            return temp
        
        # Simplified heat index calculation
        hi = temp + 0.5 * (temp - 14.0) * (humidity / 100.0)
        return hi
    
    def _calculate_wind_chill(self, temp: float, wind_speed: float) -> float:
        """Calculate wind chill (simplified)"""
        if temp > 10 or wind_speed < 1.3:  # Only applicable for cold/windy conditions
            return temp
        
        # Simplified wind chill calculation
        wc = 13.12 + 0.6215 * temp - 11.37 * (wind_speed ** 0.16) + 0.3965 * temp * (wind_speed ** 0.16)
        return wc
    
    def _calculate_comfort_index(self, temp: float, humidity: float) -> float:
        """Calculate thermal comfort index (0-1 scale)"""
        # Optimal comfort: 20-24°C, 40-60% humidity
        temp_comfort = 1.0 - min(1.0, abs(temp - 22) / 15.0)
        humidity_comfort = 1.0 - min(1.0, abs(humidity - 50) / 40.0)
        
        return (temp_comfort + humidity_comfort) / 2.0


class WeatherMLIntegration:
    """Integration between weather data and ML models"""
    
    def __init__(self, weather_provider: WeatherDataProvider):
        self.weather_provider = weather_provider
        self.feature_engineer = WeatherFeatureEngineer()
        self.weather_history = []
        self.current_forecast = None
        self.location = (40.7128, -74.0060)  # Default: NYC coordinates
        self.update_interval = 3600  # 1 hour
        self._update_task: Optional[asyncio.Task] = None
        self.is_running = False
    
    def set_location(self, latitude: float, longitude: float):
        """Set plant location for weather data"""
        self.location = (latitude, longitude)
        logger.info(f"Weather location set to: {latitude}, {longitude}")
    
    async def start_weather_monitoring(self):
        """Start continuous weather monitoring"""
        if self.is_running:
            return
        
        self.is_running = True
        self._update_task = asyncio.create_task(self._weather_update_loop())
        logger.info("Weather monitoring started")
    
    async def stop_weather_monitoring(self):
        """Stop weather monitoring"""
        self.is_running = False
        
        if self._update_task:
            self._update_task.cancel()
            self._update_task = None
        
        logger.info("Weather monitoring stopped")
    
    async def _weather_update_loop(self):
        """Weather data update loop"""
        while self.is_running:
            try:
                # Get current weather
                current_weather = await self.weather_provider.get_current_weather(
                    self.location[0], self.location[1]
                )
                
                if current_weather:
                    self.weather_history.append(current_weather)
                    
                    # Keep only last 7 days of data
                    cutoff_time = datetime.now() - timedelta(days=7)
                    self.weather_history = [
                        w for w in self.weather_history if w.timestamp >= cutoff_time
                    ]
                
                # Get forecast every 6 hours
                if (not self.current_forecast or 
                    datetime.now() - self.current_forecast.forecast_time > timedelta(hours=6)):
                    
                    forecast = await self.weather_provider.get_forecast(
                        self.location[0], self.location[1], hours=72
                    )
                    
                    if forecast:
                        self.current_forecast = forecast
                
                logger.debug(f"Updated weather data: {len(self.weather_history)} historical points")
                
                # Wait for next update
                await asyncio.sleep(self.update_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in weather update loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retry
    
    async def get_weather_features_for_ml(self, hours_history: int = 24) -> Dict[str, Any]:
        """Get weather features for ML model input"""
        try:
            # Get recent weather history
            cutoff_time = datetime.now() - timedelta(hours=hours_history)
            recent_weather = [
                w for w in self.weather_history if w.timestamp >= cutoff_time
            ]
            
            # Extract features
            features = self.feature_engineer.extract_features(
                recent_weather, self.current_forecast
            )
            
            return features
        
        except Exception as e:
            logger.error(f"Error getting weather features: {e}")
            return {}
    
    async def predict_heating_impact(self, forecast_hours: int = 24) -> Dict[str, Any]:
        """Predict weather impact on heating requirements"""
        try:
            if not self.current_forecast:
                return {'error': 'No forecast data available'}
            
            # Get forecast data for the specified period
            forecast_data = self.current_forecast.data_points[:forecast_hours]
            
            if not forecast_data:
                return {'error': 'No forecast data for specified period'}
            
            # Calculate heating demand factors
            heating_factors = []
            energy_impacts = []
            
            for weather_point in forecast_data:
                # Temperature impact (higher heating needed for lower temps)
                temp_factor = max(0.0, (25.0 - weather_point.temperature) / 25.0)
                
                # Wind impact (higher wind increases heat loss)
                wind_factor = min(1.0, weather_point.wind_speed / 20.0)
                
                # Solar impact (solar radiation reduces heating needs)
                solar_factor = max(0.0, 1.0 - weather_point.solar_radiation / 1000.0)
                
                # Combined heating factor
                heating_factor = temp_factor * (1.0 + wind_factor * 0.3) * (1.0 + solar_factor * 0.2)
                heating_factors.append(heating_factor)
                
                # Estimate energy impact (percentage increase over baseline)
                energy_impact = heating_factor * 0.5  # Up to 50% increase
                energy_impacts.append(energy_impact)
            
            return {
                'forecast_hours': forecast_hours,
                'avg_heating_factor': np.mean(heating_factors),
                'max_heating_factor': np.max(heating_factors),
                'avg_energy_impact': np.mean(energy_impacts),
                'max_energy_impact': np.max(energy_impacts),
                'heating_timeline': heating_factors,
                'energy_timeline': energy_impacts,
                'confidence': self.current_forecast.confidence
            }
        
        except Exception as e:
            logger.error(f"Error predicting heating impact: {e}")
            return {'error': str(e)}
    
    async def get_energy_optimization_features(self) -> Dict[str, Any]:
        """Get weather features optimized for energy cost optimization"""
        try:
            weather_features = await self.get_weather_features_for_ml()
            
            if not weather_features:
                return {}
            
            # Add energy-specific features
            optimization_features = weather_features.copy()
            
            # Time-of-use features (energy pricing often varies by time)
            current_time = datetime.now()
            optimization_features.update({
                'peak_hours': 1 if 16 <= current_time.hour <= 20 else 0,
                'off_peak_hours': 1 if 0 <= current_time.hour <= 6 else 0,
                'weekend_pricing': 1 if current_time.weekday() >= 5 else 0
            })
            
            # Heating demand prediction
            if 'current_temp' in weather_features:
                temp = weather_features['current_temp']
                optimization_features['heating_demand_level'] = max(0.0, (20.0 - temp) / 30.0)
            
            # Energy conservation opportunity
            if self.current_forecast and self.current_forecast.data_points:
                next_6h_temps = [wp.temperature for wp in self.current_forecast.data_points[:6]]
                if next_6h_temps:
                    temp_trend = np.polyfit(range(len(next_6h_temps)), next_6h_temps, 1)[0]
                    optimization_features['temp_trend_6h'] = temp_trend
                    optimization_features['preheating_opportunity'] = 1 if temp_trend < -1.0 else 0
            
            return optimization_features
        
        except Exception as e:
            logger.error(f"Error getting energy optimization features: {e}")
            return {}
    
    async def cleanup(self):
        """Clean up resources"""
        await self.stop_weather_monitoring()
        if hasattr(self.weather_provider, 'cleanup'):
            await self.weather_provider.cleanup()


# Factory function
def create_weather_integration(api_key: str, provider: str = "openweathermap") -> WeatherMLIntegration:
    """Create weather ML integration instance"""
    if provider == "openweathermap":
        weather_provider = OpenWeatherMapProvider(api_key)
    else:
        raise ValueError(f"Unsupported weather provider: {provider}")
    
    return WeatherMLIntegration(weather_provider)


# Example usage
if __name__ == "__main__":
    async def test_weather_integration():
        # Create weather integration (requires API key)
        api_key = "your_openweathermap_api_key"  # Replace with actual key
        weather_integration = create_weather_integration(api_key)
        
        # Set plant location (example: New York)
        weather_integration.set_location(40.7128, -74.0060)
        
        # Start monitoring
        await weather_integration.start_weather_monitoring()
        
        # Wait a bit for data to be collected
        await asyncio.sleep(10)
        
        # Get weather features for ML
        features = await weather_integration.get_weather_features_for_ml()
        print(f"Weather features: {features}")
        
        # Get energy optimization features
        energy_features = await weather_integration.get_energy_optimization_features()
        print(f"Energy features: {energy_features}")
        
        # Predict heating impact
        heating_impact = await weather_integration.predict_heating_impact()
        print(f"Heating impact: {heating_impact}")
        
        # Cleanup
        await weather_integration.cleanup()
    
    # Run test
    asyncio.run(test_weather_integration())