"""
OPC UA Client for SCADA Integration
Handles OPC UA communication with industrial control systems
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum

try:
    from asyncua import Client, Node, ua
    from asyncua.common.subscription import SubHandler
    OPC_UA_AVAILABLE = True
except ImportError:
    OPC_UA_AVAILABLE = False
    # Create dummy classes when asyncua is not available
    Client = None
    Node = None
    ua = None
    
    class SubHandler:
        """Dummy SubHandler class when asyncua is not available"""
        pass
    
    print("Warning: asyncua not installed. OPC UA functionality will be limited.")

logger = logging.getLogger(__name__)


class ConnectionState(Enum):
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"


@dataclass
class OPCTag:
    """OPC UA tag configuration"""
    name: str
    node_id: str
    data_type: str
    description: str = ""
    scaling_factor: float = 1.0
    offset: float = 0.0
    alarm_high: Optional[float] = None
    alarm_low: Optional[float] = None
    last_value: Optional[Any] = None
    last_update: Optional[datetime] = None
    quality: str = "GOOD"


@dataclass
class OPCConfiguration:
    """OPC UA client configuration"""
    endpoint_url: str
    username: Optional[str] = None
    password: Optional[str] = None
    security_policy: str = "None"
    security_mode: str = "None"
    certificate_path: Optional[str] = None
    private_key_path: Optional[str] = None
    session_timeout: int = 60000
    connection_timeout: int = 10000
    keep_alive_interval: int = 5000
    tags: List[OPCTag] = field(default_factory=list)


class OPCSubscriptionHandler(SubHandler):
    """Handles OPC UA subscription notifications"""
    
    def __init__(self, callback: Callable):
        self.callback = callback
        self.logger = logging.getLogger(__name__)
    
    def datachange_notification(self, node: Node, val: Any, data: Any):
        """Handle data change notifications"""
        try:
            node_id = node.nodeid.to_string()
            timestamp = datetime.now()
            
            # Call registered callback
            asyncio.create_task(self.callback(node_id, val, timestamp))
            
        except Exception as e:
            self.logger.error(f"Error in datachange notification: {e}")
    
    def event_notification(self, event: Any):
        """Handle event notifications"""
        self.logger.info(f"Event notification: {event}")


class OPCUAClient:
    """OPC UA client for SCADA integration"""
    
    def __init__(self, config: OPCConfiguration):
        self.config = config
        self.client: Optional[Client] = None
        self.subscription = None
        self.connection_state = ConnectionState.DISCONNECTED
        self.tags: Dict[str, OPCTag] = {tag.name: tag for tag in config.tags}
        self.node_cache: Dict[str, Node] = {}
        self.subscription_handlers: List[Callable] = []
        self.connection_callbacks: List[Callable] = []
        self._reconnect_task: Optional[asyncio.Task] = None
        self._keep_alive_task: Optional[asyncio.Task] = None
    
    async def connect(self) -> bool:
        """Connect to OPC UA server"""
        if not OPC_UA_AVAILABLE:
            logger.error("OPC UA library not available")
            return False
        
        try:
            self.connection_state = ConnectionState.CONNECTING
            logger.info(f"Connecting to OPC UA server: {self.config.endpoint_url}")
            
            self.client = Client(self.config.endpoint_url)
            
            # Configure security
            if self.config.username and self.config.password:
                self.client.set_user(self.config.username)
                self.client.set_password(self.config.password)
            
            # Set timeouts
            self.client.session_timeout = self.config.session_timeout
            
            # Connect
            await self.client.connect()
            
            # Initialize nodes
            await self._initialize_nodes()
            
            # Setup subscription
            await self._setup_subscription()
            
            self.connection_state = ConnectionState.CONNECTED
            logger.info("Successfully connected to OPC UA server")
            
            # Start keep alive
            self._keep_alive_task = asyncio.create_task(self._keep_alive_loop())
            
            # Notify connection callbacks
            await self._notify_connection_callbacks(True)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to OPC UA server: {e}")
            self.connection_state = ConnectionState.ERROR
            await self._notify_connection_callbacks(False)
            return False
    
    async def disconnect(self):
        """Disconnect from OPC UA server"""
        try:
            if self._keep_alive_task:
                self._keep_alive_task.cancel()
            
            if self._reconnect_task:
                self._reconnect_task.cancel()
            
            if self.client:
                await self.client.disconnect()
                self.client = None
            
            self.connection_state = ConnectionState.DISCONNECTED
            logger.info("Disconnected from OPC UA server")
            
        except Exception as e:
            logger.error(f"Error during disconnect: {e}")
    
    async def read_tag(self, tag_name: str) -> Optional[Any]:
        """Read a single tag value"""
        if not self.is_connected() or tag_name not in self.tags:
            return None
        
        try:
            tag = self.tags[tag_name]
            node = self.node_cache.get(tag.node_id)
            
            if not node:
                logger.error(f"Node not found for tag: {tag_name}")
                return None
            
            value = await node.read_value()
            
            # Apply scaling
            if isinstance(value, (int, float)):
                value = value * tag.scaling_factor + tag.offset
            
            # Update tag
            tag.last_value = value
            tag.last_update = datetime.now()
            tag.quality = "GOOD"
            
            return value
            
        except Exception as e:
            logger.error(f"Error reading tag {tag_name}: {e}")
            if tag_name in self.tags:
                self.tags[tag_name].quality = "BAD"
            return None
    
    async def write_tag(self, tag_name: str, value: Any) -> bool:
        """Write a value to a tag"""
        if not self.is_connected() or tag_name not in self.tags:
            return False
        
        try:
            tag = self.tags[tag_name]
            node = self.node_cache.get(tag.node_id)
            
            if not node:
                logger.error(f"Node not found for tag: {tag_name}")
                return False
            
            # Apply reverse scaling
            if isinstance(value, (int, float)):
                value = (value - tag.offset) / tag.scaling_factor
            
            await node.write_value(value)
            logger.info(f"Successfully wrote value {value} to tag {tag_name}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error writing tag {tag_name}: {e}")
            return False
    
    async def read_multiple_tags(self, tag_names: List[str]) -> Dict[str, Any]:
        """Read multiple tags at once"""
        results = {}
        
        for tag_name in tag_names:
            value = await self.read_tag(tag_name)
            if value is not None:
                results[tag_name] = value
        
        return results
    
    async def read_all_tags(self) -> Dict[str, Any]:
        """Read all configured tags"""
        return await self.read_multiple_tags(list(self.tags.keys()))
    
    def register_subscription_handler(self, callback: Callable):
        """Register a callback for subscription notifications"""
        self.subscription_handlers.append(callback)
    
    def register_connection_callback(self, callback: Callable):
        """Register a callback for connection state changes"""
        self.connection_callbacks.append(callback)
    
    def is_connected(self) -> bool:
        """Check if connected to OPC UA server"""
        return self.connection_state == ConnectionState.CONNECTED
    
    def get_connection_state(self) -> ConnectionState:
        """Get current connection state"""
        return self.connection_state
    
    def get_tag_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all tags"""
        status = {}
        for name, tag in self.tags.items():
            status[name] = {
                'value': tag.last_value,
                'quality': tag.quality,
                'last_update': tag.last_update.isoformat() if tag.last_update else None,
                'node_id': tag.node_id
            }
        return status
    
    async def _initialize_nodes(self):
        """Initialize OPC UA nodes"""
        if not self.client:
            return
        
        try:
            for tag in self.tags.values():
                try:
                    node = self.client.get_node(tag.node_id)
                    self.node_cache[tag.node_id] = node
                    logger.debug(f"Initialized node for tag {tag.name}: {tag.node_id}")
                except Exception as e:
                    logger.error(f"Failed to initialize node for tag {tag.name}: {e}")
        
        except Exception as e:
            logger.error(f"Error initializing nodes: {e}")
    
    async def _setup_subscription(self):
        """Setup OPC UA subscription"""
        if not self.client:
            return
        
        try:
            # Create subscription
            handler = OPCSubscriptionHandler(self._handle_subscription_data)
            self.subscription = await self.client.create_subscription(
                period=1000,  # 1 second
                handler=handler
            )
            
            # Subscribe to all tags
            nodes_to_subscribe = []
            for tag in self.tags.values():
                if tag.node_id in self.node_cache:
                    nodes_to_subscribe.append(self.node_cache[tag.node_id])
            
            if nodes_to_subscribe:
                await self.subscription.subscribe_data_change(nodes_to_subscribe)
                logger.info(f"Subscribed to {len(nodes_to_subscribe)} nodes")
        
        except Exception as e:
            logger.error(f"Error setting up subscription: {e}")
    
    async def _handle_subscription_data(self, node_id: str, value: Any, timestamp: datetime):
        """Handle subscription data changes"""
        try:
            # Find tag by node_id
            tag = None
            for t in self.tags.values():
                if t.node_id == node_id:
                    tag = t
                    break
            
            if not tag:
                return
            
            # Apply scaling
            if isinstance(value, (int, float)):
                value = value * tag.scaling_factor + tag.offset
            
            # Update tag
            tag.last_value = value
            tag.last_update = timestamp
            tag.quality = "GOOD"
            
            # Check alarms
            if isinstance(value, (int, float)):
                if tag.alarm_high and value > tag.alarm_high:
                    logger.warning(f"High alarm for tag {tag.name}: {value} > {tag.alarm_high}")
                if tag.alarm_low and value < tag.alarm_low:
                    logger.warning(f"Low alarm for tag {tag.name}: {value} < {tag.alarm_low}")
            
            # Notify subscription handlers
            for handler in self.subscription_handlers:
                await handler(tag.name, value, timestamp)
        
        except Exception as e:
            logger.error(f"Error handling subscription data: {e}")
    
    async def _keep_alive_loop(self):
        """Keep alive loop to maintain connection"""
        while self.is_connected():
            try:
                await asyncio.sleep(self.config.keep_alive_interval / 1000)
                
                # Simple keep alive by reading a tag
                if self.tags:
                    first_tag = next(iter(self.tags.keys()))
                    await self.read_tag(first_tag)
                
            except Exception as e:
                logger.error(f"Keep alive failed: {e}")
                self.connection_state = ConnectionState.ERROR
                
                # Start reconnection
                if not self._reconnect_task:
                    self._reconnect_task = asyncio.create_task(self._reconnect_loop())
                break
    
    async def _reconnect_loop(self):
        """Automatic reconnection loop"""
        retry_count = 0
        max_retries = 5
        
        while retry_count < max_retries:
            try:
                logger.info(f"Attempting to reconnect... (attempt {retry_count + 1})")
                
                # Disconnect first
                await self.disconnect()
                
                # Wait before reconnecting
                await asyncio.sleep(min(5 * (2 ** retry_count), 60))  # Exponential backoff
                
                # Try to reconnect
                if await self.connect():
                    logger.info("Successfully reconnected")
                    self._reconnect_task = None
                    return
                
                retry_count += 1
                
            except Exception as e:
                logger.error(f"Reconnection attempt failed: {e}")
                retry_count += 1
        
        logger.error("Max reconnection attempts reached")
        self.connection_state = ConnectionState.ERROR
        self._reconnect_task = None
    
    async def _notify_connection_callbacks(self, connected: bool):
        """Notify connection callbacks"""
        for callback in self.connection_callbacks:
            try:
                await callback(connected)
            except Exception as e:
                logger.error(f"Error in connection callback: {e}")
