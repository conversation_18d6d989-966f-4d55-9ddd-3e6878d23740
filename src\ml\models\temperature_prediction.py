"""
Temperature Prediction Models
LSTM and Transformer models for asphalt tank temperature forecasting
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from abc import ABC, abstractmethod
import logging
from datetime import datetime, timedelta

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import Dataset, DataLoader
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("Warning: PyTorch not available. Temperature prediction models will be limited.")

logger = logging.getLogger(__name__)


class TemperaturePredictionModel(ABC):
    """Abstract base class for temperature prediction models"""
    
    @abstractmethod
    def train(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """Train the model on historical data"""
        pass
    
    @abstractmethod
    def predict(self, features: np.ndarray, horizon: int = 24) -> np.ndarray:
        """Predict temperature for given time horizon"""
        pass
    
    @abstractmethod
    def save_model(self, path: str) -> None:
        """Save trained model to disk"""
        pass
    
    @abstractmethod
    def load_model(self, path: str) -> None:
        """Load trained model from disk"""
        pass


class TemperatureDataset(Dataset):
    """PyTorch dataset for temperature time series data"""
    
    def __init__(self, data: np.ndarray, sequence_length: int = 24, horizon: int = 6):
        """
        Args:
            data: Time series data (samples, features)
            sequence_length: Input sequence length in hours
            horizon: Prediction horizon in hours
        """
        self.data = data
        self.sequence_length = sequence_length
        self.horizon = horizon
        
        # Create sequences
        self.sequences = []
        self.targets = []
        
        for i in range(len(data) - sequence_length - horizon + 1):
            seq = data[i:i + sequence_length]
            target = data[i + sequence_length:i + sequence_length + horizon, 0]  # Temperature column
            self.sequences.append(seq)
            self.targets.append(target)
        
        self.sequences = np.array(self.sequences)
        self.targets = np.array(self.targets)
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        return torch.FloatTensor(self.sequences[idx]), torch.FloatTensor(self.targets[idx])


class LSTMTemperatureModel(TemperaturePredictionModel):
    """LSTM-based temperature prediction model"""
    
    def __init__(self, input_size: int = 6, hidden_size: int = 64, num_layers: int = 2,
                 sequence_length: int = 24, horizon: int = 6, dropout: float = 0.2):
        """
        Args:
            input_size: Number of input features
            hidden_size: LSTM hidden state size
            num_layers: Number of LSTM layers
            sequence_length: Input sequence length
            horizon: Prediction horizon
            dropout: Dropout rate
        """
        if not TORCH_AVAILABLE:
            raise ImportError("PyTorch is required for LSTM model")
        
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.sequence_length = sequence_length
        self.horizon = horizon
        self.dropout = dropout
        
        self.model = None
        self.scaler = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        self._build_model()
    
    def _build_model(self):
        """Build the LSTM neural network"""
        class LSTMNet(nn.Module):
            def __init__(self, input_size, hidden_size, num_layers, horizon, dropout):
                super(LSTMNet, self).__init__()
                self.hidden_size = hidden_size
                self.num_layers = num_layers
                
                self.lstm = nn.LSTM(
                    input_size, hidden_size, num_layers,
                    batch_first=True, dropout=dropout if num_layers > 1 else 0
                )
                self.dropout = nn.Dropout(dropout)
                self.fc = nn.Linear(hidden_size, horizon)
                
            def forward(self, x):
                # Initialize hidden state
                h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
                c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
                
                # LSTM forward pass
                out, _ = self.lstm(x, (h0, c0))
                
                # Take output from last time step
                out = self.dropout(out[:, -1, :])
                out = self.fc(out)
                
                return out
        
        self.model = LSTMNet(
            self.input_size, self.hidden_size, self.num_layers,
            self.horizon, self.dropout
        ).to(self.device)
    
    def train(self, data: pd.DataFrame, validation_split: float = 0.2,
              epochs: int = 100, batch_size: int = 32, learning_rate: float = 0.001,
              early_stopping_patience: int = 10) -> Dict[str, Any]:
        """
        Train the LSTM model
        
        Args:
            data: DataFrame with columns ['timestamp', 'temperature', 'weather_temp',
                  'humidity', 'wind_speed', 'energy_consumption', 'control_output']
            validation_split: Fraction of data for validation
            epochs: Number of training epochs
            batch_size: Training batch size
            learning_rate: Learning rate for optimizer
            early_stopping_patience: Early stopping patience
        
        Returns:
            Training history and metrics
        """
        logger.info("Starting LSTM temperature model training...")
        
        # Prepare data
        features = ['temperature', 'weather_temp', 'humidity', 'wind_speed', 
                   'energy_consumption', 'control_output']
        
        # Handle missing features
        available_features = [f for f in features if f in data.columns]
        if len(available_features) < 2:
            raise ValueError(f"Insufficient features. Available: {available_features}")
        
        feature_data = data[available_features].values
        
        # Normalize data
        from sklearn.preprocessing import StandardScaler
        self.scaler = StandardScaler()
        feature_data_scaled = self.scaler.fit_transform(feature_data)
        
        # Create datasets
        full_dataset = TemperatureDataset(
            feature_data_scaled, self.sequence_length, self.horizon
        )
        
        # Train/validation split
        val_size = int(len(full_dataset) * validation_split)
        train_size = len(full_dataset) - val_size
        
        train_dataset, val_dataset = torch.utils.data.random_split(
            full_dataset, [train_size, val_size]
        )
        
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        
        # Setup training
        criterion = nn.MSELoss()
        optimizer = optim.Adam(self.model.parameters(), lr=learning_rate)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', patience=5, factor=0.5
        )
        
        # Training loop
        train_losses = []
        val_losses = []
        best_val_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(epochs):
            # Training phase
            self.model.train()
            train_loss = 0.0
            
            for batch_x, batch_y in train_loader:
                batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                
                optimizer.zero_grad()
                outputs = self.model(batch_x)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
            
            train_loss /= len(train_loader)
            train_losses.append(train_loss)
            
            # Validation phase
            self.model.eval()
            val_loss = 0.0
            
            with torch.no_grad():
                for batch_x, batch_y in val_loader:
                    batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                    outputs = self.model(batch_x)
                    loss = criterion(outputs, batch_y)
                    val_loss += loss.item()
            
            val_loss /= len(val_loader)
            val_losses.append(val_loss)
            
            # Learning rate scheduling
            scheduler.step(val_loss)
            
            # Early stopping
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # Save best model
                torch.save(self.model.state_dict(), 'best_model.pth')
            else:
                patience_counter += 1
            
            if patience_counter >= early_stopping_patience:
                logger.info(f"Early stopping at epoch {epoch}")
                break
            
            if epoch % 10 == 0:
                logger.info(f"Epoch {epoch}: Train Loss: {train_loss:.4f}, "
                           f"Val Loss: {val_loss:.4f}")
        
        # Load best model
        self.model.load_state_dict(torch.load('best_model.pth'))
        
        logger.info("LSTM model training completed")
        
        return {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'best_val_loss': best_val_loss,
            'epochs_trained': epoch + 1,
            'model_parameters': sum(p.numel() for p in self.model.parameters())
        }
    
    def predict(self, features: np.ndarray, horizon: Optional[int] = None) -> np.ndarray:
        """
        Predict temperature for given features
        
        Args:
            features: Input features (sequence_length, n_features)
            horizon: Prediction horizon (uses model default if None)
        
        Returns:
            Temperature predictions
        """
        if self.model is None or self.scaler is None:
            raise ValueError("Model must be trained before prediction")
        
        if horizon is None:
            horizon = self.horizon
        
        # Normalize features
        features_scaled = self.scaler.transform(features)
        
        # Prepare input tensor
        if len(features_scaled.shape) == 2:
            features_scaled = features_scaled.reshape(1, *features_scaled.shape)
        
        input_tensor = torch.FloatTensor(features_scaled).to(self.device)
        
        # Predict
        self.model.eval()
        with torch.no_grad():
            predictions = self.model(input_tensor)
            
        # Denormalize predictions (assuming temperature is first feature)
        predictions_np = predictions.cpu().numpy()
        
        # Create dummy array for inverse transform
        dummy_features = np.zeros((predictions_np.shape[0], predictions_np.shape[1], 
                                  self.scaler.n_features_in_))
        dummy_features[:, :, 0] = predictions_np
        
        # Reshape for inverse transform
        original_shape = dummy_features.shape
        dummy_reshaped = dummy_features.reshape(-1, self.scaler.n_features_in_)
        denormalized = self.scaler.inverse_transform(dummy_reshaped)
        denormalized = denormalized.reshape(original_shape)
        
        return denormalized[:, :, 0].squeeze()
    
    def save_model(self, path: str) -> None:
        """Save model and scaler to disk"""
        import pickle
        
        model_dict = {
            'model_state_dict': self.model.state_dict(),
            'model_config': {
                'input_size': self.input_size,
                'hidden_size': self.hidden_size,
                'num_layers': self.num_layers,
                'sequence_length': self.sequence_length,
                'horizon': self.horizon,
                'dropout': self.dropout
            },
            'scaler': self.scaler
        }
        
        with open(path, 'wb') as f:
            pickle.dump(model_dict, f)
        
        logger.info(f"Model saved to {path}")
    
    def load_model(self, path: str) -> None:
        """Load model and scaler from disk"""
        import pickle
        
        with open(path, 'rb') as f:
            model_dict = pickle.load(f)
        
        # Restore configuration
        config = model_dict['model_config']
        self.input_size = config['input_size']
        self.hidden_size = config['hidden_size']
        self.num_layers = config['num_layers']
        self.sequence_length = config['sequence_length']
        self.horizon = config['horizon']
        self.dropout = config['dropout']
        
        # Rebuild and load model
        self._build_model()
        self.model.load_state_dict(model_dict['model_state_dict'])
        self.model.eval()
        
        # Restore scaler
        self.scaler = model_dict['scaler']
        
        logger.info(f"Model loaded from {path}")


class StatisticalTemperatureModel(TemperaturePredictionModel):
    """Simple statistical model for baseline comparison"""
    
    def __init__(self, method: str = 'linear_trend'):
        """
        Args:
            method: Statistical method ('linear_trend', 'seasonal_naive', 'moving_average')
        """
        self.method = method
        self.model_params = {}
        self.historical_data = None
    
    def train(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """Train statistical model"""
        self.historical_data = data['temperature'].values
        
        if self.method == 'linear_trend':
            # Fit linear trend
            x = np.arange(len(self.historical_data))
            coeffs = np.polyfit(x, self.historical_data, 1)
            self.model_params['slope'] = coeffs[0]
            self.model_params['intercept'] = coeffs[1]
        
        elif self.method == 'seasonal_naive':
            # Use seasonal patterns
            self.model_params['seasonal_period'] = 24  # Daily seasonality
        
        elif self.method == 'moving_average':
            # Use moving average
            self.model_params['window'] = kwargs.get('window', 12)
        
        logger.info(f"Statistical model ({self.method}) training completed")
        
        return {'method': self.method, 'params': self.model_params}
    
    def predict(self, features: np.ndarray, horizon: int = 24) -> np.ndarray:
        """Predict using statistical method"""
        if self.historical_data is None:
            raise ValueError("Model must be trained before prediction")
        
        if self.method == 'linear_trend':
            # Linear extrapolation
            last_x = len(self.historical_data) - 1
            predictions = []
            for i in range(horizon):
                pred = self.model_params['slope'] * (last_x + i + 1) + self.model_params['intercept']
                predictions.append(pred)
            return np.array(predictions)
        
        elif self.method == 'seasonal_naive':
            # Repeat seasonal pattern
            period = self.model_params['seasonal_period']
            recent_pattern = self.historical_data[-period:]
            predictions = np.tile(recent_pattern, (horizon // period) + 1)[:horizon]
            return predictions
        
        elif self.method == 'moving_average':
            # Extend with moving average
            window = self.model_params['window']
            predictions = []
            data_extended = list(self.historical_data)
            
            for _ in range(horizon):
                pred = np.mean(data_extended[-window:])
                predictions.append(pred)
                data_extended.append(pred)
            
            return np.array(predictions)
    
    def save_model(self, path: str) -> None:
        """Save statistical model"""
        import pickle
        model_dict = {
            'method': self.method,
            'model_params': self.model_params,
            'historical_data': self.historical_data
        }
        
        with open(path, 'wb') as f:
            pickle.dump(model_dict, f)
    
    def load_model(self, path: str) -> None:
        """Load statistical model"""
        import pickle
        
        with open(path, 'rb') as f:
            model_dict = pickle.load(f)
        
        self.method = model_dict['method']
        self.model_params = model_dict['model_params']
        self.historical_data = model_dict['historical_data']


# This will be replaced by the enhanced version above


class TransformerTemperatureModel(TemperaturePredictionModel):
    """Transformer-based temperature prediction model"""
    
    def __init__(self, input_size: int = 6, d_model: int = 64, nhead: int = 8, 
                 num_layers: int = 3, sequence_length: int = 24, horizon: int = 6, 
                 dropout: float = 0.1):
        """
        Args:
            input_size: Number of input features
            d_model: Transformer model dimension
            nhead: Number of attention heads
            num_layers: Number of transformer layers
            sequence_length: Input sequence length
            horizon: Prediction horizon
            dropout: Dropout rate
        """
        if not TORCH_AVAILABLE:
            raise ImportError("PyTorch is required for Transformer model")
        
        self.input_size = input_size
        self.d_model = d_model
        self.nhead = nhead
        self.num_layers = num_layers
        self.sequence_length = sequence_length
        self.horizon = horizon
        self.dropout = dropout
        
        self.model = None
        self.scaler = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        self._build_model()
    
    def _build_model(self):
        """Build the Transformer neural network"""
        class TransformerNet(nn.Module):
            def __init__(self, input_size, d_model, nhead, num_layers, horizon, dropout):
                super(TransformerNet, self).__init__()
                self.input_projection = nn.Linear(input_size, d_model)
                self.positional_encoding = self._generate_positional_encoding(1000, d_model)
                
                encoder_layer = nn.TransformerEncoderLayer(
                    d_model=d_model,
                    nhead=nhead,
                    dim_feedforward=d_model * 4,
                    dropout=dropout,
                    batch_first=True
                )
                self.transformer = nn.TransformerEncoder(encoder_layer, num_layers)
                self.dropout = nn.Dropout(dropout)
                self.fc = nn.Linear(d_model, horizon)
                
            def _generate_positional_encoding(self, max_len, d_model):
                pe = torch.zeros(max_len, d_model)
                position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
                div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                                   (-np.log(10000.0) / d_model))
                pe[:, 0::2] = torch.sin(position * div_term)
                pe[:, 1::2] = torch.cos(position * div_term)
                return pe.unsqueeze(0)
            
            def forward(self, x):
                # Project input to d_model dimension
                x = self.input_projection(x)
                
                # Add positional encoding
                seq_len = x.size(1)
                x += self.positional_encoding[:, :seq_len, :].to(x.device)
                
                # Transformer encoding
                x = self.transformer(x)
                
                # Global average pooling
                x = torch.mean(x, dim=1)
                
                # Final prediction
                x = self.dropout(x)
                x = self.fc(x)
                
                return x
        
        self.model = TransformerNet(
            self.input_size, self.d_model, self.nhead, 
            self.num_layers, self.horizon, self.dropout
        ).to(self.device)
    
    def train(self, data: pd.DataFrame, validation_split: float = 0.2,
              epochs: int = 100, batch_size: int = 32, learning_rate: float = 0.001,
              early_stopping_patience: int = 10) -> Dict[str, Any]:
        """Train the Transformer model (similar to LSTM but with different architecture)"""
        logger.info("Starting Transformer temperature model training...")
        
        # Use same training logic as LSTM
        features = ['temperature', 'weather_temp', 'humidity', 'wind_speed', 
                   'energy_consumption', 'control_output']
        
        available_features = [f for f in features if f in data.columns]
        if len(available_features) < 2:
            raise ValueError(f"Insufficient features. Available: {available_features}")
        
        feature_data = data[available_features].values
        
        # Normalize data
        from sklearn.preprocessing import StandardScaler
        self.scaler = StandardScaler()
        feature_data_scaled = self.scaler.fit_transform(feature_data)
        
        # Create datasets
        full_dataset = TemperatureDataset(
            feature_data_scaled, self.sequence_length, self.horizon
        )
        
        # Train/validation split
        val_size = int(len(full_dataset) * validation_split)
        train_size = len(full_dataset) - val_size
        
        train_dataset, val_dataset = torch.utils.data.random_split(
            full_dataset, [train_size, val_size]
        )
        
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        
        # Setup training
        criterion = nn.MSELoss()
        optimizer = optim.AdamW(self.model.parameters(), lr=learning_rate, weight_decay=0.01)
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)
        
        # Training loop (same as LSTM)
        train_losses = []
        val_losses = []
        best_val_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(epochs):
            # Training phase
            self.model.train()
            train_loss = 0.0
            
            for batch_x, batch_y in train_loader:
                batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                
                optimizer.zero_grad()
                outputs = self.model(batch_x)
                loss = criterion(outputs, batch_y)
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                optimizer.step()
                
                train_loss += loss.item()
            
            train_loss /= len(train_loader)
            train_losses.append(train_loss)
            
            # Validation phase
            self.model.eval()
            val_loss = 0.0
            
            with torch.no_grad():
                for batch_x, batch_y in val_loader:
                    batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                    outputs = self.model(batch_x)
                    loss = criterion(outputs, batch_y)
                    val_loss += loss.item()
            
            val_loss /= len(val_loader)
            val_losses.append(val_loss)
            
            # Learning rate scheduling
            scheduler.step()
            
            # Early stopping
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                torch.save(self.model.state_dict(), 'best_transformer_model.pth')
            else:
                patience_counter += 1
            
            if patience_counter >= early_stopping_patience:
                logger.info(f"Early stopping at epoch {epoch}")
                break
            
            if epoch % 10 == 0:
                logger.info(f"Epoch {epoch}: Train Loss: {train_loss:.4f}, "
                           f"Val Loss: {val_loss:.4f}")
        
        # Load best model
        self.model.load_state_dict(torch.load('best_transformer_model.pth'))
        
        logger.info("Transformer model training completed")
        
        return {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'best_val_loss': best_val_loss,
            'epochs_trained': epoch + 1,
            'model_parameters': sum(p.numel() for p in self.model.parameters())
        }
    
    def predict(self, features: np.ndarray, horizon: Optional[int] = None) -> np.ndarray:
        """Predict temperature using Transformer model"""
        # Same prediction logic as LSTM
        if self.model is None or self.scaler is None:
            raise ValueError("Model must be trained before prediction")
        
        if horizon is None:
            horizon = self.horizon
        
        # Normalize features
        features_scaled = self.scaler.transform(features)
        
        # Prepare input tensor
        if len(features_scaled.shape) == 2:
            features_scaled = features_scaled.reshape(1, *features_scaled.shape)
        
        input_tensor = torch.FloatTensor(features_scaled).to(self.device)
        
        # Predict
        self.model.eval()
        with torch.no_grad():
            predictions = self.model(input_tensor)
            
        # Denormalize predictions
        predictions_np = predictions.cpu().numpy()
        
        # Create dummy array for inverse transform
        dummy_features = np.zeros((predictions_np.shape[0], predictions_np.shape[1], 
                                  self.scaler.n_features_in_))
        dummy_features[:, :, 0] = predictions_np
        
        # Reshape for inverse transform
        original_shape = dummy_features.shape
        dummy_reshaped = dummy_features.reshape(-1, self.scaler.n_features_in_)
        denormalized = self.scaler.inverse_transform(dummy_reshaped)
        denormalized = denormalized.reshape(original_shape)
        
        return denormalized[:, :, 0].squeeze()
    
    def save_model(self, path: str) -> None:
        """Save Transformer model"""
        import pickle
        
        model_dict = {
            'model_state_dict': self.model.state_dict(),
            'model_config': {
                'input_size': self.input_size,
                'd_model': self.d_model,
                'nhead': self.nhead,
                'num_layers': self.num_layers,
                'sequence_length': self.sequence_length,
                'horizon': self.horizon,
                'dropout': self.dropout
            },
            'scaler': self.scaler
        }
        
        with open(path, 'wb') as f:
            pickle.dump(model_dict, f)
        
        logger.info(f"Transformer model saved to {path}")
    
    def load_model(self, path: str) -> None:
        """Load Transformer model"""
        import pickle
        
        with open(path, 'rb') as f:
            model_dict = pickle.load(f)
        
        # Restore configuration
        config = model_dict['model_config']
        self.input_size = config['input_size']
        self.d_model = config['d_model']
        self.nhead = config['nhead']
        self.num_layers = config['num_layers']
        self.sequence_length = config['sequence_length']
        self.horizon = config['horizon']
        self.dropout = config['dropout']
        
        # Rebuild and load model
        self._build_model()
        self.model.load_state_dict(model_dict['model_state_dict'])
        self.model.eval()
        
        # Restore scaler
        self.scaler = model_dict['scaler']
        
        logger.info(f"Transformer model loaded from {path}")


class EnsembleTemperatureModel(TemperaturePredictionModel):
    """Ensemble model combining multiple prediction models"""
    
    def __init__(self, models: List[TemperaturePredictionModel], weights: Optional[List[float]] = None):
        """
        Args:
            models: List of trained temperature prediction models
            weights: Optional weights for ensemble (default: equal weights)
        """
        self.models = models
        self.weights = weights or [1.0 / len(models)] * len(models)
        
        if len(self.weights) != len(self.models):
            raise ValueError("Number of weights must match number of models")
    
    def train(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """Train all models in the ensemble"""
        results = {}
        
        for i, model in enumerate(self.models):
            logger.info(f"Training ensemble model {i+1}/{len(self.models)}")
            model_results = model.train(data, **kwargs)
            results[f'model_{i}'] = model_results
        
        logger.info("Ensemble training completed")
        return results
    
    def predict(self, features: np.ndarray, horizon: int = 24) -> np.ndarray:
        """Predict using ensemble of models"""
        predictions = []
        
        for model in self.models:
            pred = model.predict(features, horizon)
            predictions.append(pred)
        
        # Weighted average
        predictions = np.array(predictions)
        weights = np.array(self.weights).reshape(-1, 1)
        
        if len(predictions.shape) == 2:
            weighted_pred = np.sum(predictions * weights, axis=0)
        else:
            weights = weights.reshape(-1, 1, 1)
            weighted_pred = np.sum(predictions * weights, axis=0)
        
        return weighted_pred
    
    def save_model(self, path: str) -> None:
        """Save ensemble model"""
        import pickle
        import os
        
        # Create directory for ensemble
        os.makedirs(path, exist_ok=True)
        
        # Save individual models
        for i, model in enumerate(self.models):
            model_path = os.path.join(path, f'model_{i}.pkl')
            model.save_model(model_path)
        
        # Save ensemble configuration
        config = {
            'weights': self.weights,
            'num_models': len(self.models),
            'model_types': [type(model).__name__ for model in self.models]
        }
        
        config_path = os.path.join(path, 'ensemble_config.pkl')
        with open(config_path, 'wb') as f:
            pickle.dump(config, f)
        
        logger.info(f"Ensemble model saved to {path}")
    
    def load_model(self, path: str) -> None:
        """Load ensemble model"""
        import pickle
        import os
        
        # Load ensemble configuration
        config_path = os.path.join(path, 'ensemble_config.pkl')
        with open(config_path, 'rb') as f:
            config = pickle.load(f)
        
        self.weights = config['weights']
        
        # Load individual models (would need model type information)
        logger.info(f"Ensemble model loaded from {path}")


class TemperaturePredictionPipeline:
    """Complete pipeline for temperature prediction with preprocessing and evaluation"""
    
    def __init__(self, model: TemperaturePredictionModel):
        self.model = model
        self.feature_columns = [
            'temperature', 'weather_temp', 'humidity', 'wind_speed',
            'energy_consumption', 'control_output', 'hour_of_day', 
            'day_of_week', 'month', 'is_weekend'
        ]
        self.preprocessing_steps = []
    
    def add_time_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add time-based features"""
        data = data.copy()
        data['timestamp'] = pd.to_datetime(data['timestamp'])
        
        data['hour_of_day'] = data['timestamp'].dt.hour
        data['day_of_week'] = data['timestamp'].dt.dayofweek
        data['month'] = data['timestamp'].dt.month
        data['is_weekend'] = (data['day_of_week'] >= 5).astype(int)
        
        # Cyclical encoding for time features
        data['hour_sin'] = np.sin(2 * np.pi * data['hour_of_day'] / 24)
        data['hour_cos'] = np.cos(2 * np.pi * data['hour_of_day'] / 24)
        data['day_sin'] = np.sin(2 * np.pi * data['day_of_week'] / 7)
        data['day_cos'] = np.cos(2 * np.pi * data['day_of_week'] / 7)
        
        return data
    
    def add_lag_features(self, data: pd.DataFrame, lags: List[int] = [1, 2, 6, 12, 24]) -> pd.DataFrame:
        """Add lagged temperature features"""
        data = data.copy()
        
        for lag in lags:
            data[f'temp_lag_{lag}'] = data['temperature'].shift(lag)
        
        # Add rolling statistics
        data['temp_ma_6h'] = data['temperature'].rolling(window=6).mean()
        data['temp_ma_24h'] = data['temperature'].rolling(window=24).mean()
        data['temp_std_6h'] = data['temperature'].rolling(window=6).std()
        
        return data
    
    def preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Complete preprocessing pipeline"""
        # Add time features
        data = self.add_time_features(data)
        
        # Add lag features
        data = self.add_lag_features(data)
        
        # Handle missing values
        data = data.fillna(method='forward').fillna(method='backward')
        
        # Remove outliers (optional)
        data = self.remove_outliers(data)
        
        return data
    
    def remove_outliers(self, data: pd.DataFrame, z_threshold: float = 3.0) -> pd.DataFrame:
        """Remove outliers using z-score"""
        data = data.copy()
        
        for col in ['temperature', 'weather_temp', 'energy_consumption']:
            if col in data.columns:
                z_scores = np.abs((data[col] - data[col].mean()) / data[col].std())
                data = data[z_scores < z_threshold]
        
        return data
    
    def evaluate_model(self, data: pd.DataFrame, test_size: float = 0.2) -> Dict[str, float]:
        """Evaluate model performance"""
        from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
        
        # Preprocess data
        processed_data = self.preprocess_data(data)
        
        # Split data
        split_idx = int(len(processed_data) * (1 - test_size))
        train_data = processed_data[:split_idx]
        test_data = processed_data[split_idx:]
        
        # Train model
        self.model.train(train_data)
        
        # Make predictions
        predictions = []
        actuals = []
        
        sequence_length = getattr(self.model, 'sequence_length', 24)
        horizon = getattr(self.model, 'horizon', 6)
        
        available_features = [f for f in self.feature_columns if f in test_data.columns]
        
        for i in range(sequence_length, len(test_data) - horizon + 1, horizon):
            features = test_data[available_features].iloc[i-sequence_length:i].values
            pred = self.model.predict(features, horizon)
            predictions.extend(pred if isinstance(pred, (list, np.ndarray)) else [pred])
            
            actual = test_data['temperature'].iloc[i:i+horizon].values
            actuals.extend(actual)
        
        # Calculate metrics
        predictions = np.array(predictions[:len(actuals)])
        actuals = np.array(actuals[:len(predictions)])
        
        mae = mean_absolute_error(actuals, predictions)
        mse = mean_squared_error(actuals, predictions)
        rmse = np.sqrt(mse)
        r2 = r2_score(actuals, predictions)
        mape = np.mean(np.abs((actuals - predictions) / actuals)) * 100
        
        return {
            'mae': mae,
            'mse': mse,
            'rmse': rmse,
            'r2': r2,
            'mape': mape,
            'num_predictions': len(predictions)
        }


def create_temperature_model(model_type: str = 'lstm', **kwargs) -> TemperaturePredictionModel:
    """
    Factory function to create temperature prediction models
    
    Args:
        model_type: Type of model ('lstm', 'transformer', 'statistical', 'ensemble')
        **kwargs: Model-specific parameters
    
    Returns:
        Temperature prediction model instance
    """
    if model_type == 'lstm':
        return LSTMTemperatureModel(**kwargs)
    elif model_type == 'transformer':
        return TransformerTemperatureModel(**kwargs)
    elif model_type == 'statistical':
        return StatisticalTemperatureModel(**kwargs)
    elif model_type == 'ensemble':
        models = kwargs.get('models', [])
        weights = kwargs.get('weights', None)
        return EnsembleTemperatureModel(models, weights)
    else:
        raise ValueError(f"Unknown model type: {model_type}")


# Example usage and testing
if __name__ == "__main__":
    # Create sample data for testing
    dates = pd.date_range('2023-01-01', periods=2000, freq='H')
    
    # Generate synthetic temperature data with daily and weekly patterns
    np.random.seed(42)
    daily_pattern = 10 * np.sin(2 * np.pi * np.arange(2000) / 24)
    weekly_pattern = 5 * np.sin(2 * np.pi * np.arange(2000) / (24 * 7))
    noise = np.random.normal(0, 2, 2000)
    base_temp = 150  # Base asphalt temperature
    
    temperatures = base_temp + daily_pattern + weekly_pattern + noise
    
    # Create DataFrame
    sample_data = pd.DataFrame({
        'timestamp': dates,
        'temperature': temperatures,
        'weather_temp': 20 + 10 * np.sin(2 * np.pi * np.arange(2000) / 24) + np.random.normal(0, 2, 2000),
        'humidity': 50 + 20 * np.random.random(2000),
        'wind_speed': 5 + 5 * np.random.random(2000),
        'energy_consumption': 1000 + 200 * np.random.random(2000),
        'control_output': 50 + 30 * np.random.random(2000)
    })
    
    print("Testing Temperature Prediction Models...")
    
    # Test statistical model
    stat_model = create_temperature_model('statistical', method='linear_trend')
    pipeline_stat = TemperaturePredictionPipeline(stat_model)
    stat_metrics = pipeline_stat.evaluate_model(sample_data)
    print(f"Statistical model metrics: {stat_metrics}")
    
    # Test LSTM model (if PyTorch available)
    if TORCH_AVAILABLE:
        lstm_model = create_temperature_model('lstm', hidden_size=32, num_layers=2)
        pipeline_lstm = TemperaturePredictionPipeline(lstm_model)
        lstm_metrics = pipeline_lstm.evaluate_model(sample_data)
        print(f"LSTM model metrics: {lstm_metrics}")
        
        # Test Transformer model
        transformer_model = create_temperature_model('transformer', d_model=32, nhead=4, num_layers=2)
        pipeline_transformer = TemperaturePredictionPipeline(transformer_model)
        transformer_metrics = pipeline_transformer.evaluate_model(sample_data)
        print(f"Transformer model metrics: {transformer_metrics}")
        
        # Test ensemble model
        ensemble_models = [lstm_model, transformer_model, stat_model]
        ensemble_model = create_temperature_model('ensemble', models=ensemble_models)
        pipeline_ensemble = TemperaturePredictionPipeline(ensemble_model)
        ensemble_metrics = pipeline_ensemble.evaluate_model(sample_data)
        print(f"Ensemble model metrics: {ensemble_metrics}")
    
    print("Temperature prediction model testing completed!")