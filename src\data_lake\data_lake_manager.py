"""
Data Lake Manager for Historical Data Storage and Batch Processing
Manages data lake operations with MinIO backend and batch processing capabilities
"""

import asyncio
import logging
import json
import pickle
import gzip
import io
from typing import Dict, List, Optional, Any, Tuple, Iterator
from dataclasses import dataclass, field
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from pathlib import Path
import hashlib
import pandas as pd
import numpy as np
from abc import ABC, abstractmethod
import warnings
warnings.filterwarnings('ignore')

try:
    import minio
    from minio import Minio
    from minio.error import S3Error
    MINIO_AVAILABLE = True
except ImportError:
    MINIO_AVAILABLE = False
    print("Warning: MinIO not available. Local storage will be used.")

try:
    import boto3
    from botocore.exceptions import ClientError
    BOTO3_AVAILABLE = True
except ImportError:
    BOTO3_AVAILABLE = False
    print("Warning: boto3 not available. AWS S3 integration will be limited.")

try:
    import pyarrow as pa
    import pyarrow.parquet as pq
    PARQUET_AVAILABLE = True
except ImportError:
    PARQUET_AVAILABLE = False
    print("Warning: PyArrow not available. Parquet support will be limited.")

logger = logging.getLogger(__name__)


class StorageFormat(Enum):
    JSON = "json"
    PARQUET = "parquet"
    CSV = "csv"
    PICKLE = "pickle"
    COMPRESSED_JSON = "json.gz"


class DataPartition(Enum):
    HOURLY = "hourly"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    YEARLY = "yearly"


class DataCategory(Enum):
    RAW_SENSOR_DATA = "raw_sensor_data"
    PROCESSED_DATA = "processed_data"
    FEATURES = "features"
    MODELS = "models"
    PREDICTIONS = "predictions"
    ALERTS = "alerts"
    METADATA = "metadata"


@dataclass
class DataLakeConfig:
    """Configuration for data lake"""
    backend: str = "minio"  # minio, s3, local
    endpoint: str = "localhost:9000"
    access_key: str = "minioadmin"
    secret_key: str = "minioadmin"
    bucket_name: str = "datalake"
    secure: bool = False
    region: str = "us-east-1"
    local_storage_path: str = "/tmp/datalake"
    default_format: StorageFormat = StorageFormat.JSON
    default_partition: DataPartition = DataPartition.DAILY
    compression_enabled: bool = True
    encryption_enabled: bool = False
    max_file_size: int = 100 * 1024 * 1024  # 100MB
    retention_days: int = 365


@dataclass
class DataObjectMetadata:
    """Metadata for data objects in the lake"""
    object_key: str
    category: DataCategory
    format: StorageFormat
    partition: DataPartition
    created_at: datetime
    updated_at: datetime
    size_bytes: int
    record_count: int
    schema_version: str = "1.0"
    tags: Dict[str, str] = field(default_factory=dict)
    checksum: str = ""
    compression: str = "none"
    encryption: str = "none"


@dataclass
class QueryRequest:
    """Request for data lake queries"""
    category: DataCategory
    start_time: datetime
    end_time: datetime
    filters: Dict[str, Any] = field(default_factory=dict)
    columns: Optional[List[str]] = None
    max_records: Optional[int] = None
    format: StorageFormat = StorageFormat.JSON
    include_metadata: bool = False


class DataLakeBackend(ABC):
    """Abstract base class for data lake backends"""
    
    @abstractmethod
    async def put_object(self, key: str, data: bytes, metadata: Dict[str, str] = None) -> bool:
        """Store object in data lake"""
        pass
    
    @abstractmethod
    async def get_object(self, key: str) -> Optional[bytes]:
        """Retrieve object from data lake"""
        pass
    
    @abstractmethod
    async def list_objects(self, prefix: str = "") -> List[str]:
        """List objects in data lake"""
        pass
    
    @abstractmethod
    async def delete_object(self, key: str) -> bool:
        """Delete object from data lake"""
        pass
    
    @abstractmethod
    async def get_object_metadata(self, key: str) -> Optional[Dict[str, str]]:
        """Get object metadata"""
        pass


class MinIOBackend(DataLakeBackend):
    """MinIO backend for data lake"""
    
    def __init__(self, config: DataLakeConfig):
        self.config = config
        self.client = None
        
        if MINIO_AVAILABLE:
            self.client = Minio(
                endpoint=config.endpoint,
                access_key=config.access_key,
                secret_key=config.secret_key,
                secure=config.secure
            )
    
    async def initialize(self) -> bool:
        """Initialize MinIO backend"""
        try:
            if not self.client:
                logger.error("MinIO client not available")
                return False
            
            # Create bucket if it doesn't exist
            if not self.client.bucket_exists(self.config.bucket_name):
                self.client.make_bucket(self.config.bucket_name)
                logger.info(f"Created bucket: {self.config.bucket_name}")
            
            return True
        
        except Exception as e:
            logger.error(f"Error initializing MinIO backend: {e}")
            return False
    
    async def put_object(self, key: str, data: bytes, metadata: Dict[str, str] = None) -> bool:
        """Store object in MinIO"""
        try:
            if not self.client:
                return False
            
            # Create file-like object from bytes
            data_stream = io.BytesIO(data)
            
            # Upload to MinIO
            self.client.put_object(
                bucket_name=self.config.bucket_name,
                object_name=key,
                data=data_stream,
                length=len(data),
                metadata=metadata or {}
            )
            
            return True
        
        except Exception as e:
            logger.error(f"Error putting object to MinIO: {e}")
            return False
    
    async def get_object(self, key: str) -> Optional[bytes]:
        """Retrieve object from MinIO"""
        try:
            if not self.client:
                return None
            
            response = self.client.get_object(self.config.bucket_name, key)
            data = response.read()
            response.close()
            response.release_conn()
            
            return data
        
        except Exception as e:
            logger.error(f"Error getting object from MinIO: {e}")
            return None
    
    async def list_objects(self, prefix: str = "") -> List[str]:
        """List objects in MinIO"""
        try:
            if not self.client:
                return []
            
            objects = self.client.list_objects(self.config.bucket_name, prefix=prefix)
            return [obj.object_name for obj in objects]
        
        except Exception as e:
            logger.error(f"Error listing objects in MinIO: {e}")
            return []
    
    async def delete_object(self, key: str) -> bool:
        """Delete object from MinIO"""
        try:
            if not self.client:
                return False
            
            self.client.remove_object(self.config.bucket_name, key)
            return True
        
        except Exception as e:
            logger.error(f"Error deleting object from MinIO: {e}")
            return False
    
    async def get_object_metadata(self, key: str) -> Optional[Dict[str, str]]:
        """Get object metadata from MinIO"""
        try:
            if not self.client:
                return None
            
            stat = self.client.stat_object(self.config.bucket_name, key)
            return {
                'size': str(stat.size),
                'last_modified': stat.last_modified.isoformat(),
                'etag': stat.etag,
                'content_type': stat.content_type,
                **stat.metadata
            }
        
        except Exception as e:
            logger.error(f"Error getting object metadata from MinIO: {e}")
            return None


class LocalFileBackend(DataLakeBackend):
    """Local file system backend for data lake"""
    
    def __init__(self, config: DataLakeConfig):
        self.config = config
        self.storage_path = Path(config.local_storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
    
    async def initialize(self) -> bool:
        """Initialize local file backend"""
        try:
            self.storage_path.mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"Error initializing local file backend: {e}")
            return False
    
    async def put_object(self, key: str, data: bytes, metadata: Dict[str, str] = None) -> bool:
        """Store object in local file system"""
        try:
            file_path = self.storage_path / key
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'wb') as f:
                f.write(data)
            
            # Store metadata separately
            if metadata:
                metadata_path = file_path.with_suffix('.metadata.json')
                with open(metadata_path, 'w') as f:
                    json.dump(metadata, f)
            
            return True
        
        except Exception as e:
            logger.error(f"Error putting object to local storage: {e}")
            return False
    
    async def get_object(self, key: str) -> Optional[bytes]:
        """Retrieve object from local file system"""
        try:
            file_path = self.storage_path / key
            
            if not file_path.exists():
                return None
            
            with open(file_path, 'rb') as f:
                return f.read()
        
        except Exception as e:
            logger.error(f"Error getting object from local storage: {e}")
            return None
    
    async def list_objects(self, prefix: str = "") -> List[str]:
        """List objects in local file system"""
        try:
            objects = []
            search_path = self.storage_path / prefix if prefix else self.storage_path
            
            if search_path.is_dir():
                for file_path in search_path.rglob("*"):
                    if file_path.is_file() and not file_path.name.endswith('.metadata.json'):
                        relative_path = file_path.relative_to(self.storage_path)
                        objects.append(str(relative_path))
            
            return objects
        
        except Exception as e:
            logger.error(f"Error listing objects in local storage: {e}")
            return []
    
    async def delete_object(self, key: str) -> bool:
        """Delete object from local file system"""
        try:
            file_path = self.storage_path / key
            
            if file_path.exists():
                file_path.unlink()
                
                # Also delete metadata file if exists
                metadata_path = file_path.with_suffix('.metadata.json')
                if metadata_path.exists():
                    metadata_path.unlink()
                
                return True
            
            return False
        
        except Exception as e:
            logger.error(f"Error deleting object from local storage: {e}")
            return False
    
    async def get_object_metadata(self, key: str) -> Optional[Dict[str, str]]:
        """Get object metadata from local file system"""
        try:
            file_path = self.storage_path / key
            metadata_path = file_path.with_suffix('.metadata.json')
            
            if not file_path.exists():
                return None
            
            metadata = {}
            
            # Basic file info
            stat = file_path.stat()
            metadata.update({
                'size': str(stat.st_size),
                'last_modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
            })
            
            # Load stored metadata
            if metadata_path.exists():
                with open(metadata_path, 'r') as f:
                    stored_metadata = json.load(f)
                    metadata.update(stored_metadata)
            
            return metadata
        
        except Exception as e:
            logger.error(f"Error getting object metadata from local storage: {e}")
            return None


class DataLakeManager:
    """Main data lake manager"""
    
    def __init__(self, config: DataLakeConfig):
        self.config = config
        self.backend = None
        self.metadata_cache = {}
        self.statistics = {
            'objects_stored': 0,
            'objects_retrieved': 0,
            'bytes_stored': 0,
            'bytes_retrieved': 0,
            'queries_executed': 0
        }
        
        # Initialize backend
        if config.backend == "minio":
            self.backend = MinIOBackend(config)
        elif config.backend == "local":
            self.backend = LocalFileBackend(config)
        else:
            raise ValueError(f"Unsupported backend: {config.backend}")
    
    async def initialize(self) -> bool:
        """Initialize data lake manager"""
        try:
            if self.backend:
                return await self.backend.initialize()
            return False
        except Exception as e:
            logger.error(f"Error initializing data lake manager: {e}")
            return False
    
    async def store_data(self, data: Any, category: DataCategory, 
                        partition_key: Optional[str] = None,
                        format: Optional[StorageFormat] = None,
                        metadata: Optional[Dict[str, str]] = None) -> Optional[str]:
        """Store data in the data lake"""
        try:
            # Determine format
            storage_format = format or self.config.default_format
            
            # Generate object key
            object_key = self._generate_object_key(category, partition_key, storage_format)
            
            # Serialize data
            serialized_data = await self._serialize_data(data, storage_format)
            
            if serialized_data is None:
                logger.error("Failed to serialize data")
                return None
            
            # Compress if enabled
            if self.config.compression_enabled:
                serialized_data = gzip.compress(serialized_data)
            
            # Prepare metadata
            storage_metadata = metadata or {}
            storage_metadata.update({
                'category': category.value,
                'format': storage_format.value,
                'timestamp': datetime.now().isoformat(),
                'compressed': str(self.config.compression_enabled),
                'size': str(len(serialized_data))
            })
            
            # Store in backend
            success = await self.backend.put_object(object_key, serialized_data, storage_metadata)
            
            if success:
                # Update statistics
                self.statistics['objects_stored'] += 1
                self.statistics['bytes_stored'] += len(serialized_data)
                
                # Cache metadata
                self.metadata_cache[object_key] = DataObjectMetadata(
                    object_key=object_key,
                    category=category,
                    format=storage_format,
                    partition=self.config.default_partition,
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                    size_bytes=len(serialized_data),
                    record_count=await self._estimate_record_count(data),
                    checksum=hashlib.md5(serialized_data).hexdigest(),
                    compression="gzip" if self.config.compression_enabled else "none"
                )
                
                logger.info(f"Stored data: {object_key}")
                return object_key
            
            return None
        
        except Exception as e:
            logger.error(f"Error storing data: {e}")
            return None
    
    async def retrieve_data(self, object_key: str, 
                           format: Optional[StorageFormat] = None) -> Optional[Any]:
        """Retrieve data from the data lake"""
        try:
            # Get raw data from backend
            raw_data = await self.backend.get_object(object_key)
            
            if raw_data is None:
                logger.warning(f"Object not found: {object_key}")
                return None
            
            # Update statistics
            self.statistics['objects_retrieved'] += 1
            self.statistics['bytes_retrieved'] += len(raw_data)
            
            # Decompress if needed
            if object_key in self.metadata_cache:
                metadata = self.metadata_cache[object_key]
                if metadata.compression == "gzip":
                    raw_data = gzip.decompress(raw_data)
            else:
                # Try to decompress (backward compatibility)
                try:
                    raw_data = gzip.decompress(raw_data)
                except:
                    pass  # Data wasn't compressed
            
            # Determine format
            storage_format = format
            if not storage_format and object_key in self.metadata_cache:
                storage_format = self.metadata_cache[object_key].format
            
            if not storage_format:
                # Guess format from extension
                if object_key.endswith('.json'):
                    storage_format = StorageFormat.JSON
                elif object_key.endswith('.parquet'):
                    storage_format = StorageFormat.PARQUET
                elif object_key.endswith('.csv'):
                    storage_format = StorageFormat.CSV
                else:
                    storage_format = StorageFormat.JSON
            
            # Deserialize data
            return await self._deserialize_data(raw_data, storage_format)
        
        except Exception as e:
            logger.error(f"Error retrieving data: {e}")
            return None
    
    async def query_data(self, query: QueryRequest) -> Optional[Any]:
        """Query data from the data lake"""
        try:
            self.statistics['queries_executed'] += 1
            
            # Find relevant objects
            object_keys = await self._find_objects_for_query(query)
            
            if not object_keys:
                logger.warning(f"No objects found for query: {query.category}")
                return None
            
            # Retrieve and combine data
            combined_data = []
            records_retrieved = 0
            
            for object_key in object_keys:
                if query.max_records and records_retrieved >= query.max_records:
                    break
                
                data = await self.retrieve_data(object_key)
                if data is not None:
                    # Apply filters
                    filtered_data = await self._apply_filters(data, query.filters)
                    
                    if filtered_data is not None:
                        combined_data.append(filtered_data)
                        records_retrieved += len(filtered_data) if hasattr(filtered_data, '__len__') else 1
            
            # Combine results
            if combined_data:
                return await self._combine_query_results(combined_data, query)
            
            return None
        
        except Exception as e:
            logger.error(f"Error querying data: {e}")
            return None
    
    async def list_objects(self, category: Optional[DataCategory] = None,
                          start_time: Optional[datetime] = None,
                          end_time: Optional[datetime] = None) -> List[str]:
        """List objects in the data lake"""
        try:
            prefix = ""
            if category:
                prefix = f"{category.value}/"
            
            all_objects = await self.backend.list_objects(prefix)
            
            # Filter by time range if specified
            if start_time or end_time:
                filtered_objects = []
                for obj_key in all_objects:
                    # Extract timestamp from object key or metadata
                    obj_time = await self._extract_object_timestamp(obj_key)
                    if obj_time:
                        if start_time and obj_time < start_time:
                            continue
                        if end_time and obj_time > end_time:
                            continue
                    filtered_objects.append(obj_key)
                return filtered_objects
            
            return all_objects
        
        except Exception as e:
            logger.error(f"Error listing objects: {e}")
            return []
    
    async def delete_data(self, object_key: str) -> bool:
        """Delete data from the data lake"""
        try:
            success = await self.backend.delete_object(object_key)
            
            if success:
                # Remove from metadata cache
                if object_key in self.metadata_cache:
                    del self.metadata_cache[object_key]
                
                logger.info(f"Deleted data: {object_key}")
            
            return success
        
        except Exception as e:
            logger.error(f"Error deleting data: {e}")
            return False
    
    async def cleanup_old_data(self, retention_days: Optional[int] = None) -> int:
        """Clean up old data based on retention policy"""
        try:
            retention = retention_days or self.config.retention_days
            cutoff_time = datetime.now() - timedelta(days=retention)
            
            deleted_count = 0
            all_objects = await self.backend.list_objects()
            
            for obj_key in all_objects:
                obj_time = await self._extract_object_timestamp(obj_key)
                if obj_time and obj_time < cutoff_time:
                    if await self.delete_data(obj_key):
                        deleted_count += 1
            
            logger.info(f"Cleaned up {deleted_count} old objects")
            return deleted_count
        
        except Exception as e:
            logger.error(f"Error cleaning up old data: {e}")
            return 0
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Get data lake statistics"""
        return {
            'statistics': self.statistics,
            'metadata_cache_size': len(self.metadata_cache),
            'backend_type': self.config.backend,
            'total_objects': len(await self.backend.list_objects())
        }
    
    def _generate_object_key(self, category: DataCategory, partition_key: Optional[str], 
                            format: StorageFormat) -> str:
        """Generate object key for storage"""
        timestamp = datetime.now()
        
        # Base path
        base_path = f"{category.value}"
        
        # Add partition
        if self.config.default_partition == DataPartition.HOURLY:
            partition_path = f"{timestamp.year}/{timestamp.month:02d}/{timestamp.day:02d}/{timestamp.hour:02d}"
        elif self.config.default_partition == DataPartition.DAILY:
            partition_path = f"{timestamp.year}/{timestamp.month:02d}/{timestamp.day:02d}"
        elif self.config.default_partition == DataPartition.WEEKLY:
            week = timestamp.isocalendar()[1]
            partition_path = f"{timestamp.year}/week_{week:02d}"
        elif self.config.default_partition == DataPartition.MONTHLY:
            partition_path = f"{timestamp.year}/{timestamp.month:02d}"
        else:
            partition_path = f"{timestamp.year}"
        
        # Add partition key if provided
        if partition_key:
            partition_path = f"{partition_path}/{partition_key}"
        
        # Add timestamp and format
        filename = f"{timestamp.strftime('%Y%m%d_%H%M%S')}_{hashlib.md5(str(timestamp).encode()).hexdigest()[:8]}"
        
        if format == StorageFormat.JSON:
            extension = ".json"
        elif format == StorageFormat.PARQUET:
            extension = ".parquet"
        elif format == StorageFormat.CSV:
            extension = ".csv"
        elif format == StorageFormat.PICKLE:
            extension = ".pickle"
        elif format == StorageFormat.COMPRESSED_JSON:
            extension = ".json.gz"
        else:
            extension = ".json"
        
        return f"{base_path}/{partition_path}/{filename}{extension}"
    
    async def _serialize_data(self, data: Any, format: StorageFormat) -> Optional[bytes]:
        """Serialize data to bytes"""
        try:
            if format == StorageFormat.JSON:
                json_str = json.dumps(data, default=str)
                return json_str.encode('utf-8')
            
            elif format == StorageFormat.PARQUET:
                if PARQUET_AVAILABLE and isinstance(data, (pd.DataFrame, list, dict)):
                    if isinstance(data, pd.DataFrame):
                        df = data
                    elif isinstance(data, list):
                        df = pd.DataFrame(data)
                    else:
                        df = pd.DataFrame([data])
                    
                    buffer = io.BytesIO()
                    df.to_parquet(buffer, index=False)
                    return buffer.getvalue()
                else:
                    logger.warning("Parquet not available, falling back to JSON")
                    json_str = json.dumps(data, default=str)
                    return json_str.encode('utf-8')
            
            elif format == StorageFormat.CSV:
                if isinstance(data, pd.DataFrame):
                    return data.to_csv(index=False).encode('utf-8')
                elif isinstance(data, list):
                    df = pd.DataFrame(data)
                    return df.to_csv(index=False).encode('utf-8')
                else:
                    logger.warning("CSV requires DataFrame or list, falling back to JSON")
                    json_str = json.dumps(data, default=str)
                    return json_str.encode('utf-8')
            
            elif format == StorageFormat.PICKLE:
                return pickle.dumps(data)
            
            elif format == StorageFormat.COMPRESSED_JSON:
                json_str = json.dumps(data, default=str)
                return gzip.compress(json_str.encode('utf-8'))
            
            else:
                logger.warning(f"Unsupported format: {format}, falling back to JSON")
                json_str = json.dumps(data, default=str)
                return json_str.encode('utf-8')
        
        except Exception as e:
            logger.error(f"Error serializing data: {e}")
            return None
    
    async def _deserialize_data(self, data: bytes, format: StorageFormat) -> Optional[Any]:
        """Deserialize data from bytes"""
        try:
            if format == StorageFormat.JSON:
                json_str = data.decode('utf-8')
                return json.loads(json_str)
            
            elif format == StorageFormat.PARQUET:
                if PARQUET_AVAILABLE:
                    buffer = io.BytesIO(data)
                    df = pd.read_parquet(buffer)
                    return df
                else:
                    logger.warning("Parquet not available, trying JSON")
                    json_str = data.decode('utf-8')
                    return json.loads(json_str)
            
            elif format == StorageFormat.CSV:
                buffer = io.StringIO(data.decode('utf-8'))
                df = pd.read_csv(buffer)
                return df
            
            elif format == StorageFormat.PICKLE:
                return pickle.loads(data)
            
            elif format == StorageFormat.COMPRESSED_JSON:
                decompressed = gzip.decompress(data)
                json_str = decompressed.decode('utf-8')
                return json.loads(json_str)
            
            else:
                logger.warning(f"Unsupported format: {format}, trying JSON")
                json_str = data.decode('utf-8')
                return json.loads(json_str)
        
        except Exception as e:
            logger.error(f"Error deserializing data: {e}")
            return None
    
    async def _estimate_record_count(self, data: Any) -> int:
        """Estimate number of records in data"""
        try:
            if isinstance(data, (list, pd.DataFrame)):
                return len(data)
            elif isinstance(data, dict):
                return 1
            else:
                return 1
        except:
            return 1
    
    async def _find_objects_for_query(self, query: QueryRequest) -> List[str]:
        """Find objects that match the query criteria"""
        try:
            # Get all objects for the category
            all_objects = await self.list_objects(query.category, query.start_time, query.end_time)
            
            # Filter by time range (additional filtering)
            filtered_objects = []
            for obj_key in all_objects:
                obj_time = await self._extract_object_timestamp(obj_key)
                if obj_time:
                    if query.start_time <= obj_time <= query.end_time:
                        filtered_objects.append(obj_key)
                else:
                    # If we can't determine time, include it
                    filtered_objects.append(obj_key)
            
            return filtered_objects
        
        except Exception as e:
            logger.error(f"Error finding objects for query: {e}")
            return []
    
    async def _extract_object_timestamp(self, object_key: str) -> Optional[datetime]:
        """Extract timestamp from object key"""
        try:
            # Try to extract from object key pattern
            parts = object_key.split('/')
            if len(parts) >= 4:
                filename = parts[-1]
                timestamp_part = filename.split('_')[0]
                if len(timestamp_part) == 15:  # YYYYMMDD_HHMMSS
                    return datetime.strptime(timestamp_part, '%Y%m%d_%H%M%S')
            
            # Fallback to metadata
            metadata = await self.backend.get_object_metadata(object_key)
            if metadata and 'timestamp' in metadata:
                return datetime.fromisoformat(metadata['timestamp'])
            
            return None
        
        except Exception as e:
            logger.error(f"Error extracting timestamp from {object_key}: {e}")
            return None
    
    async def _apply_filters(self, data: Any, filters: Dict[str, Any]) -> Optional[Any]:
        """Apply filters to data"""
        try:
            if not filters:
                return data
            
            # If data is a DataFrame, use pandas filtering
            if isinstance(data, pd.DataFrame):
                filtered_df = data.copy()
                for column, value in filters.items():
                    if column in filtered_df.columns:
                        if isinstance(value, dict):
                            # Range filter
                            if 'min' in value:
                                filtered_df = filtered_df[filtered_df[column] >= value['min']]
                            if 'max' in value:
                                filtered_df = filtered_df[filtered_df[column] <= value['max']]
                        else:
                            # Exact match
                            filtered_df = filtered_df[filtered_df[column] == value]
                
                return filtered_df
            
            # If data is a list of dicts, filter manually
            elif isinstance(data, list) and data and isinstance(data[0], dict):
                filtered_data = []
                for item in data:
                    matches = True
                    for key, value in filters.items():
                        if key in item:
                            if isinstance(value, dict):
                                # Range filter
                                if 'min' in value and item[key] < value['min']:
                                    matches = False
                                    break
                                if 'max' in value and item[key] > value['max']:
                                    matches = False
                                    break
                            else:
                                # Exact match
                                if item[key] != value:
                                    matches = False
                                    break
                    
                    if matches:
                        filtered_data.append(item)
                
                return filtered_data
            
            # For other data types, return as-is
            return data
        
        except Exception as e:
            logger.error(f"Error applying filters: {e}")
            return data
    
    async def _combine_query_results(self, data_list: List[Any], query: QueryRequest) -> Optional[Any]:
        """Combine query results"""
        try:
            if not data_list:
                return None
            
            # If all data are DataFrames, concatenate them
            if all(isinstance(data, pd.DataFrame) for data in data_list):
                combined_df = pd.concat(data_list, ignore_index=True)
                
                # Apply column selection
                if query.columns:
                    available_columns = [col for col in query.columns if col in combined_df.columns]
                    if available_columns:
                        combined_df = combined_df[available_columns]
                
                # Apply record limit
                if query.max_records:
                    combined_df = combined_df.head(query.max_records)
                
                return combined_df
            
            # If all data are lists, concatenate them
            elif all(isinstance(data, list) for data in data_list):
                combined_list = []
                for data in data_list:
                    combined_list.extend(data)
                
                # Apply record limit
                if query.max_records:
                    combined_list = combined_list[:query.max_records]
                
                return combined_list
            
            # Otherwise, return as list
            return data_list
        
        except Exception as e:
            logger.error(f"Error combining query results: {e}")
            return data_list


# Factory function
def create_data_lake_manager(config: Optional[DataLakeConfig] = None) -> DataLakeManager:
    """Create a data lake manager instance"""
    if config is None:
        config = DataLakeConfig()
    
    return DataLakeManager(config)


# Example usage
if __name__ == "__main__":
    async def test_data_lake():
        # Create data lake manager
        config = DataLakeConfig(
            backend="local",
            local_storage_path="/tmp/test_datalake"
        )
        
        data_lake = create_data_lake_manager(config)
        await data_lake.initialize()
        
        # Test data
        test_data = [
            {"timestamp": "2023-01-01T10:00:00", "temperature": 150.5, "sensor_id": "temp_001"},
            {"timestamp": "2023-01-01T10:01:00", "temperature": 151.0, "sensor_id": "temp_001"},
            {"timestamp": "2023-01-01T10:02:00", "temperature": 150.8, "sensor_id": "temp_001"}
        ]
        
        # Store data
        object_key = await data_lake.store_data(
            test_data, 
            DataCategory.RAW_SENSOR_DATA,
            partition_key="sensor_temp_001"
        )
        
        print(f"Stored data with key: {object_key}")
        
        # Retrieve data
        retrieved_data = await data_lake.retrieve_data(object_key)
        print(f"Retrieved data: {retrieved_data}")
        
        # Query data
        query = QueryRequest(
            category=DataCategory.RAW_SENSOR_DATA,
            start_time=datetime(2023, 1, 1),
            end_time=datetime(2023, 1, 2)
        )
        
        query_results = await data_lake.query_data(query)
        print(f"Query results: {query_results}")
        
        # Get statistics
        stats = await data_lake.get_statistics()
        print(f"Statistics: {json.dumps(stats, indent=2)}")
    
    # Run test
    asyncio.run(test_data_lake())