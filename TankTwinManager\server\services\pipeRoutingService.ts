/**
 * Pipe Routing Service
 * Manages pipe networks, flow calculations, and routing optimization
 */

import { 
  PipeNetwork, 
  PipeConnection, 
  PIPE_ROUTING_CONFIG,
  HOT_OIL_PIPE_NETWORK,
  ASPHALT_PIPE_NETWORK 
} from '../config/pipe-routing';

export interface FlowCalculation {
  pipeId: string;
  flowRate: number; // L/min
  velocity: number; // m/s
  pressureDrop: number; // bar
  reynoldsNumber: number;
  frictionFactor: number;
  heatLoss?: number; // kW (for hot-oil pipes)
}

export interface PipeSystemStatus {
  networkId: string;
  operationalStatus: 'normal' | 'warning' | 'critical' | 'offline';
  totalFlowRate: number; // L/min
  systemPressure: number; // bar
  averageTemperature: number; // °C
  efficiency: number; // %
  alarms: PipeAlarm[];
  maintenanceRequired: boolean;
}

export interface PipeAlarm {
  id: string;
  pipeId: string;
  type: 'pressure' | 'temperature' | 'flow' | 'leak' | 'blockage';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  acknowledged: boolean;
}

export interface RouteOptimization {
  sourceId: string;
  destinationId: string;
  optimalRoute: string[]; // Array of pipe IDs
  totalLength: number; // m
  estimatedPressureDrop: number; // bar
  estimatedFlowTime: number; // minutes
  energyConsumption: number; // kWh
}

export class PipeRoutingService {
  private networks: Map<string, PipeNetwork> = new Map();
  private flowCalculations: Map<string, FlowCalculation> = new Map();
  private systemStatus: Map<string, PipeSystemStatus> = new Map();
  private alarms: PipeAlarm[] = [];

  constructor() {
    this.initializeNetworks();
    this.calculateInitialFlows();
  }

  private initializeNetworks() {
    // Initialize pipe networks
    this.networks.set(HOT_OIL_PIPE_NETWORK.id, HOT_OIL_PIPE_NETWORK);
    this.networks.set(ASPHALT_PIPE_NETWORK.id, ASPHALT_PIPE_NETWORK);

    // Initialize system status
    this.networks.forEach((network, id) => {
      this.systemStatus.set(id, {
        networkId: id,
        operationalStatus: 'normal',
        totalFlowRate: network.operationalData.flowRate,
        systemPressure: network.operationalData.operatingPressure,
        averageTemperature: network.operationalData.operatingTemperature,
        efficiency: 85,
        alarms: [],
        maintenanceRequired: false
      });
    });

    console.log(`Initialized ${this.networks.size} pipe networks`);
  }

  private calculateInitialFlows() {
    this.networks.forEach(network => {
      network.connections.forEach(pipe => {
        const flowCalc = this.calculatePipeFlow(pipe, network.operationalData.flowRate);
        this.flowCalculations.set(pipe.id, flowCalc);
      });
    });
  }

  public calculatePipeFlow(pipe: PipeConnection, flowRate: number): FlowCalculation {
    // Pipe flow calculations using Darcy-Weisbach equation
    const diameter = pipe.specifications.diameter / 1000; // Convert mm to m
    const area = Math.PI * Math.pow(diameter / 2, 2); // m²
    const velocity = (flowRate / 60000) / area; // m/s (convert L/min to m³/s)
    
    // Fluid properties (simplified for asphalt/hot oil)
    const density = pipe.type.includes('hot_oil') ? 850 : 1000; // kg/m³
    const viscosity = pipe.type.includes('hot_oil') ? 0.001 : 0.1; // Pa·s
    
    // Reynolds number
    const reynoldsNumber = (density * velocity * diameter) / viscosity;
    
    // Friction factor (simplified Colebrook-White)
    const roughness = 0.000045; // m (steel pipe roughness)
    const frictionFactor = this.calculateFrictionFactor(reynoldsNumber, roughness / diameter);
    
    // Pressure drop (Darcy-Weisbach equation)
    const pressureDrop = frictionFactor * (pipe.specifications.length / diameter) * 
      (density * Math.pow(velocity, 2)) / (2 * 100000); // Convert to bar
    
    // Heat loss calculation for hot-oil pipes
    let heatLoss = 0;
    if (pipe.type.includes('hot_oil') && pipe.specifications.insulation) {
      const tempDiff = pipe.specifications.maxTemperature - 20; // Ambient temp 20°C
      const thermalConductivity = 0.04; // W/mK for mineral wool
      const insulationThickness = (pipe.specifications.insulationThickness || 100) / 1000; // Convert mm to m
      const pipeCircumference = Math.PI * diameter;
      
      heatLoss = (2 * Math.PI * thermalConductivity * pipe.specifications.length * tempDiff) /
        Math.log((diameter + 2 * insulationThickness) / diameter) / 1000; // kW
    }

    return {
      pipeId: pipe.id,
      flowRate,
      velocity,
      pressureDrop,
      reynoldsNumber,
      frictionFactor,
      heatLoss
    };
  }

  private calculateFrictionFactor(reynoldsNumber: number, relativeRoughness: number): number {
    if (reynoldsNumber < 2300) {
      // Laminar flow
      return 64 / reynoldsNumber;
    } else {
      // Turbulent flow (simplified Colebrook-White)
      return 0.25 / Math.pow(Math.log10(relativeRoughness / 3.7 + 5.74 / Math.pow(reynoldsNumber, 0.9)), 2);
    }
  }

  public updateFlowRates(networkId: string, newFlowRate: number) {
    const network = this.networks.get(networkId);
    if (!network) return;

    // Update network operational data
    network.operationalData.flowRate = newFlowRate;

    // Recalculate flows for all pipes in the network
    network.connections.forEach(pipe => {
      const flowCalc = this.calculatePipeFlow(pipe, newFlowRate);
      this.flowCalculations.set(pipe.id, flowCalc);
    });

    // Update system status
    const status = this.systemStatus.get(networkId);
    if (status) {
      status.totalFlowRate = newFlowRate;
      this.checkSystemAlarms(networkId);
    }
  }

  public optimizeRoute(sourceId: string, destinationId: string, networkId: string): RouteOptimization {
    const network = this.networks.get(networkId);
    if (!network) {
      throw new Error(`Network ${networkId} not found`);
    }

    // Simplified route optimization using shortest path
    const route = this.findShortestPath(sourceId, destinationId, network);
    const totalLength = route.reduce((sum, pipeId) => {
      const pipe = network.connections.find(p => p.id === pipeId);
      return sum + (pipe?.specifications.length || 0);
    }, 0);

    const totalPressureDrop = route.reduce((sum, pipeId) => {
      const flowCalc = this.flowCalculations.get(pipeId);
      return sum + (flowCalc?.pressureDrop || 0);
    }, 0);

    return {
      sourceId,
      destinationId,
      optimalRoute: route,
      totalLength,
      estimatedPressureDrop: totalPressureDrop,
      estimatedFlowTime: totalLength / 100, // Simplified: 100 m/min flow speed
      energyConsumption: totalPressureDrop * network.operationalData.flowRate / 60 / 1000 // kWh
    };
  }

  private findShortestPath(sourceId: string, destinationId: string, network: PipeNetwork): string[] {
    // Simplified shortest path algorithm
    // In a real implementation, you would use Dijkstra's algorithm or similar
    const relevantPipes = network.connections.filter(pipe => 
      pipe.startPoint.id === sourceId || pipe.endPoint.id === destinationId ||
      pipe.startPoint.type === 'junction' || pipe.endPoint.type === 'junction'
    );

    return relevantPipes.map(pipe => pipe.id);
  }

  private checkSystemAlarms(networkId: string) {
    const network = this.networks.get(networkId);
    const status = this.systemStatus.get(networkId);
    if (!network || !status) return;

    // Clear existing alarms for this network
    this.alarms = this.alarms.filter(alarm => 
      !network.connections.some(pipe => pipe.id === alarm.pipeId)
    );

    // Check for new alarms
    network.connections.forEach(pipe => {
      const flowCalc = this.flowCalculations.get(pipe.id);
      if (!flowCalc) return;

      // High pressure alarm
      if (flowCalc.pressureDrop > pipe.specifications.maxPressure * 0.8) {
        this.addAlarm({
          id: `ALARM-${Date.now()}-${pipe.id}`,
          pipeId: pipe.id,
          type: 'pressure',
          severity: 'high',
          message: `High pressure drop detected in ${pipe.name}`,
          timestamp: new Date(),
          acknowledged: false
        });
      }

      // High velocity alarm
      if (flowCalc.velocity > 3.0) { // 3 m/s typical limit
        this.addAlarm({
          id: `ALARM-${Date.now()}-${pipe.id}`,
          pipeId: pipe.id,
          type: 'flow',
          severity: 'medium',
          message: `High velocity detected in ${pipe.name}`,
          timestamp: new Date(),
          acknowledged: false
        });
      }
    });

    // Update system status
    status.alarms = this.alarms.filter(alarm => 
      network.connections.some(pipe => pipe.id === alarm.pipeId)
    );
    status.operationalStatus = status.alarms.length > 0 ? 'warning' : 'normal';
  }

  private addAlarm(alarm: PipeAlarm) {
    this.alarms.push(alarm);
    console.log(`Pipe alarm: ${alarm.message}`);
  }

  // Public API methods
  public getNetworks(): PipeNetwork[] {
    return Array.from(this.networks.values());
  }

  public getNetwork(networkId: string): PipeNetwork | undefined {
    return this.networks.get(networkId);
  }

  public getFlowCalculations(): Map<string, FlowCalculation> {
    return new Map(this.flowCalculations);
  }

  public getSystemStatus(): Map<string, PipeSystemStatus> {
    return new Map(this.systemStatus);
  }

  public getAlarms(): PipeAlarm[] {
    return [...this.alarms];
  }

  public acknowledgeAlarm(alarmId: string): boolean {
    const alarm = this.alarms.find(a => a.id === alarmId);
    if (alarm) {
      alarm.acknowledged = true;
      return true;
    }
    return false;
  }

  public getPipeRouting(): typeof PIPE_ROUTING_CONFIG {
    return PIPE_ROUTING_CONFIG;
  }
}
