"""
InfluxDB Client for Time-Series Data Storage
Handles real-time tank temperature, SCADA data, and energy metrics
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import numpy as np
import pandas as pd

try:
    from influxdb_client import InfluxDBClient, Point, WritePrecision, WriteOptions
    from influxdb_client.client.write_api import ASYNCHRONOUS
    from influxdb_client.client.exceptions import InfluxDBError
    INFLUXDB_AVAILABLE = True
except ImportError:
    INFLUXDB_AVAILABLE = False
    print("Warning: influxdb-client not installed. Time-series storage will be limited.")

logger = logging.getLogger(__name__)


@dataclass
class InfluxDBConfig:
    """InfluxDB connection configuration"""
    url: str = "http://localhost:8086"
    token: str = ""
    org: str = "asphalt_plant"
    bucket: str = "tank_data"
    measurement_prefix: str = "tank"
    batch_size: int = 1000
    flush_interval: int = 1000  # milliseconds
    retry_interval: int = 5000  # milliseconds
    max_retries: int = 3
    timeout: int = 10000  # milliseconds


@dataclass
class TankDataPoint:
    """Data point for tank measurements"""
    tank_id: str
    timestamp: datetime
    zone_temperatures: Dict[int, float] = field(default_factory=dict)
    heating_powers: Dict[int, float] = field(default_factory=dict)
    setpoints: Dict[int, float] = field(default_factory=dict)
    ambient_temperature: Optional[float] = None
    energy_consumption: Optional[float] = None
    energy_cost: Optional[float] = None
    total_power: Optional[float] = None
    efficiency: Optional[float] = None
    alarms: List[str] = field(default_factory=list)
    control_mode: Optional[str] = None
    tags: Dict[str, Any] = field(default_factory=dict)


class InfluxDBTimeSeriesClient:
    """InfluxDB client for tank time-series data"""
    
    def __init__(self, config: InfluxDBConfig):
        if not INFLUXDB_AVAILABLE:
            raise ImportError("influxdb-client is required for time-series storage")
        
        self.config = config
        self.client: Optional[InfluxDBClient] = None
        self.write_api = None
        self.query_api = None
        self.is_connected = False
        self._write_queue: List[Point] = []
        self._queue_lock = asyncio.Lock()
        
    async def connect(self) -> bool:
        """Connect to InfluxDB"""
        try:
            logger.info(f"Connecting to InfluxDB at {self.config.url}")
            
            self.client = InfluxDBClient(
                url=self.config.url,
                token=self.config.token,
                org=self.config.org,
                timeout=self.config.timeout
            )
            
            # Test connection
            ready = self.client.ready()
            if not ready.ready:
                logger.error("InfluxDB not ready")
                return False
            
            # Setup write API with batching
            write_options = WriteOptions(
                batch_size=self.config.batch_size,
                flush_interval=self.config.flush_interval,
                retry_interval=self.config.retry_interval,
                max_retries=self.config.max_retries
            )
            
            self.write_api = self.client.write_api(write_options=write_options)
            self.query_api = self.client.query_api()
            
            self.is_connected = True
            logger.info("Successfully connected to InfluxDB")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to InfluxDB: {e}")
            self.is_connected = False
            return False
    
    async def disconnect(self):
        """Disconnect from InfluxDB"""
        try:
            # Flush remaining writes
            await self._flush_queue()
            
            if self.write_api:
                self.write_api.close()
            
            if self.client:
                self.client.close()
            
            self.is_connected = False
            logger.info("Disconnected from InfluxDB")
            
        except Exception as e:
            logger.error(f"Error during InfluxDB disconnect: {e}")
    
    async def write_tank_data(self, data_point: TankDataPoint) -> bool:
        """Write tank data point to InfluxDB"""
        if not self.is_connected:
            logger.error("Not connected to InfluxDB")
            return False
        
        try:
            points = self._create_points_from_data(data_point)
            
            async with self._queue_lock:
                self._write_queue.extend(points)
                
                # Batch write if queue is large enough
                if len(self._write_queue) >= self.config.batch_size:
                    await self._flush_queue()
            
            return True
            
        except Exception as e:
            logger.error(f"Error writing tank data: {e}")
            return False
    
    async def write_batch_data(self, data_points: List[TankDataPoint]) -> bool:
        """Write multiple tank data points"""
        if not self.is_connected:
            logger.error("Not connected to InfluxDB")
            return False
        
        try:
            all_points = []
            for data_point in data_points:
                points = self._create_points_from_data(data_point)
                all_points.extend(points)
            
            # Write directly for large batches
            if all_points:
                self.write_api.write(
                    bucket=self.config.bucket,
                    org=self.config.org,
                    record=all_points
                )
            
            logger.debug(f"Wrote batch of {len(all_points)} points")
            return True
            
        except Exception as e:
            logger.error(f"Error writing batch data: {e}")
            return False
    
    def _create_points_from_data(self, data_point: TankDataPoint) -> List[Point]:
        """Create InfluxDB points from tank data"""
        points = []
        
        # Zone temperature points
        for zone, temperature in data_point.zone_temperatures.items():
            point = (Point(f"{self.config.measurement_prefix}_temperature")
                   .tag("tank_id", data_point.tank_id)
                   .tag("zone", str(zone))
                   .field("temperature", float(temperature))
                   .time(data_point.timestamp, WritePrecision.MS))
            points.append(point)
        
        # Heating power points
        for zone, power in data_point.heating_powers.items():
            point = (Point(f"{self.config.measurement_prefix}_heating")
                   .tag("tank_id", data_point.tank_id)
                   .tag("zone", str(zone))
                   .field("power", float(power))
                   .time(data_point.timestamp, WritePrecision.MS))
            points.append(point)
        
        # Setpoint points
        for zone, setpoint in data_point.setpoints.items():
            point = (Point(f"{self.config.measurement_prefix}_setpoint")
                   .tag("tank_id", data_point.tank_id)
                   .tag("zone", str(zone))
                   .field("setpoint", float(setpoint))
                   .time(data_point.timestamp, WritePrecision.MS))
            points.append(point)
        
        # System-level metrics
        if data_point.ambient_temperature is not None:
            point = (Point(f"{self.config.measurement_prefix}_environment")
                   .tag("tank_id", data_point.tank_id)
                   .field("ambient_temperature", float(data_point.ambient_temperature))
                   .time(data_point.timestamp, WritePrecision.MS))
            points.append(point)
        
        if data_point.energy_consumption is not None:
            point = (Point(f"{self.config.measurement_prefix}_energy")
                   .tag("tank_id", data_point.tank_id)
                   .field("consumption", float(data_point.energy_consumption))
                   .time(data_point.timestamp, WritePrecision.MS))
            points.append(point)
        
        if data_point.energy_cost is not None:
            point = (Point(f"{self.config.measurement_prefix}_cost")
                   .tag("tank_id", data_point.tank_id)
                   .field("cost", float(data_point.energy_cost))
                   .time(data_point.timestamp, WritePrecision.MS))
            points.append(point)
        
        if data_point.total_power is not None:
            point = (Point(f"{self.config.measurement_prefix}_power")
                   .tag("tank_id", data_point.tank_id)
                   .field("total_power", float(data_point.total_power))
                   .time(data_point.timestamp, WritePrecision.MS))
            points.append(point)
        
        if data_point.efficiency is not None:
            point = (Point(f"{self.config.measurement_prefix}_efficiency")
                   .tag("tank_id", data_point.tank_id)
                   .field("efficiency", float(data_point.efficiency))
                   .time(data_point.timestamp, WritePrecision.MS))
            points.append(point)
        
        if data_point.control_mode:
            point = (Point(f"{self.config.measurement_prefix}_control")
                   .tag("tank_id", data_point.tank_id)
                   .tag("mode", data_point.control_mode)
                   .field("active", 1)
                   .time(data_point.timestamp, WritePrecision.MS))
            points.append(point)
        
        # Alarms
        for alarm in data_point.alarms:
            point = (Point(f"{self.config.measurement_prefix}_alarm")
                   .tag("tank_id", data_point.tank_id)
                   .tag("alarm_type", alarm)
                   .field("active", 1)
                   .time(data_point.timestamp, WritePrecision.MS))
            points.append(point)
        
        # Additional tags
        for tag_name, tag_value in data_point.tags.items():
            if isinstance(tag_value, (int, float)):
                point = (Point(f"{self.config.measurement_prefix}_tag")
                       .tag("tank_id", data_point.tank_id)
                       .tag("tag_name", tag_name)
                       .field("value", float(tag_value))
                       .time(data_point.timestamp, WritePrecision.MS))
                points.append(point)
        
        return points
    
    async def _flush_queue(self):
        """Flush write queue to InfluxDB"""
        if not self._write_queue:
            return
        
        try:
            points_to_write = self._write_queue.copy()
            self._write_queue.clear()
            
            self.write_api.write(
                bucket=self.config.bucket,
                org=self.config.org,
                record=points_to_write
            )
            
            logger.debug(f"Flushed {len(points_to_write)} points to InfluxDB")
            
        except Exception as e:
            logger.error(f"Error flushing write queue: {e}")
            # Re-add points to queue for retry
            self._write_queue.extend(points_to_write)
    
    async def query_tank_data(self, tank_id: str, start_time: datetime, 
                            end_time: Optional[datetime] = None,
                            measurement_types: Optional[List[str]] = None) -> pd.DataFrame:
        """Query tank data from InfluxDB"""
        if not self.is_connected:
            raise RuntimeError("Not connected to InfluxDB")
        
        if end_time is None:
            end_time = datetime.now()
        
        # Default to all measurement types
        if measurement_types is None:
            measurement_types = [
                "tank_temperature", "tank_heating", "tank_setpoint",
                "tank_environment", "tank_energy", "tank_cost",
                "tank_power", "tank_efficiency"
            ]
        
        try:
            # Build query
            measurement_filter = " or ".join([f'r._measurement == "{m}"' for m in measurement_types])
            
            query = f'''
                from(bucket: "{self.config.bucket}")
                |> range(start: {start_time.isoformat()}Z, stop: {end_time.isoformat()}Z)
                |> filter(fn: (r) => {measurement_filter})
                |> filter(fn: (r) => r.tank_id == "{tank_id}")
                |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
            '''
            
            # Execute query
            result = self.query_api.query_data_frame(query)
            
            if result.empty:
                logger.info(f"No data found for tank {tank_id} in time range")
                return pd.DataFrame()
            
            # Clean up DataFrame
            result['_time'] = pd.to_datetime(result['_time'])
            result = result.sort_values('_time')
            
            logger.info(f"Retrieved {len(result)} records for tank {tank_id}")
            return result
            
        except Exception as e:
            logger.error(f"Error querying tank data: {e}")
            return pd.DataFrame()
    
    async def query_zone_temperatures(self, tank_id: str, start_time: datetime,
                                    end_time: Optional[datetime] = None) -> pd.DataFrame:
        """Query zone temperature data specifically"""
        if not self.is_connected:
            raise RuntimeError("Not connected to InfluxDB")
        
        if end_time is None:
            end_time = datetime.now()
        
        try:
            query = f'''
                from(bucket: "{self.config.bucket}")
                |> range(start: {start_time.isoformat()}Z, stop: {end_time.isoformat()}Z)
                |> filter(fn: (r) => r._measurement == "tank_temperature")
                |> filter(fn: (r) => r.tank_id == "{tank_id}")
                |> pivot(rowKey:["_time", "zone"], columnKey: ["_field"], valueColumn: "_value")
            '''
            
            result = self.query_api.query_data_frame(query)
            
            if result.empty:
                return pd.DataFrame()
            
            # Reshape data for easier analysis
            result['_time'] = pd.to_datetime(result['_time'])
            result = result.sort_values(['_time', 'zone'])
            
            return result
            
        except Exception as e:
            logger.error(f"Error querying zone temperatures: {e}")
            return pd.DataFrame()
    
    async def get_latest_tank_status(self, tank_id: str) -> Dict[str, Any]:
        """Get latest status for a tank"""
        if not self.is_connected:
            raise RuntimeError("Not connected to InfluxDB")
        
        try:
            # Query latest data points
            query = f'''
                from(bucket: "{self.config.bucket}")
                |> range(start: -1h)
                |> filter(fn: (r) => r.tank_id == "{tank_id}")
                |> last()
            '''
            
            result = self.query_api.query_data_frame(query)
            
            if result.empty:
                return {}
            
            # Parse results into status dictionary
            status = {
                'tank_id': tank_id,
                'timestamp': None,
                'zone_temperatures': {},
                'heating_powers': {},
                'setpoints': {},
                'energy_metrics': {},
                'alarms': []
            }
            
            for _, row in result.iterrows():
                measurement = row['_measurement']
                field = row['_field']
                value = row['_value']
                time = row['_time']
                
                if status['timestamp'] is None or time > status['timestamp']:
                    status['timestamp'] = time
                
                if measurement == 'tank_temperature' and field == 'temperature':
                    zone = row.get('zone', '0')
                    status['zone_temperatures'][zone] = value
                elif measurement == 'tank_heating' and field == 'power':
                    zone = row.get('zone', '0')
                    status['heating_powers'][zone] = value
                elif measurement == 'tank_setpoint' and field == 'setpoint':
                    zone = row.get('zone', '0')
                    status['setpoints'][zone] = value
                elif measurement == 'tank_energy':
                    status['energy_metrics'][field] = value
                elif measurement == 'tank_alarm' and field == 'active':
                    alarm_type = row.get('alarm_type', 'unknown')
                    status['alarms'].append(alarm_type)
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting latest tank status: {e}")
            return {}
    
    async def calculate_energy_statistics(self, tank_id: str, start_time: datetime,
                                        end_time: Optional[datetime] = None) -> Dict[str, float]:
        """Calculate energy consumption statistics"""
        if not self.is_connected:
            raise RuntimeError("Not connected to InfluxDB")
        
        if end_time is None:
            end_time = datetime.now()
        
        try:
            # Query energy data
            query = f'''
                from(bucket: "{self.config.bucket}")
                |> range(start: {start_time.isoformat()}Z, stop: {end_time.isoformat()}Z)
                |> filter(fn: (r) => r._measurement == "tank_energy" or r._measurement == "tank_cost")
                |> filter(fn: (r) => r.tank_id == "{tank_id}")
                |> group(columns: ["_field"])
                |> mean()
            '''
            
            result = self.query_api.query_data_frame(query)
            
            if result.empty:
                return {}
            
            stats = {}
            for _, row in result.iterrows():
                field = row['_field']
                value = row['_value']
                stats[f'avg_{field}'] = value
            
            # Calculate total consumption (approximate)
            consumption_query = f'''
                from(bucket: "{self.config.bucket}")
                |> range(start: {start_time.isoformat()}Z, stop: {end_time.isoformat()}Z)
                |> filter(fn: (r) => r._measurement == "tank_energy" and r._field == "consumption")
                |> filter(fn: (r) => r.tank_id == "{tank_id}")
                |> integral(unit: 1h)
            '''
            
            consumption_result = self.query_api.query_data_frame(consumption_query)
            if not consumption_result.empty:
                stats['total_consumption'] = consumption_result['_value'].iloc[0]
            
            return stats
            
        except Exception as e:
            logger.error(f"Error calculating energy statistics: {e}")
            return {}
    
    async def delete_old_data(self, retention_days: int = 90) -> bool:
        """Delete data older than retention period"""
        if not self.is_connected:
            raise RuntimeError("Not connected to InfluxDB")
        
        try:
            cutoff_time = datetime.now() - timedelta(days=retention_days)
            
            # Use InfluxDB delete API
            delete_api = self.client.delete_api()
            
            delete_api.delete(
                start=datetime(2020, 1, 1),  # Very old start date
                stop=cutoff_time,
                predicate='',  # Delete all measurements
                bucket=self.config.bucket,
                org=self.config.org
            )
            
            logger.info(f"Deleted data older than {retention_days} days")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting old data: {e}")
            return False


class InfluxDBDataIngestionPipeline:
    """Pipeline for ingesting SCADA data into InfluxDB"""
    
    def __init__(self, influx_client: InfluxDBTimeSeriesClient):
        self.influx_client = influx_client
        self.ingestion_queue: List[TankDataPoint] = []
        self.is_running = False
        self._ingestion_task: Optional[asyncio.Task] = None
        self.batch_size = 100
        self.flush_interval = 5.0  # seconds
    
    async def start_ingestion(self):
        """Start the data ingestion pipeline"""
        if self.is_running:
            return
        
        self.is_running = True
        self._ingestion_task = asyncio.create_task(self._ingestion_loop())
        logger.info("Data ingestion pipeline started")
    
    async def stop_ingestion(self):
        """Stop the data ingestion pipeline"""
        self.is_running = False
        
        if self._ingestion_task:
            self._ingestion_task.cancel()
            await self._flush_queue()
        
        logger.info("Data ingestion pipeline stopped")
    
    async def ingest_tank_data(self, data_point: TankDataPoint):
        """Add tank data point to ingestion queue"""
        self.ingestion_queue.append(data_point)
        
        # Auto-flush if queue is large
        if len(self.ingestion_queue) >= self.batch_size:
            await self._flush_queue()
    
    async def _ingestion_loop(self):
        """Main ingestion loop"""
        while self.is_running:
            try:
                if self.ingestion_queue:
                    await self._flush_queue()
                
                await asyncio.sleep(self.flush_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in ingestion loop: {e}")
                await asyncio.sleep(1.0)
    
    async def _flush_queue(self):
        """Flush ingestion queue to InfluxDB"""
        if not self.ingestion_queue:
            return
        
        try:
            batch = self.ingestion_queue.copy()
            self.ingestion_queue.clear()
            
            success = await self.influx_client.write_batch_data(batch)
            
            if success:
                logger.debug(f"Ingested batch of {len(batch)} data points")
            else:
                # Re-add to queue on failure
                self.ingestion_queue.extend(batch)
                logger.error("Failed to ingest batch, re-queuing")
                
        except Exception as e:
            logger.error(f"Error flushing ingestion queue: {e}")


# Factory function
def create_influxdb_client(config: Optional[InfluxDBConfig] = None) -> InfluxDBTimeSeriesClient:
    """Create InfluxDB client with default configuration"""
    if config is None:
        config = InfluxDBConfig()
    
    return InfluxDBTimeSeriesClient(config)


# Example usage
if __name__ == "__main__":
    import asyncio
    
    async def test_influxdb():
        # Create test configuration
        config = InfluxDBConfig(
            url="http://localhost:8086",
            token="your-token-here",  # Replace with actual token
            org="asphalt_plant",
            bucket="tank_data"
        )
        
        # Create client
        client = create_influxdb_client(config)
        
        # Test connection
        connected = await client.connect()
        if not connected:
            print("Failed to connect to InfluxDB")
            return
        
        # Create test data point
        test_data = TankDataPoint(
            tank_id="tank_001",
            timestamp=datetime.now(),
            zone_temperatures={0: 150.5, 1: 151.2, 2: 149.8, 3: 150.1},
            heating_powers={0: 25000, 1: 30000, 2: 28000, 3: 26000},
            setpoints={0: 150.0, 1: 150.0, 2: 150.0, 3: 150.0},
            ambient_temperature=22.5,
            energy_consumption=109000,
            energy_cost=13.08,
            total_power=109000,
            efficiency=0.92,
            control_mode="automatic"
        )
        
        # Write test data
        success = await client.write_tank_data(test_data)
        if success:
            print("Successfully wrote test data")
        
        # Query recent data
        start_time = datetime.now() - timedelta(hours=1)
        data = await client.query_tank_data("tank_001", start_time)
        print(f"Retrieved {len(data)} records")
        
        # Get latest status
        status = await client.get_latest_tank_status("tank_001")
        print(f"Latest status: {status}")
        
        # Disconnect
        await client.disconnect()
        print("Test completed")
    
    # Run test if InfluxDB is available
    if INFLUXDB_AVAILABLE:
        asyncio.run(test_influxdb())
    else:
        print("InfluxDB client not available")