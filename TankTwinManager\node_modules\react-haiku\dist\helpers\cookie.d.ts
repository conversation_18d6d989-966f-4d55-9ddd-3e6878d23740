export declare const parseToDataType: <T>(value: string | undefined, isItRetry?: boolean) => T | undefined;
export declare const parseToCookieType: <T>(value: T) => string;
export declare const getCookie: <T>(name: string) => T | undefined;
export declare const getCookies: <T extends Record<string, unknown> = Record<string, unknown>>(cookies?: string[]) => T;
export declare const setCookie: <T>(name: string, value: T, expireDays: number) => void;
export declare const deleteCookie: (name: string) => void;
