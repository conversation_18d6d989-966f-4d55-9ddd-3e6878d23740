#!/usr/bin/env python3
"""
Tank System Main Application
Comprehensive industrial tank control system with:
- Real-time SCADA integration
- Advanced physics simulation
- Intelligent control algorithms
- Digital twin capabilities
- REST API interface
- Real-time monitoring and alerting
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path
from typing import Dict, List, Optional
import yaml
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

# Import tank system components
from digital_twin.core.tank_models import (
    TankConfiguration, TankGeometry, TankType, TankSensor, TankActuator,
    TankControlParameters, SensorType, ActuatorType, TankAlarm, AlarmSeverity
)
from digital_twin.core.tank_control_algorithms import TankControllerManager
from digital_twin.scada.tank_control_integration import (
    TankControlSystem, TankControlConfiguration
)
from digital_twin.scada.opc_client import OPCConfiguration, OPCTag
from digital_twin.simulation.tank_physics_engine import (
    TankPhysicsEngine, SimulationConfiguration, create_tank_physics_engine
)
from src.api.tank_endpoints import get_tank_router, initialize_tank_system
from src.api.mlops_endpoints import get_mlops_router

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tank_system.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class TankSystemApplication:
    """Main tank system application"""
    
    def __init__(self, config_path: str = "config/tank_system_config.yaml"):
        self.config_path = config_path
        self.config: Dict = {}
        self.app: Optional[FastAPI] = None
        
        # System components
        self.tank_control_system: Optional[TankControlSystem] = None
        self.tank_physics_engine: Optional[TankPhysicsEngine] = None
        self.tank_control_manager: Optional[TankControllerManager] = None
        
        # Runtime state
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        
    async def load_configuration(self):
        """Load system configuration from YAML file"""
        try:
            config_file = Path(self.config_path)
            if not config_file.exists():
                raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
            with open(config_file, 'r') as f:
                self.config = yaml.safe_load(f)
            
            logger.info(f"Configuration loaded from {self.config_path}")
            
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            raise
    
    async def initialize_components(self):
        """Initialize all system components"""
        try:
            logger.info("Initializing tank system components...")
            
            # Initialize tank control manager
            self.tank_control_manager = TankControllerManager()
            
            # Load tank configurations
            await self._load_tank_configurations()
            
            # Initialize SCADA integration
            await self._initialize_scada_integration()
            
            # Initialize physics simulation
            await self._initialize_physics_simulation()
            
            # Initialize web API
            await self._initialize_web_api()
            
            logger.info("All components initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing components: {e}")
            raise
    
    async def _load_tank_configurations(self):
        """Load tank configurations from config file"""
        try:
            tanks_config = self.config.get('tanks', {})
            
            for tank_id, tank_data in tanks_config.items():
                if not tank_data.get('is_active', True):
                    continue
                
                # Create tank geometry
                geom_data = tank_data['geometry']
                geometry = TankGeometry(
                    tank_type=TankType.HEATING,
                    capacity=geom_data['capacity'],
                    diameter=geom_data['diameter'],
                    height=geom_data['height'],
                    volume=3.14159 * (geom_data['diameter']/2)**2 * geom_data['height'],
                    surface_area=3.14159 * geom_data['diameter'] * geom_data['height'],
                    insulation_thickness=geom_data.get('insulation_thickness', 0.1),
                    material=geom_data.get('material', 'steel'),
                    design_pressure=geom_data.get('design_pressure', 5.0),
                    design_temperature=geom_data.get('design_temperature', 200.0)
                )
                
                # Create sensors
                sensors = []
                for sensor_data in tank_data.get('sensors', []):
                    sensor = TankSensor(
                        sensor_id=sensor_data['sensor_id'],
                        sensor_type=SensorType(sensor_data['sensor_type']),
                        description=sensor_data['description'],
                        unit=sensor_data['unit'],
                        min_value=sensor_data['min_value'],
                        max_value=sensor_data['max_value'],
                        accuracy=sensor_data['accuracy'],
                        location=sensor_data['location'],
                        node_id=sensor_data['node_id'],
                        alarm_high=sensor_data.get('alarm_high'),
                        alarm_low=sensor_data.get('alarm_low')
                    )
                    sensors.append(sensor)
                
                # Create actuators
                actuators = []
                for actuator_data in tank_data.get('actuators', []):
                    actuator = TankActuator(
                        actuator_id=actuator_data['actuator_id'],
                        actuator_type=ActuatorType(actuator_data['actuator_type']),
                        description=actuator_data['description'],
                        unit=actuator_data['unit'],
                        min_value=actuator_data['min_value'],
                        max_value=actuator_data['max_value'],
                        location=actuator_data['location'],
                        node_id=actuator_data['node_id'],
                        is_enabled=actuator_data.get('is_enabled', True)
                    )
                    actuators.append(actuator)
                
                # Create control parameters
                ctrl_data = tank_data['control_parameters']
                control_params = TankControlParameters(
                    temperature_setpoint=ctrl_data['temperature_setpoint'],
                    temperature_deadband=ctrl_data['temperature_deadband'],
                    pressure_setpoint=ctrl_data['pressure_setpoint'],
                    pressure_deadband=ctrl_data['pressure_deadband'],
                    level_setpoint=ctrl_data['level_setpoint'],
                    level_deadband=ctrl_data['level_deadband'],
                    heating_power_limit=ctrl_data['heating_power_limit']
                )
                
                # Create alarms
                alarms = []
                for alarm_data in tank_data.get('alarms', []):
                    alarm = TankAlarm(
                        alarm_id=alarm_data['alarm_id'],
                        description=alarm_data['description'],
                        severity=AlarmSeverity(alarm_data['severity']),
                        condition=alarm_data['condition'],
                        enabled=alarm_data.get('enabled', True)
                    )
                    alarms.append(alarm)
                
                # Create tank configuration
                tank_config = TankConfiguration(
                    tank_id=tank_id,
                    name=tank_data['name'],
                    description=tank_data['description'],
                    geometry=geometry,
                    sensors=sensors,
                    actuators=actuators,
                    control_parameters=control_params,
                    alarms=alarms,
                    thermal_properties=tank_data.get('thermal_properties', {}),
                    fluid_properties=tank_data.get('fluid_properties', {})
                )
                
                # Add tank to control manager
                await self.tank_control_manager.add_tank(tank_config)
                
                logger.info(f"Loaded tank configuration: {tank_id}")
            
        except Exception as e:
            logger.error(f"Error loading tank configurations: {e}")
            raise
    
    async def _initialize_scada_integration(self):
        """Initialize SCADA integration"""
        try:
            scada_config = self.config.get('scada', {})
            
            if scada_config.get('opc_ua', {}).get('enabled', False):
                # Create OPC UA configuration
                opc_config_data = scada_config['opc_ua']
                
                # Create OPC tags from all tank sensors and actuators
                opc_tags = []
                for tank_config in self.tank_control_manager.tank_configs.values():
                    # Add sensor tags
                    for sensor in tank_config.sensors:
                        tag = OPCTag(
                            name=sensor.sensor_id,
                            node_id=sensor.node_id,
                            data_type="float",
                            description=sensor.description
                        )
                        opc_tags.append(tag)
                    
                    # Add actuator tags
                    for actuator in tank_config.actuators:
                        tag = OPCTag(
                            name=actuator.actuator_id,
                            node_id=actuator.node_id,
                            data_type="float",
                            description=actuator.description
                        )
                        opc_tags.append(tag)
                
                opc_config = OPCConfiguration(
                    endpoint_url=opc_config_data['endpoint_url'],
                    username=opc_config_data.get('username'),
                    password=opc_config_data.get('password'),
                    session_timeout=opc_config_data.get('session_timeout', 60000),
                    connection_timeout=opc_config_data.get('connection_timeout', 10000),
                    keep_alive_interval=opc_config_data.get('keep_alive_interval', 5000),
                    tags=opc_tags
                )
                
                # Create control mapping for the first tank
                first_tank = list(self.tank_control_manager.tank_configs.values())[0]
                from digital_twin.scada.tank_control_integration import TankControlMapping
                
                control_mapping = TankControlMapping(
                    tank_id=first_tank.tank_id,
                    zone_temperature_tags=[sensor.node_id for sensor in first_tank.sensors if sensor.sensor_type == SensorType.TEMPERATURE],
                    heating_power_tags=[actuator.node_id for actuator in first_tank.actuators if actuator.actuator_type == ActuatorType.HEATER],
                    target_temperature_tags=[f"{first_tank.tank_id}.setpoint"]
                )
                
                # Create tank controller configuration
                controller_config = TankControlConfiguration(
                    tank_config=first_tank,
                    control_mapping=control_mapping,
                    opc_config=opc_config,
                    update_frequency=self.config.get('control', {}).get('scan_rate', 1.0)
                )
                
                # Initialize tank control system
                self.tank_control_system = TankControlSystem(controller_config)
                
                # Initialize and start
                if await self.tank_control_system.initialize():
                    await self.tank_control_system.start()
                    logger.info("SCADA integration initialized successfully")
                else:
                    logger.error("Failed to initialize SCADA integration")
            
        except Exception as e:
            logger.error(f"Error initializing SCADA integration: {e}")
            # Continue without SCADA for development/testing
    
    async def _initialize_physics_simulation(self):
        """Initialize physics simulation"""
        try:
            sim_config = self.config.get('simulation', {})
            
            if sim_config.get('enabled', True):
                # Create physics engine
                tank_configs = list(self.tank_control_manager.tank_configs.values())
                self.tank_physics_engine = create_tank_physics_engine(
                    tank_configs=tank_configs,
                    real_time_factor=sim_config.get('real_time_factor', 1.0),
                    enable_advanced_models=sim_config.get('models', {}).get('fluid_dynamics', {}).get('enabled', True)
                )
                
                # Initialize physics engine
                if await self.tank_physics_engine.initialize(tank_configs):
                    await self.tank_physics_engine.start()
                    logger.info("Physics simulation initialized successfully")
                else:
                    logger.error("Failed to initialize physics simulation")
            
        except Exception as e:
            logger.error(f"Error initializing physics simulation: {e}")
            # Continue without physics simulation
    
    async def _initialize_web_api(self):
        """Initialize web API"""
        try:
            api_config = self.config.get('api', {})
            
            @asynccontextmanager
            async def lifespan(app: FastAPI):
                # Startup
                await initialize_tank_system()
                
                # Make components available globally
                import src.api.tank_endpoints as tank_endpoints
                tank_endpoints.tank_control_system = self.tank_control_system
                tank_endpoints.tank_physics_engine = self.tank_physics_engine
                tank_endpoints.tank_control_manager = self.tank_control_manager
                
                yield
                
                # Shutdown
                if self.tank_control_system:
                    await self.tank_control_system.stop()
                if self.tank_physics_engine:
                    await self.tank_physics_engine.stop()
            
            # Create FastAPI app
            self.app = FastAPI(
                title=api_config.get('title', 'Tank Control System API'),
                description=api_config.get('description', 'REST API for tank control'),
                version=api_config.get('version', '1.0.0'),
                lifespan=lifespan
            )
            
            # Add CORS middleware
            cors_config = api_config.get('cors', {})
            self.app.add_middleware(
                CORSMiddleware,
                allow_origins=cors_config.get('allow_origins', ["*"]),
                allow_credentials=True,
                allow_methods=cors_config.get('allow_methods', ["*"]),
                allow_headers=cors_config.get('allow_headers', ["*"])
            )
            
            # Include tank API router
            self.app.include_router(get_tank_router())
            
            # Include MLOps API router
            self.app.include_router(get_mlops_router())
            
            # Add health check endpoint
            @self.app.get("/health")
            async def health_check():
                return {
                    "status": "healthy",
                    "timestamp": "2024-01-01T00:00:00Z",
                    "components": {
                        "scada": self.tank_control_system is not None,
                        "physics": self.tank_physics_engine is not None,
                        "control": self.tank_control_manager is not None
                    }
                }
            
            logger.info("Web API initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing web API: {e}")
            raise
    
    async def start(self):
        """Start the tank system application"""
        try:
            logger.info("Starting Tank Control System...")
            
            # Load configuration
            await self.load_configuration()
            
            # Initialize components
            await self.initialize_components()
            
            # Set running flag
            self.is_running = True
            
            # Start web server
            api_config = self.config.get('api', {})
            config = uvicorn.Config(
                app=self.app,
                host=api_config.get('host', '0.0.0.0'),
                port=api_config.get('port', 8001),  # Use port 8001 to avoid conflicts
                log_level="info"
            )
            
            server = uvicorn.Server(config)
            
            logger.info(f"Tank Control System started successfully")
            logger.info(f"API server running on http://{config.host}:{config.port}")
            logger.info(f"API documentation: http://{config.host}:{config.port}/docs")
            
            # Run server
            await server.serve()
            
        except Exception as e:
            logger.error(f"Error starting tank system: {e}")
            raise
    
    async def stop(self):
        """Stop the tank system application"""
        try:
            logger.info("Stopping Tank Control System...")
            
            # Set shutdown event
            self.shutdown_event.set()
            
            # Stop components
            if self.tank_control_system:
                await self.tank_control_system.stop()
            
            if self.tank_physics_engine:
                await self.tank_physics_engine.stop()
            
            self.is_running = False
            
            logger.info("Tank Control System stopped successfully")
            
        except Exception as e:
            logger.error(f"Error stopping tank system: {e}")
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown...")
            asyncio.create_task(self.stop())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """Main entry point"""
    try:
        # Create and start application
        app = TankSystemApplication()
        app.setup_signal_handlers()
        
        await app.start()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Run the application
    asyncio.run(main())
