# Minimal requirements for screen data extraction and basic functionality

# Core Dependencies
numpy>=1.21.0
pandas>=1.3.0
matplotlib>=3.5.0
scikit-learn>=1.0.0

# Screen capture and image processing
pillow>=9.0.0
opencv-python>=4.6.0
pyautogui>=0.9.54
pytesseract>=0.3.10
easyocr>=1.6.0
mss>=6.1.0

# Web Framework
fastapi>=0.85.0
uvicorn[standard]>=0.18.0
pydantic>=1.9.0

# Machine Learning (PyTorch)
torch>=1.12.0
torchvision>=0.13.0

# Async and utilities
aiohttp>=3.8.0
aiofiles>=0.8.0
asyncio-mqtt>=0.11.0

# Data processing
redis>=4.3.0
influxdb-client>=1.30.0

# Configuration
pyyaml>=6.0
python-dotenv>=0.19.0
click>=8.0.0

# Database
sqlalchemy>=1.4.0

# Visualization (basic)
plotly>=5.10.0
seaborn>=0.11.0

# Utilities
joblib>=1.1.0
structlog>=22.1.0