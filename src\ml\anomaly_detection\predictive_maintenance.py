"""
Predictive Maintenance and Anomaly Detection System
Advanced anomaly detection for asphalt tank heating systems with predictive maintenance capabilities
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from abc import ABC, abstractmethod
import warnings
warnings.filterwarnings('ignore')

try:
    from sklearn.ensemble import IsolationForest
    from sklearn.svm import OneClassSVM
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.decomposition import PCA
    from sklearn.cluster import DBSCAN
    from sklearn.metrics import classification_report
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("Warning: scikit-learn not available. ML-based anomaly detection will be limited.")

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import Dataset, DataLoader
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("Warning: PyTorch not available. Deep learning anomaly detection will be limited.")

logger = logging.getLogger(__name__)


class AnomalyType(Enum):
    TEMPERATURE_ANOMALY = "temperature_anomaly"
    POWER_ANOMALY = "power_anomaly"
    EFFICIENCY_ANOMALY = "efficiency_anomaly"
    VIBRATION_ANOMALY = "vibration_anomaly"
    HEATING_ELEMENT_FAULT = "heating_element_fault"
    SENSOR_FAULT = "sensor_fault"
    CONTROL_SYSTEM_FAULT = "control_system_fault"
    PREDICTIVE_FAILURE = "predictive_failure"
    DRIFT_ANOMALY = "drift_anomaly"
    SEASONAL_ANOMALY = "seasonal_anomaly"


class SeverityLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class AnomalyDetectionResult:
    """Result of anomaly detection"""
    timestamp: datetime
    anomaly_type: AnomalyType
    severity: SeverityLevel
    anomaly_score: float
    confidence: float
    description: str
    affected_components: List[str] = field(default_factory=list)
    recommended_actions: List[str] = field(default_factory=list)
    predicted_failure_time: Optional[datetime] = None
    maintenance_priority: int = 1  # 1 = highest, 5 = lowest
    raw_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MaintenanceRecommendation:
    """Maintenance recommendation based on anomaly detection"""
    component: str
    action: str
    urgency: SeverityLevel
    estimated_cost: float
    estimated_time: timedelta
    risk_if_delayed: str
    required_parts: List[str] = field(default_factory=list)
    required_skills: List[str] = field(default_factory=list)
    scheduling_constraints: Dict[str, Any] = field(default_factory=dict)


class AnomalyDetector(ABC):
    """Abstract base class for anomaly detectors"""
    
    @abstractmethod
    async def train(self, training_data: pd.DataFrame) -> bool:
        """Train the anomaly detector"""
        pass
    
    @abstractmethod
    async def detect(self, data: pd.DataFrame) -> List[AnomalyDetectionResult]:
        """Detect anomalies in data"""
        pass
    
    @abstractmethod
    async def update_model(self, new_data: pd.DataFrame) -> bool:
        """Update model with new data"""
        pass


class StatisticalAnomalyDetector(AnomalyDetector):
    """Statistical anomaly detection using control charts and statistical methods"""
    
    def __init__(self, confidence_level: float = 0.95):
        self.confidence_level = confidence_level
        self.control_limits = {}
        self.moving_averages = {}
        self.moving_stds = {}
        self.window_size = 50
        self.seasonal_patterns = {}
        self.trend_models = {}
        self.is_trained = False
        
    async def train(self, training_data: pd.DataFrame) -> bool:
        """Train statistical models on historical data"""
        try:
            logger.info("Training statistical anomaly detector...")
            
            # Calculate control limits for each numeric column
            numeric_columns = training_data.select_dtypes(include=[np.number]).columns
            
            for column in numeric_columns:
                data = training_data[column].dropna()
                
                if len(data) < 30:  # Need sufficient data
                    logger.warning(f"Insufficient data for column {column}")
                    continue
                
                # Calculate statistical parameters
                mean = data.mean()
                std = data.std()
                
                # Control limits (3-sigma)
                alpha = 1 - self.confidence_level
                z_score = 3.0  # 3-sigma limits
                
                self.control_limits[column] = {
                    'mean': mean,
                    'std': std,
                    'upper_limit': mean + z_score * std,
                    'lower_limit': mean - z_score * std,
                    'upper_warning': mean + 2 * std,
                    'lower_warning': mean - 2 * std
                }
                
                # Calculate moving statistics
                self.moving_averages[column] = data.rolling(self.window_size).mean()
                self.moving_stds[column] = data.rolling(self.window_size).std()
                
                # Detect seasonal patterns
                if len(data) >= 168:  # At least a week of hourly data
                    self.seasonal_patterns[column] = self._detect_seasonal_pattern(data)
                
                # Fit trend model
                self.trend_models[column] = self._fit_trend_model(data)
            
            self.is_trained = True
            logger.info(f"Statistical anomaly detector trained on {len(numeric_columns)} features")
            return True
            
        except Exception as e:
            logger.error(f"Error training statistical anomaly detector: {e}")
            return False
    
    async def detect(self, data: pd.DataFrame) -> List[AnomalyDetectionResult]:
        """Detect statistical anomalies"""
        if not self.is_trained:
            logger.warning("Detector not trained")
            return []
        
        anomalies = []
        
        try:
            for column in self.control_limits:
                if column not in data.columns:
                    continue
                
                series = data[column].dropna()
                if len(series) == 0:
                    continue
                
                limits = self.control_limits[column]
                
                for idx, value in series.items():
                    timestamp = data.loc[idx, 'timestamp'] if 'timestamp' in data.columns else datetime.now()
                    
                    # Check control limits
                    if value > limits['upper_limit'] or value < limits['lower_limit']:
                        severity = SeverityLevel.HIGH
                        score = abs(value - limits['mean']) / limits['std']
                        
                        anomaly = AnomalyDetectionResult(
                            timestamp=timestamp,
                            anomaly_type=self._classify_statistical_anomaly(column, value, limits),
                            severity=severity,
                            anomaly_score=min(score, 10.0),
                            confidence=0.9,
                            description=f"{column} value {value:.2f} outside control limits [{limits['lower_limit']:.2f}, {limits['upper_limit']:.2f}]",
                            affected_components=[column],
                            recommended_actions=self._get_statistical_recommendations(column, value, limits),
                            raw_data={'value': value, 'limits': limits}
                        )
                        anomalies.append(anomaly)
                        
                    elif value > limits['upper_warning'] or value < limits['lower_warning']:
                        severity = SeverityLevel.MEDIUM
                        score = abs(value - limits['mean']) / limits['std']
                        
                        anomaly = AnomalyDetectionResult(
                            timestamp=timestamp,
                            anomaly_type=AnomalyType.DRIFT_ANOMALY,
                            severity=severity,
                            anomaly_score=score,
                            confidence=0.7,
                            description=f"{column} value {value:.2f} in warning zone",
                            affected_components=[column],
                            recommended_actions=["Monitor closely", "Check sensor calibration"],
                            raw_data={'value': value, 'limits': limits}
                        )
                        anomalies.append(anomaly)
            
            # Check for trend anomalies
            trend_anomalies = await self._detect_trend_anomalies(data)
            anomalies.extend(trend_anomalies)
            
            # Check for seasonal anomalies
            seasonal_anomalies = await self._detect_seasonal_anomalies(data)
            anomalies.extend(seasonal_anomalies)
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Error in statistical anomaly detection: {e}")
            return []
    
    async def update_model(self, new_data: pd.DataFrame) -> bool:
        """Update statistical models with new data"""
        try:
            # For simplicity, retrain with combined data
            # In production, would use online learning algorithms
            return await self.train(new_data)
        except Exception as e:
            logger.error(f"Error updating statistical model: {e}")
            return False
    
    def _detect_seasonal_pattern(self, data: pd.Series) -> Dict[str, Any]:
        """Detect seasonal patterns in data"""
        try:
            # Simple seasonal decomposition
            if len(data) >= 168:  # Weekly pattern
                weekly_pattern = data.groupby(data.index % 168).mean()
                return {
                    'pattern_type': 'weekly',
                    'pattern_values': weekly_pattern.to_dict(),
                    'pattern_strength': weekly_pattern.std() / data.std()
                }
        except:
            pass
        
        return {'pattern_type': 'none'}
    
    def _fit_trend_model(self, data: pd.Series) -> Dict[str, Any]:
        """Fit trend model to data"""
        try:
            if len(data) >= 50:
                x = np.arange(len(data))
                coeffs = np.polyfit(x, data.values, 1)
                return {
                    'slope': coeffs[0],
                    'intercept': coeffs[1],
                    'r_squared': np.corrcoef(x, data.values)[0, 1] ** 2
                }
        except:
            pass
        
        return {'slope': 0, 'intercept': data.mean(), 'r_squared': 0}
    
    def _classify_statistical_anomaly(self, column: str, value: float, 
                                    limits: Dict[str, float]) -> AnomalyType:
        """Classify type of statistical anomaly"""
        if 'temperature' in column.lower():
            return AnomalyType.TEMPERATURE_ANOMALY
        elif 'power' in column.lower() or 'energy' in column.lower():
            return AnomalyType.POWER_ANOMALY
        elif 'efficiency' in column.lower():
            return AnomalyType.EFFICIENCY_ANOMALY
        else:
            return AnomalyType.DRIFT_ANOMALY
    
    def _get_statistical_recommendations(self, column: str, value: float, 
                                       limits: Dict[str, float]) -> List[str]:
        """Get recommendations for statistical anomalies"""
        recommendations = []
        
        if 'temperature' in column.lower():
            if value > limits['upper_limit']:
                recommendations.extend([
                    "Check cooling system",
                    "Verify heating element operation",
                    "Inspect insulation"
                ])
            else:
                recommendations.extend([
                    "Check heating elements",
                    "Verify power supply",
                    "Inspect temperature sensors"
                ])
        elif 'power' in column.lower():
            if value > limits['upper_limit']:
                recommendations.extend([
                    "Check for equipment overload",
                    "Inspect electrical connections",
                    "Verify control settings"
                ])
            else:
                recommendations.extend([
                    "Check heating element continuity",
                    "Verify power supply voltage",
                    "Inspect control circuits"
                ])
        else:
            recommendations.extend([
                "Investigate root cause",
                "Check sensor calibration",
                "Review historical data"
            ])
        
        return recommendations
    
    async def _detect_trend_anomalies(self, data: pd.DataFrame) -> List[AnomalyDetectionResult]:
        """Detect trend anomalies"""
        anomalies = []
        
        try:
            for column in self.trend_models:
                if column not in data.columns:
                    continue
                
                trend_model = self.trend_models[column]
                series = data[column].dropna()
                
                if len(series) < 10:
                    continue
                
                # Calculate recent trend
                recent_data = series.tail(10)
                x = np.arange(len(recent_data))
                recent_coeffs = np.polyfit(x, recent_data.values, 1)
                recent_slope = recent_coeffs[0]
                
                # Compare with historical trend
                expected_slope = trend_model['slope']
                slope_threshold = abs(expected_slope) * 2  # 200% change threshold
                
                if abs(recent_slope - expected_slope) > slope_threshold:
                    timestamp = data.index[-1] if hasattr(data.index[-1], 'to_pydatetime') else datetime.now()
                    
                    anomaly = AnomalyDetectionResult(
                        timestamp=timestamp,
                        anomaly_type=AnomalyType.DRIFT_ANOMALY,
                        severity=SeverityLevel.MEDIUM,
                        anomaly_score=abs(recent_slope - expected_slope) / max(abs(expected_slope), 0.1),
                        confidence=0.8,
                        description=f"Trend change detected in {column}: {recent_slope:.4f} vs expected {expected_slope:.4f}",
                        affected_components=[column],
                        recommended_actions=["Investigate trend change", "Check for equipment degradation"],
                        raw_data={'recent_slope': recent_slope, 'expected_slope': expected_slope}
                    )
                    anomalies.append(anomaly)
                    
        except Exception as e:
            logger.error(f"Error detecting trend anomalies: {e}")
        
        return anomalies
    
    async def _detect_seasonal_anomalies(self, data: pd.DataFrame) -> List[AnomalyDetectionResult]:
        """Detect seasonal anomalies"""
        anomalies = []
        
        try:
            for column in self.seasonal_patterns:
                if column not in data.columns or self.seasonal_patterns[column]['pattern_type'] == 'none':
                    continue
                
                pattern = self.seasonal_patterns[column]
                series = data[column].dropna()
                
                for idx, value in series.items():
                    # Get expected seasonal value
                    if pattern['pattern_type'] == 'weekly' and len(series) > 0:
                        time_index = idx % 168  # Weekly pattern
                        expected_value = pattern['pattern_values'].get(time_index, series.mean())
                        seasonal_std = pattern['pattern_strength'] * series.std()
                        
                        if abs(value - expected_value) > 3 * seasonal_std:
                            timestamp = data.loc[idx, 'timestamp'] if 'timestamp' in data.columns else datetime.now()
                            
                            anomaly = AnomalyDetectionResult(
                                timestamp=timestamp,
                                anomaly_type=AnomalyType.SEASONAL_ANOMALY,
                                severity=SeverityLevel.MEDIUM,
                                anomaly_score=abs(value - expected_value) / max(seasonal_std, 0.1),
                                confidence=0.7,
                                description=f"Seasonal anomaly in {column}: {value:.2f} vs expected {expected_value:.2f}",
                                affected_components=[column],
                                recommended_actions=["Check for operational changes", "Verify seasonal adjustments"],
                                raw_data={'value': value, 'expected': expected_value, 'seasonal_std': seasonal_std}
                            )
                            anomalies.append(anomaly)
                            
        except Exception as e:
            logger.error(f"Error detecting seasonal anomalies: {e}")
        
        return anomalies


class MLAnomalyDetector(AnomalyDetector):
    """Machine learning-based anomaly detection"""
    
    def __init__(self, method: str = 'isolation_forest'):
        """
        Args:
            method: ML method ('isolation_forest', 'one_class_svm', 'autoencoder')
        """
        self.method = method
        self.model = None
        self.scaler = None
        self.feature_names = []
        self.is_trained = False
        self.contamination = 0.1  # Expected proportion of anomalies
        
        if not SKLEARN_AVAILABLE and method in ['isolation_forest', 'one_class_svm']:
            logger.warning(f"scikit-learn not available. Cannot use {method}")
            
        if not TORCH_AVAILABLE and method == 'autoencoder':
            logger.warning("PyTorch not available. Cannot use autoencoder")
    
    async def train(self, training_data: pd.DataFrame) -> bool:
        """Train ML anomaly detection model"""
        try:
            logger.info(f"Training ML anomaly detector using {self.method}...")
            
            # Prepare features
            features = self._prepare_features(training_data)
            if features is None or len(features) == 0:
                return False
            
            # Scale features
            self.scaler = StandardScaler()
            features_scaled = self.scaler.fit_transform(features)
            
            # Train model based on method
            if self.method == 'isolation_forest' and SKLEARN_AVAILABLE:
                self.model = IsolationForest(
                    contamination=self.contamination,
                    random_state=42,
                    n_estimators=100
                )
                self.model.fit(features_scaled)
                
            elif self.method == 'one_class_svm' and SKLEARN_AVAILABLE:
                self.model = OneClassSVM(
                    nu=self.contamination,
                    kernel='rbf',
                    gamma='scale'
                )
                self.model.fit(features_scaled)
                
            elif self.method == 'autoencoder' and TORCH_AVAILABLE:
                self.model = await self._train_autoencoder(features_scaled)
                
            else:
                logger.error(f"Method {self.method} not available or unsupported")
                return False
            
            self.is_trained = True
            logger.info(f"ML anomaly detector trained on {features_scaled.shape[0]} samples")
            return True
            
        except Exception as e:
            logger.error(f"Error training ML anomaly detector: {e}")
            return False
    
    async def detect(self, data: pd.DataFrame) -> List[AnomalyDetectionResult]:
        """Detect anomalies using ML model"""
        if not self.is_trained:
            logger.warning("ML detector not trained")
            return []
        
        anomalies = []
        
        try:
            # Prepare features
            features = self._prepare_features(data)
            if features is None or len(features) == 0:
                return []
            
            # Scale features
            features_scaled = self.scaler.transform(features)
            
            # Get predictions and scores
            if self.method in ['isolation_forest', 'one_class_svm']:
                predictions = self.model.predict(features_scaled)
                scores = self.model.score_samples(features_scaled) if hasattr(self.model, 'score_samples') else np.ones(len(features_scaled))
                
                # Convert scores to anomaly scores (higher = more anomalous)
                if self.method == 'isolation_forest':
                    anomaly_scores = -scores  # Isolation forest scores are negative
                else:
                    anomaly_scores = -scores  # One-class SVM scores are also negative for anomalies
                    
            elif self.method == 'autoencoder':
                predictions, anomaly_scores = await self._predict_autoencoder(features_scaled)
            
            # Create anomaly results
            for i, (pred, score) in enumerate(zip(predictions, anomaly_scores)):
                if pred == -1:  # Anomaly detected
                    timestamp = data.iloc[i].get('timestamp', datetime.now())
                    
                    # Determine severity based on score
                    if score > np.percentile(anomaly_scores, 95):
                        severity = SeverityLevel.HIGH
                    elif score > np.percentile(anomaly_scores, 80):
                        severity = SeverityLevel.MEDIUM
                    else:
                        severity = SeverityLevel.LOW
                    
                    # Identify most anomalous features
                    feature_contributions = self._analyze_feature_contributions(features_scaled[i])
                    affected_components = [self.feature_names[j] for j in feature_contributions[:3]]
                    
                    anomaly = AnomalyDetectionResult(
                        timestamp=timestamp,
                        anomaly_type=self._classify_ml_anomaly(features_scaled[i], feature_contributions),
                        severity=severity,
                        anomaly_score=score,
                        confidence=0.8,
                        description=f"ML anomaly detected using {self.method}",
                        affected_components=affected_components,
                        recommended_actions=self._get_ml_recommendations(affected_components),
                        raw_data={'feature_values': features[i].tolist(), 'method': self.method}
                    )
                    anomalies.append(anomaly)
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Error in ML anomaly detection: {e}")
            return []
    
    async def update_model(self, new_data: pd.DataFrame) -> bool:
        """Update ML model with new data"""
        try:
            # For simplicity, retrain with new data
            # In production, would use online learning
            return await self.train(new_data)
        except Exception as e:
            logger.error(f"Error updating ML model: {e}")
            return False
    
    def _prepare_features(self, data: pd.DataFrame) -> Optional[np.ndarray]:
        """Prepare features for ML model"""
        try:
            # Select numeric columns
            numeric_columns = data.select_dtypes(include=[np.number]).columns.tolist()
            
            # Remove timestamp columns
            numeric_columns = [col for col in numeric_columns if 'timestamp' not in col.lower() and 'time' not in col.lower()]
            
            if len(numeric_columns) == 0:
                logger.warning("No numeric features found")
                return None
            
            # Store feature names
            self.feature_names = numeric_columns
            
            # Extract features
            features = data[numeric_columns].fillna(0).values
            
            return features
            
        except Exception as e:
            logger.error(f"Error preparing features: {e}")
            return None
    
    async def _train_autoencoder(self, features: np.ndarray):
        """Train autoencoder model"""
        try:
            # Define autoencoder architecture
            input_dim = features.shape[1]
            hidden_dim = max(2, input_dim // 2)
            
            class Autoencoder(nn.Module):
                def __init__(self, input_dim, hidden_dim):
                    super(Autoencoder, self).__init__()
                    self.encoder = nn.Sequential(
                        nn.Linear(input_dim, hidden_dim),
                        nn.ReLU(),
                        nn.Linear(hidden_dim, hidden_dim // 2),
                        nn.ReLU()
                    )
                    self.decoder = nn.Sequential(
                        nn.Linear(hidden_dim // 2, hidden_dim),
                        nn.ReLU(),
                        nn.Linear(hidden_dim, input_dim)
                    )
                
                def forward(self, x):
                    encoded = self.encoder(x)
                    decoded = self.decoder(encoded)
                    return decoded
            
            # Create and train model
            model = Autoencoder(input_dim, hidden_dim)
            criterion = nn.MSELoss()
            optimizer = optim.Adam(model.parameters(), lr=0.001)
            
            # Convert to tensor
            features_tensor = torch.FloatTensor(features)
            
            # Training loop
            model.train()
            for epoch in range(100):  # Limited epochs for speed
                optimizer.zero_grad()
                outputs = model(features_tensor)
                loss = criterion(outputs, features_tensor)
                loss.backward()
                optimizer.step()
                
                if epoch % 20 == 0:
                    logger.debug(f"Autoencoder epoch {epoch}, loss: {loss.item():.4f}")
            
            model.eval()
            return model
            
        except Exception as e:
            logger.error(f"Error training autoencoder: {e}")
            return None
    
    async def _predict_autoencoder(self, features: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Predict using autoencoder"""
        try:
            features_tensor = torch.FloatTensor(features)
            
            with torch.no_grad():
                reconstructed = self.model(features_tensor)
                reconstruction_errors = torch.mean((features_tensor - reconstructed) ** 2, dim=1)
            
            # Threshold for anomaly detection
            threshold = torch.quantile(reconstruction_errors, 1 - self.contamination)
            predictions = (reconstruction_errors > threshold).numpy().astype(int)
            predictions[predictions == 0] = 1  # Normal
            predictions[predictions == 1] = -1  # Anomaly
            
            return predictions, reconstruction_errors.numpy()
            
        except Exception as e:
            logger.error(f"Error in autoencoder prediction: {e}")
            return np.ones(len(features)), np.zeros(len(features))
    
    def _analyze_feature_contributions(self, feature_vector: np.ndarray) -> List[int]:
        """Analyze which features contribute most to anomaly"""
        try:
            # Simple approach: features with highest absolute values after scaling
            contributions = np.abs(feature_vector)
            return np.argsort(contributions)[::-1]
        except:
            return list(range(len(feature_vector)))
    
    def _classify_ml_anomaly(self, feature_vector: np.ndarray, 
                           feature_contributions: List[int]) -> AnomalyType:
        """Classify ML-detected anomaly type"""
        if len(feature_contributions) == 0:
            return AnomalyType.DRIFT_ANOMALY
        
        # Check most contributing feature
        top_feature = self.feature_names[feature_contributions[0]]
        
        if 'temperature' in top_feature.lower():
            return AnomalyType.TEMPERATURE_ANOMALY
        elif 'power' in top_feature.lower() or 'energy' in top_feature.lower():
            return AnomalyType.POWER_ANOMALY
        elif 'efficiency' in top_feature.lower():
            return AnomalyType.EFFICIENCY_ANOMALY
        else:
            return AnomalyType.DRIFT_ANOMALY
    
    def _get_ml_recommendations(self, affected_components: List[str]) -> List[str]:
        """Get recommendations for ML-detected anomalies"""
        recommendations = []
        
        for component in affected_components:
            if 'temperature' in component.lower():
                recommendations.append("Investigate temperature sensor and heating system")
            elif 'power' in component.lower():
                recommendations.append("Check electrical system and power consumption")
            elif 'efficiency' in component.lower():
                recommendations.append("Analyze system efficiency and performance")
            else:
                recommendations.append(f"Investigate {component} anomaly")
        
        # Add general recommendations
        recommendations.extend([
            "Review recent operational changes",
            "Check maintenance logs",
            "Monitor trend continuation"
        ])
        
        return recommendations


class HybridAnomalyDetector(AnomalyDetector):
    """Hybrid detector combining statistical and ML methods"""
    
    def __init__(self, statistical_weight: float = 0.4, ml_weight: float = 0.6):
        self.statistical_detector = StatisticalAnomalyDetector()
        self.ml_detector = MLAnomalyDetector('isolation_forest')
        self.statistical_weight = statistical_weight
        self.ml_weight = ml_weight
        self.ensemble_threshold = 0.5
        
    async def train(self, training_data: pd.DataFrame) -> bool:
        """Train both statistical and ML detectors"""
        try:
            stat_success = await self.statistical_detector.train(training_data)
            ml_success = await self.ml_detector.train(training_data)
            
            return stat_success or ml_success  # Success if at least one works
            
        except Exception as e:
            logger.error(f"Error training hybrid detector: {e}")
            return False
    
    async def detect(self, data: pd.DataFrame) -> List[AnomalyDetectionResult]:
        """Detect anomalies using ensemble approach"""
        try:
            # Get results from both detectors
            stat_anomalies = await self.statistical_detector.detect(data)
            ml_anomalies = await self.ml_detector.detect(data)
            
            # Combine results
            combined_anomalies = []
            
            # Add statistical anomalies with adjusted confidence
            for anomaly in stat_anomalies:
                anomaly.confidence *= self.statistical_weight
                anomaly.description = f"[Statistical] {anomaly.description}"
                combined_anomalies.append(anomaly)
            
            # Add ML anomalies with adjusted confidence
            for anomaly in ml_anomalies:
                anomaly.confidence *= self.ml_weight
                anomaly.description = f"[ML] {anomaly.description}"
                combined_anomalies.append(anomaly)
            
            # Look for consensus anomalies (detected by both methods)
            consensus_anomalies = self._find_consensus_anomalies(stat_anomalies, ml_anomalies)
            combined_anomalies.extend(consensus_anomalies)
            
            # Sort by severity and confidence
            combined_anomalies.sort(key=lambda x: (x.severity.value, -x.confidence))
            
            return combined_anomalies
            
        except Exception as e:
            logger.error(f"Error in hybrid anomaly detection: {e}")
            return []
    
    async def update_model(self, new_data: pd.DataFrame) -> bool:
        """Update both detectors"""
        try:
            stat_success = await self.statistical_detector.update_model(new_data)
            ml_success = await self.ml_detector.update_model(new_data)
            
            return stat_success or ml_success
            
        except Exception as e:
            logger.error(f"Error updating hybrid detector: {e}")
            return False
    
    def _find_consensus_anomalies(self, stat_anomalies: List[AnomalyDetectionResult],
                                ml_anomalies: List[AnomalyDetectionResult]) -> List[AnomalyDetectionResult]:
        """Find anomalies detected by both methods"""
        consensus = []
        
        for stat_anomaly in stat_anomalies:
            for ml_anomaly in ml_anomalies:
                # Check if they're close in time and related
                time_diff = abs((stat_anomaly.timestamp - ml_anomaly.timestamp).total_seconds())
                
                if time_diff < 300:  # Within 5 minutes
                    # Create consensus anomaly with higher confidence
                    consensus_anomaly = AnomalyDetectionResult(
                        timestamp=stat_anomaly.timestamp,
                        anomaly_type=stat_anomaly.anomaly_type,
                        severity=max(stat_anomaly.severity, ml_anomaly.severity, key=lambda x: ['low', 'medium', 'high', 'critical'].index(x.value)),
                        anomaly_score=(stat_anomaly.anomaly_score + ml_anomaly.anomaly_score) / 2,
                        confidence=min(0.95, stat_anomaly.confidence + ml_anomaly.confidence),
                        description=f"[CONSENSUS] {stat_anomaly.description} + {ml_anomaly.description}",
                        affected_components=list(set(stat_anomaly.affected_components + ml_anomaly.affected_components)),
                        recommended_actions=list(set(stat_anomaly.recommended_actions + ml_anomaly.recommended_actions)),
                        maintenance_priority=min(stat_anomaly.maintenance_priority, ml_anomaly.maintenance_priority)
                    )
                    consensus.append(consensus_anomaly)
        
        return consensus


class PredictiveMaintenanceEngine:
    """Complete predictive maintenance engine"""
    
    def __init__(self, anomaly_detector: AnomalyDetector):
        self.anomaly_detector = anomaly_detector
        self.maintenance_history = []
        self.failure_patterns = {}
        self.component_health = {}
        self.maintenance_schedules = {}
        self.cost_models = {}
        
    async def analyze_system_health(self, current_data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze overall system health"""
        try:
            # Detect anomalies
            anomalies = await self.anomaly_detector.detect(current_data)
            
            # Calculate health scores
            health_scores = self._calculate_health_scores(current_data, anomalies)
            
            # Generate maintenance recommendations
            recommendations = self._generate_maintenance_recommendations(anomalies, health_scores)
            
            # Predict failure probabilities
            failure_predictions = self._predict_failures(current_data, anomalies)
            
            # Calculate maintenance costs
            cost_analysis = self._analyze_maintenance_costs(recommendations)
            
            return {
                'timestamp': datetime.now(),
                'overall_health_score': np.mean(list(health_scores.values())),
                'component_health_scores': health_scores,
                'anomalies_detected': len(anomalies),
                'critical_anomalies': len([a for a in anomalies if a.severity == SeverityLevel.CRITICAL]),
                'anomalies': [self._anomaly_to_dict(a) for a in anomalies],
                'maintenance_recommendations': [self._recommendation_to_dict(r) for r in recommendations],
                'failure_predictions': failure_predictions,
                'cost_analysis': cost_analysis,
                'recommended_actions': self._prioritize_actions(recommendations)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing system health: {e}")
            return {'error': str(e)}
    
    def _calculate_health_scores(self, data: pd.DataFrame, 
                               anomalies: List[AnomalyDetectionResult]) -> Dict[str, float]:
        """Calculate health scores for system components"""
        health_scores = {}
        
        # Get all numeric columns as potential components
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        
        for component in numeric_columns:
            if 'timestamp' in component.lower() or 'time' in component.lower():
                continue
            
            # Base health score
            base_score = 100.0
            
            # Reduce score for each anomaly affecting this component
            for anomaly in anomalies:
                if component in anomaly.affected_components or component in str(anomaly.description).lower():
                    penalty = {
                        SeverityLevel.LOW: 5,
                        SeverityLevel.MEDIUM: 15,
                        SeverityLevel.HIGH: 30,
                        SeverityLevel.CRITICAL: 50
                    }.get(anomaly.severity, 10)
                    
                    base_score -= penalty * anomaly.confidence
            
            # Ensure score is between 0 and 100
            health_scores[component] = max(0, min(100, base_score))
        
        return health_scores
    
    def _generate_maintenance_recommendations(self, anomalies: List[AnomalyDetectionResult],
                                           health_scores: Dict[str, float]) -> List[MaintenanceRecommendation]:
        """Generate maintenance recommendations"""
        recommendations = []
        
        # Recommendations based on anomalies
        for anomaly in anomalies:
            for component in anomaly.affected_components:
                if health_scores.get(component, 100) < 80:  # Component needs attention
                    
                    # Determine action based on anomaly type
                    if anomaly.anomaly_type == AnomalyType.TEMPERATURE_ANOMALY:
                        action = "Inspect temperature sensors and heating elements"
                        estimated_cost = 500.0
                        estimated_time = timedelta(hours=2)
                        required_parts = ["temperature sensor", "heating element"]
                        required_skills = ["electrical", "instrumentation"]
                        
                    elif anomaly.anomaly_type == AnomalyType.POWER_ANOMALY:
                        action = "Check electrical system and power distribution"
                        estimated_cost = 800.0
                        estimated_time = timedelta(hours=4)
                        required_parts = ["electrical components", "contactors"]
                        required_skills = ["electrical", "controls"]
                        
                    elif anomaly.anomaly_type == AnomalyType.EFFICIENCY_ANOMALY:
                        action = "Performance optimization and system tuning"
                        estimated_cost = 300.0
                        estimated_time = timedelta(hours=3)
                        required_parts = []
                        required_skills = ["process", "controls"]
                        
                    else:
                        action = f"Investigate {anomaly.anomaly_type.value} in {component}"
                        estimated_cost = 400.0
                        estimated_time = timedelta(hours=2)
                        required_parts = []
                        required_skills = ["general maintenance"]
                    
                    # Risk assessment
                    if anomaly.severity == SeverityLevel.CRITICAL:
                        risk = "System failure, production loss"
                        urgency = SeverityLevel.CRITICAL
                    elif anomaly.severity == SeverityLevel.HIGH:
                        risk = "Equipment damage, efficiency loss"
                        urgency = SeverityLevel.HIGH
                    else:
                        risk = "Gradual performance degradation"
                        urgency = anomaly.severity
                    
                    recommendation = MaintenanceRecommendation(
                        component=component,
                        action=action,
                        urgency=urgency,
                        estimated_cost=estimated_cost,
                        estimated_time=estimated_time,
                        risk_if_delayed=risk,
                        required_parts=required_parts,
                        required_skills=required_skills,
                        scheduling_constraints={
                            'can_perform_online': anomaly.severity != SeverityLevel.CRITICAL,
                            'requires_shutdown': anomaly.severity == SeverityLevel.CRITICAL,
                            'weather_dependent': False
                        }
                    )
                    recommendations.append(recommendation)
        
        # Preventive maintenance based on health scores
        for component, score in health_scores.items():
            if 60 < score < 80:  # Preventive maintenance range
                recommendation = MaintenanceRecommendation(
                    component=component,
                    action=f"Preventive maintenance for {component}",
                    urgency=SeverityLevel.LOW,
                    estimated_cost=200.0,
                    estimated_time=timedelta(hours=1),
                    risk_if_delayed="Gradual performance decline",
                    required_parts=[],
                    required_skills=["general maintenance"],
                    scheduling_constraints={'can_perform_online': True}
                )
                recommendations.append(recommendation)
        
        return recommendations
    
    def _predict_failures(self, data: pd.DataFrame, 
                         anomalies: List[AnomalyDetectionResult]) -> Dict[str, Any]:
        """Predict potential failures"""
        predictions = {}
        
        # Simple failure prediction based on anomaly trends
        critical_anomalies = [a for a in anomalies if a.severity == SeverityLevel.CRITICAL]
        high_anomalies = [a for a in anomalies if a.severity == SeverityLevel.HIGH]
        
        if critical_anomalies:
            predictions['immediate_failure_risk'] = 0.8
            predictions['estimated_failure_time'] = datetime.now() + timedelta(hours=24)
            predictions['most_likely_failures'] = [a.anomaly_type.value for a in critical_anomalies]
        elif high_anomalies:
            predictions['immediate_failure_risk'] = 0.4
            predictions['estimated_failure_time'] = datetime.now() + timedelta(days=7)
            predictions['most_likely_failures'] = [a.anomaly_type.value for a in high_anomalies]
        else:
            predictions['immediate_failure_risk'] = 0.1
            predictions['estimated_failure_time'] = None
            predictions['most_likely_failures'] = []
        
        return predictions
    
    def _analyze_maintenance_costs(self, recommendations: List[MaintenanceRecommendation]) -> Dict[str, Any]:
        """Analyze maintenance costs and priorities"""
        if not recommendations:
            return {'total_cost': 0, 'urgent_cost': 0, 'cost_breakdown': {}}
        
        total_cost = sum(r.estimated_cost for r in recommendations)
        urgent_cost = sum(r.estimated_cost for r in recommendations 
                         if r.urgency in [SeverityLevel.CRITICAL, SeverityLevel.HIGH])
        
        cost_breakdown = {}
        for rec in recommendations:
            urgency = rec.urgency.value
            if urgency not in cost_breakdown:
                cost_breakdown[urgency] = 0
            cost_breakdown[urgency] += rec.estimated_cost
        
        return {
            'total_cost': total_cost,
            'urgent_cost': urgent_cost,
            'cost_breakdown': cost_breakdown,
            'cost_per_component': {rec.component: rec.estimated_cost for rec in recommendations}
        }
    
    def _prioritize_actions(self, recommendations: List[MaintenanceRecommendation]) -> List[str]:
        """Prioritize maintenance actions"""
        # Sort by urgency and cost
        sorted_recommendations = sorted(
            recommendations,
            key=lambda x: (['critical', 'high', 'medium', 'low'].index(x.urgency.value), x.estimated_cost)
        )
        
        return [rec.action for rec in sorted_recommendations[:5]]  # Top 5 actions
    
    def _anomaly_to_dict(self, anomaly: AnomalyDetectionResult) -> Dict[str, Any]:
        """Convert anomaly to dictionary"""
        return {
            'timestamp': anomaly.timestamp.isoformat(),
            'type': anomaly.anomaly_type.value,
            'severity': anomaly.severity.value,
            'score': anomaly.anomaly_score,
            'confidence': anomaly.confidence,
            'description': anomaly.description,
            'affected_components': anomaly.affected_components,
            'recommended_actions': anomaly.recommended_actions
        }
    
    def _recommendation_to_dict(self, rec: MaintenanceRecommendation) -> Dict[str, Any]:
        """Convert recommendation to dictionary"""
        return {
            'component': rec.component,
            'action': rec.action,
            'urgency': rec.urgency.value,
            'estimated_cost': rec.estimated_cost,
            'estimated_hours': rec.estimated_time.total_seconds() / 3600,
            'risk_if_delayed': rec.risk_if_delayed,
            'required_parts': rec.required_parts,
            'required_skills': rec.required_skills
        }


# Factory functions
def create_anomaly_detector(detector_type: str = 'hybrid', **kwargs) -> AnomalyDetector:
    """Create anomaly detector instance"""
    if detector_type == 'statistical':
        return StatisticalAnomalyDetector(**kwargs)
    elif detector_type == 'ml':
        method = kwargs.get('method', 'isolation_forest')
        return MLAnomalyDetector(method)
    elif detector_type == 'hybrid':
        return HybridAnomalyDetector(**kwargs)
    else:
        raise ValueError(f"Unknown detector type: {detector_type}")


def create_predictive_maintenance_system(detector_type: str = 'hybrid', **kwargs) -> PredictiveMaintenanceEngine:
    """Create complete predictive maintenance system"""
    detector = create_anomaly_detector(detector_type, **kwargs)
    return PredictiveMaintenanceEngine(detector)


# Example usage
if __name__ == "__main__":
    async def test_predictive_maintenance():
        # Create test data
        dates = pd.date_range('2023-01-01', periods=1000, freq='H')
        np.random.seed(42)
        
        # Normal operation data
        normal_data = pd.DataFrame({
            'timestamp': dates,
            'tank_temperature': 150 + 5 * np.random.normal(0, 1, 1000),
            'power_consumption': 100 + 10 * np.random.normal(0, 1, 1000),
            'heating_efficiency': 0.85 + 0.05 * np.random.normal(0, 1, 1000),
            'ambient_temperature': 20 + 10 * np.sin(2 * np.pi * np.arange(1000) / 24) + np.random.normal(0, 2, 1000)
        })
        
        # Add some anomalies
        anomaly_data = normal_data.copy()
        anomaly_data.loc[500:510, 'tank_temperature'] = 200  # Temperature spike
        anomaly_data.loc[700:720, 'power_consumption'] = 200  # Power anomaly
        anomaly_data.loc[800:810, 'heating_efficiency'] = 0.5  # Efficiency drop
        
        print("Testing Predictive Maintenance System...")
        
        # Create predictive maintenance system
        pm_system = create_predictive_maintenance_system('hybrid')
        
        # Train on normal data
        print("Training anomaly detector...")
        training_success = await pm_system.anomaly_detector.train(normal_data)
        print(f"Training successful: {training_success}")
        
        # Analyze system health with anomalous data
        print("Analyzing system health...")
        health_analysis = await pm_system.analyze_system_health(anomaly_data)
        
        print(f"Overall Health Score: {health_analysis['overall_health_score']:.1f}")
        print(f"Anomalies Detected: {health_analysis['anomalies_detected']}")
        print(f"Critical Anomalies: {health_analysis['critical_anomalies']}")
        print(f"Total Maintenance Cost: ${health_analysis['cost_analysis']['total_cost']:.2f}")
        
        # Print top recommendations
        print("\nTop Maintenance Recommendations:")
        for i, action in enumerate(health_analysis['recommended_actions'][:3], 1):
            print(f"{i}. {action}")
        
        # Print failure predictions
        if health_analysis['failure_predictions'].get('immediate_failure_risk', 0) > 0.5:
            print(f"\nWARNING: High failure risk ({health_analysis['failure_predictions']['immediate_failure_risk']:.1%})")
            if health_analysis['failure_predictions']['estimated_failure_time']:
                print(f"Estimated failure time: {health_analysis['failure_predictions']['estimated_failure_time']}")
        
        print("\nPredictive maintenance testing completed!")
    
    # Run test
    asyncio.run(test_predictive_maintenance())