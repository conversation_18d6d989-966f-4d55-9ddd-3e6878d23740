{"version": 3, "file": "TextsHighHZ.js", "sources": ["../../src/components/TextsHighHZ.tsx"], "sourcesContent": null, "names": ["memo", "useThree", "useRef", "useEvent", "get<PERSON>erf", "matriceCount", "colorsGraph", "matrice<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxs", "Suspense", "Text", "localFont", "jsx", "THREE", "Fragment"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,MAAM,aAAkCA,MAAA;AAAA,EACtC,CAAC;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EAAA,MACI;AACE,UAAA,EAAE,OAAO,GAAG,QAAQ,EAAA,IAAMC,MAAS,SAAA,CAAC,MAAM,EAAE,QAAQ;AACpD,UAAA,SAASC,aAAY,IAAI;AACzB,UAAA,iBAAiBA,aAAY,IAAI;AAEvCC,WAAA,SAAS,OAAO,SAAS,kBAAkB,CAAC,KAAK,EAAE,GAAG;;AACpD,UAAI,CAAC,OAAO,CAAC,OAAO,QAAS;AAE7B,UAAI,YAAY;AACd,eAAO,QAAQ,QAAQ,KAAK,MAAMC,MAAAA,UAAU,aAAa,KAAK,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG;AAAA,UACnG;AAAA,QAAA;AAAA,MAEJ;AAEA,UAAI,CAAC,OAAQ;AAET,UAAA,OAAO,IAAI,MAAM;AACrB,UAAI,eAAe;AACV,gBAAA,QAAG,KAAK,aAAR,mBAAkB;AAAA,MAAA,WAChB,WAAW,gBAAgB;AACpC,eAAOC,aAAa,aAAA;AAAA,MACX,WAAA,CAAC,UAAU,GAAG,KAAK,QAAQ;AACpC,cAAM,QAAa,WAAW,GAAG,KAAK,SAAS,GAAG,KAAK;AACvD,eAAO,MAAM,MAAM;AAAA,MACrB;AAEA,UAAI,WAAW,OAAO;AACpB,eAAO,QAAQ,QAAQD,MAAA,QAAA,EAAU,kBAC7BE,iBAAY,UAAU,EAAE,UAAU,SAAA,IAClC,OAAOA,KAAAA,YAAY,UAAU,EAAE,IAAI,UAAU;AAAA,MACnD;AACA,aAAO,QAAQ,QAAQ,KAAK,MAAM,OAAO,KAAK,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,QAAQ,KAAK;AAElG,UAAI,aAAa;AACT,cAAA,gBAAqB,GAAG,KAAK;AAEnC,YAAI,OAAO,kBAAkB,eAAe,WAAW,gBAAgB;AACrE;AAAA,QACF;AAEI,YAAA;AACJ,YAAI,WAAW,gBAAgB;AAC7B,yBAAeC,aAAkB,kBAAA;AAAA,QAAA,OAC5B;AACL,yBAAe,cAAc,MAAM;AAAA,QACrC;AAEA,YAAI,eAAe,GAAG;AACb,iBAAA,QAAQ,WAAW,WAAW;AACrC,yBAAe,QAAQ,WAAW,OAAO,IAAI,WAAW,MAAM;AAE9D,iBAAO,QAAQ,SAAS,IAAI,IAAI,IAAI,UAAU,WAAW;AACzD,yBAAe,QAAQ,OACrB,SAAS,KAAK,MAAM,eAAe,KAAK,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,QAAQ,KAAK;AAAA,QAAA,OACzF;AACL,cAAI,eAAe,QAAQ,KAAM,gBAAe,QAAQ,OAAO;AAE/D,iBAAO,QAAQ,SAAS,IAAI,IAAI,IAAI,UAAU;AAC9C,iBAAO,QAAQ,WAAW;AAAA,QAC5B;AAAA,MACF;AACAF,mBAAA,aAAa,SAAS;AACtB,aAAO,QAAQ;AACf,aAAO,QAAQ,YAAY,KAAK,OAAO,QAAQ,MAAM;AAAA,IAAA,CACtD;AAEC,WAAAG,2BAAA,KAACC,MAAS,UAAA,EAAA,UAAU,MAClB,UAAA;AAAA,MAAAD,2BAAA;AAAA,QAACE,KAAA;AAAA,QAAA;AAAA,UACC,WAAU;AAAA,UACV,kBAAkB;AAAA,UAClB,KAAK;AAAA,UACL,MAAMC;AAAAA,UACN;AAAA,UACA,UAAU,CAAC,CAAC,IAAI,IAAI,UAAU,UAAU,IAAI,IAAI,UAAU,UAAU,CAAC;AAAA,UACrE;AAAA,UACA,YAAW;AAAA,UACX,UAAU,CAAC,SAAS;AAClB,iBAAK,aAAa;AAClBN,yBAAA,aAAa,SAAS;AACjB,iBAAA,YAAY,KAAK,KAAK,MAAM;AAAA,UACnC;AAAA,UACA,UAAA;AAAA,YAACO,2BAAAA,IAAA,qBAAA,EAAkB,UAAUC,iBAAM,eAAgB,CAAA;AAAA,YAAE;AAAA,UAAA;AAAA,QAAA;AAAA,MACvD;AAAA,MACC,eACCD,2BAAA;AAAA,QAACF,KAAA;AAAA,QAAA;AAAA,UACC,WAAU;AAAA,UACV,kBAAkB;AAAA,UAClB,KAAK;AAAA,UACL,MAAMC;AAAAA,UACN,UAAU;AAAA,UACV,UAAU,CAAC,CAAC,IAAI,IAAI,UAAU,UAAU,IAAI,IAAI,UAAU,WAAW,MAAM,CAAC;AAAA,UAC5E,OAAO;AAAA,UACP,YAAW;AAAA,UACX,UAAU,CAAC,SAAS;AAClB,iBAAK,aAAa;AAClBN,yBAAA,aAAa,SAAS;AACjB,iBAAA,YAAY,KAAK,KAAK,MAAM;AAAA,UACnC;AAAA,UACA,UAACO,2BAAA,IAAA,qBAAA,EAAkB,UAAUC,iBAAM,gBAAgB;AAAA,QAAA;AAAA,MACrD;AAAA,IAEJ,EAAA,CAAA;AAAA,EAEJ;AACF;AAEO,MAAM,cAA+B,CAAC,EAAE,YAAY,YAAY,SAAS,mBAAmB;AAIjG,QAAM,WAAmB;AACzB,SAEIL,2BAAA,KAAAM,qBAAA,EAAA,UAAA;AAAA,IAAAF,2BAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC;AAAA,QACA,OAAO,OAAON,iBAAY,UAAU,EAAE,IAAI,SAAU,CAAA;AAAA,QACpD,QAAM;AAAA,QACN,QAAO;AAAA,QACP;AAAA,QACA,SAAS;AAAA,QACT,OAAO;AAAA,MAAA;AAAA,IACT;AAAA,IACAM,2BAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,OAAO,OAAON,iBAAY,UAAU,EAAE,IAAI,SAAU,CAAA;AAAA,QACpD,QAAM;AAAA,QACN,QAAO;AAAA,QACP;AAAA,QACA,SAAS;AAAA,QACT,OAAO;AAAA,MAAA;AAAA,IACT;AAAA,IAEAM,2BAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,OAAO,OAAON,iBAAY,UAAU,EAAE,IAAI,SAAU,CAAA;AAAA,QACpD,QAAM;AAAA,QACN,QAAO;AAAA,QACP;AAAA,QACA,SAAS;AAAA,QACT,OAAO;AAAA,MAAA;AAAA,IACT;AAAA,IACC,CAAC,UAEEE,2BAAAA,KAAAM,WAAA,UAAA,EAAA,UAAA;AAAA,MAACF,2BAAAA,IAAA,YAAA,EAAW,QAAO,SAAQ,UAAoB,SAAS,KAAK,OAAO,GAAG,aAAW,KAAC,CAAA;AAAA,MACnFA,2BAAAA,IAAC,YAAW,EAAA,QAAO,aAAY,UAAoB,SAAS,KAAK,OAAO,GAAG,aAAW,KAAC,CAAA;AAAA,MACtFA,2BAAAA,IAAA,YAAA,EAAW,UAAQ,MAAC,QAAO,cAAa,UAAoB,SAAS,IAAI,SAAS,GAAG,OAAO,EAAG,CAAA;AAAA,MAC/FA,2BAAAA,IAAA,YAAA,EAAW,UAAQ,MAAC,QAAO,YAAW,UAAoB,SAAS,IAAI,SAAS,IAAI,OAAO,EAAG,CAAA;AAAA,MAC9FA,2BAAAA,IAAA,YAAA,EAAW,eAAa,MAAC,QAAO,YAAW,UAAoB,SAAS,IAAI,SAAS,KAAK,OAAO,EAAG,CAAA;AAAA,MACpGA,2BAAAA,IAAA,YAAA,EAAW,QAAO,SAAQ,UAAoB,SAAS,IAAI,SAAS,KAAK,OAAO,GAAG,aAAW,KAAC,CAAA;AAAA,MAC/FA,2BAAAA,IAAA,YAAA,EAAW,QAAO,UAAS,UAAoB,SAAS,IAAI,SAAS,KAAK,OAAO,GAAG,aAAW,KAAC,CAAA;AAAA,MAChG,gBACCA,2BAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,QAAM;AAAA,UACN,QAAO;AAAA,UACP;AAAA,UACA,SAAS;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,UACP,aAAW;AAAA,QAAA;AAAA,MACb;AAAA,IAAA,EAAA,CAEJ,IACE;AAAA,IAEH,cACCA,2BAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,OAAO,OAAON,iBAAY,UAAU,EAAE,OAAO,SAAU,CAAA;AAAA,QACvD;AAAA,QACA;AAAA,QACA,SAAS;AAAA,QACT,SAAS,UAAU,MAAM;AAAA,QACzB,OAAO,WAAW,SAAS;AAAA,MAAA;AAAA,IAC7B;AAAA,EAEJ,EAAA,CAAA;AAEJ;;"}