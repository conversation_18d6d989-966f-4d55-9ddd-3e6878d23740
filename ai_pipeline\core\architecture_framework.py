"""
AI/ML Pipeline Architecture Framework
Based on the architecture diagram showing comprehensive ML/AI pipeline
with Application Handling, World State Controller, 3D Assets, Surrogate Models, and AI Training
"""

import asyncio
import logging
import uuid
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
from typing import Dict, List, Optional, Any, Union, Callable
import numpy as np
import torch
import torch.nn as nn
from pathlib import Path

logger = logging.getLogger(__name__)


class ComponentType(Enum):
    """Component types in the AI pipeline"""
    APPLICATION_HANDLING = "application_handling"
    WORLD_STATE_CONTROLLER = "world_state_controller"
    ASSETS_3D = "3d_assets"
    SURROGATE_MODEL_HVM = "surrogate_model_hvm"
    AI_MODEL_TRAINING = "ai_model_training"


class ProcessingStage(Enum):
    """Processing stages in the pipeline"""
    INPUT = "input"
    PREPROCESSING = "preprocessing"
    SIMULATION = "simulation"
    MODEL_INFERENCE = "model_inference"
    TRAINING = "training"
    OUTPUT = "output"
    VALIDATION = "validation"


@dataclass
class PipelineData:
    """Data container for pipeline processing"""
    data_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    stage: ProcessingStage = ProcessingStage.INPUT
    component_type: ComponentType = ComponentType.APPLICATION_HANDLING
    data: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    tensors: Dict[str, torch.Tensor] = field(default_factory=dict)
    arrays: Dict[str, np.ndarray] = field(default_factory=dict)
    
    def add_tensor(self, key: str, tensor: torch.Tensor):
        """Add tensor data"""
        self.tensors[key] = tensor
    
    def add_array(self, key: str, array: np.ndarray):
        """Add numpy array data"""
        self.arrays[key] = array
    
    def get_tensor(self, key: str, default=None) -> Optional[torch.Tensor]:
        """Get tensor by key"""
        return self.tensors.get(key, default)
    
    def get_array(self, key: str, default=None) -> Optional[np.ndarray]:
        """Get array by key"""
        return self.arrays.get(key, default)


class PipelineComponent(ABC):
    """Abstract base class for all pipeline components"""
    
    def __init__(self, component_id: str, component_type: ComponentType):
        self.component_id = component_id
        self.component_type = component_type
        self.is_initialized = False
        self.metrics = {}
        
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the component"""
        pass
    
    @abstractmethod
    async def process(self, data: PipelineData) -> PipelineData:
        """Process data through the component"""
        pass
    
    @abstractmethod
    async def cleanup(self):
        """Clean up resources"""
        pass
    
    def update_metrics(self, metrics: Dict[str, Any]):
        """Update component metrics"""
        self.metrics.update(metrics)
        self.metrics['last_updated'] = datetime.now(timezone.utc)


class PipelineOrchestrator:
    """Main orchestrator for the AI/ML pipeline"""
    
    def __init__(self):
        self.components: Dict[str, PipelineComponent] = {}
        self.pipeline_graph: Dict[str, List[str]] = {}
        self.running = False
        self.processing_queue = asyncio.Queue()
        
    def register_component(self, component: PipelineComponent):
        """Register a component in the pipeline"""
        self.components[component.component_id] = component
        logger.info(f"Registered component: {component.component_id}")
    
    def connect_components(self, from_component: str, to_component: str):
        """Connect two components in the pipeline"""
        if from_component not in self.pipeline_graph:
            self.pipeline_graph[from_component] = []
        self.pipeline_graph[from_component].append(to_component)
        logger.info(f"Connected {from_component} -> {to_component}")
    
    async def initialize_pipeline(self) -> bool:
        """Initialize all components in the pipeline"""
        try:
            for component_id, component in self.components.items():
                if not await component.initialize():
                    logger.error(f"Failed to initialize component: {component_id}")
                    return False
                logger.info(f"Initialized component: {component_id}")
            
            self.running = True
            logger.info("Pipeline initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing pipeline: {e}")
            return False
    
    async def process_data(self, data: PipelineData, start_component: str) -> PipelineData:
        """Process data through the pipeline starting from a specific component"""
        current_data = data
        queue = [start_component]
        processed = set()
        
        while queue:
            component_id = queue.pop(0)
            
            if component_id in processed:
                continue
            
            if component_id not in self.components:
                logger.warning(f"Component not found: {component_id}")
                continue
            
            try:
                component = self.components[component_id]
                logger.debug(f"Processing data through component: {component_id}")
                
                current_data = await component.process(current_data)
                processed.add(component_id)
                
                # Add connected components to queue
                if component_id in self.pipeline_graph:
                    queue.extend(self.pipeline_graph[component_id])
                
            except Exception as e:
                logger.error(f"Error processing data in component {component_id}: {e}")
                raise
        
        return current_data
    
    async def shutdown_pipeline(self):
        """Shutdown the pipeline and cleanup resources"""
        self.running = False
        
        for component_id, component in self.components.items():
            try:
                await component.cleanup()
                logger.info(f"Cleaned up component: {component_id}")
            except Exception as e:
                logger.error(f"Error cleaning up component {component_id}: {e}")
        
        logger.info("Pipeline shutdown complete")
    
    def get_pipeline_status(self) -> Dict[str, Any]:
        """Get the status of the entire pipeline"""
        return {
            "running": self.running,
            "components": {
                comp_id: {
                    "type": comp.component_type.value,
                    "initialized": comp.is_initialized,
                    "metrics": comp.metrics
                }
                for comp_id, comp in self.components.items()
            },
            "connections": self.pipeline_graph
        }


class ModelRegistry:
    """Registry for managing AI/ML models"""
    
    def __init__(self):
        self.models: Dict[str, torch.nn.Module] = {}
        self.model_metadata: Dict[str, Dict[str, Any]] = {}
    
    def register_model(self, model_id: str, model: torch.nn.Module, metadata: Dict[str, Any] = None):
        """Register a model"""
        self.models[model_id] = model
        self.model_metadata[model_id] = metadata or {}
        self.model_metadata[model_id]['registered_at'] = datetime.now(timezone.utc)
        logger.info(f"Registered model: {model_id}")
    
    def get_model(self, model_id: str) -> Optional[torch.nn.Module]:
        """Get a model by ID"""
        return self.models.get(model_id)
    
    def get_model_metadata(self, model_id: str) -> Optional[Dict[str, Any]]:
        """Get model metadata"""
        return self.model_metadata.get(model_id)
    
    def list_models(self) -> List[str]:
        """List all registered models"""
        return list(self.models.keys())
    
    async def load_model_from_checkpoint(self, model_id: str, checkpoint_path: Path) -> bool:
        """Load model from checkpoint"""
        try:
            if model_id not in self.models:
                logger.error(f"Model {model_id} not registered")
                return False
            
            checkpoint = torch.load(checkpoint_path, map_location='cpu')
            self.models[model_id].load_state_dict(checkpoint['model_state_dict'])
            
            # Update metadata
            if model_id in self.model_metadata:
                self.model_metadata[model_id]['checkpoint_loaded'] = str(checkpoint_path)
                self.model_metadata[model_id]['loaded_at'] = datetime.now(timezone.utc)
            
            logger.info(f"Loaded model {model_id} from checkpoint: {checkpoint_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading model {model_id}: {e}")
            return False
    
    async def save_model_checkpoint(self, model_id: str, checkpoint_path: Path, 
                                  optimizer_state: Dict = None, epoch: int = None) -> bool:
        """Save model checkpoint"""
        try:
            if model_id not in self.models:
                logger.error(f"Model {model_id} not registered")
                return False
            
            checkpoint = {
                'model_state_dict': self.models[model_id].state_dict(),
                'metadata': self.model_metadata.get(model_id, {}),
                'saved_at': datetime.now(timezone.utc).isoformat()
            }
            
            if optimizer_state:
                checkpoint['optimizer_state_dict'] = optimizer_state
            
            if epoch is not None:
                checkpoint['epoch'] = epoch
            
            # Ensure directory exists
            checkpoint_path.parent.mkdir(parents=True, exist_ok=True)
            
            torch.save(checkpoint, checkpoint_path)
            logger.info(f"Saved model {model_id} checkpoint: {checkpoint_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving model {model_id}: {e}")
            return False


class DeviceManager:
    """Manager for handling different compute devices (CPU, GPU, etc.)"""
    
    def __init__(self):
        self.devices = self._detect_devices()
        self.default_device = self._get_default_device()
    
    def _detect_devices(self) -> Dict[str, torch.device]:
        """Detect available devices"""
        devices = {'cpu': torch.device('cpu')}
        
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                devices[f'cuda:{i}'] = torch.device(f'cuda:{i}')
                
        # Add support for other accelerators (MPS, etc.)
        if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            devices['mps'] = torch.device('mps')
        
        return devices
    
    def _get_default_device(self) -> torch.device:
        """Get default device based on availability"""
        if 'cuda:0' in self.devices:
            return self.devices['cuda:0']
        elif 'mps' in self.devices:
            return self.devices['mps']
        else:
            return self.devices['cpu']
    
    def get_device(self, device_name: str = None) -> torch.device:
        """Get device by name or return default"""
        if device_name is None:
            return self.default_device
        
        if device_name in self.devices:
            return self.devices[device_name]
        
        logger.warning(f"Device {device_name} not available, using default")
        return self.default_device
    
    def get_device_info(self) -> Dict[str, Any]:
        """Get information about available devices"""
        info = {
            'available_devices': list(self.devices.keys()),
            'default_device': str(self.default_device)
        }
        
        if torch.cuda.is_available():
            info['cuda_devices'] = []
            for i in range(torch.cuda.device_count()):
                device_info = {
                    'index': i,
                    'name': torch.cuda.get_device_name(i),
                    'memory_total': torch.cuda.get_device_properties(i).total_memory,
                    'memory_available': torch.cuda.get_device_properties(i).total_memory - torch.cuda.memory_allocated(i)
                }
                info['cuda_devices'].append(device_info)
        
        return info


# Global instances
pipeline_orchestrator = PipelineOrchestrator()
model_registry = ModelRegistry()
device_manager = DeviceManager()


# Utility functions
def create_pipeline_data(data: Dict[str, Any] = None, 
                        component_type: ComponentType = ComponentType.APPLICATION_HANDLING,
                        stage: ProcessingStage = ProcessingStage.INPUT) -> PipelineData:
    """Create a new PipelineData instance"""
    return PipelineData(
        data=data or {},
        component_type=component_type,
        stage=stage
    )


def tensor_to_numpy(tensor: torch.Tensor) -> np.ndarray:
    """Convert PyTorch tensor to numpy array"""
    return tensor.detach().cpu().numpy()


def numpy_to_tensor(array: np.ndarray, device: torch.device = None) -> torch.Tensor:
    """Convert numpy array to PyTorch tensor"""
    tensor = torch.from_numpy(array)
    if device:
        tensor = tensor.to(device)
    return tensor


async def initialize_ai_pipeline() -> bool:
    """Initialize the entire AI pipeline"""
    logger.info("Initializing AI/ML Pipeline...")
    
    # Initialize device manager
    device_info = device_manager.get_device_info()
    logger.info(f"Available devices: {device_info}")
    
    # Initialize pipeline orchestrator
    success = await pipeline_orchestrator.initialize_pipeline()
    
    if success:
        logger.info("AI/ML Pipeline initialized successfully")
    else:
        logger.error("Failed to initialize AI/ML Pipeline")
    
    return success


async def shutdown_ai_pipeline():
    """Shutdown the AI pipeline"""
    logger.info("Shutting down AI/ML Pipeline...")
    await pipeline_orchestrator.shutdown_pipeline()
    logger.info("AI/ML Pipeline shutdown complete")