<html>
  <head>
    <title>gifuct-js demo</title>
  </head>
  <body>
    <div>
      <canvas id="c"></canvas>
    </div>
    <div style="margin: 10px 0;">
      <input id="url" type="text" />
      <button id="loadGIF">Load</button>
      <button id="playpause">Play/Pause</button>
    </div>
    <div style="margin-bottom: 10px;">
      <input id="edgedetect" type="checkbox" />
      Edge Detect
      <input id="invert" type="checkbox" />
      Invert Colours
      <input id="grayscale" type="checkbox" />
      Grayscale
    </div>
    <div>
      <input id="pixels" type="range" min="0" max="100" step="1" value="100" />
      Pixelate Amount
    </div>

    <script src="demo.build.js"></script>
  </body>
</html>
