"""
Time-Series Database Manager for Real-time Data Storage
Integrates with InfluxDB for high-performance time-series data operations
"""

import asyncio
import logging
import json
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
import warnings
warnings.filterwarnings('ignore')

try:
    import influxdb_client
    from influxdb_client import InfluxDBClient, Point, WritePrecision
    from influxdb_client.client.write_api import SYNCHRONOUS, ASYNCHRONOUS
    from influxdb_client.client.query_api import QueryApi
    from influxdb_client.client.exceptions import InfluxDBError
    INFLUXDB_AVAILABLE = True
except ImportError:
    INFLUXDB_AVAILABLE = False
    print("Warning: InfluxDB client not available. Time-series functionality will be limited.")

try:
    import sqlite3
    SQLITE_AVAILABLE = True
except ImportError:
    SQLITE_AVAILABLE = False
    print("Warning: SQLite not available. Local time-series storage will be limited.")

logger = logging.getLogger(__name__)


class AggregationFunction(Enum):
    MEAN = "mean"
    SUM = "sum"
    MIN = "min"
    MAX = "max"
    COUNT = "count"
    LAST = "last"
    FIRST = "first"
    MEDIAN = "median"
    STDDEV = "stddev"
    PERCENTILE = "percentile"


@dataclass
class TimeSeriesPoint:
    """Time-series data point"""
    measurement: str
    timestamp: datetime
    value: Union[float, int, str, bool]
    tags: Dict[str, str] = field(default_factory=dict)
    fields: Dict[str, Union[float, int, str, bool]] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'measurement': self.measurement,
            'timestamp': self.timestamp.isoformat(),
            'value': self.value,
            'tags': self.tags,
            'fields': self.fields
        }


@dataclass
class TimeSeriesQuery:
    """Time-series query parameters"""
    measurement: str
    start_time: datetime
    end_time: datetime
    tags: Dict[str, str] = field(default_factory=dict)
    fields: List[str] = field(default_factory=list)
    aggregation: Optional[AggregationFunction] = None
    window: Optional[timedelta] = None
    fill_strategy: str = "null"  # null, previous, linear, zero
    limit: Optional[int] = None
    descending: bool = False


@dataclass
class TimeSeriesConfig:
    """Configuration for time-series database"""
    backend: str = "influxdb"  # influxdb, sqlite, memory
    url: str = "http://localhost:8086"
    token: str = ""
    org: str = "my-org"
    bucket: str = "my-bucket"
    database: str = "timeseries"  # For SQLite
    retention_days: int = 90
    batch_size: int = 1000
    flush_interval: int = 1000  # milliseconds
    enable_gzip: bool = True
    precision: str = "s"  # s, ms, us, ns


class TimeSeriesBackend(ABC):
    """Abstract base class for time-series backends"""
    
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the backend"""
        pass
    
    @abstractmethod
    async def write_point(self, point: TimeSeriesPoint) -> bool:
        """Write a single point"""
        pass
    
    @abstractmethod
    async def write_points(self, points: List[TimeSeriesPoint]) -> bool:
        """Write multiple points"""
        pass
    
    @abstractmethod
    async def query_data(self, query: TimeSeriesQuery) -> Optional[pd.DataFrame]:
        """Query data"""
        pass
    
    @abstractmethod
    async def get_measurements(self) -> List[str]:
        """Get list of measurements"""
        pass
    
    @abstractmethod
    async def get_tags(self, measurement: str) -> Dict[str, List[str]]:
        """Get tags for a measurement"""
        pass
    
    @abstractmethod
    async def delete_data(self, measurement: str, start_time: datetime, end_time: datetime) -> bool:
        """Delete data in time range"""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup resources"""
        pass


class InfluxDBBackend(TimeSeriesBackend):
    """InfluxDB backend for time-series data"""
    
    def __init__(self, config: TimeSeriesConfig):
        self.config = config
        self.client = None
        self.write_api = None
        self.query_api = None
        self.delete_api = None
        self.is_connected = False
    
    async def initialize(self) -> bool:
        """Initialize InfluxDB connection"""
        try:
            if not INFLUXDB_AVAILABLE:
                logger.error("InfluxDB client not available")
                return False
            
            self.client = InfluxDBClient(
                url=self.config.url,
                token=self.config.token,
                org=self.config.org,
                enable_gzip=self.config.enable_gzip
            )
            
            # Test connection
            health = self.client.health()
            if health.status == "pass":
                self.write_api = self.client.write_api(write_options=SYNCHRONOUS)
                self.query_api = self.client.query_api()
                self.delete_api = self.client.delete_api()
                self.is_connected = True
                logger.info("InfluxDB connection established")
                return True
            else:
                logger.error(f"InfluxDB health check failed: {health.status}")
                return False
        
        except Exception as e:
            logger.error(f"Error initializing InfluxDB: {e}")
            return False
    
    async def write_point(self, point: TimeSeriesPoint) -> bool:
        """Write a single point to InfluxDB"""
        try:
            if not self.is_connected:
                return False
            
            # Create InfluxDB point
            influx_point = Point(point.measurement)
            
            # Add tags
            for key, value in point.tags.items():
                influx_point = influx_point.tag(key, value)
            
            # Add fields
            if point.fields:
                for key, value in point.fields.items():
                    influx_point = influx_point.field(key, value)
            else:
                # Use main value as field
                influx_point = influx_point.field("value", point.value)
            
            # Set timestamp
            influx_point = influx_point.time(point.timestamp)
            
            # Write to InfluxDB
            self.write_api.write(bucket=self.config.bucket, record=influx_point)
            
            return True
        
        except Exception as e:
            logger.error(f"Error writing point to InfluxDB: {e}")
            return False
    
    async def write_points(self, points: List[TimeSeriesPoint]) -> bool:
        """Write multiple points to InfluxDB"""
        try:
            if not self.is_connected:
                return False
            
            # Convert to InfluxDB points
            influx_points = []
            for point in points:
                influx_point = Point(point.measurement)
                
                # Add tags
                for key, value in point.tags.items():
                    influx_point = influx_point.tag(key, value)
                
                # Add fields
                if point.fields:
                    for key, value in point.fields.items():
                        influx_point = influx_point.field(key, value)
                else:
                    influx_point = influx_point.field("value", point.value)
                
                # Set timestamp
                influx_point = influx_point.time(point.timestamp)
                influx_points.append(influx_point)
            
            # Write batch to InfluxDB
            self.write_api.write(bucket=self.config.bucket, record=influx_points)
            
            return True
        
        except Exception as e:
            logger.error(f"Error writing points to InfluxDB: {e}")
            return False
    
    async def query_data(self, query: TimeSeriesQuery) -> Optional[pd.DataFrame]:
        """Query data from InfluxDB"""
        try:
            if not self.is_connected:
                return None
            
            # Build Flux query
            flux_query = self._build_flux_query(query)
            
            # Execute query
            result = self.query_api.query(flux_query)
            
            # Convert to DataFrame
            records = []
            for table in result:
                for record in table.records:
                    row = {
                        'timestamp': record.get_time(),
                        'measurement': record.get_measurement(),
                        'field': record.get_field(),
                        'value': record.get_value()
                    }
                    
                    # Add tags
                    for key, value in record.values.items():
                        if key.startswith('_') or key in ['result', 'table']:
                            continue
                        row[key] = value
                    
                    records.append(row)
            
            if records:
                df = pd.DataFrame(records)
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                return df
            
            return None
        
        except Exception as e:
            logger.error(f"Error querying InfluxDB: {e}")
            return None
    
    async def get_measurements(self) -> List[str]:
        """Get list of measurements from InfluxDB"""
        try:
            if not self.is_connected:
                return []
            
            flux_query = f'''
                import "influxdata/influxdb/schema"
                schema.measurements(bucket: "{self.config.bucket}")
            '''
            
            result = self.query_api.query(flux_query)
            measurements = []
            
            for table in result:
                for record in table.records:
                    measurements.append(record.get_value())
            
            return measurements
        
        except Exception as e:
            logger.error(f"Error getting measurements from InfluxDB: {e}")
            return []
    
    async def get_tags(self, measurement: str) -> Dict[str, List[str]]:
        """Get tags for a measurement from InfluxDB"""
        try:
            if not self.is_connected:
                return {}
            
            flux_query = f'''
                import "influxdata/influxdb/schema"
                schema.tagKeys(bucket: "{self.config.bucket}", measurement: "{measurement}")
            '''
            
            result = self.query_api.query(flux_query)
            tags = {}
            
            for table in result:
                for record in table.records:
                    tag_key = record.get_value()
                    
                    # Get tag values
                    value_query = f'''
                        import "influxdata/influxdb/schema"
                        schema.tagValues(bucket: "{self.config.bucket}", tag: "{tag_key}")
                    '''
                    
                    value_result = self.query_api.query(value_query)
                    values = []
                    
                    for value_table in value_result:
                        for value_record in value_table.records:
                            values.append(value_record.get_value())
                    
                    tags[tag_key] = values
            
            return tags
        
        except Exception as e:
            logger.error(f"Error getting tags from InfluxDB: {e}")
            return {}
    
    async def delete_data(self, measurement: str, start_time: datetime, end_time: datetime) -> bool:
        """Delete data from InfluxDB"""
        try:
            if not self.is_connected:
                return False
            
            # Use delete API
            self.delete_api.delete(
                start=start_time,
                stop=end_time,
                predicate=f'_measurement="{measurement}"',
                bucket=self.config.bucket,
                org=self.config.org
            )
            
            return True
        
        except Exception as e:
            logger.error(f"Error deleting data from InfluxDB: {e}")
            return False
    
    async def cleanup(self) -> None:
        """Cleanup InfluxDB resources"""
        try:
            if self.client:
                self.client.close()
                self.is_connected = False
                logger.info("InfluxDB connection closed")
        
        except Exception as e:
            logger.error(f"Error cleaning up InfluxDB: {e}")
    
    def _build_flux_query(self, query: TimeSeriesQuery) -> str:
        """Build Flux query string"""
        # Base query
        flux_query = f'from(bucket: "{self.config.bucket}")'
        
        # Time range
        flux_query += f' |> range(start: {query.start_time.isoformat()}Z, stop: {query.end_time.isoformat()}Z)'
        
        # Filter by measurement
        flux_query += f' |> filter(fn: (r) => r["_measurement"] == "{query.measurement}")'
        
        # Filter by tags
        for key, value in query.tags.items():
            flux_query += f' |> filter(fn: (r) => r["{key}"] == "{value}")'
        
        # Filter by fields
        if query.fields:
            field_filter = " or ".join([f'r["_field"] == "{field}"' for field in query.fields])
            flux_query += f' |> filter(fn: (r) => {field_filter})'
        
        # Aggregation
        if query.aggregation and query.window:
            window_duration = f"{int(query.window.total_seconds())}s"
            
            if query.aggregation == AggregationFunction.MEAN:
                flux_query += f' |> aggregateWindow(every: {window_duration}, fn: mean)'
            elif query.aggregation == AggregationFunction.SUM:
                flux_query += f' |> aggregateWindow(every: {window_duration}, fn: sum)'
            elif query.aggregation == AggregationFunction.MIN:
                flux_query += f' |> aggregateWindow(every: {window_duration}, fn: min)'
            elif query.aggregation == AggregationFunction.MAX:
                flux_query += f' |> aggregateWindow(every: {window_duration}, fn: max)'
            elif query.aggregation == AggregationFunction.COUNT:
                flux_query += f' |> aggregateWindow(every: {window_duration}, fn: count)'
            elif query.aggregation == AggregationFunction.LAST:
                flux_query += f' |> aggregateWindow(every: {window_duration}, fn: last)'
            elif query.aggregation == AggregationFunction.FIRST:
                flux_query += f' |> aggregateWindow(every: {window_duration}, fn: first)'
        
        # Fill strategy
        if query.fill_strategy != "null":
            flux_query += f' |> fill(usePrevious: {query.fill_strategy == "previous"})'
        
        # Sort
        if query.descending:
            flux_query += ' |> sort(columns: ["_time"], desc: true)'
        else:
            flux_query += ' |> sort(columns: ["_time"])'
        
        # Limit
        if query.limit:
            flux_query += f' |> limit(n: {query.limit})'
        
        return flux_query


class SQLiteBackend(TimeSeriesBackend):
    """SQLite backend for time-series data (for testing/development)"""
    
    def __init__(self, config: TimeSeriesConfig):
        self.config = config
        self.connection = None
        self.is_connected = False
    
    async def initialize(self) -> bool:
        """Initialize SQLite connection"""
        try:
            if not SQLITE_AVAILABLE:
                logger.error("SQLite not available")
                return False
            
            self.connection = sqlite3.connect(self.config.database)
            self.connection.execute('''
                CREATE TABLE IF NOT EXISTS timeseries_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    measurement TEXT NOT NULL,
                    timestamp DATETIME NOT NULL,
                    value REAL,
                    tags TEXT,
                    fields TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create indexes
            self.connection.execute('''
                CREATE INDEX IF NOT EXISTS idx_measurement_time 
                ON timeseries_data(measurement, timestamp)
            ''')
            
            self.connection.commit()
            self.is_connected = True
            logger.info("SQLite time-series database initialized")
            return True
        
        except Exception as e:
            logger.error(f"Error initializing SQLite: {e}")
            return False
    
    async def write_point(self, point: TimeSeriesPoint) -> bool:
        """Write a single point to SQLite"""
        try:
            if not self.is_connected:
                return False
            
            cursor = self.connection.cursor()
            cursor.execute('''
                INSERT INTO timeseries_data (measurement, timestamp, value, tags, fields)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                point.measurement,
                point.timestamp.isoformat(),
                point.value,
                json.dumps(point.tags),
                json.dumps(point.fields)
            ))
            
            self.connection.commit()
            return True
        
        except Exception as e:
            logger.error(f"Error writing point to SQLite: {e}")
            return False
    
    async def write_points(self, points: List[TimeSeriesPoint]) -> bool:
        """Write multiple points to SQLite"""
        try:
            if not self.is_connected:
                return False
            
            cursor = self.connection.cursor()
            data = [(
                point.measurement,
                point.timestamp.isoformat(),
                point.value,
                json.dumps(point.tags),
                json.dumps(point.fields)
            ) for point in points]
            
            cursor.executemany('''
                INSERT INTO timeseries_data (measurement, timestamp, value, tags, fields)
                VALUES (?, ?, ?, ?, ?)
            ''', data)
            
            self.connection.commit()
            return True
        
        except Exception as e:
            logger.error(f"Error writing points to SQLite: {e}")
            return False
    
    async def query_data(self, query: TimeSeriesQuery) -> Optional[pd.DataFrame]:
        """Query data from SQLite"""
        try:
            if not self.is_connected:
                return None
            
            sql_query = '''
                SELECT measurement, timestamp, value, tags, fields
                FROM timeseries_data
                WHERE measurement = ? AND timestamp >= ? AND timestamp <= ?
            '''
            
            params = [query.measurement, query.start_time.isoformat(), query.end_time.isoformat()]
            
            if query.descending:
                sql_query += ' ORDER BY timestamp DESC'
            else:
                sql_query += ' ORDER BY timestamp ASC'
            
            if query.limit:
                sql_query += f' LIMIT {query.limit}'
            
            df = pd.read_sql_query(sql_query, self.connection, params=params)
            
            if not df.empty:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                
                # Parse tags and fields
                df['tags'] = df['tags'].apply(lambda x: json.loads(x) if x else {})
                df['fields'] = df['fields'].apply(lambda x: json.loads(x) if x else {})
            
            return df
        
        except Exception as e:
            logger.error(f"Error querying SQLite: {e}")
            return None
    
    async def get_measurements(self) -> List[str]:
        """Get list of measurements from SQLite"""
        try:
            if not self.is_connected:
                return []
            
            cursor = self.connection.cursor()
            cursor.execute('SELECT DISTINCT measurement FROM timeseries_data')
            
            return [row[0] for row in cursor.fetchall()]
        
        except Exception as e:
            logger.error(f"Error getting measurements from SQLite: {e}")
            return []
    
    async def get_tags(self, measurement: str) -> Dict[str, List[str]]:
        """Get tags for a measurement from SQLite"""
        try:
            if not self.is_connected:
                return {}
            
            cursor = self.connection.cursor()
            cursor.execute('''
                SELECT DISTINCT tags FROM timeseries_data 
                WHERE measurement = ? AND tags IS NOT NULL
            ''', (measurement,))
            
            all_tags = {}
            for row in cursor.fetchall():
                if row[0]:
                    tags = json.loads(row[0])
                    for key, value in tags.items():
                        if key not in all_tags:
                            all_tags[key] = []
                        if value not in all_tags[key]:
                            all_tags[key].append(value)
            
            return all_tags
        
        except Exception as e:
            logger.error(f"Error getting tags from SQLite: {e}")
            return {}
    
    async def delete_data(self, measurement: str, start_time: datetime, end_time: datetime) -> bool:
        """Delete data from SQLite"""
        try:
            if not self.is_connected:
                return False
            
            cursor = self.connection.cursor()
            cursor.execute('''
                DELETE FROM timeseries_data
                WHERE measurement = ? AND timestamp >= ? AND timestamp <= ?
            ''', (measurement, start_time.isoformat(), end_time.isoformat()))
            
            self.connection.commit()
            return True
        
        except Exception as e:
            logger.error(f"Error deleting data from SQLite: {e}")
            return False
    
    async def cleanup(self) -> None:
        """Cleanup SQLite resources"""
        try:
            if self.connection:
                self.connection.close()
                self.is_connected = False
                logger.info("SQLite connection closed")
        
        except Exception as e:
            logger.error(f"Error cleaning up SQLite: {e}")


class TimeSeriesManager:
    """Time-series database manager"""
    
    def __init__(self, config: TimeSeriesConfig):
        self.config = config
        self.backend = None
        self.statistics = {
            'points_written': 0,
            'points_queried': 0,
            'queries_executed': 0,
            'errors': 0
        }
        
        # Initialize backend
        if config.backend == "influxdb":
            self.backend = InfluxDBBackend(config)
        elif config.backend == "sqlite":
            self.backend = SQLiteBackend(config)
        else:
            raise ValueError(f"Unsupported backend: {config.backend}")
    
    async def initialize(self) -> bool:
        """Initialize time-series manager"""
        try:
            if self.backend:
                success = await self.backend.initialize()
                if success:
                    logger.info(f"Time-series manager initialized with {self.config.backend} backend")
                return success
            return False
        except Exception as e:
            logger.error(f"Error initializing time-series manager: {e}")
            return False
    
    async def write_sensor_data(self, sensor_id: str, measurement: str, 
                               value: Union[float, int], tags: Optional[Dict[str, str]] = None,
                               fields: Optional[Dict[str, Any]] = None,
                               timestamp: Optional[datetime] = None) -> bool:
        """Write sensor data point"""
        try:
            point = TimeSeriesPoint(
                measurement=measurement,
                timestamp=timestamp or datetime.now(),
                value=value,
                tags=tags or {},
                fields=fields or {}
            )
            
            # Add sensor_id as tag
            point.tags['sensor_id'] = sensor_id
            
            success = await self.backend.write_point(point)
            if success:
                self.statistics['points_written'] += 1
            else:
                self.statistics['errors'] += 1
            
            return success
        
        except Exception as e:
            logger.error(f"Error writing sensor data: {e}")
            self.statistics['errors'] += 1
            return False
    
    async def write_batch_data(self, points: List[TimeSeriesPoint]) -> bool:
        """Write batch of data points"""
        try:
            success = await self.backend.write_points(points)
            if success:
                self.statistics['points_written'] += len(points)
            else:
                self.statistics['errors'] += 1
            
            return success
        
        except Exception as e:
            logger.error(f"Error writing batch data: {e}")
            self.statistics['errors'] += 1
            return False
    
    async def query_sensor_data(self, sensor_id: str, measurement: str,
                               start_time: datetime, end_time: datetime,
                               aggregation: Optional[AggregationFunction] = None,
                               window: Optional[timedelta] = None) -> Optional[pd.DataFrame]:
        """Query sensor data"""
        try:
            query = TimeSeriesQuery(
                measurement=measurement,
                start_time=start_time,
                end_time=end_time,
                tags={'sensor_id': sensor_id},
                aggregation=aggregation,
                window=window
            )
            
            result = await self.backend.query_data(query)
            if result is not None:
                self.statistics['points_queried'] += len(result)
                self.statistics['queries_executed'] += 1
            
            return result
        
        except Exception as e:
            logger.error(f"Error querying sensor data: {e}")
            self.statistics['errors'] += 1
            return None
    
    async def get_latest_values(self, measurement: str, 
                               tags: Optional[Dict[str, str]] = None,
                               limit: int = 100) -> Optional[pd.DataFrame]:
        """Get latest values for a measurement"""
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)  # Look back 1 hour
            
            query = TimeSeriesQuery(
                measurement=measurement,
                start_time=start_time,
                end_time=end_time,
                tags=tags or {},
                limit=limit,
                descending=True
            )
            
            result = await self.backend.query_data(query)
            if result is not None:
                self.statistics['points_queried'] += len(result)
                self.statistics['queries_executed'] += 1
            
            return result
        
        except Exception as e:
            logger.error(f"Error getting latest values: {e}")
            self.statistics['errors'] += 1
            return None
    
    async def get_aggregated_data(self, measurement: str, 
                                 start_time: datetime, end_time: datetime,
                                 aggregation: AggregationFunction,
                                 window: timedelta,
                                 tags: Optional[Dict[str, str]] = None) -> Optional[pd.DataFrame]:
        """Get aggregated data"""
        try:
            query = TimeSeriesQuery(
                measurement=measurement,
                start_time=start_time,
                end_time=end_time,
                tags=tags or {},
                aggregation=aggregation,
                window=window
            )
            
            result = await self.backend.query_data(query)
            if result is not None:
                self.statistics['points_queried'] += len(result)
                self.statistics['queries_executed'] += 1
            
            return result
        
        except Exception as e:
            logger.error(f"Error getting aggregated data: {e}")
            self.statistics['errors'] += 1
            return None
    
    async def get_temperature_history(self, sensor_id: str, 
                                     start_time: datetime, end_time: datetime,
                                     resample_interval: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Get temperature history for a sensor"""
        try:
            df = await self.query_sensor_data(sensor_id, "temperature", start_time, end_time)
            
            if df is not None and not df.empty:
                # Set timestamp as index
                df.set_index('timestamp', inplace=True)
                
                # Resample if requested
                if resample_interval:
                    df = df.resample(resample_interval).mean()
                
                return df
            
            return None
        
        except Exception as e:
            logger.error(f"Error getting temperature history: {e}")
            return None
    
    async def get_energy_consumption(self, start_time: datetime, end_time: datetime,
                                    aggregation_window: str = "1H") -> Optional[pd.DataFrame]:
        """Get energy consumption data"""
        try:
            df = await self.query_sensor_data("energy_meter", "power", start_time, end_time)
            
            if df is not None and not df.empty:
                # Set timestamp as index
                df.set_index('timestamp', inplace=True)
                
                # Aggregate by time window
                energy_df = df.resample(aggregation_window).sum()
                energy_df.rename(columns={'value': 'energy_consumption'}, inplace=True)
                
                return energy_df
            
            return None
        
        except Exception as e:
            logger.error(f"Error getting energy consumption: {e}")
            return None
    
    async def cleanup_old_data(self, retention_days: Optional[int] = None) -> bool:
        """Clean up old data based on retention policy"""
        try:
            retention = retention_days or self.config.retention_days
            cutoff_time = datetime.now() - timedelta(days=retention)
            
            # Get all measurements
            measurements = await self.backend.get_measurements()
            
            deleted_count = 0
            for measurement in measurements:
                success = await self.backend.delete_data(
                    measurement, 
                    datetime.min, 
                    cutoff_time
                )
                if success:
                    deleted_count += 1
            
            logger.info(f"Cleaned up old data for {deleted_count} measurements")
            return True
        
        except Exception as e:
            logger.error(f"Error cleaning up old data: {e}")
            return False
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Get time-series manager statistics"""
        return {
            'statistics': self.statistics,
            'backend_type': self.config.backend,
            'measurements': len(await self.backend.get_measurements())
        }
    
    async def cleanup(self) -> None:
        """Cleanup resources"""
        try:
            if self.backend:
                await self.backend.cleanup()
            logger.info("Time-series manager cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


# Factory function
def create_timeseries_manager(config: Optional[TimeSeriesConfig] = None) -> TimeSeriesManager:
    """Create a time-series manager instance"""
    if config is None:
        config = TimeSeriesConfig()
    
    return TimeSeriesManager(config)


# Example usage
if __name__ == "__main__":
    async def test_timeseries_manager():
        # Create time-series manager with SQLite backend for testing
        config = TimeSeriesConfig(
            backend="sqlite",
            database="/tmp/test_timeseries.db"
        )
        
        ts_manager = create_timeseries_manager(config)
        await ts_manager.initialize()
        
        # Write some test data
        now = datetime.now()
        for i in range(10):
            await ts_manager.write_sensor_data(
                sensor_id="temp_001",
                measurement="temperature",
                value=150.0 + i * 0.5,
                tags={"location": "tank_1", "unit": "celsius"},
                timestamp=now + timedelta(minutes=i)
            )
        
        # Query data
        start_time = now - timedelta(minutes=5)
        end_time = now + timedelta(minutes=15)
        
        df = await ts_manager.query_sensor_data(
            sensor_id="temp_001",
            measurement="temperature",
            start_time=start_time,
            end_time=end_time
        )
        
        print(f"Query result:\n{df}")
        
        # Get latest values
        latest = await ts_manager.get_latest_values("temperature", limit=5)
        print(f"Latest values:\n{latest}")
        
        # Get statistics
        stats = await ts_manager.get_statistics()
        print(f"Statistics: {json.dumps(stats, indent=2)}")
        
        # Cleanup
        await ts_manager.cleanup()
    
    # Run test
    asyncio.run(test_timeseries_manager())