{"version": 3, "file": "Graph.js", "sources": ["../../src/components/Graph.tsx"], "sourcesContent": null, "names": ["useMemo", "useRef", "THREE", "chart", "get<PERSON>erf", "useFrame", "colorsGraph", "jsxs", "Fragment", "matriceCount", "jsx", "usePerf", "Graph", "<PERSON><PERSON>", "TextsHighHZ", "Graphpc", "PauseIcon", "matrice<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,MAAM,aAA6B,CAAC,EAAC,YAAY,SAAS,QAAO,EAAC,QAAQ,KAAK,IAAI,GAAE,QAAO;AAEpF,QAAA,SAAcA,MAAAA,QAAQ,MAAM;AACzB,WAAA;AAAA,MACL,KAAK,IAAI,aAAa,MAAM,SAAS,CAAC;AAAA,MACtC,KAAK,IAAI,aAAa,MAAM,SAAS,CAAC;AAAA;AAAA,MAEtC,KAAK,IAAI,aAAa,MAAM,SAAS,CAAC;AAAA,IAAA;AAAA,EACxC,GACC,CAAC,KAAK,CAAC;AAEJ,QAAA,SAAQC,aAAY,IAAI;AACxB,QAAA,YAAWA,aAAY,IAAI;AAC3B,QAAA,SAAQA,aAAY,IAAI;AACxB,QAAA,SAAQA,aAAY,IAAI;AAExB,QAAA,YAAYD,MAAAA,QAAQ,MAAM,IAAIE,iBAAM,QAAQ,GAAE,GAAE,CAAC,GAAG,CAAA,CAAE;AAC5D,QAAM,eAAe,CAAC,SAAiB,SAAiB,GAAG,KAAU,aAAuB;AAC1F,QAAI,SAAS;AACb,UAAM,EAAC,OAAO,GAAG,QAAQ,MAAK;AAE9B,UAAMC,SAAQC,MAAAA,QAAU,EAAA,MAAM,KAAK,OAAO;AAC1C,QAAI,CAACD,UAASA,OAAM,WAAW,GAAG;AAChC;AAAA,IACF;AACM,UAAA,UAAU,UAAU,IAAI;AACxB,UAAA,aAAa,UAAU,KAAK;AAClC,QAAI,MAAMA,OAAM;AAChB,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAI,MAAMC,MAAAA,UAAU,MAAM,aAAa,IAAI,KAAK;AAC5CD,UAAAA,OAAM,EAAE,MAAM,QAAW;AACvBA,YAAAA,OAAM,EAAE,IAAI,QAAQ;AACbA,mBAAAA,OAAM,EAAE,IAAI;AAAA,QACvB;AACU,kBAAA,IAAI,UAAU,KAAK,MAAM,MAAM,IAAI,UAAU,KAAK,IAAI,GAAI,KAAK,IAAI,KAAKA,OAAM,EAAE,CAAC,IAAI,SAAU,OAAO,IAAI,UAAU,IAAI,cAAc,IAAI,GAAG,CAAC;AAExJ,kBAAU,QAAQ,IAAI,WAAW,SAAS,OAAO,IAAI,CAAC;AAAA,MACxD;AAAA,IACF;AAEI,QAAA,WAAW,SAAS,cAAc;AAAA,EAAA;AAIxCE,QAAAA,SAAS,SAAS,iBAAiB,EAAC,YAAW;AAE7C,iBAAa,OAAO,GAAG,OAAO,SAAS,QAAQ;AAC/C,QAAI,UAAU,SAAS;AACrB,gBAAU,QAAQ,MAAM,IAAID,gBAAU,kBAAkBE,iBAAY,UAAU,EAAE,UAAU,aAAa,OAAOA,KAAAA,YAAY,UAAU,EAAE,IAAI,SAAA,CAAU,GAAG;AAAA,IACzJ;AACA,iBAAa,OAAO,GAAG,OAAO,SAAS,QAAQ;AAE7C,iBAAa,OAAO,GAAG,OAAO,SAAS,QAAQ;AAAA,EAAA,CAElD;AACD,SAGIC,2BAAA,KAAAC,qBAAA,EAAA,UAAA;AAAA,IAACD,2BAAAA,KAAA,QAAA,EAAK,UAAU,CAAC,SAAO;AACpB,WAAK,aAAa;AAClBE,mBAAA,aAAa,SAAS;AACjB,WAAA,YAAY,KAAK,KAAK,MAAM;AAAA,IAEnC,GAAA,UAAA;AAAA,MAACC,2BAAAA,IAAA,kBAAA,EAAe,KAAK,QACnB,UAAAA,2BAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACG,QAAQ;AAAA,UACR,OAAO,MAAM;AAAA,UACb,OAAO,OAAO;AAAA,UACd,UAAU;AAAA,UACV,OAAOR,iBAAM;AAAA,UACb,aAAW;AAAA,QAAA;AAAA,MAAA,GAEjB;AAAA,qCACC,qBAAkB,EAAA,KAAK,WAAW,OAAO,OAAOI,iBAAY,UAAU,EAAE,IAAI,SAAU,CAAA,KAAK,aAAW,MAAC,SAAS,KAAK;AAAA,IAAA,GACxH;AAAA,IAECC,2BAAAA,KAAA,QAAA,EAAK,UAAU,CAAC,SAAO;AACpB,WAAK,aAAa;AAClBE,mBAAA,aAAa,SAAS;AACjB,WAAA,YAAY,KAAK,KAAK,MAAM;AAAA,IAEnC,GAAA,UAAA;AAAA,MAACC,2BAAAA,IAAA,kBAAA,EAAe,KAAK,QACnB,UAAAA,2BAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACG,QAAQ;AAAA,UACR,OAAO,MAAM;AAAA,UACb,OAAO,OAAO;AAAA,UACd,UAAU;AAAA,UACV,OAAOR,iBAAM;AAAA,UACb,aAAW;AAAA,QAAA;AAAA,MAAA,GAEjB;AAAA,MACCQ,2BAAA,IAAA,qBAAA,EAAkB,OAAO,OAAOJ,iBAAY,UAAU,EAAE,IAAI,SAAA,CAAU,KAAK,aAAW,MAAC,SAAS,KAAK;AAAA,IAAA,GACxG;AAAA,IAECC,2BAAAA,KAAA,QAAA,EAAK,UAAU,CAAC,SAAO;AACpB,WAAK,aAAa;AAClBE,mBAAA,aAAa,SAAS;AACjB,WAAA,YAAY,KAAK,KAAK,MAAM;AAAA,IAEnC,GAAA,UAAA;AAAA,MAACC,2BAAAA,IAAA,kBAAA,EAAe,KAAK,QACnB,UAAAA,2BAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,QAAQ;AAAA,UACR,OAAO,MAAM;AAAA,UACb,OAAO,OAAO;AAAA,UACd,UAAU;AAAA,UACV,OAAOR,iBAAM;AAAA,UACb,aAAW;AAAA,QAAA;AAAA,MAAA,GAEf;AAAA,MACCQ,2BAAA,IAAA,qBAAA,EAAkB,OAAO,OAAOJ,iBAAY,UAAU,EAAE,IAAI,SAAA,CAAU,KAAK,aAAW,MAAC,SAAS,KAAK;AAAA,IAAA,GACxG;AAAA,EACF,EAAA,CAAA;AAEJ;AAEO,MAAM,UAA2B,CAAC;AAAA,EACvC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAW;AAAA,EACX,YAAW;AAAA,EACX;AACF,MAAM;AACE,QAAA,SAASL,MAAAA,OAAY,MAAS;AAEpC,QAAM,SAASU,MAAAA,QAAQ,CAAC,UAAU,MAAM,MAAM;AAE5C,SAAAJ,2BAAA;AAAA,IAACK,OAAA;AAAA,IAAA;AAAA,MACC,OAAO;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,QACV,QAAQ,GAAG,UAAU,KAAK,YAAY,MAAM,EAAG;AAAA,QAC/C,UAAU,GAAG,UAAU,UAAU,aAAa,UAAU,OAAO;AAAA,MACjE;AAAA,MAEA,UAAA;AAAA,QAAAF,2BAAA;AAAA,UAACG,MAAA;AAAA,UAAA;AAAA,YACC,KAAK;AAAA,YACL,cAAY;AAAA,YACZ,QAAQ,EAAE,UAAU,CAAC,GAAG,GAAG,CAAC,EAAE;AAAA,YAC9B,KAAK,YAAY,CAAC,GAAE,CAAC,IAAI;AAAA,YACzB,IAAI;AAAA,cACF,WAAW;AAAA,cACX,OAAO;AAAA,cACP,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,YACA,WAAW,CAAC,EAAC,YAAW;AAChB,oBAAA,SAAS,CAAC,QAAsB;AAEpC,oBAAI,wBAAsB;AAC1B,oBAAI,mBAAiB;AAAA,cAAA,CACtB;AAAA,YACH;AAAA,YACA,MAAM;AAAA,YACN,OAAO;AAAA,cACL,cAAc;AAAA,cACd,UAAU;AAAA,cACV,eAAe;AAAA,cACf,YAAY;AAAA,cACZ,QAAQ,GAAG,UAAU,KAAK,YAAY,MAAM,EAAG;AAAA,YACjD;AAAA,YAEC,UAAA,CAAC,SAEEN,2BAAA,KAAAC,WAAA,UAAA,EAAA,UAAA;AAAA,cAAAE,2BAAA,IAAC,UAAS,EAAA;AAAA,cACTA,2BAAAA,IAAAI,YAAAA,aAAA,EAAY,YAAwB,SAAkB,aAA4B,CAAA;AAAA,cAClF,aAAaJ,2BAAA;AAAA,gBAAC;AAAA,gBAAA;AAAA,kBACb;AAAA,kBACA;AAAA,kBACA;AAAA,gBAAA;AAAA,cACF;AAAA,YAAA,EAAA,CACF,IACE;AAAA,UAAA;AAAA,QACN;AAAA,QACC,0CACEK,gBACC,EAAA,UAAA;AAAA,UAAAL,2BAAA,IAACM,WAAU,WAAA,EAAA;AAAA,UAAE;AAAA,QAAA,GACf;AAAA,MAAA;AAAA,IAAA;AAAA,EAAA;AAIR;AAEA,MAAM,WAAW,MAAK;AAEpBX,QAAA,SAAS,SAAS,cAAc,EAAE,IAAI,OAAO,UAAU;AACrD,WAAO,aAAa;AACpBI,iBAAA,aAAa,SAAS;AACf,WAAA,YAAY,KAAK,OAAO,MAAM;AACrC,WAAO,mBAAmB,KAAK,OAAO,WAAW,EAAE;AAChD,OAAA,OAAO,OAAM,MAAM;AACtBQ,iBAAA,kBAAkB,QAAQ;AAC1BR,iBAAA,aAAa,QAAQ;AAAA,KACpB,QAAQ;AAGJ,SAAA;AACT;;"}