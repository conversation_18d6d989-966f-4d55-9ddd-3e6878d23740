version: '3.8'

services:
  # Digital Twin Application
  digital-twin-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: digital-twin-app
    ports:
      - "8000:8000"  # API port
      - "8001:8001"  # Metrics port
    environment:
      - PYTHONPATH=/app
      - CONFIG_FILE=/app/config/digital_twin_config.yaml
      - LOG_LEVEL=INFO
    volumes:
      - .:/app
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - redis
      - postgres
      - kafka
    networks:
      - digital-twin-network
    restart: unless-stopped

  # Redis for caching and real-time data
  redis:
    image: redis:7-alpine
    container_name: digital-twin-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - digital-twin-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # PostgreSQL for persistent data
  postgres:
    image: postgres:14-alpine
    container_name: digital-twin-postgres
    environment:
      POSTGRES_DB: digital_twin
      POSTGRES_USER: dt_user
      POSTGRES_PASSWORD: dt_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - digital-twin-network
    restart: unless-stopped

  # Apache Kafka for message streaming
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: digital-twin-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - digital-twin-network
    restart: unless-stopped

  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: digital-twin-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
    networks:
      - digital-twin-network
    restart: unless-stopped

  # InfluxDB for time-series data
  influxdb:
    image: influxdb:2.0-alpine
    container_name: digital-twin-influxdb
    ports:
      - "8086:8086"
    environment:
      INFLUXDB_DB: digital_twin
      INFLUXDB_ADMIN_USER: admin
      INFLUXDB_ADMIN_PASSWORD: admin_password
      INFLUXDB_USER: dt_user
      INFLUXDB_USER_PASSWORD: dt_password
    volumes:
      - influxdb_data:/var/lib/influxdb2
    networks:
      - digital-twin-network
    restart: unless-stopped

  # Prometheus for monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: digital-twin-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - digital-twin-network
    restart: unless-stopped

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: digital-twin-grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin_password
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - digital-twin-network
    restart: unless-stopped
    depends_on:
      - prometheus

  # NGINX for load balancing and reverse proxy
  nginx:
    image: nginx:alpine
    container_name: digital-twin-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./config/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - digital-twin-app
    networks:
      - digital-twin-network
    restart: unless-stopped

  # OPC UA Server Simulator (for testing)
  opcua-server:
    image: open62541/open62541:latest
    container_name: digital-twin-opcua-server
    ports:
      - "4840:4840"
    command: /usr/bin/ua_server_ctt
    networks:
      - digital-twin-network
    restart: unless-stopped

  # Modbus Simulator (for testing)
  modbus-simulator:
    image: oitc/modbus-server
    container_name: digital-twin-modbus-simulator
    ports:
      - "502:502"
    environment:
      - MODBUS_PORT=502
    networks:
      - digital-twin-network
    restart: unless-stopped

  # Jupyter Lab for development and analysis
  jupyter:
    build:
      context: .
      dockerfile: Dockerfile.jupyter
    container_name: digital-twin-jupyter
    ports:
      - "8888:8888"
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=digital-twin-token
    volumes:
      - .:/home/<USER>/work
      - jupyter_data:/home/<USER>
    networks:
      - digital-twin-network
    restart: unless-stopped

  # MLflow for experiment tracking
  mlflow:
    image: python:3.9-slim
    container_name: digital-twin-mlflow
    ports:
      - "5000:5000"
    environment:
      - MLFLOW_BACKEND_STORE_URI=**********************************************/digital_twin
      - MLFLOW_DEFAULT_ARTIFACT_ROOT=/mlflow/artifacts
    volumes:
      - mlflow_data:/mlflow
    networks:
      - digital-twin-network
    depends_on:
      - postgres
    restart: unless-stopped
    command: bash -c "pip install mlflow psycopg2-binary && mlflow server --host 0.0.0.0 --port 5000 --backend-store-uri **********************************************/digital_twin --default-artifact-root /mlflow/artifacts"

  # Portainer for container management
  portainer:
    image: portainer/portainer-ce:latest
    container_name: digital-twin-portainer
    ports:
      - "9000:9000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_data:/data
    networks:
      - digital-twin-network
    restart: unless-stopped

networks:
  digital-twin-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  redis_data:
  postgres_data:
  influxdb_data:
  prometheus_data:
  grafana_data:
  jupyter_data:
  mlflow_data:
  portainer_data: