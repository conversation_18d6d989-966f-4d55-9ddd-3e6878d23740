// server/index.ts
import express2 from "express";

// server/routes.ts
import { createServer } from "http";
import { Server as SocketIOServer } from "socket.io";

// server/services/scadaSimulator.ts
import { nanoid } from "nanoid";

// server/services/mlPredictionService.ts
var MLPredictionService = class {
  models = /* @__PURE__ */ new Map();
  historicalData = /* @__PURE__ */ new Map();
  maxHistorySize = 1e3;
  // Keep last 1000 readings per tank
  constructor() {
    this.initializeModels();
  }
  initializeModels() {
    const tempModel = {
      id: "temp-prediction-v1",
      name: "Temperature Prediction Model",
      accuracy: 94.2,
      lastTrained: new Date(Date.now() - 24 * 60 * 60 * 1e3),
      // 24 hours ago
      status: "active",
      parameters: {
        lookbackWindow: 30,
        // 30 data points
        predictionHorizon: 10,
        // Predict 10 steps ahead
        features: ["temperature", "targetTemperature", "boilerStatus", "currentLevel", "ambientTemp"]
      }
    };
    const boilerModel = {
      id: "boiler-control-v2",
      name: "Boiler Control Optimization",
      accuracy: 89.7,
      lastTrained: new Date(Date.now() - 12 * 60 * 60 * 1e3),
      // 12 hours ago
      status: "active",
      parameters: {
        lookbackWindow: 20,
        predictionHorizon: 5,
        features: ["temperature", "targetTemperature", "energyConsumption", "efficiency", "ambientTemp"]
      }
    };
    const maintenanceModel = {
      id: "maintenance-predict-v1",
      name: "Predictive Maintenance",
      accuracy: 92.1,
      lastTrained: new Date(Date.now() - 6 * 60 * 60 * 1e3),
      // 6 hours ago
      status: "active",
      parameters: {
        lookbackWindow: 100,
        predictionHorizon: 1,
        features: ["temperature", "efficiency", "energyConsumption", "vibration", "pressure"]
      }
    };
    this.models.set(tempModel.id, tempModel);
    this.models.set(boilerModel.id, boilerModel);
    this.models.set(maintenanceModel.id, maintenanceModel);
  }
  updateHistoricalData(tanks) {
    tanks.forEach((tank) => {
      if (!this.historicalData.has(tank.id)) {
        this.historicalData.set(tank.id, []);
      }
      const history = this.historicalData.get(tank.id);
      history.push({ ...tank, lastUpdated: /* @__PURE__ */ new Date() });
      if (history.length > this.maxHistorySize) {
        history.splice(0, history.length - this.maxHistorySize);
      }
    });
  }
  generatePrediction(tank) {
    const history = this.historicalData.get(tank.id) || [];
    const prediction = this.simulateMLPrediction(tank, history);
    return prediction;
  }
  simulateMLPrediction(tank, history) {
    const now = Date.now();
    const predictedTemperature = this.predictTemperatureSequence(tank, history);
    const boilerAction = this.predictBoilerAction(tank, history);
    const energyOptimization = this.calculateEnergyOptimization(tank, history);
    const failureRisk = this.assessFailureRisk(tank, history);
    const maintenanceWindow = this.predictMaintenanceWindow(tank, history);
    return {
      nextBoilerAction: boilerAction.action,
      actionConfidence: boilerAction.confidence,
      predictedTemperature,
      timeToTarget: this.calculateTimeToTarget(tank, predictedTemperature),
      energyOptimization,
      failureRisk,
      maintenanceWindow
    };
  }
  predictTemperatureSequence(tank, history) {
    const sequence = [];
    const lookback = Math.min(10, history.length);
    if (lookback < 5) {
      for (let i = 0; i < 10; i++) {
        sequence.push(tank.temperature + (tank.targetTemperature - tank.temperature) * 0.1 * i);
      }
      return sequence;
    }
    const recentTemps = history.slice(-lookback).map((h) => h.temperature);
    const tempTrend = (recentTemps[recentTemps.length - 1] - recentTemps[0]) / lookback;
    let currentTemp = tank.temperature;
    const boilerEffect = tank.boilerStatus === "active" ? 0.8 : -0.3;
    const targetPull = (tank.targetTemperature - currentTemp) * 0.15;
    for (let i = 0; i < 10; i++) {
      const noise = (Math.random() - 0.5) * 0.5;
      const prediction = currentTemp + tempTrend + boilerEffect + targetPull + noise;
      sequence.push(Math.max(100, Math.min(200, prediction)));
      currentTemp = prediction;
    }
    return sequence;
  }
  predictBoilerAction(tank, history) {
    const tempDiff = tank.temperature - tank.targetTemperature;
    const recentHistory = history.slice(-5);
    let action = "no_action";
    let confidence = 0.5;
    if (tank.boilerStatus === "maintenance") {
      return { action: "maintain", confidence: 0.95 };
    }
    if (recentHistory.length >= 3) {
      const trends = recentHistory.map(
        (h, i) => i > 0 ? h.temperature - recentHistory[i - 1].temperature : 0
      ).slice(1);
      const avgTrend = trends.reduce((a, b) => a + b, 0) / trends.length;
      if (tempDiff < -8 && avgTrend < -0.5) {
        action = "start";
        confidence = Math.min(0.95, 0.7 + Math.abs(tempDiff) * 0.02);
      } else if (tempDiff > 8 && avgTrend > 0.5) {
        action = "stop";
        confidence = Math.min(0.95, 0.7 + Math.abs(tempDiff) * 0.02);
      } else if (Math.abs(tempDiff) < 3 && tank.boilerStatus === "active") {
        action = "stop";
        confidence = 0.75;
      } else if (Math.abs(tempDiff) > 5 && tank.boilerStatus === "inactive") {
        action = "start";
        confidence = 0.8;
      }
    }
    return { action, confidence };
  }
  calculateEnergyOptimization(tank, history) {
    const recentHistory = history.slice(-20);
    if (recentHistory.length < 5) return 75;
    const avgBoilerOnTime = recentHistory.filter((h) => h.boilerStatus === "active").length / recentHistory.length;
    const tempVariance = this.calculateVariance(recentHistory.map((h) => h.temperature));
    const baseEfficiency = 100 - tempVariance * 2 - Math.abs(avgBoilerOnTime - 0.4) * 50;
    return Math.max(20, Math.min(100, baseEfficiency));
  }
  assessFailureRisk(tank, history) {
    const recentHistory = history.slice(-50);
    if (recentHistory.length < 10) return 0.1;
    let riskScore = 0;
    const tempVariance = this.calculateVariance(recentHistory.map((h) => h.temperature));
    riskScore += tempVariance * 1e-3;
    const boilerSwitches = recentHistory.reduce(
      (count, h, i) => i > 0 && h.boilerStatus !== recentHistory[i - 1].boilerStatus ? count + 1 : count,
      0
    );
    riskScore += boilerSwitches / recentHistory.length * 0.3;
    const avgEfficiency = tank.efficiency || 85;
    if (avgEfficiency < 70) riskScore += 0.2;
    if (avgEfficiency < 50) riskScore += 0.3;
    return Math.max(0, Math.min(1, riskScore));
  }
  predictMaintenanceWindow(tank, history) {
    const failureRisk = this.assessFailureRisk(tank, history);
    if (failureRisk > 0.7) {
      return new Date(Date.now() + 3 * 24 * 60 * 60 * 1e3);
    } else if (failureRisk > 0.4) {
      return new Date(Date.now() + 14 * 24 * 60 * 60 * 1e3);
    } else if (failureRisk > 0.2) {
      return new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3);
    }
    return null;
  }
  calculateTimeToTarget(tank, predictedSequence) {
    const targetTemp = tank.targetTemperature;
    const tolerance = 2;
    for (let i = 0; i < predictedSequence.length; i++) {
      if (Math.abs(predictedSequence[i] - targetTemp) <= tolerance) {
        return (i + 1) * 2;
      }
    }
    return -1;
  }
  calculateVariance(values) {
    if (values.length < 2) return 0;
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const squaredDiffs = values.map((value) => Math.pow(value - mean, 2));
    return squaredDiffs.reduce((a, b) => a + b, 0) / values.length;
  }
  getModelStatus() {
    return Array.from(this.models.values());
  }
  retrain(modelId) {
    const model = this.models.get(modelId);
    if (!model) return false;
    model.status = "training";
    setTimeout(() => {
      model.status = "active";
      model.lastTrained = /* @__PURE__ */ new Date();
      model.accuracy = Math.min(99, model.accuracy + Math.random() * 2 - 1);
    }, 5e3);
    return true;
  }
  simulateRealtimeSensorData(tank) {
    const now = /* @__PURE__ */ new Date();
    const tempNoise = (Math.random() - 0.5) * 0.8;
    const levelNoise = (Math.random() - 0.5) * 50;
    const pressureBase = 2.5 + (tank.temperature - 150) * 0.01;
    const flowBase = tank.boilerStatus === "active" ? 15 + Math.random() * 5 : 2 + Math.random() * 3;
    return {
      ...tank,
      sensors: {
        temperatureSensor: {
          value: tank.temperature + tempNoise,
          timestamp: now,
          status: Math.random() > 0.05 ? "online" : "maintenance",
          accuracy: 99.2 + Math.random() * 0.6,
          lastCalibration: new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1e3)
        },
        levelSensor: {
          value: tank.currentLevel + levelNoise,
          timestamp: now,
          status: Math.random() > 0.03 ? "online" : "maintenance",
          accuracy: 98.8 + Math.random() * 1,
          lastCalibration: new Date(now.getTime() - Math.random() * 14 * 24 * 60 * 60 * 1e3)
        },
        pressureSensor: {
          value: pressureBase + (Math.random() - 0.5) * 0.2,
          timestamp: now,
          status: Math.random() > 0.02 ? "online" : "offline",
          accuracy: 99.5 + Math.random() * 0.4,
          lastCalibration: new Date(now.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1e3)
        },
        flowSensor: {
          value: flowBase,
          timestamp: now,
          status: Math.random() > 0.04 ? "online" : "maintenance",
          accuracy: 97.2 + Math.random() * 2,
          lastCalibration: new Date(now.getTime() - Math.random() * 21 * 24 * 60 * 60 * 1e3)
        }
      },
      efficiency: 85 + Math.random() * 10 - (tank.status === "critical" ? 15 : 0),
      maintenanceScore: Math.random() * 100,
      energyConsumption: tank.boilerStatus === "active" ? 25 + Math.random() * 10 : 5 + Math.random() * 3
    };
  }
};

// server/config/plant-layout.ts
var PLANT_LAYOUT_CONFIG = {
  // Tank positions matching real facility layout (scale 1:20m)
  tanks: [
    {
      id: 1,
      name: "ASP-01",
      position: [-6, 0, -8],
      capacity: 5e4,
      type: "storage",
      hasHeatingCoil: true,
      coilTurns: 3,
      connections: {
        hotOilInlet: "HO-SUPPLY-01",
        hotOilOutlet: "HO-RETURN-01",
        asphaltOutlet: "ASP-OUT-01",
        sensors: ["TT-01", "LT-01", "PT-01"]
      },
      heatingCoilConfig: {
        coilId: "HC-ASP-01",
        type: "3-turn",
        specifications: {
          turns: 3,
          coilDiameter: 1800,
          // mm (1.8m diameter)
          pipeDiameter: 50,
          // mm (2" pipe)
          totalLength: 17,
          // m (3 turns × π × 1.8m)
          material: "stainless_steel",
          wallThickness: 3,
          // mm
          surfaceFinish: "smooth"
        },
        positioning: {
          elevationFromBottom: 0.5,
          // m
          centerOffset: [0, 0],
          supportType: "bottom_mounted",
          accessPoints: ["AP-HC-01-INLET", "AP-HC-01-OUTLET", "AP-HC-01-DRAIN"]
        },
        thermalCharacteristics: {
          heatTransferArea: 2.67,
          // m² (π × 0.05 × 17)
          heatTransferCoefficient: 500,
          // W/m²K
          foulingFactor: 2e-4,
          // m²K/W
          thermalEfficiency: 85,
          // %
          maxHeatTransferRate: 75
          // kW
        },
        fluidDynamics: {
          maxFlowRate: 200,
          // L/min
          minFlowRate: 20,
          // L/min
          pressureDrop: 0.5,
          // bar at nominal flow
          reynoldsNumber: 15e3,
          prandtlNumber: 7.5
        },
        operationalLimits: {
          maxTemperature: 320,
          // °C
          maxPressure: 10,
          // bar
          maxThermalStress: 150,
          // MPa
          fatigueLifeCycles: 1e5
        }
      },
      thermalProperties: {
        fluidProperties: {
          density: 1e3,
          // kg/m³ (asphalt)
          specificHeat: 2100,
          // J/kg·K
          thermalConductivity: 0.15,
          // W/m·K
          viscosity: 0.5,
          // Pa·s at operating temperature
          expansionCoefficient: 7e-4
          // 1/K
        },
        tankMaterial: {
          density: 7850,
          // kg/m³ (steel)
          specificHeat: 460,
          // J/kg·K
          thermalConductivity: 50,
          // W/m·K
          thermalDiffusivity: 14e-6
          // m²/s
        },
        insulation: {
          type: "mineral_wool",
          thickness: 100,
          // mm
          thermalConductivity: 0.04,
          // W/m·K
          temperatureRating: 200,
          // °C
          weatherResistant: true
        },
        heatLoss: {
          ambientTemperature: 20,
          // °C
          windSpeed: 3,
          // m/s
          convectionCoefficient: 15,
          // W/m²·K
          radiationEmissivity: 0.9,
          totalHeatLossCoefficient: 2.5
          // W/m²·K
        }
      },
      geometry: {
        shape: "cylindrical",
        dimensions: {
          diameter: 4.5,
          // m
          height: 6,
          // m
          wallThickness: 12
          // mm
        },
        volumes: {
          totalVolume: 5e4,
          // L
          workingVolume: 45e3,
          // L
          deadVolume: 2e3,
          // L
          vaporSpace: 3e3
          // L
        },
        surfaces: {
          totalSurfaceArea: 120,
          // m²
          heatedSurfaceArea: 95,
          // m²
          insulatedSurfaceArea: 120
          // m²
        },
        structural: {
          designPressure: 3,
          // bar
          designTemperature: 200,
          // °C
          materialGrade: "A516-70",
          corrosionAllowance: 3,
          // mm
          windLoadRating: 1.5,
          // kPa
          seismicRating: "Zone 2"
        }
      },
      controlParameters: {
        temperature: {
          setpoint: 150,
          // °C
          deadband: 2,
          // °C
          rampRate: 1,
          // °C/min
          maxRampRate: 5,
          // °C/min
          alarmLimits: {
            highHigh: 180,
            // °C
            high: 170,
            // °C
            low: 130,
            // °C
            lowLow: 120
            // °C
          }
        },
        level: {
          setpoint: 75,
          // %
          deadband: 5,
          // %
          alarmLimits: {
            highHigh: 95,
            // %
            high: 90,
            // %
            low: 20,
            // %
            lowLow: 10
            // %
          }
        },
        pressure: {
          setpoint: 1.5,
          // bar
          deadband: 0.2,
          // bar
          alarmLimits: {
            highHigh: 2.8,
            // bar
            high: 2.5,
            // bar
            low: 0.5,
            // bar
            lowLow: 0.2
            // bar
          }
        },
        heating: {
          pidParameters: {
            kp: 2,
            ki: 0.1,
            kd: 0.05,
            outputLimits: [0, 100]
          },
          heatingStrategy: "continuous",
          energyOptimization: true,
          loadFollowing: true
        }
      }
    },
    {
      id: 2,
      name: "ASP-02",
      position: [-2, 0, -8],
      capacity: 5e4,
      type: "storage",
      hasHeatingCoil: true,
      coilTurns: 3,
      connections: {
        hotOilInlet: "HO-SUPPLY-02",
        hotOilOutlet: "HO-RETURN-02",
        asphaltOutlet: "ASP-OUT-02",
        sensors: ["TT-02", "LT-02", "PT-02"]
      }
    },
    {
      id: 3,
      name: "ASP-03",
      position: [2, 0, -8],
      capacity: 5e4,
      type: "storage",
      hasHeatingCoil: true,
      coilTurns: 3,
      connections: {
        hotOilInlet: "HO-SUPPLY-03",
        hotOilOutlet: "HO-RETURN-03",
        asphaltOutlet: "ASP-OUT-03",
        sensors: ["TT-03", "LT-03", "PT-03"]
      }
    },
    {
      id: 4,
      name: "ASP-04",
      position: [6, 0, -8],
      capacity: 5e4,
      type: "storage",
      hasHeatingCoil: true,
      coilTurns: 3,
      connections: {
        hotOilInlet: "HO-SUPPLY-04",
        hotOilOutlet: "HO-RETURN-04",
        asphaltOutlet: "ASP-OUT-04",
        sensors: ["TT-04", "LT-04", "PT-04"]
      }
    },
    {
      id: 5,
      name: "ASP-05",
      position: [-4, 0, -4],
      capacity: 5e4,
      type: "storage",
      hasHeatingCoil: true,
      coilTurns: 3,
      connections: {
        hotOilInlet: "HO-SUPPLY-05",
        hotOilOutlet: "HO-RETURN-05",
        asphaltOutlet: "ASP-OUT-05",
        sensors: ["TT-05", "LT-05", "PT-05"]
      }
    },
    {
      id: 6,
      name: "ASP-06",
      position: [0, 0, -4],
      capacity: 5e4,
      type: "storage",
      hasHeatingCoil: true,
      coilTurns: 3,
      connections: {
        hotOilInlet: "HO-SUPPLY-06",
        hotOilOutlet: "HO-RETURN-06",
        asphaltOutlet: "ASP-OUT-06",
        sensors: ["TT-06", "LT-06", "PT-06"]
      }
    },
    {
      id: 7,
      name: "ASP-07",
      position: [4, 0, -4],
      capacity: 5e4,
      type: "storage",
      hasHeatingCoil: true,
      coilTurns: 3,
      connections: {
        hotOilInlet: "HO-SUPPLY-07",
        hotOilOutlet: "HO-RETURN-07",
        asphaltOutlet: "ASP-OUT-07",
        sensors: ["TT-07", "LT-07", "PT-07"]
      }
    },
    {
      id: 8,
      name: "ASP-08",
      position: [-2, 0, 0],
      capacity: 5e4,
      type: "storage",
      hasHeatingCoil: true,
      coilTurns: 3,
      connections: {
        hotOilInlet: "HO-SUPPLY-08",
        hotOilOutlet: "HO-RETURN-08",
        asphaltOutlet: "ASP-OUT-08",
        sensors: ["TT-08", "LT-08", "PT-08"]
      }
    },
    {
      id: 9,
      name: "ASP-09",
      position: [2, 0, 0],
      capacity: 5e4,
      type: "storage",
      hasHeatingCoil: true,
      coilTurns: 3,
      connections: {
        hotOilInlet: "HO-SUPPLY-09",
        hotOilOutlet: "HO-RETURN-09",
        asphaltOutlet: "ASP-OUT-09",
        sensors: ["TT-09", "LT-09", "PT-09"]
      }
    },
    {
      id: 10,
      name: "ASP-10",
      position: [0, 0, 4],
      capacity: 5e4,
      type: "storage",
      hasHeatingCoil: true,
      coilTurns: 3,
      connections: {
        hotOilInlet: "HO-SUPPLY-10",
        hotOilOutlet: "HO-RETURN-10",
        asphaltOutlet: "ASP-OUT-10",
        sensors: ["TT-10", "LT-10", "PT-10"]
      }
    }
  ],
  // Hot-oil circulation system
  hotOilSystem: {
    boiler: {
      position: [-12, 0, 2],
      capacity: 2e3,
      // kW
      fuelType: "gas",
      maxTemperature: 300,
      // °C
      efficiency: 85
      // %
    }
  },
  // Asphalt loading stations
  loadingSystem: {
    loadingStations: [
      {
        id: "LS-01",
        position: [8, 0, -10],
        loadingArms: 2,
        maxFlowRate: 1e3,
        // L/min
        truckCapacity: 3e4,
        // L
        automationLevel: "semi-auto",
        connectedTanks: [1, 2, 3, 4]
      },
      {
        id: "LS-02",
        position: [12, 0, -6],
        loadingArms: 2,
        maxFlowRate: 1e3,
        // L/min
        truckCapacity: 3e4,
        // L
        automationLevel: "semi-auto",
        connectedTanks: [5, 6, 7]
      }
    ]
  }
};

// server/models/HotOilSystem.ts
function createHeatingCoil(tankId, position) {
  return {
    id: `HC-${tankId.toString().padStart(2, "0")}`,
    tankId,
    name: `Heating Coil Tank ${tankId}`,
    position,
    specifications: {
      turns: 3,
      diameter: 50,
      // mm
      coilDiameter: 1800,
      // mm (1.8m diameter coil)
      length: 17,
      // m (approximately 3 turns of 1.8m diameter)
      material: "stainless_steel",
      heatTransferArea: 2.67,
      // m² (π * 0.05 * 17)
      heatTransferCoefficient: 500
      // W/m²K
    },
    thermalData: {
      hotOilInletTemp: 280,
      hotOilOutletTemp: 260,
      hotOilFlowRate: 100,
      heatTransferRate: 50,
      efficiency: 85,
      tankTemperature: 150,
      ambientTemperature: 20
    },
    operationalData: {
      isActive: true,
      flowRate: 100,
      pressure: 3.5,
      deltaTemperature: 20,
      heatDuty: 50,
      operatingHours: 0
    },
    controlParameters: {
      targetHeatTransfer: 50,
      maxFlowRate: 200,
      minFlowRate: 20,
      temperatureControl: {
        setpoint: 150,
        deadband: 2,
        controlMode: "automatic"
      }
    },
    sensors: {
      inletTemperature: `TT-HC-${tankId}-IN`,
      outletTemperature: `TT-HC-${tankId}-OUT`,
      flowMeter: `FT-HC-${tankId}`,
      pressureSensor: `PT-HC-${tankId}`
    }
  };
}
function createHotOilBoiler() {
  return {
    id: "HOB-01",
    name: "Hot Oil Boiler #1",
    position: [-12, 0, 2],
    specifications: {
      heatingCapacity: 2e3,
      // kW
      fuelType: "natural_gas",
      maxTemperature: 300,
      minTemperature: 150,
      efficiency: 85,
      fuelConsumption: 200
      // m³/h
    },
    operationalData: {
      currentTemperature: 280,
      targetTemperature: 280,
      fuelFlow: 150,
      heatOutput: 1500,
      efficiency: 83,
      operatingHours: 0,
      lastMaintenance: /* @__PURE__ */ new Date()
    },
    controlParameters: {
      temperatureSetpoint: 280,
      temperatureTolerance: 5,
      pidParameters: {
        kp: 2,
        ki: 0.1,
        kd: 0.05
      },
      safetyLimits: {
        maxTemperature: 320,
        maxPressure: 10,
        maxFuelFlow: 250
      }
    },
    status: {
      isRunning: true,
      isHeating: true,
      alarmActive: false,
      maintenanceRequired: false,
      fuelLevel: 85
    }
  };
}

// server/models/AsphaltLoadingSystem.ts
function createLoadingStation(id, position, connectedTanks) {
  return {
    id,
    name: `Loading Station ${id}`,
    position,
    specifications: {
      numberOfArms: 2,
      maxFlowRate: 1e3,
      // L/min per arm
      maxTruckCapacity: 3e4,
      // L
      loadingHeight: 4,
      // m
      armReach: 8,
      // m
      automationLevel: "semi_automatic",
      safetyFeatures: [
        "Emergency Stop",
        "Truck Position Detection",
        "Overflow Protection",
        "Temperature Monitoring",
        "Grounding System"
      ]
    },
    operationalData: {
      currentFlowRate: 0,
      totalVolumeLoaded: 0,
      loadingTime: 0,
      truckPresent: false,
      loadingInProgress: false,
      lastLoadingTime: /* @__PURE__ */ new Date(),
      dailyLoadings: 0
    },
    truckData: {
      truckId: "",
      capacity: 0,
      currentVolume: 0,
      targetVolume: 0,
      arrivalTime: /* @__PURE__ */ new Date(),
      estimatedDepartureTime: /* @__PURE__ */ new Date(),
      driverInfo: {
        name: "",
        license: "",
        company: ""
      }
    },
    loadingArms: [
      createLoadingArm(`${id}-ARM-01`, id),
      createLoadingArm(`${id}-ARM-02`, id)
    ],
    controlSystem: {
      mode: "automatic",
      presetVolumes: [1e4, 15e3, 2e4, 25e3, 3e4],
      flowRateSetpoint: 800,
      temperatureSetpoint: 150,
      safetyInterlocks: {
        truckPositionOk: false,
        emergencyStopActive: false,
        armPositionOk: true,
        temperatureOk: true
      }
    },
    sensors: {
      truckDetection: `TD-${id}`,
      weightScale: `WS-${id}`,
      temperatureSensor: `TT-${id}`,
      flowMeter: `FT-${id}`,
      levelSensor: `LT-${id}`
    },
    status: {
      operational: true,
      maintenanceRequired: false,
      alarmActive: false,
      lastMaintenance: /* @__PURE__ */ new Date(),
      nextMaintenance: new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3)
      // 30 days
    }
  };
}
function createLoadingArm(id, stationId) {
  return {
    id,
    stationId,
    name: `Loading Arm ${id}`,
    specifications: {
      diameter: 100,
      // mm
      length: 8,
      // m
      maxFlowRate: 500,
      // L/min
      maxTemperature: 200,
      // °C
      maxPressure: 10,
      // bar
      material: "stainless_steel",
      insulationType: "steam_traced"
    },
    position: {
      basePosition: [0, 3, 0],
      currentPosition: [0, 3, 0],
      targetPosition: [0, 3, 0],
      rotation: 0,
      elevation: 0
    },
    operationalData: {
      flowRate: 0,
      pressure: 0,
      temperature: 150,
      isConnected: false,
      isFlowing: false,
      totalVolumeDelivered: 0
    },
    controlSystem: {
      positionControl: {
        mode: "manual",
        targetX: 0,
        targetY: 3,
        targetZ: 0
      },
      flowControl: {
        mode: "automatic",
        setpoint: 400,
        valve: {
          position: 0,
          type: "ball",
          actuator: "pneumatic"
        }
      }
    },
    sensors: {
      positionSensors: [`PS-${id}-X`, `PS-${id}-Y`, `PS-${id}-Z`],
      flowMeter: `FT-${id}`,
      pressureSensor: `PT-${id}`,
      temperatureSensor: `TT-${id}`,
      connectionSensor: `CS-${id}`
    },
    status: {
      operational: true,
      connected: false,
      alarmActive: false,
      maintenanceRequired: false
    }
  };
}

// server/config/pipe-routing.ts
var HOT_OIL_PIPE_NETWORK = {
  id: "HOT_OIL_CIRCULATION",
  name: "Hot-Oil Circulation System",
  type: "hot_oil_circulation",
  connections: [
    // Main supply line from boiler
    {
      id: "HO-MAIN-SUPPLY",
      name: "Hot-Oil Main Supply Line",
      type: "hot_oil_supply",
      startPoint: {
        type: "boiler",
        id: "HOB-01",
        position: [-12, 0, 2],
        connectionPort: "outlet"
      },
      endPoint: {
        type: "junction",
        id: "HO-JUNCTION-01",
        position: [-8, 1, 0],
        connectionPort: "inlet"
      },
      specifications: {
        diameter: 150,
        // mm
        length: 25,
        // m
        material: "carbon_steel",
        insulation: true,
        insulationType: "mineral_wool",
        insulationThickness: 100,
        tracing: "electric",
        maxPressure: 10,
        maxTemperature: 320,
        flowDirection: "unidirectional"
      },
      routing: {
        waypoints: [[-12, 1, 2], [-10, 1, 1], [-8, 1, 0]],
        elevation: 1,
        supportType: "pipe_rack",
        accessPoints: ["AP-HO-01", "AP-HO-02"]
      },
      instrumentation: {
        flowMeter: "FT-HO-MAIN",
        pressureSensors: ["PT-HO-01", "PT-HO-02"],
        temperatureSensors: ["TT-HO-01"],
        valves: ["HV-HO-01"],
        isolationValves: ["IV-HO-01", "IV-HO-02"]
      }
    },
    // Distribution lines to tanks (bottom row)
    {
      id: "HO-DIST-01",
      name: "Hot-Oil Distribution to Tank 01",
      type: "hot_oil_supply",
      startPoint: {
        type: "junction",
        id: "HO-JUNCTION-01",
        position: [-8, 1, 0],
        connectionPort: "outlet_01"
      },
      endPoint: {
        type: "tank",
        id: "ASP-01",
        position: [-6, -2, -8],
        connectionPort: "heating_coil_inlet"
      },
      specifications: {
        diameter: 50,
        length: 12,
        material: "stainless_steel",
        insulation: true,
        insulationType: "mineral_wool",
        insulationThickness: 50,
        maxPressure: 8,
        maxTemperature: 300,
        flowDirection: "unidirectional"
      },
      routing: {
        waypoints: [[-8, 1, 0], [-7, 1, -4], [-6, 0, -8], [-6, -2, -8]],
        elevation: 0,
        supportType: "underground",
        accessPoints: ["AP-HO-T01"]
      },
      instrumentation: {
        flowMeter: "FT-HO-T01",
        temperatureSensors: ["TT-HO-T01-IN"],
        valves: ["HV-HO-T01"]
      }
    },
    // Main return line to boiler
    {
      id: "HO-MAIN-RETURN",
      name: "Hot-Oil Main Return Line",
      type: "hot_oil_return",
      startPoint: {
        type: "junction",
        id: "HO-RETURN-JUNCTION",
        position: [-8, 0.5, 0],
        connectionPort: "inlet"
      },
      endPoint: {
        type: "boiler",
        id: "HOB-01",
        position: [-12, 0, 2],
        connectionPort: "inlet"
      },
      specifications: {
        diameter: 150,
        length: 25,
        material: "carbon_steel",
        insulation: true,
        insulationType: "mineral_wool",
        insulationThickness: 100,
        maxPressure: 6,
        maxTemperature: 280,
        flowDirection: "unidirectional"
      },
      routing: {
        waypoints: [[-8, 0.5, 0], [-10, 0.5, 1], [-12, 0.5, 2]],
        elevation: 0.5,
        supportType: "pipe_rack",
        accessPoints: ["AP-HO-RET-01", "AP-HO-RET-02"]
      },
      instrumentation: {
        flowMeter: "FT-HO-RETURN",
        pressureSensors: ["PT-HO-RET-01"],
        temperatureSensors: ["TT-HO-RET-01"],
        valves: ["HV-HO-RET-01"]
      }
    }
  ],
  mainHeaders: {
    supply: void 0,
    // Will be set to main supply line
    return: void 0
    // Will be set to main return line
  },
  operationalData: {
    totalLength: 250,
    // m
    totalVolume: 3500,
    // L
    operatingPressure: 4.5,
    // bar
    operatingTemperature: 280,
    // °C
    flowRate: 1e3,
    // L/min
    pressureDrop: 1.5
    // bar
  },
  controlSystems: {
    pressureControl: true,
    temperatureControl: true,
    flowControl: true,
    automaticValves: ["HV-HO-01", "HV-HO-RET-01"]
  }
};
var ASPHALT_PIPE_NETWORK = {
  id: "ASPHALT_DISTRIBUTION",
  name: "Asphalt Product Distribution System",
  type: "asphalt_distribution",
  connections: [
    // Main distribution header
    {
      id: "ASP-MAIN-HEADER",
      name: "Asphalt Main Distribution Header",
      type: "asphalt_product",
      startPoint: {
        type: "junction",
        id: "ASP-JUNCTION-MAIN",
        position: [0, 0.5, -6],
        connectionPort: "inlet"
      },
      endPoint: {
        type: "junction",
        id: "ASP-JUNCTION-LOADING",
        position: [8, 0.5, -8],
        connectionPort: "outlet"
      },
      specifications: {
        diameter: 200,
        length: 15,
        material: "carbon_steel",
        insulation: true,
        insulationType: "polyurethane",
        insulationThickness: 75,
        tracing: "steam",
        maxPressure: 6,
        maxTemperature: 180,
        flowDirection: "unidirectional"
      },
      routing: {
        waypoints: [[0, 0.5, -6], [4, 0.5, -7], [8, 0.5, -8]],
        elevation: 0.5,
        supportType: "pipe_rack",
        accessPoints: ["AP-ASP-01", "AP-ASP-02"]
      },
      instrumentation: {
        flowMeter: "FT-ASP-MAIN",
        pressureSensors: ["PT-ASP-01", "PT-ASP-02"],
        temperatureSensors: ["TT-ASP-01"],
        valves: ["AV-ASP-MAIN-01", "AV-ASP-MAIN-02"]
      }
    },
    // Loading station connections
    {
      id: "ASP-LOADING-01",
      name: "Asphalt Line to Loading Station 01",
      type: "asphalt_loading",
      startPoint: {
        type: "junction",
        id: "ASP-JUNCTION-LOADING",
        position: [8, 0.5, -8],
        connectionPort: "outlet_01"
      },
      endPoint: {
        type: "loading_station",
        id: "LS-01",
        position: [8, 0, -10],
        connectionPort: "inlet"
      },
      specifications: {
        diameter: 100,
        length: 5,
        material: "stainless_steel",
        insulation: true,
        insulationType: "polyurethane",
        insulationThickness: 50,
        tracing: "steam",
        maxPressure: 5,
        maxTemperature: 170,
        flowDirection: "unidirectional"
      },
      routing: {
        waypoints: [[8, 0.5, -8], [8, 0.2, -9], [8, 0, -10]],
        elevation: 0,
        supportType: "ground_level",
        accessPoints: ["AP-ASP-LS01"]
      },
      instrumentation: {
        flowMeter: "FT-ASP-LS01",
        temperatureSensors: ["TT-ASP-LS01"],
        valves: ["AV-ASP-LS01"]
      }
    }
  ],
  mainHeaders: {
    distribution: []
    // Will be populated with distribution lines
  },
  operationalData: {
    totalLength: 180,
    // m
    totalVolume: 2800,
    // L
    operatingPressure: 3.5,
    // bar
    operatingTemperature: 150,
    // °C
    flowRate: 800,
    // L/min
    pressureDrop: 1
    // bar
  },
  controlSystems: {
    pressureControl: true,
    temperatureControl: true,
    flowControl: true,
    automaticValves: ["AV-ASP-MAIN-01", "AV-ASP-LS01"]
  }
};
var PIPE_ROUTING_CONFIG = {
  networks: [HOT_OIL_PIPE_NETWORK, ASPHALT_PIPE_NETWORK],
  junctions: [
    {
      id: "HO-JUNCTION-01",
      name: "Hot-Oil Main Distribution Junction",
      position: [-8, 1, 0],
      type: "distribution",
      connections: ["HO-MAIN-SUPPLY", "HO-DIST-01", "HO-DIST-02", "HO-DIST-03"]
    },
    {
      id: "ASP-JUNCTION-MAIN",
      name: "Asphalt Main Collection Junction",
      position: [0, 0.5, -6],
      type: "collection",
      connections: ["ASP-T01-OUT", "ASP-T02-OUT", "ASP-T03-OUT", "ASP-MAIN-HEADER"]
    }
  ],
  valves: [
    {
      id: "HV-HO-01",
      name: "Hot-Oil Main Supply Valve",
      position: [-10, 1, 1.5],
      type: "control",
      actuator: "pneumatic",
      size: 150
      // mm
    },
    {
      id: "AV-ASP-MAIN-01",
      name: "Asphalt Main Header Valve",
      position: [2, 0.5, -6.5],
      type: "control",
      actuator: "electric",
      size: 200
      // mm
    }
  ],
  pumps: [
    {
      id: "HO-PUMP-01",
      name: "Hot-Oil Circulation Pump",
      position: [-11, 0, 2],
      type: "centrifugal",
      specifications: {
        flowRate: 1200,
        // L/min
        head: 50,
        // m
        power: 15
        // kW
      }
    },
    {
      id: "ASP-PUMP-01",
      name: "Asphalt Transfer Pump",
      position: [-1, 0, -6],
      type: "positive_displacement",
      specifications: {
        flowRate: 800,
        // L/min
        pressure: 6,
        // bar
        power: 12
        // kW
      }
    }
  ]
};

// server/services/pipeRoutingService.ts
var PipeRoutingService = class {
  networks = /* @__PURE__ */ new Map();
  flowCalculations = /* @__PURE__ */ new Map();
  systemStatus = /* @__PURE__ */ new Map();
  alarms = [];
  constructor() {
    this.initializeNetworks();
    this.calculateInitialFlows();
  }
  initializeNetworks() {
    this.networks.set(HOT_OIL_PIPE_NETWORK.id, HOT_OIL_PIPE_NETWORK);
    this.networks.set(ASPHALT_PIPE_NETWORK.id, ASPHALT_PIPE_NETWORK);
    this.networks.forEach((network, id) => {
      this.systemStatus.set(id, {
        networkId: id,
        operationalStatus: "normal",
        totalFlowRate: network.operationalData.flowRate,
        systemPressure: network.operationalData.operatingPressure,
        averageTemperature: network.operationalData.operatingTemperature,
        efficiency: 85,
        alarms: [],
        maintenanceRequired: false
      });
    });
    console.log(`Initialized ${this.networks.size} pipe networks`);
  }
  calculateInitialFlows() {
    this.networks.forEach((network) => {
      network.connections.forEach((pipe) => {
        const flowCalc = this.calculatePipeFlow(pipe, network.operationalData.flowRate);
        this.flowCalculations.set(pipe.id, flowCalc);
      });
    });
  }
  calculatePipeFlow(pipe, flowRate) {
    const diameter = pipe.specifications.diameter / 1e3;
    const area = Math.PI * Math.pow(diameter / 2, 2);
    const velocity = flowRate / 6e4 / area;
    const density = pipe.type.includes("hot_oil") ? 850 : 1e3;
    const viscosity = pipe.type.includes("hot_oil") ? 1e-3 : 0.1;
    const reynoldsNumber = density * velocity * diameter / viscosity;
    const roughness = 45e-6;
    const frictionFactor = this.calculateFrictionFactor(reynoldsNumber, roughness / diameter);
    const pressureDrop = frictionFactor * (pipe.specifications.length / diameter) * (density * Math.pow(velocity, 2)) / (2 * 1e5);
    let heatLoss = 0;
    if (pipe.type.includes("hot_oil") && pipe.specifications.insulation) {
      const tempDiff = pipe.specifications.maxTemperature - 20;
      const thermalConductivity = 0.04;
      const insulationThickness = (pipe.specifications.insulationThickness || 100) / 1e3;
      const pipeCircumference = Math.PI * diameter;
      heatLoss = 2 * Math.PI * thermalConductivity * pipe.specifications.length * tempDiff / Math.log((diameter + 2 * insulationThickness) / diameter) / 1e3;
    }
    return {
      pipeId: pipe.id,
      flowRate,
      velocity,
      pressureDrop,
      reynoldsNumber,
      frictionFactor,
      heatLoss
    };
  }
  calculateFrictionFactor(reynoldsNumber, relativeRoughness) {
    if (reynoldsNumber < 2300) {
      return 64 / reynoldsNumber;
    } else {
      return 0.25 / Math.pow(Math.log10(relativeRoughness / 3.7 + 5.74 / Math.pow(reynoldsNumber, 0.9)), 2);
    }
  }
  updateFlowRates(networkId, newFlowRate) {
    const network = this.networks.get(networkId);
    if (!network) return;
    network.operationalData.flowRate = newFlowRate;
    network.connections.forEach((pipe) => {
      const flowCalc = this.calculatePipeFlow(pipe, newFlowRate);
      this.flowCalculations.set(pipe.id, flowCalc);
    });
    const status = this.systemStatus.get(networkId);
    if (status) {
      status.totalFlowRate = newFlowRate;
      this.checkSystemAlarms(networkId);
    }
  }
  optimizeRoute(sourceId, destinationId, networkId) {
    const network = this.networks.get(networkId);
    if (!network) {
      throw new Error(`Network ${networkId} not found`);
    }
    const route = this.findShortestPath(sourceId, destinationId, network);
    const totalLength = route.reduce((sum, pipeId) => {
      const pipe = network.connections.find((p) => p.id === pipeId);
      return sum + (pipe?.specifications.length || 0);
    }, 0);
    const totalPressureDrop = route.reduce((sum, pipeId) => {
      const flowCalc = this.flowCalculations.get(pipeId);
      return sum + (flowCalc?.pressureDrop || 0);
    }, 0);
    return {
      sourceId,
      destinationId,
      optimalRoute: route,
      totalLength,
      estimatedPressureDrop: totalPressureDrop,
      estimatedFlowTime: totalLength / 100,
      // Simplified: 100 m/min flow speed
      energyConsumption: totalPressureDrop * network.operationalData.flowRate / 60 / 1e3
      // kWh
    };
  }
  findShortestPath(sourceId, destinationId, network) {
    const relevantPipes = network.connections.filter(
      (pipe) => pipe.startPoint.id === sourceId || pipe.endPoint.id === destinationId || pipe.startPoint.type === "junction" || pipe.endPoint.type === "junction"
    );
    return relevantPipes.map((pipe) => pipe.id);
  }
  checkSystemAlarms(networkId) {
    const network = this.networks.get(networkId);
    const status = this.systemStatus.get(networkId);
    if (!network || !status) return;
    this.alarms = this.alarms.filter(
      (alarm) => !network.connections.some((pipe) => pipe.id === alarm.pipeId)
    );
    network.connections.forEach((pipe) => {
      const flowCalc = this.flowCalculations.get(pipe.id);
      if (!flowCalc) return;
      if (flowCalc.pressureDrop > pipe.specifications.maxPressure * 0.8) {
        this.addAlarm({
          id: `ALARM-${Date.now()}-${pipe.id}`,
          pipeId: pipe.id,
          type: "pressure",
          severity: "high",
          message: `High pressure drop detected in ${pipe.name}`,
          timestamp: /* @__PURE__ */ new Date(),
          acknowledged: false
        });
      }
      if (flowCalc.velocity > 3) {
        this.addAlarm({
          id: `ALARM-${Date.now()}-${pipe.id}`,
          pipeId: pipe.id,
          type: "flow",
          severity: "medium",
          message: `High velocity detected in ${pipe.name}`,
          timestamp: /* @__PURE__ */ new Date(),
          acknowledged: false
        });
      }
    });
    status.alarms = this.alarms.filter(
      (alarm) => network.connections.some((pipe) => pipe.id === alarm.pipeId)
    );
    status.operationalStatus = status.alarms.length > 0 ? "warning" : "normal";
  }
  addAlarm(alarm) {
    this.alarms.push(alarm);
    console.log(`Pipe alarm: ${alarm.message}`);
  }
  // Public API methods
  getNetworks() {
    return Array.from(this.networks.values());
  }
  getNetwork(networkId) {
    return this.networks.get(networkId);
  }
  getFlowCalculations() {
    return new Map(this.flowCalculations);
  }
  getSystemStatus() {
    return new Map(this.systemStatus);
  }
  getAlarms() {
    return [...this.alarms];
  }
  acknowledgeAlarm(alarmId) {
    const alarm = this.alarms.find((a) => a.id === alarmId);
    if (alarm) {
      alarm.acknowledged = true;
      return true;
    }
    return false;
  }
  getPipeRouting() {
    return PIPE_ROUTING_CONFIG;
  }
};

// server/config/scada-sensor-mapping.ts
var TANK_SENSOR_MAPPINGS = [
  {
    groupName: "TANK_TEMPERATURES",
    description: "Tank temperature sensors for all asphalt storage tanks",
    updateRate: 1e3,
    // 1 second
    priority: "critical",
    tags: [
      {
        tagName: "TT_ASP_01",
        description: "Tank ASP-01 Temperature",
        sensorType: "temperature",
        dataType: "float",
        unit: "\xB0C",
        range: { min: 0, max: 200 },
        precision: 1,
        scanRate: 1e3,
        alarmLimits: {
          highHigh: 180,
          high: 170,
          low: 130,
          lowLow: 120
        },
        digitalTwinMapping: {
          componentType: "tank",
          componentId: "ASP-01",
          property: "temperature"
        },
        communication: {
          protocol: "modbus",
          address: "************",
          register: 40001
        },
        calibration: {
          lastCalibrated: /* @__PURE__ */ new Date("2024-01-15"),
          calibrationDue: /* @__PURE__ */ new Date("2025-01-15"),
          calibrationCertificate: "CAL-TT-01-2024",
          accuracy: 0.5
        }
      },
      {
        tagName: "TT_ASP_02",
        description: "Tank ASP-02 Temperature",
        sensorType: "temperature",
        dataType: "float",
        unit: "\xB0C",
        range: { min: 0, max: 200 },
        precision: 1,
        scanRate: 1e3,
        alarmLimits: {
          highHigh: 180,
          high: 170,
          low: 130,
          lowLow: 120
        },
        digitalTwinMapping: {
          componentType: "tank",
          componentId: "ASP-02",
          property: "temperature"
        },
        communication: {
          protocol: "modbus",
          address: "************",
          register: 40002
        },
        calibration: {
          lastCalibrated: /* @__PURE__ */ new Date("2024-01-15"),
          calibrationDue: /* @__PURE__ */ new Date("2025-01-15"),
          calibrationCertificate: "CAL-TT-02-2024",
          accuracy: 0.5
        }
      }
      // Additional tank temperature sensors would follow the same pattern
    ]
  },
  {
    groupName: "TANK_LEVELS",
    description: "Tank level sensors for all asphalt storage tanks",
    updateRate: 2e3,
    // 2 seconds
    priority: "high",
    tags: [
      {
        tagName: "LT_ASP_01",
        description: "Tank ASP-01 Level",
        sensorType: "level",
        dataType: "float",
        unit: "%",
        range: { min: 0, max: 100 },
        precision: 1,
        scanRate: 2e3,
        alarmLimits: {
          highHigh: 95,
          high: 90,
          low: 20,
          lowLow: 10
        },
        digitalTwinMapping: {
          componentType: "tank",
          componentId: "ASP-01",
          property: "currentLevel"
        },
        communication: {
          protocol: "modbus",
          address: "************",
          register: 40011
        },
        calibration: {
          lastCalibrated: /* @__PURE__ */ new Date("2024-01-15"),
          calibrationDue: /* @__PURE__ */ new Date("2025-01-15"),
          calibrationCertificate: "CAL-LT-01-2024",
          accuracy: 1
        }
      }
    ]
  },
  {
    groupName: "TANK_PRESSURES",
    description: "Tank pressure sensors for all asphalt storage tanks",
    updateRate: 1e3,
    // 1 second
    priority: "high",
    tags: [
      {
        tagName: "PT_ASP_01",
        description: "Tank ASP-01 Pressure",
        sensorType: "pressure",
        dataType: "float",
        unit: "bar",
        range: { min: 0, max: 5 },
        precision: 2,
        scanRate: 1e3,
        alarmLimits: {
          highHigh: 2.8,
          high: 2.5,
          low: 0.5,
          lowLow: 0.2
        },
        digitalTwinMapping: {
          componentType: "tank",
          componentId: "ASP-01",
          property: "pressure"
        },
        communication: {
          protocol: "modbus",
          address: "************",
          register: 40021
        },
        calibration: {
          lastCalibrated: /* @__PURE__ */ new Date("2024-01-15"),
          calibrationDue: /* @__PURE__ */ new Date("2025-01-15"),
          calibrationCertificate: "CAL-PT-01-2024",
          accuracy: 0.25
        }
      }
    ]
  }
];
var HOT_OIL_SENSOR_MAPPINGS = [
  {
    groupName: "HOT_OIL_BOILER",
    description: "Hot-oil boiler sensors and controls",
    updateRate: 500,
    // 0.5 seconds
    priority: "critical",
    tags: [
      {
        tagName: "TT_HOB_SUPPLY",
        description: "Hot-Oil Boiler Supply Temperature",
        sensorType: "temperature",
        dataType: "float",
        unit: "\xB0C",
        range: { min: 0, max: 350 },
        precision: 1,
        scanRate: 500,
        alarmLimits: {
          highHigh: 320,
          high: 300,
          low: 200,
          lowLow: 150
        },
        digitalTwinMapping: {
          componentType: "boiler",
          componentId: "HOB-01",
          property: "currentTemperature"
        },
        communication: {
          protocol: "modbus",
          address: "************",
          register: 40001
        },
        calibration: {
          lastCalibrated: /* @__PURE__ */ new Date("2024-01-15"),
          calibrationDue: /* @__PURE__ */ new Date("2025-01-15"),
          calibrationCertificate: "CAL-HOB-TT-2024",
          accuracy: 0.5
        }
      },
      {
        tagName: "FT_HOB_FUEL",
        description: "Hot-Oil Boiler Fuel Flow",
        sensorType: "flow",
        dataType: "float",
        unit: "m\xB3/h",
        range: { min: 0, max: 300 },
        precision: 1,
        scanRate: 1e3,
        alarmLimits: {
          highHigh: 280,
          high: 250,
          low: 10,
          lowLow: 0
        },
        digitalTwinMapping: {
          componentType: "boiler",
          componentId: "HOB-01",
          property: "fuelFlow"
        },
        communication: {
          protocol: "modbus",
          address: "************",
          register: 40002
        },
        calibration: {
          lastCalibrated: /* @__PURE__ */ new Date("2024-01-15"),
          calibrationDue: /* @__PURE__ */ new Date("2025-01-15"),
          calibrationCertificate: "CAL-HOB-FT-2024",
          accuracy: 1
        }
      }
    ]
  },
  {
    groupName: "HOT_OIL_CIRCULATION",
    description: "Hot-oil circulation system sensors",
    updateRate: 1e3,
    // 1 second
    priority: "high",
    tags: [
      {
        tagName: "FT_HO_MAIN_SUPPLY",
        description: "Hot-Oil Main Supply Flow",
        sensorType: "flow",
        dataType: "float",
        unit: "L/min",
        range: { min: 0, max: 1500 },
        precision: 1,
        scanRate: 1e3,
        alarmLimits: {
          highHigh: 1400,
          high: 1200,
          low: 100,
          lowLow: 50
        },
        digitalTwinMapping: {
          componentType: "pipe",
          componentId: "HO-MAIN-SUPPLY",
          property: "flowRate"
        },
        communication: {
          protocol: "modbus",
          address: "************",
          register: 40001
        },
        calibration: {
          lastCalibrated: /* @__PURE__ */ new Date("2024-01-15"),
          calibrationDue: /* @__PURE__ */ new Date("2025-01-15"),
          calibrationCertificate: "CAL-HO-FT-2024",
          accuracy: 1
        }
      }
    ]
  }
];
var LOADING_STATION_SENSOR_MAPPINGS = [
  {
    groupName: "LOADING_STATION_01",
    description: "Loading Station 01 sensors and controls",
    updateRate: 1e3,
    // 1 second
    priority: "high",
    tags: [
      {
        tagName: "FT_LS01_FLOW",
        description: "Loading Station 01 Flow Rate",
        sensorType: "flow",
        dataType: "float",
        unit: "L/min",
        range: { min: 0, max: 1200 },
        precision: 1,
        scanRate: 1e3,
        digitalTwinMapping: {
          componentType: "loading_station",
          componentId: "LS-01",
          property: "currentFlowRate"
        },
        communication: {
          protocol: "modbus",
          address: "************",
          register: 40001
        },
        calibration: {
          lastCalibrated: /* @__PURE__ */ new Date("2024-01-15"),
          calibrationDue: /* @__PURE__ */ new Date("2025-01-15"),
          calibrationCertificate: "CAL-LS01-FT-2024",
          accuracy: 1
        }
      },
      {
        tagName: "DI_LS01_TRUCK_PRESENT",
        description: "Loading Station 01 Truck Present",
        sensorType: "status",
        dataType: "boolean",
        unit: "",
        range: { min: 0, max: 1 },
        precision: 0,
        scanRate: 500,
        digitalTwinMapping: {
          componentType: "loading_station",
          componentId: "LS-01",
          property: "truckPresent"
        },
        communication: {
          protocol: "modbus",
          address: "************",
          register: 10001
        },
        calibration: {
          lastCalibrated: /* @__PURE__ */ new Date("2024-01-15"),
          calibrationDue: /* @__PURE__ */ new Date("2025-01-15"),
          calibrationCertificate: "CAL-LS01-DI-2024",
          accuracy: 100
        }
      }
    ]
  }
];
var SCADA_SENSOR_CONFIG = {
  tagGroups: [
    ...TANK_SENSOR_MAPPINGS,
    ...HOT_OIL_SENSOR_MAPPINGS,
    ...LOADING_STATION_SENSOR_MAPPINGS
  ],
  communicationSettings: {
    modbusSettings: {
      baudRate: 9600,
      dataBits: 8,
      stopBits: 1,
      parity: "none",
      timeout: 5e3
      // milliseconds
    },
    opcUaSettings: {
      endpointUrl: "opc.tcp://*************:4840",
      securityPolicy: "None",
      securityMode: "None",
      sessionTimeout: 6e4
      // milliseconds
    }
  },
  dataLogging: {
    enabled: true,
    logInterval: 6e4,
    // 1 minute
    retentionPeriod: 365,
    // days
    compressionEnabled: true
  },
  alarmSettings: {
    alarmDelay: 5,
    // seconds
    alarmRetryCount: 3,
    alarmAcknowledgmentTimeout: 300,
    // seconds
    alarmHistoryRetention: 90
    // days
  }
};

// server/services/scadaSensorMappingService.ts
var SCADASensorMappingService = class {
  sensorMap = /* @__PURE__ */ new Map();
  alarmConfigs = /* @__PURE__ */ new Map();
  activeAlarms = /* @__PURE__ */ new Map();
  sensorData = /* @__PURE__ */ new Map();
  lastUpdateTimes = /* @__PURE__ */ new Map();
  constructor() {
    this.initializeSensorMappings();
    this.initializeAlarmConfigurations();
  }
  initializeSensorMappings() {
    SCADA_SENSOR_CONFIG.tagGroups.forEach((group) => {
      group.tags.forEach((sensor) => {
        this.sensorMap.set(sensor.tagName, sensor);
      });
    });
    console.log(`Initialized ${this.sensorMap.size} SCADA sensor mappings`);
  }
  initializeAlarmConfigurations() {
    this.sensorMap.forEach((sensor, tagName) => {
      if (sensor.alarmLimits) {
        if (sensor.alarmLimits.highHigh !== void 0) {
          this.alarmConfigs.set(`${tagName}_HH`, {
            alarmId: `${tagName}_HH`,
            tagName,
            alarmType: "high",
            setpoint: sensor.alarmLimits.highHigh,
            deadband: (sensor.alarmLimits.highHigh - sensor.alarmLimits.high) / 2,
            delay: 5,
            priority: "critical",
            message: `${sensor.description} - High High Alarm`,
            actions: ["log", "notify", "shutdown"],
            acknowledgmentRequired: true,
            autoReset: false
          });
        }
        if (sensor.alarmLimits.high !== void 0) {
          this.alarmConfigs.set(`${tagName}_H`, {
            alarmId: `${tagName}_H`,
            tagName,
            alarmType: "high",
            setpoint: sensor.alarmLimits.high,
            deadband: 2,
            delay: 10,
            priority: "high",
            message: `${sensor.description} - High Alarm`,
            actions: ["log", "notify"],
            acknowledgmentRequired: true,
            autoReset: true
          });
        }
        if (sensor.alarmLimits.low !== void 0) {
          this.alarmConfigs.set(`${tagName}_L`, {
            alarmId: `${tagName}_L`,
            tagName,
            alarmType: "low",
            setpoint: sensor.alarmLimits.low,
            deadband: 2,
            delay: 10,
            priority: "high",
            message: `${sensor.description} - Low Alarm`,
            actions: ["log", "notify"],
            acknowledgmentRequired: true,
            autoReset: true
          });
        }
        if (sensor.alarmLimits.lowLow !== void 0) {
          this.alarmConfigs.set(`${tagName}_LL`, {
            alarmId: `${tagName}_LL`,
            tagName,
            alarmType: "low",
            setpoint: sensor.alarmLimits.lowLow,
            deadband: (sensor.alarmLimits.low - sensor.alarmLimits.lowLow) / 2,
            delay: 5,
            priority: "critical",
            message: `${sensor.description} - Low Low Alarm`,
            actions: ["log", "notify", "shutdown"],
            acknowledgmentRequired: true,
            autoReset: false
          });
        }
      }
    });
    console.log(`Initialized ${this.alarmConfigs.size} alarm configurations`);
  }
  updateSensorData(tagName, value, source = "simulation") {
    const sensor = this.sensorMap.get(tagName);
    if (!sensor) {
      console.warn(`Unknown sensor tag: ${tagName}`);
      return false;
    }
    const validation = this.validateSensorData(sensor, value);
    if (!validation.isValid) {
      console.error(`Sensor validation failed for ${tagName}:`, validation.errors);
      return false;
    }
    const dataPoint = {
      tagName,
      value: validation.transformedValue !== void 0 ? validation.transformedValue : value,
      timestamp: /* @__PURE__ */ new Date(),
      quality: "good",
      source
    };
    this.sensorData.set(tagName, dataPoint);
    this.lastUpdateTimes.set(tagName, dataPoint.timestamp);
    this.checkAlarms(tagName, dataPoint.value);
    return true;
  }
  validateSensorData(sensor, value) {
    const errors = [];
    const warnings = [];
    let transformedValue;
    if (sensor.dataType === "float" || sensor.dataType === "integer") {
      const numValue = typeof value === "number" ? value : parseFloat(value);
      if (isNaN(numValue)) {
        errors.push(`Invalid numeric value: ${value}`);
        return { isValid: false, errors, warnings };
      }
      if (numValue < sensor.range.min || numValue > sensor.range.max) {
        warnings.push(`Value ${numValue} outside range [${sensor.range.min}, ${sensor.range.max}]`);
      }
      transformedValue = sensor.dataType === "integer" ? Math.round(numValue) : parseFloat(numValue.toFixed(sensor.precision));
    } else if (sensor.dataType === "boolean") {
      if (typeof value === "boolean") {
        transformedValue = value;
      } else if (typeof value === "number") {
        transformedValue = value !== 0;
      } else if (typeof value === "string") {
        transformedValue = value.toLowerCase() === "true" || value === "1";
      } else {
        errors.push(`Invalid boolean value: ${value}`);
      }
    } else if (sensor.dataType === "string") {
      transformedValue = String(value);
    }
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      transformedValue
    };
  }
  checkAlarms(tagName, value) {
    this.alarmConfigs.forEach((alarmConfig, alarmId) => {
      if (alarmConfig.tagName !== tagName) return;
      const isAlarmActive = this.activeAlarms.has(alarmId);
      let shouldTrigger = false;
      if (alarmConfig.alarmType === "high") {
        shouldTrigger = value > alarmConfig.setpoint;
      } else if (alarmConfig.alarmType === "low") {
        shouldTrigger = value < alarmConfig.setpoint;
      }
      if (shouldTrigger && !isAlarmActive) {
        const alarmEvent = {
          alarmId,
          tagName,
          alarmType: alarmConfig.alarmType,
          currentValue: value,
          setpoint: alarmConfig.setpoint,
          message: alarmConfig.message,
          timestamp: /* @__PURE__ */ new Date(),
          priority: alarmConfig.priority,
          acknowledged: false
        };
        this.activeAlarms.set(alarmId, alarmEvent);
        console.log(`Alarm triggered: ${alarmConfig.message} (Value: ${value})`);
      } else if (!shouldTrigger && isAlarmActive) {
        const activeAlarm = this.activeAlarms.get(alarmId);
        const deadbandValue = alarmConfig.alarmType === "high" ? alarmConfig.setpoint - alarmConfig.deadband : alarmConfig.setpoint + alarmConfig.deadband;
        const shouldReset = alarmConfig.alarmType === "high" ? value < deadbandValue : value > deadbandValue;
        if (shouldReset && alarmConfig.autoReset) {
          this.activeAlarms.delete(alarmId);
          console.log(`Alarm auto-reset: ${alarmConfig.message} (Value: ${value})`);
        }
      }
    });
  }
  acknowledgeAlarm(alarmId, acknowledgedBy) {
    const alarm = this.activeAlarms.get(alarmId);
    if (!alarm) {
      return false;
    }
    alarm.acknowledged = true;
    alarm.acknowledgedBy = acknowledgedBy;
    alarm.acknowledgedAt = /* @__PURE__ */ new Date();
    console.log(`Alarm acknowledged: ${alarm.message} by ${acknowledgedBy}`);
    return true;
  }
  getSensorData(tagName) {
    if (tagName) {
      const data = this.sensorData.get(tagName);
      return data ? [data] : [];
    }
    return Array.from(this.sensorData.values());
  }
  getActiveAlarms() {
    return Array.from(this.activeAlarms.values());
  }
  getSensorMapping(tagName) {
    return this.sensorMap.get(tagName);
  }
  getAllSensorMappings() {
    return Array.from(this.sensorMap.values());
  }
  getTagGroups() {
    return SCADA_SENSOR_CONFIG.tagGroups;
  }
  mapToDigitalTwin(tagName, value) {
    const sensor = this.sensorMap.get(tagName);
    if (!sensor) {
      return null;
    }
    return {
      componentType: sensor.digitalTwinMapping.componentType,
      componentId: sensor.digitalTwinMapping.componentId,
      property: sensor.digitalTwinMapping.property,
      value
    };
  }
  generateSensorReport() {
    const now = /* @__PURE__ */ new Date();
    const staleThreshold = 6e4;
    const failedThreshold = 3e5;
    const sensorHealth = {};
    let activeSensors = 0;
    this.sensorMap.forEach((sensor, tagName) => {
      const lastUpdate = this.lastUpdateTimes.get(tagName);
      if (!lastUpdate) {
        sensorHealth[tagName] = "failed";
      } else {
        const timeSinceUpdate = now.getTime() - lastUpdate.getTime();
        if (timeSinceUpdate > failedThreshold) {
          sensorHealth[tagName] = "failed";
        } else if (timeSinceUpdate > staleThreshold) {
          sensorHealth[tagName] = "stale";
        } else {
          sensorHealth[tagName] = "healthy";
          activeSensors++;
        }
      }
    });
    const activeAlarms = Array.from(this.activeAlarms.values());
    const criticalAlarms = activeAlarms.filter((alarm) => alarm.priority === "critical").length;
    return {
      totalSensors: this.sensorMap.size,
      activeSensors,
      alarmCount: activeAlarms.length,
      criticalAlarms,
      lastUpdateSummary: Object.fromEntries(this.lastUpdateTimes),
      sensorHealth
    };
  }
};

// server/physics/HotOilCirculationPhysics.ts
var HotOilCirculationPhysics = class {
  fluidProperties;
  pumps = /* @__PURE__ */ new Map();
  pipes = /* @__PURE__ */ new Map();
  heatExchangers = /* @__PURE__ */ new Map();
  currentState;
  timeStep = 1;
  // seconds
  constructor() {
    this.initializeFluidProperties();
    this.initializeSystemComponents();
    this.currentState = this.createInitialState();
  }
  initializeFluidProperties() {
    this.fluidProperties = {
      density: 850,
      // kg/m³
      viscosity: 1e-3,
      // Pa·s (very low viscosity at high temp)
      specificHeat: 2200,
      // J/kg·K
      thermalConductivity: 0.12,
      // W/m·K
      temperature: 280
      // °C
    };
  }
  initializeSystemComponents() {
    this.pumps.set("HO-PUMP-01", {
      id: "HO-PUMP-01",
      type: "centrifugal",
      nominalFlowRate: 1200,
      // L/min
      nominalHead: 50,
      // m
      nominalSpeed: 1450,
      // RPM
      efficiency: 85,
      // %
      powerRating: 15,
      // kW
      impellerDiameter: 250,
      // mm
      curveCoefficients: {
        a: 55,
        // Head at zero flow
        b: -3e-4,
        // Head vs flow slope
        c: 0.85
        // Peak efficiency
      }
    });
    this.pipes.set("HO-MAIN-SUPPLY", {
      id: "HO-MAIN-SUPPLY",
      length: 25,
      // m
      diameter: 0.15,
      // m (150mm)
      roughness: 45e-6,
      // m (steel pipe)
      elevation: 1,
      // m
      insulation: {
        thickness: 0.1,
        // m
        thermalConductivity: 0.04
        // W/m·K
      }
    });
    for (let i = 1; i <= 10; i++) {
      this.heatExchangers.set(`HC-ASP-${i.toString().padStart(2, "0")}`, {
        id: `HC-ASP-${i.toString().padStart(2, "0")}`,
        type: "3-turn-coil",
        area: 2.67,
        // m²
        overallHeatTransferCoefficient: 500,
        // W/m²·K
        foulingFactor: 2e-4,
        // m²·K/W
        effectiveness: 85,
        // %
        hotSideFlowRate: 100,
        // L/min
        coldSideFlowRate: 0
        // No flow on cold side (tank contents)
      });
    }
  }
  createInitialState() {
    return {
      timestamp: /* @__PURE__ */ new Date(),
      flowRates: /* @__PURE__ */ new Map([
        ["HO-MAIN-SUPPLY", 1e3],
        ["HO-MAIN-RETURN", 1e3]
      ]),
      pressures: /* @__PURE__ */ new Map([
        ["HO-PUMP-DISCHARGE", 5],
        ["HO-PUMP-SUCTION", 1]
      ]),
      temperatures: /* @__PURE__ */ new Map([
        ["HO-SUPPLY", 280],
        ["HO-RETURN", 260]
      ]),
      pumpSpeeds: /* @__PURE__ */ new Map([
        ["HO-PUMP-01", 1450]
      ]),
      valvePositions: /* @__PURE__ */ new Map([
        ["HV-HO-01", 100]
      ]),
      heatTransferRates: /* @__PURE__ */ new Map()
    };
  }
  simulateTimeStep(deltaTime = this.timeStep) {
    this.updateFluidProperties();
    this.calculatePumpPerformance();
    this.calculatePressureDrops();
    this.calculateHeatTransfer();
    this.updateTemperatures();
    this.updateFlowDistribution();
    this.currentState.timestamp = /* @__PURE__ */ new Date();
    return { ...this.currentState };
  }
  updateFluidProperties() {
    const avgTemp = this.getAverageSystemTemperature();
    this.fluidProperties.temperature = avgTemp;
    this.fluidProperties.density = 900 - 0.7 * (avgTemp - 20);
    this.fluidProperties.viscosity = Math.exp(-0.02 * avgTemp + 3);
  }
  calculatePumpPerformance() {
    this.pumps.forEach((pump, pumpId) => {
      const speed = this.currentState.pumpSpeeds.get(pumpId) || pump.nominalSpeed;
      const flowRate = this.currentState.flowRates.get("HO-MAIN-SUPPLY") || 0;
      const speedRatio = speed / pump.nominalSpeed;
      const flowRatio = flowRate / pump.nominalFlowRate;
      const head = pump.curveCoefficients.a + pump.curveCoefficients.b * Math.pow(flowRate, 2) * Math.pow(speedRatio, 2);
      const efficiency = pump.curveCoefficients.c * (1 - Math.pow(flowRatio - 1, 2) * 0.3);
      const power = this.fluidProperties.density * 9.81 * head * (flowRate / 6e4) / (efficiency / 100) / 1e3;
      const pressure = this.fluidProperties.density * 9.81 * head / 1e5;
      this.currentState.pressures.set(`${pumpId}-DISCHARGE`, pressure);
    });
  }
  calculatePressureDrops() {
    this.pipes.forEach((pipe, pipeId) => {
      const flowRate = this.currentState.flowRates.get(pipeId) || 0;
      const velocity = flowRate / 6e4 / (Math.PI * Math.pow(pipe.diameter / 2, 2));
      const reynolds = this.fluidProperties.density * velocity * pipe.diameter / this.fluidProperties.viscosity;
      const relativeRoughness = pipe.roughness / pipe.diameter;
      let frictionFactor;
      if (reynolds < 2300) {
        frictionFactor = 64 / reynolds;
      } else {
        frictionFactor = 0.25 / Math.pow(
          Math.log10(relativeRoughness / 3.7 + 5.74 / Math.pow(reynolds, 0.9)),
          2
        );
      }
      const pressureDrop = frictionFactor * (pipe.length / pipe.diameter) * (this.fluidProperties.density * Math.pow(velocity, 2)) / (2 * 1e5);
      const elevationPressure = this.fluidProperties.density * 9.81 * pipe.elevation / 1e5;
      const totalPressureDrop = pressureDrop + elevationPressure;
      this.currentState.pressures.set(`${pipeId}-DROP`, totalPressureDrop);
    });
  }
  calculateHeatTransfer() {
    this.heatExchangers.forEach((hx, hxId) => {
      const hotInletTemp = this.currentState.temperatures.get("HO-SUPPLY") || 280;
      const coldInletTemp = this.getTankTemperature(hxId) || 150;
      const flowRate = hx.hotSideFlowRate / 6e4;
      const heatCapacityRate = this.fluidProperties.density * flowRate * this.fluidProperties.specificHeat;
      const tempDiff = hotInletTemp - coldInletTemp;
      const maxHeatTransfer = heatCapacityRate * tempDiff;
      const actualHeatTransfer = hx.effectiveness / 100 * maxHeatTransfer;
      const foulingEffect = 1 / (1 + hx.foulingFactor * hx.overallHeatTransferCoefficient);
      const adjustedHeatTransfer = actualHeatTransfer * foulingEffect;
      this.currentState.heatTransferRates.set(hxId, adjustedHeatTransfer / 1e3);
      const hotOutletTemp = hotInletTemp - adjustedHeatTransfer / heatCapacityRate;
      this.currentState.temperatures.set(`${hxId}-OUTLET`, hotOutletTemp);
    });
  }
  updateTemperatures() {
    let totalFlow = 0;
    let weightedTempSum = 0;
    this.heatExchangers.forEach((hx, hxId) => {
      const outletTemp = this.currentState.temperatures.get(`${hxId}-OUTLET`) || 260;
      const flow = hx.hotSideFlowRate;
      totalFlow += flow;
      weightedTempSum += outletTemp * flow;
    });
    const returnTemp = totalFlow > 0 ? weightedTempSum / totalFlow : 260;
    this.currentState.temperatures.set("HO-RETURN", returnTemp);
    const supplyTemp = this.currentState.temperatures.get("HO-SUPPLY") || 280;
    this.currentState.temperatures.set("HO-SUPPLY", supplyTemp);
  }
  updateFlowDistribution() {
    const mainFlow = this.currentState.flowRates.get("HO-MAIN-SUPPLY") || 1e3;
    const numberOfHeatExchangers = this.heatExchangers.size;
    const flowPerHeatExchanger = mainFlow / numberOfHeatExchangers;
    this.heatExchangers.forEach((hx, hxId) => {
      this.currentState.flowRates.set(hxId, flowPerHeatExchanger);
    });
    this.currentState.flowRates.set("HO-MAIN-RETURN", mainFlow);
  }
  getAverageSystemTemperature() {
    const supplyTemp = this.currentState.temperatures.get("HO-SUPPLY") || 280;
    const returnTemp = this.currentState.temperatures.get("HO-RETURN") || 260;
    return (supplyTemp + returnTemp) / 2;
  }
  getTankTemperature(heatExchangerId) {
    const tankId = heatExchangerId.replace("HC-ASP-", "");
    return 150;
  }
  // Public API methods
  getCurrentState() {
    return { ...this.currentState };
  }
  setPumpSpeed(pumpId, speed) {
    if (this.pumps.has(pumpId)) {
      this.currentState.pumpSpeeds.set(pumpId, speed);
      return true;
    }
    return false;
  }
  setValvePosition(valveId, position) {
    this.currentState.valvePositions.set(valveId, Math.max(0, Math.min(100, position)));
    return true;
  }
  getSystemEfficiency() {
    const totalHeatTransfer = Array.from(this.currentState.heatTransferRates.values()).reduce((sum, rate) => sum + rate, 0);
    const pumpPower = Array.from(this.pumps.values()).reduce((sum, pump) => sum + pump.powerRating, 0);
    return totalHeatTransfer / pumpPower;
  }
  getEnergyBalance() {
    const heatOutput = Array.from(this.currentState.heatTransferRates.values()).reduce((sum, rate) => sum + rate, 0);
    const heatLoss = this.pipes.size * 5;
    const heatInput = heatOutput + heatLoss;
    const efficiency = heatOutput / heatInput * 100;
    return {
      heatInput,
      heatOutput,
      heatLoss,
      efficiency
    };
  }
};

// server/physics/ThreeTurnCoilThermalSimulation.ts
var ThreeTurnCoilThermalSimulation = class {
  geometry;
  fluidProperties;
  boundaryConditions;
  currentState;
  segments = 30;
  // Number of segments for numerical simulation
  constructor(geometry, fluidProperties) {
    this.geometry = geometry;
    this.fluidProperties = fluidProperties;
    this.boundaryConditions = this.createDefaultBoundaryConditions();
    this.currentState = this.createInitialState();
  }
  createDefaultBoundaryConditions() {
    return {
      hotOilInletTemperature: 280,
      // °C
      hotOilFlowRate: 100,
      // L/min
      tankTemperature: 150,
      // °C
      ambientTemperature: 20,
      // °C
      tankLevel: 75,
      // %
      foulingResistance: 2e-4
      // m²·K/W
    };
  }
  createInitialState() {
    return {
      timestamp: /* @__PURE__ */ new Date(),
      hotOilTemperatures: new Array(this.segments).fill(280),
      pipeWallTemperatures: new Array(this.segments).fill(220),
      heatTransferRates: new Array(this.segments).fill(0),
      totalHeatTransfer: 0,
      hotOilOutletTemperature: 260,
      thermalEfficiency: 85,
      pressureDrop: 0.5,
      reynoldsNumber: 15e3,
      nusseltNumber: 80,
      heatTransferCoefficients: {
        hotOilSide: 1200,
        tankSide: 800,
        overall: 500
      }
    };
  }
  simulate(boundaryConditions, timeStep = 1) {
    this.boundaryConditions = boundaryConditions;
    this.updateFluidProperties();
    this.calculateFlowCharacteristics();
    this.calculateHeatTransferCoefficients();
    this.solveThermalDistribution();
    this.calculateOverallPerformance();
    this.currentState.timestamp = /* @__PURE__ */ new Date();
    return { ...this.currentState };
  }
  updateFluidProperties() {
    const avgHotOilTemp = (this.boundaryConditions.hotOilInletTemperature + this.currentState.hotOilOutletTemperature) / 2;
    this.fluidProperties.hotOil.temperature = avgHotOilTemp;
    this.fluidProperties.hotOil.density = 900 - 0.7 * (avgHotOilTemp - 20);
    this.fluidProperties.hotOil.viscosity = Math.exp(-0.02 * avgHotOilTemp + 3) / 1e3;
    this.fluidProperties.tankFluid.temperature = this.boundaryConditions.tankTemperature;
  }
  calculateFlowCharacteristics() {
    const velocity = this.calculateVelocity();
    const diameter = this.geometry.pipeDiameter;
    this.currentState.reynoldsNumber = this.fluidProperties.hotOil.density * velocity * diameter / this.fluidProperties.hotOil.viscosity;
    this.currentState.pressureDrop = this.calculatePressureDrop(velocity);
  }
  calculateVelocity() {
    const volumeFlowRate = this.boundaryConditions.hotOilFlowRate / 6e4;
    const crossSectionalArea = Math.PI * Math.pow(this.geometry.pipeDiameter / 2, 2);
    return volumeFlowRate / crossSectionalArea;
  }
  calculatePressureDrop(velocity) {
    const diameter = this.geometry.pipeDiameter;
    const length = this.geometry.totalLength;
    const roughness = 45e-6;
    const relativeRoughness = roughness / diameter;
    let frictionFactor;
    if (this.currentState.reynoldsNumber < 2300) {
      frictionFactor = 64 / this.currentState.reynoldsNumber;
    } else {
      frictionFactor = 0.25 / Math.pow(
        Math.log10(relativeRoughness / 3.7 + 5.74 / Math.pow(this.currentState.reynoldsNumber, 0.9)),
        2
      );
    }
    const curvatureRatio = diameter / this.geometry.coilDiameter;
    const curvatureFactor = 1 + 0.033 * Math.pow(curvatureRatio, 0.5);
    const pressureDrop = frictionFactor * curvatureFactor * (length / diameter) * (this.fluidProperties.hotOil.density * Math.pow(velocity, 2)) / (2 * 1e5);
    return pressureDrop;
  }
  calculateHeatTransferCoefficients() {
    const velocity = this.calculateVelocity();
    const diameter = this.geometry.pipeDiameter;
    let nusselt;
    if (this.currentState.reynoldsNumber > 2300) {
      const prandtl = this.fluidProperties.hotOil.viscosity * this.fluidProperties.hotOil.specificHeat / this.fluidProperties.hotOil.thermalConductivity;
      const curvatureRatio = diameter / this.geometry.coilDiameter;
      const curvatureCorrection = 1 + 3.5 * curvatureRatio;
      nusselt = 0.023 * Math.pow(this.currentState.reynoldsNumber, 0.8) * Math.pow(prandtl, 0.4) * curvatureCorrection;
    } else {
      nusselt = 3.66;
    }
    this.currentState.nusseltNumber = nusselt;
    this.currentState.heatTransferCoefficients.hotOilSide = nusselt * this.fluidProperties.hotOil.thermalConductivity / diameter;
    const tempDiff = Math.abs(this.boundaryConditions.tankTemperature - this.currentState.pipeWallTemperatures[0]);
    const grashof = this.calculateGrashofNumber(tempDiff);
    const prandtlTank = this.fluidProperties.tankFluid.viscosity * this.fluidProperties.tankFluid.specificHeat / this.fluidProperties.tankFluid.thermalConductivity;
    const rayleigh = grashof * prandtlTank;
    let nusseltTank;
    if (rayleigh < 1e9) {
      nusseltTank = 0.54 * Math.pow(rayleigh, 0.25);
    } else {
      nusseltTank = 0.15 * Math.pow(rayleigh, 0.33);
    }
    this.currentState.heatTransferCoefficients.tankSide = nusseltTank * this.fluidProperties.tankFluid.thermalConductivity / this.geometry.pipeDiameter;
    const pipeWallResistance = Math.log((this.geometry.pipeDiameter + this.geometry.pipeWallThickness) / this.geometry.pipeDiameter) / (2 * Math.PI * this.geometry.material.thermalConductivity);
    const totalResistance = 1 / this.currentState.heatTransferCoefficients.hotOilSide + pipeWallResistance + this.boundaryConditions.foulingResistance + 1 / this.currentState.heatTransferCoefficients.tankSide;
    this.currentState.heatTransferCoefficients.overall = 1 / totalResistance;
  }
  calculateGrashofNumber(tempDiff) {
    const beta = 1 / (this.fluidProperties.tankFluid.temperature + 273.15);
    const g = 9.81;
    const L = this.geometry.pipeDiameter;
    const nu = this.fluidProperties.tankFluid.viscosity / this.fluidProperties.tankFluid.density;
    return g * beta * tempDiff * Math.pow(L, 3) / Math.pow(nu, 2);
  }
  solveThermalDistribution() {
    const segmentLength = this.geometry.totalLength / this.segments;
    const massFlowRate = this.boundaryConditions.hotOilFlowRate / 6e4 * this.fluidProperties.hotOil.density;
    this.currentState.hotOilTemperatures[0] = this.boundaryConditions.hotOilInletTemperature;
    for (let i = 1; i < this.segments; i++) {
      const prevTemp = this.currentState.hotOilTemperatures[i - 1];
      const heatTransferArea = Math.PI * this.geometry.pipeDiameter * segmentLength;
      const effectiveTankTemp = this.getEffectiveTankTemperature(i);
      const tempDiff = prevTemp - effectiveTankTemp;
      const heatTransferRate = this.currentState.heatTransferCoefficients.overall * heatTransferArea * tempDiff;
      this.currentState.heatTransferRates[i] = heatTransferRate / segmentLength;
      const tempDrop = heatTransferRate / (massFlowRate * this.fluidProperties.hotOil.specificHeat);
      this.currentState.hotOilTemperatures[i] = prevTemp - tempDrop;
      this.currentState.pipeWallTemperatures[i] = (prevTemp + effectiveTankTemp) / 2;
    }
    this.currentState.hotOilOutletTemperature = this.currentState.hotOilTemperatures[this.segments - 1];
  }
  getEffectiveTankTemperature(segmentIndex) {
    const segmentHeight = segmentIndex / this.segments * this.geometry.coilDiameter;
    const liquidHeight = this.boundaryConditions.tankLevel / 100 * 6;
    if (segmentHeight > liquidHeight) {
      return this.boundaryConditions.ambientTemperature + (this.boundaryConditions.tankTemperature - this.boundaryConditions.ambientTemperature) * 0.3;
    } else {
      return this.boundaryConditions.tankTemperature;
    }
  }
  calculateOverallPerformance() {
    this.currentState.totalHeatTransfer = this.currentState.heatTransferRates.reduce((sum, rate) => sum + rate, 0) * this.geometry.totalLength / this.segments / 1e3;
    const maxPossibleHeatTransfer = this.boundaryConditions.hotOilFlowRate / 6e4 * this.fluidProperties.hotOil.density * this.fluidProperties.hotOil.specificHeat * (this.boundaryConditions.hotOilInletTemperature - this.boundaryConditions.tankTemperature) / 1e3;
    this.currentState.thermalEfficiency = this.currentState.totalHeatTransfer / maxPossibleHeatTransfer * 100;
  }
  // Public API methods
  getCurrentState() {
    return { ...this.currentState };
  }
  setBoundaryConditions(conditions) {
    this.boundaryConditions = { ...this.boundaryConditions, ...conditions };
  }
  getCoilGeometry() {
    return { ...this.geometry };
  }
  getFluidProperties() {
    return { ...this.fluidProperties };
  }
  calculateOptimalFlowRate(targetHeatTransfer) {
    let flowRate = 50;
    let bestFlowRate = flowRate;
    let minError = Infinity;
    for (let i = 0; i < 20; i++) {
      const testConditions = { ...this.boundaryConditions, hotOilFlowRate: flowRate };
      const testState = this.simulate(testConditions);
      const error = Math.abs(testState.totalHeatTransfer - targetHeatTransfer);
      if (error < minError) {
        minError = error;
        bestFlowRate = flowRate;
      }
      if (testState.totalHeatTransfer < targetHeatTransfer) {
        flowRate += 10;
      } else {
        flowRate -= 5;
      }
      if (flowRate < 10) flowRate = 10;
      if (flowRate > 300) flowRate = 300;
    }
    return bestFlowRate;
  }
};

// server/physics/AsphaltFlowSimulation.ts
var AsphaltFlowSimulation = class {
  asphaltProperties;
  pipes = /* @__PURE__ */ new Map();
  valves = /* @__PURE__ */ new Map();
  pumps = /* @__PURE__ */ new Map();
  currentState;
  ambientTemperature = 20;
  // °C
  constructor(asphaltGrade = "PG64-22") {
    this.asphaltProperties = this.getAsphaltProperties(asphaltGrade);
    this.initializeSystemComponents();
    this.currentState = this.createInitialState();
  }
  getAsphaltProperties(grade) {
    const properties = {
      "PG64-22": {
        grade: "PG64-22",
        density: 1020,
        // kg/m³
        baseViscosity: 0.4,
        // Pa·s at 135°C
        referenceTemperature: 135,
        // °C
        viscosityTemperatureIndex: 2.5,
        penetration: 85,
        // dmm
        softeningPoint: 48,
        // °C
        flashPoint: 230,
        // °C
        specificHeat: 2e3,
        // J/kg·K
        thermalConductivity: 0.17
        // W/m·K
      },
      "PG70-28": {
        grade: "PG70-28",
        density: 1030,
        baseViscosity: 0.5,
        referenceTemperature: 135,
        viscosityTemperatureIndex: 2.3,
        penetration: 65,
        softeningPoint: 52,
        flashPoint: 240,
        specificHeat: 2e3,
        thermalConductivity: 0.17
      },
      "PG76-22": {
        grade: "PG76-22",
        density: 1040,
        baseViscosity: 0.6,
        referenceTemperature: 135,
        viscosityTemperatureIndex: 2.1,
        penetration: 55,
        softeningPoint: 58,
        flashPoint: 250,
        specificHeat: 2e3,
        thermalConductivity: 0.17
      },
      "AC-20": {
        grade: "AC-20",
        density: 1010,
        baseViscosity: 0.35,
        referenceTemperature: 135,
        viscosityTemperatureIndex: 2.7,
        penetration: 95,
        softeningPoint: 45,
        flashPoint: 220,
        specificHeat: 2e3,
        thermalConductivity: 0.17
      },
      "AC-30": {
        grade: "AC-30",
        density: 1015,
        baseViscosity: 0.45,
        referenceTemperature: 135,
        viscosityTemperatureIndex: 2.4,
        penetration: 75,
        softeningPoint: 50,
        flashPoint: 235,
        specificHeat: 2e3,
        thermalConductivity: 0.17
      }
    };
    return properties[grade];
  }
  initializeSystemComponents() {
    this.pipes.set("ASP-MAIN-HEADER", {
      id: "ASP-MAIN-HEADER",
      startPosition: [0, 0.5, -6],
      endPosition: [8, 0.5, -8],
      diameter: 0.2,
      // 200mm
      length: 15,
      roughness: 45e-6,
      elevation: 0.5,
      insulation: {
        type: "steam_traced",
        temperature: 160,
        efficiency: 85
      },
      material: "carbon_steel"
    });
    this.pipes.set("ASP-LOADING-01", {
      id: "ASP-LOADING-01",
      startPosition: [8, 0.5, -8],
      endPosition: [8, 0, -10],
      diameter: 0.1,
      // 100mm
      length: 5,
      roughness: 45e-6,
      elevation: 0,
      insulation: {
        type: "steam_traced",
        temperature: 160,
        efficiency: 85
      },
      material: "stainless_steel"
    });
    this.valves.set("AV-ASP-MAIN-01", {
      id: "AV-ASP-MAIN-01",
      position: [2, 0.5, -6.5],
      type: "ball",
      diameter: 0.2,
      cvCoefficient: 150,
      currentPosition: 100,
      characteristics: "linear",
      actuator: {
        type: "pneumatic",
        responseTime: 5,
        accuracy: 1
      }
    });
    this.pumps.set("ASP-PUMP-01", {
      id: "ASP-PUMP-01",
      type: "gear",
      maxFlowRate: 800,
      maxPressure: 6,
      maxViscosity: 2,
      efficiency: 85,
      speedRange: [100, 1200],
      temperatureRange: [120, 180]
    });
  }
  createInitialState() {
    return {
      timestamp: /* @__PURE__ */ new Date(),
      flowRates: /* @__PURE__ */ new Map([
        ["ASP-MAIN-HEADER", 600],
        ["ASP-LOADING-01", 300]
      ]),
      pressures: /* @__PURE__ */ new Map([
        ["ASP-PUMP-DISCHARGE", 4],
        ["ASP-MAIN-HEADER", 3.5]
      ]),
      temperatures: /* @__PURE__ */ new Map([
        ["ASP-MAIN-HEADER", 150],
        ["ASP-LOADING-01", 148]
      ]),
      viscosities: /* @__PURE__ */ new Map(),
      reynoldsNumbers: /* @__PURE__ */ new Map(),
      pressureDrops: /* @__PURE__ */ new Map(),
      valvePositions: /* @__PURE__ */ new Map([
        ["AV-ASP-MAIN-01", 100]
      ]),
      pumpSpeeds: /* @__PURE__ */ new Map([
        ["ASP-PUMP-01", 800]
      ]),
      heatLosses: /* @__PURE__ */ new Map()
    };
  }
  simulate(timeStep = 1) {
    this.updateViscosities();
    this.calculateFlowCharacteristics();
    this.calculatePressureDrops();
    this.calculateHeatTransfer();
    this.updateValveFlows();
    this.calculatePumpPerformance();
    this.updateSystemPressures();
    this.currentState.timestamp = /* @__PURE__ */ new Date();
    return { ...this.currentState };
  }
  updateViscosities() {
    this.currentState.temperatures.forEach((temperature, componentId) => {
      const viscosity = this.calculateViscosity(temperature);
      this.currentState.viscosities.set(componentId, viscosity);
    });
  }
  calculateViscosity(temperature) {
    const tempKelvin = temperature + 273.15;
    const refTempKelvin = this.asphaltProperties.referenceTemperature + 273.15;
    const logViscosity = Math.log10(this.asphaltProperties.baseViscosity) + this.asphaltProperties.viscosityTemperatureIndex * Math.log10(refTempKelvin / tempKelvin);
    return Math.pow(10, logViscosity);
  }
  calculateFlowCharacteristics() {
    this.pipes.forEach((pipe, pipeId) => {
      const flowRate = this.currentState.flowRates.get(pipeId) || 0;
      const temperature = this.currentState.temperatures.get(pipeId) || 150;
      const viscosity = this.currentState.viscosities.get(pipeId) || 0.1;
      const velocity = flowRate / 6e4 / (Math.PI * Math.pow(pipe.diameter / 2, 2));
      const reynoldsNumber = this.asphaltProperties.density * velocity * pipe.diameter / viscosity;
      this.currentState.reynoldsNumbers.set(pipeId, reynoldsNumber);
    });
  }
  calculatePressureDrops() {
    this.pipes.forEach((pipe, pipeId) => {
      const flowRate = this.currentState.flowRates.get(pipeId) || 0;
      const viscosity = this.currentState.viscosities.get(pipeId) || 0.1;
      const reynoldsNumber = this.currentState.reynoldsNumbers.get(pipeId) || 1e3;
      const velocity = flowRate / 6e4 / (Math.PI * Math.pow(pipe.diameter / 2, 2));
      let frictionFactor;
      if (reynoldsNumber < 2100) {
        frictionFactor = 64 / reynoldsNumber;
      } else {
        const relativeRoughness = pipe.roughness / pipe.diameter;
        frictionFactor = 0.25 / Math.pow(
          Math.log10(relativeRoughness / 3.7 + 5.74 / Math.pow(reynoldsNumber, 0.9)),
          2
        );
        const viscosityCorrection = 1 + 0.1 * Math.log10(viscosity / 1e-3);
        frictionFactor *= viscosityCorrection;
      }
      const pressureDrop = frictionFactor * (pipe.length / pipe.diameter) * (this.asphaltProperties.density * Math.pow(velocity, 2)) / (2 * 1e5);
      const elevationPressure = this.asphaltProperties.density * 9.81 * pipe.elevation / 1e5;
      const totalPressureDrop = pressureDrop + elevationPressure;
      this.currentState.pressureDrops.set(pipeId, totalPressureDrop);
    });
  }
  calculateHeatTransfer() {
    this.pipes.forEach((pipe, pipeId) => {
      const temperature = this.currentState.temperatures.get(pipeId) || 150;
      const flowRate = this.currentState.flowRates.get(pipeId) || 0;
      let heatLoss = 0;
      if (pipe.insulation.type !== "none") {
        const surfaceArea = Math.PI * pipe.diameter * pipe.length;
        const tempDiff = temperature - this.ambientTemperature;
        const overallU = 2;
        heatLoss = overallU * surfaceArea * tempDiff / 1e3;
        if (pipe.insulation.type !== "none") {
          const tracingHeat = pipe.insulation.efficiency / 100 * heatLoss * 1.2;
          heatLoss -= tracingHeat;
        }
      }
      this.currentState.heatLosses.set(pipeId, Math.max(0, heatLoss));
      if (flowRate > 0) {
        const massFlowRate = flowRate / 6e4 * this.asphaltProperties.density;
        const tempDrop = heatLoss * 1e3 / (massFlowRate * this.asphaltProperties.specificHeat);
        const newTemperature = temperature - tempDrop;
        this.currentState.temperatures.set(pipeId, Math.max(newTemperature, this.ambientTemperature + 20));
      }
    });
  }
  updateValveFlows() {
    this.valves.forEach((valve, valveId) => {
      const position = this.currentState.valvePositions.get(valveId) || 100;
      const upstreamPressure = this.currentState.pressures.get("ASP-PUMP-DISCHARGE") || 4;
      const downstreamPressure = this.currentState.pressures.get("ASP-MAIN-HEADER") || 3.5;
      let flowCoefficient;
      switch (valve.characteristics) {
        case "linear":
          flowCoefficient = valve.cvCoefficient * (position / 100);
          break;
        case "equal_percentage":
          flowCoefficient = valve.cvCoefficient * Math.pow(position / 100, 2);
          break;
        case "quick_opening":
          flowCoefficient = valve.cvCoefficient * Math.sqrt(position / 100);
          break;
        default:
          flowCoefficient = valve.cvCoefficient * (position / 100);
      }
      const pressureDrop = Math.max(0.1, upstreamPressure - downstreamPressure);
      const specificGravity = this.asphaltProperties.density / 1e3;
      const flowRate = flowCoefficient * Math.sqrt(pressureDrop / specificGravity);
      const connectedPipe = "ASP-MAIN-HEADER";
      this.currentState.flowRates.set(connectedPipe, flowRate);
    });
  }
  calculatePumpPerformance() {
    this.pumps.forEach((pump, pumpId) => {
      const speed = this.currentState.pumpSpeeds.get(pumpId) || 800;
      const temperature = this.currentState.temperatures.get("ASP-MAIN-HEADER") || 150;
      const viscosity = this.currentState.viscosities.get("ASP-MAIN-HEADER") || 0.1;
      if (temperature < pump.temperatureRange[0] || temperature > pump.temperatureRange[1]) {
        console.warn(`Pump ${pumpId} operating outside temperature range`);
      }
      if (viscosity > pump.maxViscosity) {
        console.warn(`Pump ${pumpId} operating above maximum viscosity`);
      }
      const speedRatio = speed / 1e3;
      const viscosityEffect = Math.max(0.5, 1 - (viscosity - 0.1) / pump.maxViscosity);
      const flowRate = pump.maxFlowRate * speedRatio * viscosityEffect;
      const pressure = pump.maxPressure * speedRatio * Math.sqrt(viscosityEffect);
      this.currentState.flowRates.set("ASP-MAIN-HEADER", flowRate);
      this.currentState.pressures.set("ASP-PUMP-DISCHARGE", pressure);
    });
  }
  updateSystemPressures() {
    let currentPressure = this.currentState.pressures.get("ASP-PUMP-DISCHARGE") || 4;
    this.pipes.forEach((pipe, pipeId) => {
      const pressureDrop = this.currentState.pressureDrops.get(pipeId) || 0;
      currentPressure -= pressureDrop;
      this.currentState.pressures.set(pipeId, Math.max(0.5, currentPressure));
    });
  }
  // Public API methods
  getCurrentState() {
    return { ...this.currentState };
  }
  setValvePosition(valveId, position) {
    if (this.valves.has(valveId)) {
      this.currentState.valvePositions.set(valveId, Math.max(0, Math.min(100, position)));
      return true;
    }
    return false;
  }
  setPumpSpeed(pumpId, speed) {
    const pump = this.pumps.get(pumpId);
    if (pump) {
      const clampedSpeed = Math.max(pump.speedRange[0], Math.min(pump.speedRange[1], speed));
      this.currentState.pumpSpeeds.set(pumpId, clampedSpeed);
      return true;
    }
    return false;
  }
  getCurrentAsphaltProperties() {
    return { ...this.asphaltProperties };
  }
  calculateOptimalTemperature(targetViscosity) {
    const logTargetViscosity = Math.log10(targetViscosity);
    const logBaseViscosity = Math.log10(this.asphaltProperties.baseViscosity);
    const refTempKelvin = this.asphaltProperties.referenceTemperature + 273.15;
    const tempRatio = Math.pow(10, (logBaseViscosity - logTargetViscosity) / this.asphaltProperties.viscosityTemperatureIndex);
    const targetTempKelvin = refTempKelvin / tempRatio;
    return targetTempKelvin - 273.15;
  }
  getFlowEfficiency() {
    const totalFlowRate = Array.from(this.currentState.flowRates.values()).reduce((sum, rate) => sum + rate, 0);
    const totalPressureDrop = Array.from(this.currentState.pressureDrops.values()).reduce((sum, drop) => sum + drop, 0);
    const totalHeatLoss = Array.from(this.currentState.heatLosses.values()).reduce((sum, loss) => sum + loss, 0);
    const hydraulicEfficiency = Math.max(0, 100 - totalPressureDrop * 10);
    const thermalEfficiency = Math.max(0, 100 - totalHeatLoss * 5);
    return (hydraulicEfficiency + thermalEfficiency) / 2;
  }
};

// server/physics/HotOilBoilerSimulation.ts
var HotOilBoilerSimulation = class {
  specifications;
  safetySystem;
  controlSystem;
  operationalState;
  alarms;
  ambientTemperature = 20;
  // °C
  simulationStartTime = /* @__PURE__ */ new Date();
  constructor(specifications) {
    this.specifications = specifications;
    this.initializeSafetySystem();
    this.initializeControlSystem();
    this.operationalState = this.createInitialState();
    this.alarms = this.createInitialAlarms();
  }
  initializeSafetySystem() {
    this.safetySystem = {
      flameFailureDetection: true,
      lowWaterCutoff: true,
      highTemperatureShutdown: true,
      highPressureShutdown: true,
      emergencyShutdown: false,
      gasLeakDetection: true,
      ventilationMonitoring: true,
      safetyValves: {
        count: 2,
        setpressure: 12,
        // bar
        capacity: 5e3
        // kg/h
      }
    };
  }
  initializeControlSystem() {
    this.controlSystem = {
      temperatureControl: {
        mode: "automatic",
        setpoint: 280,
        // °C
        deadband: 2,
        // °C
        pidParameters: {
          kp: 2,
          ki: 0.1,
          kd: 0.05,
          outputLimits: [10, 100]
          // 10-100% output
        }
      },
      fuelControl: {
        mode: "modulating",
        modulationRange: [20, 100],
        // 20-100% capacity
        airFuelRatio: 10.5,
        // Stoichiometric ratio
        excessAir: 15
        // % excess air
      },
      sequenceControl: {
        startupSequence: [
          "pre_purge",
          "ignition_preparation",
          "pilot_ignition",
          "main_flame_ignition",
          "normal_operation"
        ],
        shutdownSequence: [
          "fuel_cutoff",
          "post_purge",
          "fan_shutdown",
          "system_isolation"
        ],
        purgeTime: 30,
        // seconds
        ignitionTime: 10
        // seconds
      }
    };
  }
  createInitialState() {
    return {
      timestamp: /* @__PURE__ */ new Date(),
      isRunning: false,
      currentLoad: 0,
      outputTemperature: this.ambientTemperature,
      returnTemperature: this.ambientTemperature,
      fuelFlow: 0,
      airFlow: 0,
      stackTemperature: this.ambientTemperature,
      efficiency: 0,
      heatOutput: 0,
      fuelConsumption: 0,
      operatingHours: 0,
      cycleCount: 0,
      lastStartup: /* @__PURE__ */ new Date(),
      lastShutdown: /* @__PURE__ */ new Date(),
      maintenanceHours: 0
    };
  }
  createInitialAlarms() {
    return {
      flameFailure: false,
      highTemperature: false,
      lowWaterLevel: false,
      highPressure: false,
      gasLeak: false,
      lowFuelPressure: false,
      highStackTemperature: false,
      lowEfficiency: false,
      maintenanceRequired: false
    };
  }
  simulate(heatDemand, returnTemperature, timeStep = 1) {
    if (this.operationalState.isRunning) {
      this.operationalState.operatingHours += timeStep / 3600;
    }
    const requiredLoad = Math.min(100, heatDemand / this.specifications.ratedCapacity * 100);
    const shouldRun = requiredLoad > this.specifications.turndownRatio * 100;
    if (shouldRun && !this.operationalState.isRunning) {
      this.startBoiler();
    } else if (!shouldRun && this.operationalState.isRunning) {
      this.shutdownBoiler();
    }
    if (this.operationalState.isRunning) {
      this.updateLoad(requiredLoad);
      this.calculateCombustion();
      this.updateTemperatures(returnTemperature, timeStep);
      this.calculateEfficiency();
      this.updateFuelConsumption();
      this.checkSafetyAlarms();
    } else {
      this.coolDown(timeStep);
    }
    this.operationalState.timestamp = /* @__PURE__ */ new Date();
    return { ...this.operationalState };
  }
  startBoiler() {
    if (this.checkStartupConditions()) {
      this.operationalState.isRunning = true;
      this.operationalState.lastStartup = /* @__PURE__ */ new Date();
      this.operationalState.cycleCount++;
      console.log(`Boiler ${this.specifications.id} started`);
    }
  }
  shutdownBoiler() {
    this.operationalState.isRunning = false;
    this.operationalState.currentLoad = 0;
    this.operationalState.fuelFlow = 0;
    this.operationalState.lastShutdown = /* @__PURE__ */ new Date();
    console.log(`Boiler ${this.specifications.id} shutdown`);
  }
  checkStartupConditions() {
    if (this.alarms.gasLeak || this.alarms.lowWaterLevel || this.safetySystem.emergencyShutdown) {
      return false;
    }
    return true;
  }
  updateLoad(requiredLoad) {
    const currentLoad = this.operationalState.currentLoad;
    const maxChangeRate = 5;
    const loadDifference = requiredLoad - currentLoad;
    const maxChange = maxChangeRate * 1;
    if (Math.abs(loadDifference) <= maxChange) {
      this.operationalState.currentLoad = requiredLoad;
    } else {
      this.operationalState.currentLoad += Math.sign(loadDifference) * maxChange;
    }
    const minLoad = this.specifications.turndownRatio * 100;
    this.operationalState.currentLoad = Math.max(
      minLoad,
      Math.min(100, this.operationalState.currentLoad)
    );
  }
  calculateCombustion() {
    const load = this.operationalState.currentLoad / 100;
    this.operationalState.fuelFlow = this.specifications.fuelConsumption.ratedFlow * load;
    const stoichiometricAir = this.operationalState.fuelFlow * this.controlSystem.fuelControl.airFuelRatio;
    this.operationalState.airFlow = stoichiometricAir * (1 + this.controlSystem.fuelControl.excessAir / 100);
    const baseStackTemp = 150;
    const loadEffect = load * 50;
    this.operationalState.stackTemperature = baseStackTemp + loadEffect;
  }
  updateTemperatures(returnTemp, timeStep) {
    this.operationalState.returnTemperature = returnTemp;
    const setpoint = this.controlSystem.temperatureControl.setpoint;
    const currentTemp = this.operationalState.outputTemperature;
    const error = setpoint - currentTemp;
    const pidOutput = this.controlSystem.temperatureControl.pidParameters.kp * error;
    const load = this.operationalState.currentLoad / 100;
    const heatInput = this.specifications.ratedCapacity * load;
    const thermalMass = 5e3;
    const specificHeat = 0.5;
    const tempRise = heatInput * timeStep / (thermalMass * specificHeat);
    const tempLoss = (currentTemp - this.ambientTemperature) * 1e-3 * timeStep;
    this.operationalState.outputTemperature += tempRise - tempLoss + pidOutput * 0.1;
    this.operationalState.outputTemperature = Math.max(
      this.ambientTemperature,
      Math.min(this.specifications.maxTemperature, this.operationalState.outputTemperature)
    );
  }
  calculateEfficiency() {
    const load = this.operationalState.currentLoad;
    let efficiency = this.specifications.efficiency.rated;
    if (load < 50) {
      efficiency = this.specifications.efficiency.minimum + (this.specifications.efficiency.rated - this.specifications.efficiency.minimum) * (load / 50);
    }
    const stackLoss = (this.operationalState.stackTemperature - this.ambientTemperature) * 0.1;
    efficiency -= stackLoss;
    this.operationalState.efficiency = Math.max(50, Math.min(95, efficiency));
    const fuelEnergyInput = this.operationalState.fuelFlow * this.specifications.fuelConsumption.heatingValue / 3600;
    this.operationalState.heatOutput = fuelEnergyInput * (this.operationalState.efficiency / 100);
  }
  updateFuelConsumption() {
    this.operationalState.fuelConsumption = this.operationalState.fuelFlow;
  }
  checkSafetyAlarms() {
    this.alarms.highTemperature = this.operationalState.outputTemperature > this.specifications.maxTemperature * 0.95;
    this.alarms.highStackTemperature = this.operationalState.stackTemperature > 300;
    this.alarms.lowEfficiency = this.operationalState.efficiency < 70;
    this.alarms.maintenanceRequired = this.operationalState.operatingHours > 8760;
    if (this.alarms.highTemperature || this.alarms.gasLeak || this.alarms.lowWaterLevel) {
      this.safetySystem.emergencyShutdown = true;
      this.shutdownBoiler();
    }
  }
  coolDown(timeStep) {
    const coolingRate = 0.5;
    const tempDiff = this.operationalState.outputTemperature - this.ambientTemperature;
    if (tempDiff > 0) {
      this.operationalState.outputTemperature -= Math.min(coolingRate * timeStep, tempDiff);
    }
    this.operationalState.stackTemperature = this.operationalState.outputTemperature + 20;
    this.operationalState.heatOutput = 0;
    this.operationalState.efficiency = 0;
  }
  // Public API methods
  getCurrentState() {
    return { ...this.operationalState };
  }
  getAlarms() {
    return { ...this.alarms };
  }
  getSpecifications() {
    return { ...this.specifications };
  }
  setTemperatureSetpoint(setpoint) {
    if (setpoint >= this.specifications.minTemperature && setpoint <= this.specifications.maxTemperature) {
      this.controlSystem.temperatureControl.setpoint = setpoint;
      return true;
    }
    return false;
  }
  acknowledgeAlarm(alarmType) {
    if (alarmType in this.alarms) {
      this.alarms[alarmType] = false;
      return true;
    }
    return false;
  }
  emergencyShutdown() {
    this.safetySystem.emergencyShutdown = true;
    this.shutdownBoiler();
    console.log(`Emergency shutdown activated for boiler ${this.specifications.id}`);
  }
  resetEmergencyShutdown() {
    if (this.checkStartupConditions()) {
      this.safetySystem.emergencyShutdown = false;
      return true;
    }
    return false;
  }
  getPerformanceMetrics() {
    const totalTime = (Date.now() - this.simulationStartTime.getTime()) / 1e3 / 3600;
    const availability = totalTime > 0 ? this.operationalState.operatingHours / totalTime * 100 : 0;
    const fuelConsumptionRate = this.operationalState.heatOutput > 0 ? this.operationalState.fuelConsumption / this.operationalState.heatOutput : 0;
    const emissionsRate = fuelConsumptionRate * this.specifications.emissions.co2Factor;
    return {
      availability,
      efficiency: this.operationalState.efficiency,
      fuelConsumptionRate,
      emissionsRate,
      maintenanceInterval: 8760 - this.operationalState.operatingHours
      // Hours until next maintenance
    };
  }
};

// server/physics/LoadingStationOperationsSimulation.ts
var LoadingStationOperationsSimulation = class {
  stationId;
  currentTruck;
  currentSequence;
  positioning;
  loadingArms = /* @__PURE__ */ new Map();
  safetySystem;
  operationalMetrics;
  constructor(stationId) {
    this.stationId = stationId;
    this.initializeSafetySystem();
    this.initializeLoadingArms();
    this.operationalMetrics = {
      totalLoadings: 0,
      totalVolume: 0,
      averageLoadingTime: 25,
      efficiency: 85,
      downtime: 0,
      lastMaintenance: /* @__PURE__ */ new Date()
    };
  }
  initializeSafetySystem() {
    this.safetySystem = {
      emergencyStop: {
        isActive: false,
        triggeredBy: ""
      },
      groundingSystem: {
        isConnected: false,
        resistance: 1e6,
        // High resistance when not connected
        continuityCheck: false
      },
      spillContainment: {
        isReady: true,
        capacity: 5e3,
        // L
        drainValves: true
      },
      fireSuppressionSystem: {
        isArmed: true,
        detectors: ["smoke", "heat", "flame"],
        suppressionAgents: ["foam", "co2"]
      },
      ventilation: {
        isOperational: true,
        airChangesPerHour: 12,
        gasDetection: true
      },
      accessControl: {
        authorizedPersonnel: ["operator", "supervisor", "maintenance"],
        restrictedAreas: ["loading_bay", "control_room"],
        lockoutTagout: false
      }
    };
  }
  initializeLoadingArms() {
    for (let i = 1; i <= 2; i++) {
      const armId = `${this.stationId}-ARM-${i.toString().padStart(2, "0")}`;
      this.loadingArms.set(armId, {
        armId,
        position: {
          x: 0,
          y: 3 + (i - 1) * 2,
          // Spaced 2m apart
          z: 0,
          rotation: 0,
          elevation: 0
        },
        targetPosition: {
          x: 0,
          y: 3 + (i - 1) * 2,
          z: 0,
          rotation: 0,
          elevation: 0
        },
        movement: {
          isMoving: false,
          speed: 0,
          acceleration: 0,
          maxSpeed: 0.5
          // m/s
        },
        connection: {
          isConnected: false,
          connectionForce: 0,
          sealIntegrity: false,
          leakDetection: false
        },
        flowControl: {
          valvePosition: 0,
          flowRate: 0,
          pressure: 0,
          temperature: 150
        }
      });
    }
  }
  startLoadingSequence(truck, operatorId) {
    if (this.currentSequence && this.currentSequence.status !== "completed") {
      throw new Error("Loading sequence already in progress");
    }
    this.currentTruck = truck;
    this.currentSequence = {
      id: `LS-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      stationId: this.stationId,
      truckId: truck.id,
      status: "pending",
      startTime: /* @__PURE__ */ new Date(),
      estimatedEndTime: new Date(Date.now() + this.estimateLoadingTime(truck.loadingOrder.targetVolume) * 60 * 1e3),
      currentStep: 0,
      steps: this.createLoadingSteps(truck),
      operatorId,
      batchData: this.createBatchRecord(truck)
    };
    console.log(`Loading sequence ${this.currentSequence.id} started for truck ${truck.id}`);
    return { ...this.currentSequence };
  }
  createLoadingSteps(truck) {
    return [
      {
        id: "STEP-01",
        name: "Truck Arrival and Registration",
        description: "Verify truck credentials and loading order",
        status: "pending",
        estimatedDuration: 120,
        // 2 minutes
        requirements: ["Valid loading order", "Driver credentials", "Vehicle inspection"],
        safetyChecks: ["Vehicle condition", "Emergency equipment"],
        automationLevel: "manual",
        operatorActions: ["Verify documents", "Check vehicle"],
        systemActions: ["Log arrival time", "Create batch record"]
      },
      {
        id: "STEP-02",
        name: "Truck Positioning",
        description: "Guide truck to correct loading position",
        status: "pending",
        estimatedDuration: 180,
        // 3 minutes
        requirements: ["Clear loading bay", "Positioning guidance active"],
        safetyChecks: ["Clearance verification", "Alignment check"],
        automationLevel: "semi_auto",
        operatorActions: ["Guide driver", "Verify position"],
        systemActions: ["Activate guidance system", "Monitor positioning"]
      },
      {
        id: "STEP-03",
        name: "Safety System Activation",
        description: "Activate all safety systems and perform checks",
        status: "pending",
        estimatedDuration: 240,
        // 4 minutes
        requirements: ["Truck properly positioned", "Engine off", "Parking brake set"],
        safetyChecks: ["Grounding connection", "Emergency stops", "Fire suppression", "Spill containment"],
        automationLevel: "full_auto",
        operatorActions: ["Connect grounding", "Verify safety systems"],
        systemActions: ["Test emergency stops", "Verify interlocks", "Arm fire suppression"]
      },
      {
        id: "STEP-04",
        name: "Loading Arm Connection",
        description: "Position and connect loading arms to truck",
        status: "pending",
        estimatedDuration: 300,
        // 5 minutes
        requirements: ["Safety systems active", "Arms in position"],
        safetyChecks: ["Connection integrity", "Seal verification", "Leak test"],
        automationLevel: "semi_auto",
        operatorActions: ["Guide arm positioning", "Verify connections"],
        systemActions: ["Move arms to position", "Test connections", "Verify seals"]
      },
      {
        id: "STEP-05",
        name: "Pre-Loading Verification",
        description: "Final checks before starting product transfer",
        status: "pending",
        estimatedDuration: 120,
        // 2 minutes
        requirements: ["All connections verified", "Product temperature OK"],
        safetyChecks: ["System pressure test", "Temperature verification"],
        automationLevel: "full_auto",
        operatorActions: ["Final verification"],
        systemActions: ["Pressure test", "Temperature check", "Flow path verification"]
      },
      {
        id: "STEP-06",
        name: "Product Loading",
        description: "Transfer asphalt product to truck tank",
        status: "pending",
        estimatedDuration: truck.loadingOrder.targetVolume / 800 * 60,
        // Based on 800 L/min flow rate
        requirements: ["All systems verified", "Operator authorization"],
        safetyChecks: ["Continuous monitoring", "Overflow protection", "Emergency stop ready"],
        automationLevel: "full_auto",
        operatorActions: ["Monitor loading", "Emergency response ready"],
        systemActions: ["Control flow rate", "Monitor volume", "Temperature control", "Safety monitoring"]
      },
      {
        id: "STEP-07",
        name: "Loading Completion",
        description: "Complete loading and disconnect equipment",
        status: "pending",
        estimatedDuration: 300,
        // 5 minutes
        requirements: ["Target volume reached", "Product quality verified"],
        safetyChecks: ["Pressure relief", "Arm disconnection", "Spill cleanup"],
        automationLevel: "semi_auto",
        operatorActions: ["Verify completion", "Disconnect arms", "Final inspection"],
        systemActions: ["Stop flow", "Relieve pressure", "Retract arms", "Generate documentation"]
      },
      {
        id: "STEP-08",
        name: "Documentation and Departure",
        description: "Generate documentation and release truck",
        status: "pending",
        estimatedDuration: 180,
        // 3 minutes
        requirements: ["Loading completed", "Quality verified"],
        safetyChecks: ["Final safety check", "Area cleanup"],
        automationLevel: "manual",
        operatorActions: ["Generate bill of lading", "Final inspection", "Release truck"],
        systemActions: ["Print documents", "Update records", "Log departure"]
      }
    ];
  }
  createBatchRecord(truck) {
    return {
      batchId: `BATCH-${Date.now()}`,
      productType: truck.loadingOrder.productType,
      sourceTanks: [1, 2, 3],
      // Simplified - would be determined by product availability
      targetVolume: truck.loadingOrder.targetVolume,
      actualVolume: 0,
      startTemperature: truck.loadingOrder.targetTemperature,
      endTemperature: truck.loadingOrder.targetTemperature,
      averageFlowRate: 0,
      peakFlowRate: 0,
      loadingTime: 0,
      qualityParameters: {
        viscosity: 0.4,
        // cP
        density: 1020,
        // kg/m³
        temperature: truck.loadingOrder.targetTemperature,
        grade: truck.loadingOrder.productType
      },
      billOfLading: {
        number: `BOL-${Date.now()}`,
        issueTime: /* @__PURE__ */ new Date(),
        netWeight: 0,
        grossWeight: 0,
        qualityCertificate: `QC-${Date.now()}`
      }
    };
  }
  estimateLoadingTime(volume) {
    const setupTime = 15;
    const loadingRate = 800;
    const cleanupTime = 8;
    return setupTime + volume / loadingRate + cleanupTime;
  }
  simulate(timeStep = 1) {
    if (this.currentSequence) {
      this.updateLoadingSequence(timeStep);
      this.updateTruckPositioning(timeStep);
      this.updateLoadingArms(timeStep);
      this.updateSafetySystem(timeStep);
    }
    return {
      sequence: this.currentSequence ? { ...this.currentSequence } : void 0,
      positioning: this.positioning ? { ...this.positioning } : void 0,
      loadingArms: Array.from(this.loadingArms.values()),
      safetySystem: { ...this.safetySystem },
      metrics: { ...this.operationalMetrics }
    };
  }
  updateLoadingSequence(timeStep) {
    if (!this.currentSequence || this.currentSequence.status === "completed") return;
    const currentStep = this.currentSequence.steps[this.currentSequence.currentStep];
    if (!currentStep) return;
    if (currentStep.status === "pending") {
      currentStep.status = "in_progress";
      currentStep.startTime = /* @__PURE__ */ new Date();
      this.currentSequence.status = this.getSequenceStatusFromStep(currentStep.name);
    }
    if (currentStep.status === "in_progress") {
      const elapsed = currentStep.startTime ? (Date.now() - currentStep.startTime.getTime()) / 1e3 : 0;
      if (elapsed >= currentStep.estimatedDuration) {
        currentStep.status = "completed";
        currentStep.endTime = /* @__PURE__ */ new Date();
        currentStep.actualDuration = elapsed;
        this.currentSequence.currentStep++;
        if (this.currentSequence.currentStep >= this.currentSequence.steps.length) {
          this.currentSequence.status = "completed";
          this.currentSequence.actualEndTime = /* @__PURE__ */ new Date();
          this.completeLoading();
        }
      }
    }
  }
  getSequenceStatusFromStep(stepName) {
    switch (stepName) {
      case "Truck Positioning":
        return "positioning";
      case "Safety System Activation":
        return "safety_check";
      case "Product Loading":
        return "loading";
      case "Loading Completion":
        return "completing";
      default:
        return "pending";
    }
  }
  updateTruckPositioning(timeStep) {
    if (!this.currentTruck || !this.currentSequence) return;
    const positioningStep = this.currentSequence.steps.find((s) => s.name === "Truck Positioning");
    if (positioningStep?.status === "in_progress") {
      if (!this.positioning) {
        this.positioning = {
          stationId: this.stationId,
          truckId: this.currentTruck.id,
          position: { x: -10, y: 0, angle: 0 },
          // Starting position
          alignment: {
            isAligned: false,
            lateralOffset: 2,
            longitudinalOffset: 1.5,
            angularOffset: 5
          },
          safetyZone: {
            isInSafetyZone: false,
            clearances: { front: 3, rear: 3, left: 2, right: 2 }
          },
          guidanceSystem: {
            type: "laser_guided",
            accuracy: 5,
            // cm
            status: "active"
          }
        };
      }
      const targetX = 0;
      const targetY = 0;
      const targetAngle = 0;
      this.positioning.position.x += (targetX - this.positioning.position.x) * 0.1 * timeStep;
      this.positioning.position.y += (targetY - this.positioning.position.y) * 0.1 * timeStep;
      this.positioning.position.angle += (targetAngle - this.positioning.position.angle) * 0.1 * timeStep;
      this.positioning.alignment.lateralOffset = Math.abs(this.positioning.position.y);
      this.positioning.alignment.longitudinalOffset = Math.abs(this.positioning.position.x);
      this.positioning.alignment.angularOffset = Math.abs(this.positioning.position.angle);
      this.positioning.alignment.isAligned = this.positioning.alignment.lateralOffset < 0.1 && this.positioning.alignment.longitudinalOffset < 0.1 && this.positioning.alignment.angularOffset < 1;
      this.positioning.safetyZone.isInSafetyZone = this.positioning.alignment.isAligned;
    }
  }
  updateLoadingArms(timeStep) {
    if (!this.currentSequence) return;
    const connectionStep = this.currentSequence.steps.find((s) => s.name === "Loading Arm Connection");
    const loadingStep = this.currentSequence.steps.find((s) => s.name === "Product Loading");
    this.loadingArms.forEach((arm) => {
      if (connectionStep?.status === "in_progress") {
        arm.movement.isMoving = true;
        arm.position.x += (arm.targetPosition.x - arm.position.x) * 0.2 * timeStep;
        arm.position.y += (arm.targetPosition.y - arm.position.y) * 0.2 * timeStep;
        arm.position.z += (arm.targetPosition.z - arm.position.z) * 0.2 * timeStep;
        const distance = Math.sqrt(
          Math.pow(arm.position.x - arm.targetPosition.x, 2) + Math.pow(arm.position.y - arm.targetPosition.y, 2) + Math.pow(arm.position.z - arm.targetPosition.z, 2)
        );
        if (distance < 0.1) {
          arm.movement.isMoving = false;
          arm.connection.isConnected = true;
          arm.connection.sealIntegrity = true;
        }
      }
      if (loadingStep?.status === "in_progress" && arm.connection.isConnected) {
        arm.flowControl.valvePosition = 100;
        arm.flowControl.flowRate = 400;
        arm.flowControl.pressure = 3.5;
        arm.flowControl.temperature = 150;
        if (this.currentSequence) {
          this.currentSequence.batchData.actualVolume += arm.flowControl.flowRate * timeStep / 60;
          this.currentSequence.batchData.averageFlowRate = (this.currentSequence.batchData.averageFlowRate + arm.flowControl.flowRate) / 2;
          this.currentSequence.batchData.peakFlowRate = Math.max(this.currentSequence.batchData.peakFlowRate, arm.flowControl.flowRate);
        }
      } else {
        arm.flowControl.valvePosition = 0;
        arm.flowControl.flowRate = 0;
      }
    });
  }
  updateSafetySystem(timeStep) {
    if (!this.currentSequence) return;
    const safetyStep = this.currentSequence.steps.find((s) => s.name === "Safety System Activation");
    if (safetyStep?.status === "in_progress" || safetyStep?.status === "completed") {
      this.safetySystem.groundingSystem.isConnected = true;
      this.safetySystem.groundingSystem.resistance = 0.5;
      this.safetySystem.groundingSystem.continuityCheck = true;
    }
    if (this.currentSequence.status === "loading") {
      const totalFlowRate = Array.from(this.loadingArms.values()).reduce((sum, arm) => sum + arm.flowControl.flowRate, 0);
      if (totalFlowRate > 1e3) {
        this.triggerEmergencyStop("High flow rate detected");
      }
    }
  }
  triggerEmergencyStop(reason) {
    this.safetySystem.emergencyStop.isActive = true;
    this.safetySystem.emergencyStop.triggeredBy = reason;
    this.safetySystem.emergencyStop.timestamp = /* @__PURE__ */ new Date();
    this.loadingArms.forEach((arm) => {
      arm.flowControl.valvePosition = 0;
      arm.flowControl.flowRate = 0;
    });
    if (this.currentSequence) {
      this.currentSequence.status = "aborted";
    }
    console.log(`Emergency stop triggered: ${reason}`);
  }
  completeLoading() {
    if (!this.currentSequence) return;
    this.operationalMetrics.totalLoadings++;
    this.operationalMetrics.totalVolume += this.currentSequence.batchData.actualVolume;
    const loadingTime = this.currentSequence.actualEndTime && this.currentSequence.startTime ? (this.currentSequence.actualEndTime.getTime() - this.currentSequence.startTime.getTime()) / 6e4 : 0;
    this.operationalMetrics.averageLoadingTime = (this.operationalMetrics.averageLoadingTime + loadingTime) / 2;
    this.currentSequence.batchData.loadingTime = loadingTime;
    this.currentSequence.batchData.billOfLading.netWeight = this.currentSequence.batchData.actualVolume * this.currentSequence.batchData.qualityParameters.density / 1e3;
    console.log(`Loading completed for truck ${this.currentTruck?.id}: ${this.currentSequence.batchData.actualVolume}L loaded`);
  }
  // Public API methods
  getCurrentSequence() {
    return this.currentSequence ? { ...this.currentSequence } : void 0;
  }
  getOperationalMetrics() {
    return { ...this.operationalMetrics };
  }
  acknowledgeEmergencyStop() {
    if (this.safetySystem.emergencyStop.isActive) {
      this.safetySystem.emergencyStop.isActive = false;
      this.safetySystem.emergencyStop.triggeredBy = "";
      return true;
    }
    return false;
  }
  resetStation() {
    this.currentSequence = void 0;
    this.currentTruck = void 0;
    this.positioning = void 0;
    this.loadingArms.forEach((arm) => {
      arm.connection.isConnected = false;
      arm.flowControl.valvePosition = 0;
      arm.flowControl.flowRate = 0;
    });
    this.safetySystem.groundingSystem.isConnected = false;
    this.safetySystem.emergencyStop.isActive = false;
  }
};

// server/services/scadaSimulator.ts
var SCADASimulator = class {
  tanks = [];
  intervalId = null;
  alertCallback;
  updateCallback;
  mlService;
  // Enhanced plant systems
  hotOilBoiler;
  heatingCoils = /* @__PURE__ */ new Map();
  loadingStations = [];
  pipeRoutingService;
  sensorMappingService;
  hotOilPhysics;
  coilThermalSimulations = /* @__PURE__ */ new Map();
  asphaltFlowSimulation;
  boilerSimulation;
  loadingStationSimulations = /* @__PURE__ */ new Map();
  systemStartTime = /* @__PURE__ */ new Date();
  // Hot-oil circulation system operational data
  hotOilSystemData = {
    supplyTemperature: 280,
    // °C
    returnTemperature: 260,
    // °C
    flowRate: 1e3,
    // L/min total system flow
    systemPressure: 4.5,
    // bar
    totalHeatLoad: 500,
    // kW
    pumpSpeed: 1450,
    // RPM
    fuelConsumption: 150,
    // m³/h
    systemEfficiency: 83
    // %
  };
  constructor() {
    this.mlService = new MLPredictionService();
    this.pipeRoutingService = new PipeRoutingService();
    this.sensorMappingService = new SCADASensorMappingService();
    this.hotOilPhysics = new HotOilCirculationPhysics();
    this.asphaltFlowSimulation = new AsphaltFlowSimulation("PG64-22");
    this.boilerSimulation = this.createBoilerSimulation();
    this.initializePlantSystems();
    this.initializeTanks();
  }
  createBoilerSimulation() {
    const boilerSpecs = {
      id: "HOB-01",
      name: "Hot Oil Boiler #1",
      fuelType: "natural_gas",
      ratedCapacity: 2e3,
      // kW
      maxTemperature: 320,
      // °C
      minTemperature: 150,
      // °C
      efficiency: {
        rated: 85,
        // %
        minimum: 70,
        // %
        curve: [20, 70, 50, 80, 75, 84, 100, 85]
        // [load%, efficiency%] pairs
      },
      turndownRatio: 0.2,
      // 20% minimum load
      responseTime: 300,
      // 5 minutes to reach setpoint
      fuelConsumption: {
        ratedFlow: 200,
        // m³/h natural gas
        heatingValue: 35800,
        // kJ/m³
        density: 0.8
        // kg/m³
      },
      emissions: {
        nox: 150,
        // mg/m³
        co: 100,
        // mg/m³
        co2Factor: 0.2
        // kg CO2/kWh
      }
    };
    return new HotOilBoilerSimulation(boilerSpecs);
  }
  initializePlantSystems() {
    this.hotOilBoiler = createHotOilBoiler();
    PLANT_LAYOUT_CONFIG.tanks.forEach((tankConfig) => {
      const heatingCoil = createHeatingCoil(tankConfig.id, [
        tankConfig.position[0],
        tankConfig.position[1] - 2,
        // Under the tank
        tankConfig.position[2]
      ]);
      this.heatingCoils.set(tankConfig.id, heatingCoil);
    });
    this.loadingStations = PLANT_LAYOUT_CONFIG.loadingSystem.loadingStations.map(
      (stationConfig) => createLoadingStation(stationConfig.id, stationConfig.position, stationConfig.connectedTanks)
    );
    this.initializeCoilThermalSimulations();
    this.initializeLoadingStationSimulations();
    console.log(`Initialized plant systems: ${this.heatingCoils.size} heating coils, ${this.loadingStations.length} loading stations, ${this.coilThermalSimulations.size} thermal simulations, ${this.loadingStationSimulations.size} loading simulations`);
  }
  initializeCoilThermalSimulations() {
    PLANT_LAYOUT_CONFIG.tanks.forEach((tankConfig) => {
      const coilGeometry = {
        turns: 3,
        coilDiameter: tankConfig.heatingCoilConfig.specifications.coilDiameter / 1e3,
        // Convert mm to m
        pipeDiameter: tankConfig.heatingCoilConfig.specifications.pipeDiameter / 1e3,
        // Convert mm to m
        pipeWallThickness: tankConfig.heatingCoilConfig.specifications.wallThickness / 1e3,
        // Convert mm to m
        totalLength: tankConfig.heatingCoilConfig.specifications.totalLength,
        elevationFromBottom: tankConfig.heatingCoilConfig.positioning.elevationFromBottom,
        material: {
          thermalConductivity: 16,
          // W/m·K for stainless steel
          density: 8e3,
          // kg/m³
          specificHeat: 500
          // J/kg·K
        }
      };
      const fluidProperties = {
        hotOil: {
          density: 850,
          viscosity: 1e-3,
          specificHeat: 2200,
          thermalConductivity: 0.12,
          temperature: 280
        },
        tankFluid: {
          density: tankConfig.thermalProperties.fluidProperties.density,
          viscosity: tankConfig.thermalProperties.fluidProperties.viscosity,
          specificHeat: tankConfig.thermalProperties.fluidProperties.specificHeat,
          thermalConductivity: tankConfig.thermalProperties.fluidProperties.thermalConductivity,
          temperature: 150
        }
      };
      const thermalSimulation = new ThreeTurnCoilThermalSimulation(coilGeometry, fluidProperties);
      this.coilThermalSimulations.set(tankConfig.id, thermalSimulation);
    });
  }
  initializeLoadingStationSimulations() {
    PLANT_LAYOUT_CONFIG.loadingSystem.loadingStations.forEach((stationConfig) => {
      const stationId = `LS-${stationConfig.id.toString().padStart(2, "0")}`;
      const loadingSimulation = new LoadingStationOperationsSimulation(stationId);
      this.loadingStationSimulations.set(stationId, loadingSimulation);
    });
  }
  simulateHotOilCirculation() {
    const physicsState = this.hotOilPhysics.simulateTimeStep(2);
    const totalHeatTransfer = Array.from(physicsState.heatTransferRates.values()).reduce((sum, rate) => sum + rate, 0);
    const returnTemp = physicsState.temperatures.get("HO-RETURN") || 260;
    const boilerState = this.boilerSimulation.simulate(totalHeatTransfer, returnTemp, 2);
    const boiler = this.hotOilBoiler;
    boiler.operationalData.currentTemperature = boilerState.outputTemperature;
    boiler.operationalData.fuelFlow = boilerState.fuelConsumption;
    boiler.operationalData.heatOutput = boilerState.heatOutput;
    boiler.operationalData.efficiency = boilerState.efficiency;
    boiler.status.isRunning = boilerState.isRunning;
    this.hotOilSystemData.supplyTemperature = boilerState.outputTemperature;
    this.hotOilSystemData.returnTemperature = returnTemp;
    this.hotOilSystemData.flowRate = physicsState.flowRates.get("HO-MAIN-SUPPLY") || 1e3;
    this.hotOilSystemData.totalHeatLoad = boilerState.heatOutput;
    this.hotOilSystemData.systemEfficiency = boilerState.efficiency;
    this.hotOilSystemData.fuelConsumption = boilerState.fuelConsumption;
    const pumpPressure = physicsState.pressures.get("HO-PUMP-01-DISCHARGE") || 4.5;
    this.hotOilSystemData.systemPressure = pumpPressure;
    this.heatingCoils.forEach((coil, tankId) => {
      const tank = this.tanks.find((t) => t.id === tankId);
      const thermalSim = this.coilThermalSimulations.get(tankId);
      if (!tank || !thermalSim) return;
      const coilId = `HC-ASP-${tankId.toString().padStart(2, "0")}`;
      const coilFlow = physicsState.flowRates.get(coilId) || 100;
      const supplyTemp = this.hotOilSystemData.supplyTemperature;
      thermalSim.setBoundaryConditions({
        hotOilInletTemperature: supplyTemp,
        hotOilFlowRate: coilFlow,
        tankTemperature: tank.temperature,
        ambientTemperature: 20,
        tankLevel: tank.currentLevel,
        foulingResistance: 2e-4
      });
      const thermalState = thermalSim.simulate({
        hotOilInletTemperature: supplyTemp,
        hotOilFlowRate: coilFlow,
        tankTemperature: tank.temperature,
        ambientTemperature: 20,
        tankLevel: tank.currentLevel,
        foulingResistance: 2e-4
      }, 2);
      coil.thermalData.hotOilInletTemp = supplyTemp;
      coil.thermalData.hotOilOutletTemp = thermalState.hotOilOutletTemperature;
      coil.thermalData.heatTransferRate = thermalState.totalHeatTransfer;
      coil.thermalData.tankTemperature = tank.temperature;
      coil.thermalData.efficiency = thermalState.thermalEfficiency;
      coil.operationalData.deltaTemperature = supplyTemp - thermalState.hotOilOutletTemperature;
      coil.operationalData.heatDuty = thermalState.totalHeatTransfer;
      coil.operationalData.flowRate = coilFlow;
      coil.operationalData.pressure = 3.5 + thermalState.pressureDrop;
      coil.operationalData.operatingHours += 2 / 3600;
      if (tank.temperature < tank.targetTemperature && thermalState.totalHeatTransfer > 0) {
        const tankConfig = PLANT_LAYOUT_CONFIG.tanks.find((t) => t.id === tankId);
        if (tankConfig) {
          const tankMass = tank.capacity * tankConfig.thermalProperties.fluidProperties.density / 1e3;
          const specificHeat = tankConfig.thermalProperties.fluidProperties.specificHeat;
          const heatingRate = thermalState.totalHeatTransfer * 1e3 / (tankMass * specificHeat);
          tank.temperature += heatingRate * 2;
          tank.temperature = Math.min(tank.targetTemperature + 2, tank.temperature);
        }
      }
    });
    boiler.operationalData.operatingHours += 2 / 3600;
  }
  simulateAsphaltFlow() {
    const asphaltFlowState = this.asphaltFlowSimulation.simulate(2);
    this.loadingStations.forEach((station) => {
      if (station.operationalData.loadingInProgress) {
        const availableFlowRate = asphaltFlowState.flowRates.get("ASP-LOADING-01") || 0;
        const asphaltTemperature = asphaltFlowState.temperatures.get("ASP-LOADING-01") || 150;
        const asphaltViscosity = asphaltFlowState.viscosities.get("ASP-LOADING-01") || 0.1;
        let effectiveFlowRate = Math.min(station.controlSystem.flowRateSetpoint, availableFlowRate);
        const viscosityFactor = Math.max(0.5, 1 - (asphaltViscosity - 0.1) / 1);
        effectiveFlowRate *= viscosityFactor;
        const temperatureFactor = Math.max(0.3, (asphaltTemperature - 120) / 50);
        effectiveFlowRate *= temperatureFactor;
        station.operationalData.currentFlowRate = effectiveFlowRate;
        station.loadingArms.forEach((arm) => {
          if (arm.operationalData.isFlowing) {
            arm.operationalData.flowRate = effectiveFlowRate / station.loadingArms.length;
            arm.operationalData.temperature = asphaltTemperature;
            arm.operationalData.pressure = asphaltFlowState.pressures.get("ASP-LOADING-01") || 2.5;
          }
        });
      }
    });
    const totalAsphaltFlow = Array.from(asphaltFlowState.flowRates.values()).reduce((sum, rate) => sum + rate, 0);
    if (totalAsphaltFlow > 0) {
      const activeTanks = this.tanks.filter((tank) => tank.currentLevel > 10);
      const flowPerTank = totalAsphaltFlow / activeTanks.length;
      activeTanks.forEach((tank) => {
        const volumeReduction = flowPerTank * 2 / 60;
        const levelReduction = volumeReduction / tank.capacity * 100;
        tank.currentLevel = Math.max(0, tank.currentLevel - levelReduction);
      });
    }
  }
  simulateLoadingStationOperations() {
    this.loadingStationSimulations.forEach((simulation, stationId) => {
      const simulationResult = simulation.simulate(2);
      const stationNumber = stationId.split("-")[1];
      const station = this.loadingStations.find((s) => s.id === stationNumber);
      if (station && simulationResult.sequence) {
        station.operationalData.loadingInProgress = simulationResult.sequence.status === "loading" || simulationResult.sequence.status === "positioning" || simulationResult.sequence.status === "safety_check";
        if (simulationResult.sequence.status === "loading") {
          station.operationalData.currentFlowRate = simulationResult.loadingArms.reduce((sum, arm) => sum + arm.flowControl.flowRate, 0);
        } else {
          station.operationalData.currentFlowRate = 0;
        }
        station.loadingArms.forEach((arm, index) => {
          const simArm = simulationResult.loadingArms[index];
          if (simArm) {
            arm.operationalData.isFlowing = simArm.flowControl.flowRate > 0;
            arm.operationalData.flowRate = simArm.flowControl.flowRate;
            arm.operationalData.pressure = simArm.flowControl.pressure;
            arm.operationalData.temperature = simArm.flowControl.temperature;
            arm.operationalData.isConnected = simArm.connection.isConnected;
          }
        });
        const metrics = simulationResult.metrics;
        station.operationalData.efficiency = metrics.totalLoadings > 0 ? metrics.totalVolume / (metrics.totalLoadings * 25e3) * 100 : 85;
      }
    });
  }
  updateSCADASensorData() {
    this.tanks.forEach((tank) => {
      this.sensorMappingService.updateSensorData(
        `TT_ASP_${tank.id.toString().padStart(2, "0")}`,
        tank.temperature,
        "simulation"
      );
      this.sensorMappingService.updateSensorData(
        `LT_ASP_${tank.id.toString().padStart(2, "0")}`,
        tank.currentLevel,
        "simulation"
      );
      this.sensorMappingService.updateSensorData(
        `PT_ASP_${tank.id.toString().padStart(2, "0")}`,
        tank.pressure,
        "simulation"
      );
    });
    const boiler = this.hotOilBoiler;
    this.sensorMappingService.updateSensorData(
      "TT_HOB_SUPPLY",
      boiler.operationalData.currentTemperature,
      "simulation"
    );
    this.sensorMappingService.updateSensorData(
      "FT_HOB_FUEL",
      boiler.operationalData.fuelFlow,
      "simulation"
    );
    this.sensorMappingService.updateSensorData(
      "FT_HO_MAIN_SUPPLY",
      this.hotOilSystemData.flowRate,
      "simulation"
    );
    this.loadingStations.forEach((station) => {
      this.sensorMappingService.updateSensorData(
        `FT_${station.id}_FLOW`,
        station.operationalData.currentFlowRate,
        "simulation"
      );
      this.sensorMappingService.updateSensorData(
        `DI_${station.id}_TRUCK_PRESENT`,
        station.operationalData.truckPresent,
        "simulation"
      );
    });
  }
  simulateLoadingStations() {
    this.loadingStations.forEach((station) => {
      if (!station.operationalData.truckPresent && Math.random() < 1e-3) {
        station.operationalData.truckPresent = true;
        station.truckData.truckId = `TRK-${Math.floor(Math.random() * 9999).toString().padStart(4, "0")}`;
        station.truckData.capacity = 25e3 + Math.random() * 1e4;
        station.truckData.currentVolume = 0;
        station.truckData.targetVolume = station.truckData.capacity * (0.8 + Math.random() * 0.2);
        station.truckData.arrivalTime = /* @__PURE__ */ new Date();
        const loadingTimeMinutes = station.truckData.targetVolume / station.specifications.maxFlowRate;
        station.truckData.estimatedDepartureTime = new Date(Date.now() + loadingTimeMinutes * 60 * 1e3);
        console.log(`Truck ${station.truckData.truckId} arrived at ${station.name}, target: ${station.truckData.targetVolume}L`);
      }
      if (station.operationalData.truckPresent && !station.operationalData.loadingInProgress) {
        if (station.controlSystem.safetyInterlocks.truckPositionOk && station.controlSystem.safetyInterlocks.temperatureOk) {
          station.operationalData.loadingInProgress = true;
          station.operationalData.currentFlowRate = station.controlSystem.flowRateSetpoint;
        }
      }
      if (station.operationalData.loadingInProgress) {
        const loadingRate = station.operationalData.currentFlowRate / 60;
        const volumeToAdd = loadingRate * 2;
        station.truckData.currentVolume += volumeToAdd;
        station.operationalData.totalVolumeLoaded += volumeToAdd;
        station.operationalData.loadingTime += 2 / 60;
        if (station.truckData.currentVolume >= station.truckData.targetVolume) {
          station.operationalData.loadingInProgress = false;
          station.operationalData.currentFlowRate = 0;
          station.operationalData.dailyLoadings++;
          console.log(`Loading complete for truck ${station.truckData.truckId}: ${station.truckData.currentVolume}L loaded`);
          setTimeout(() => {
            station.operationalData.truckPresent = false;
            station.truckData = {
              truckId: "",
              capacity: 0,
              currentVolume: 0,
              targetVolume: 0,
              arrivalTime: /* @__PURE__ */ new Date(),
              estimatedDepartureTime: /* @__PURE__ */ new Date(),
              driverInfo: { name: "", license: "", company: "" }
            };
          }, 5e3);
        }
        const volumePerTank = volumeToAdd / station.specifications.numberOfArms;
        station.loadingArms.forEach((arm) => {
          if (arm.operationalData.isFlowing) {
            const connectedTanks = this.tanks.filter(
              (tank) => station.connectedTanks?.includes(tank.id)
            );
            connectedTanks.forEach((tank) => {
              tank.currentLevel = Math.max(0, tank.currentLevel - volumePerTank / connectedTanks.length);
            });
            arm.operationalData.flowRate = station.operationalData.currentFlowRate / station.specifications.numberOfArms;
            arm.operationalData.totalVolumeDelivered += volumePerTank;
            arm.operationalData.pressure = 2.5 + Math.random() * 1;
            arm.operationalData.temperature = 145 + Math.random() * 10;
          }
        });
      }
      station.controlSystem.safetyInterlocks.truckPositionOk = station.operationalData.truckPresent;
      station.controlSystem.safetyInterlocks.temperatureOk = true;
      station.controlSystem.safetyInterlocks.armPositionOk = true;
      station.controlSystem.safetyInterlocks.emergencyStopActive = false;
    });
  }
  initializeTanks() {
    PLANT_LAYOUT_CONFIG.tanks.forEach((tankConfig, index) => {
      const i = tankConfig.id;
      const now = /* @__PURE__ */ new Date();
      const heatingCoil = this.heatingCoils.get(i);
      this.tanks.push({
        id: i,
        name: tankConfig.name,
        temperature: 140 + Math.random() * 20,
        // 140-160°C base
        targetTemperature: 150,
        capacity: 8e4 + Math.random() * 4e4,
        // 80k-120k liters (larger realistic tanks)
        currentLevel: 3e4 + Math.random() * 6e4,
        // 30k-90k liters
        status: "normal",
        boilerStatus: Math.random() > 0.2 ? "active" : "inactive",
        lastUpdated: now,
        position: tankConfig.position,
        sensors: {
          temperatureSensor: {
            value: 140 + Math.random() * 20,
            timestamp: now,
            status: "online",
            accuracy: 99 + Math.random(),
            lastCalibration: new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1e3)
          },
          levelSensor: {
            value: 2e4 + Math.random() * 4e4,
            timestamp: now,
            status: "online",
            accuracy: 98.5 + Math.random() * 1.5,
            lastCalibration: new Date(now.getTime() - Math.random() * 14 * 24 * 60 * 60 * 1e3)
          },
          pressureSensor: {
            value: 2.3 + Math.random() * 0.4,
            timestamp: now,
            status: "online",
            accuracy: 99.2 + Math.random() * 0.6,
            lastCalibration: new Date(now.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1e3)
          },
          flowSensor: {
            value: 12 + Math.random() * 8,
            timestamp: now,
            status: "online",
            accuracy: 97.8 + Math.random() * 1.5,
            lastCalibration: new Date(now.getTime() - Math.random() * 21 * 24 * 60 * 60 * 1e3)
          }
        },
        prediction: {
          nextBoilerAction: "no_action",
          actionConfidence: 0.5,
          predictedTemperature: [],
          timeToTarget: -1,
          energyOptimization: 75,
          failureRisk: 0.1,
          maintenanceWindow: null
        },
        efficiency: 85 + Math.random() * 10,
        maintenanceScore: Math.random() * 100,
        energyConsumption: 20 + Math.random() * 15
      });
    });
  }
  startSimulation(updateInterval = 2e3) {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
    this.intervalId = setInterval(() => {
      this.updateTanks();
      this.checkAlerts();
      if (this.updateCallback) {
        this.updateCallback([...this.tanks]);
      }
    }, updateInterval);
    console.log("SCADA simulation started");
  }
  stopSimulation() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    console.log("SCADA simulation stopped");
  }
  updateTanks() {
    this.simulateHotOilCirculation();
    this.simulateAsphaltFlow();
    this.simulateLoadingStations();
    this.simulateLoadingStationOperations();
    this.updateSCADASensorData();
    this.tanks.forEach((tank) => {
      const enhancedTank = this.mlService.simulateRealtimeSensorData(tank);
      const prediction = this.mlService.generatePrediction(enhancedTank);
      const tempDiff = tank.targetTemperature - tank.temperature;
      let tempChange = tempDiff * 0.1 + (Math.random() - 0.5) * 2;
      if (prediction.actionConfidence > 0.8) {
        if (prediction.nextBoilerAction === "start" && tank.boilerStatus === "inactive") {
          tank.boilerStatus = "active";
          tempChange += 1.5;
        } else if (prediction.nextBoilerAction === "stop" && tank.boilerStatus === "active") {
          tank.boilerStatus = "inactive";
          tempChange -= 0.5;
        }
      } else {
        if (tank.temperature < tank.targetTemperature - 5 && tank.boilerStatus === "inactive") {
          tank.boilerStatus = "active";
        } else if (tank.temperature > tank.targetTemperature + 5 && tank.boilerStatus === "active") {
          tank.boilerStatus = "inactive";
        }
      }
      tank.temperature = Math.max(100, Math.min(200, tank.temperature + tempChange));
      const levelChange = (Math.random() - 0.6) * 1e3;
      tank.currentLevel = Math.max(0, Math.min(tank.capacity, tank.currentLevel + levelChange));
      if (Math.random() < 1e-3) {
        tank.boilerStatus = "maintenance";
      }
      tank.sensors = enhancedTank.sensors;
      tank.prediction = prediction;
      tank.efficiency = enhancedTank.efficiency;
      tank.maintenanceScore = enhancedTank.maintenanceScore;
      tank.energyConsumption = enhancedTank.energyConsumption;
      tank.status = this.calculateTankStatus(tank);
      tank.lastUpdated = /* @__PURE__ */ new Date();
    });
    this.mlService.updateHistoricalData(this.tanks);
  }
  calculateTankStatus(tank) {
    const tempDiff = Math.abs(tank.temperature - tank.targetTemperature);
    const levelPercentage = tank.currentLevel / tank.capacity * 100;
    if (tempDiff > 15 || levelPercentage < 10 || levelPercentage > 95 || tank.boilerStatus === "maintenance") {
      return "critical";
    }
    if (tempDiff > 8 || levelPercentage < 20 || levelPercentage > 85) {
      return "warning";
    }
    return "normal";
  }
  checkAlerts() {
    this.tanks.forEach((tank) => {
      const tempDiff = Math.abs(tank.temperature - tank.targetTemperature);
      const levelPercentage = tank.currentLevel / tank.capacity * 100;
      if (tempDiff > 20) {
        this.createAlert(
          tank.id,
          "temperature",
          "critical",
          `Critical temperature deviation: ${tempDiff.toFixed(1)}\xB0C from target`
        );
      } else if (tempDiff > 10) {
        this.createAlert(
          tank.id,
          "temperature",
          "high",
          `High temperature deviation: ${tempDiff.toFixed(1)}\xB0C from target`
        );
      }
      if (levelPercentage < 5) {
        this.createAlert(
          tank.id,
          "level",
          "critical",
          `Critically low level: ${levelPercentage.toFixed(1)}%`
        );
      } else if (levelPercentage < 15) {
        this.createAlert(
          tank.id,
          "level",
          "medium",
          `Low level warning: ${levelPercentage.toFixed(1)}%`
        );
      } else if (levelPercentage > 95) {
        this.createAlert(
          tank.id,
          "level",
          "high",
          `Tank nearly full: ${levelPercentage.toFixed(1)}%`
        );
      }
      if (tank.boilerStatus === "maintenance") {
        this.createAlert(
          tank.id,
          "boiler",
          "high",
          "Boiler requires maintenance"
        );
      }
    });
  }
  createAlert(tankId, type, severity, message) {
    if (Math.random() < 0.05) {
      const alert = {
        id: nanoid(),
        tankId,
        type,
        severity,
        message,
        timestamp: /* @__PURE__ */ new Date(),
        acknowledged: false
      };
      if (this.alertCallback) {
        this.alertCallback(alert);
      }
    }
  }
  setAlertCallback(callback) {
    this.alertCallback = callback;
  }
  setUpdateCallback(callback) {
    this.updateCallback = callback;
  }
  getTanks() {
    return [...this.tanks];
  }
  getHotOilSystem() {
    return {
      boiler: this.hotOilBoiler,
      systemData: this.hotOilSystemData,
      heatingCoils: Array.from(this.heatingCoils.entries()).map(([tankId, coil]) => ({
        tankId,
        ...coil
      }))
    };
  }
  getLoadingStations() {
    return [...this.loadingStations];
  }
  getSystemOverview() {
    const pipeSystemStatus = this.pipeRoutingService.getSystemStatus();
    const pipeAlarms = this.pipeRoutingService.getAlarms();
    return {
      tanks: this.tanks.length,
      hotOilBoiler: {
        temperature: this.hotOilBoiler.operationalData.currentTemperature,
        efficiency: this.hotOilBoiler.operationalData.efficiency,
        fuelFlow: this.hotOilBoiler.operationalData.fuelFlow,
        status: this.hotOilBoiler.status.isRunning ? "running" : "stopped"
      },
      hotOilSystem: this.hotOilSystemData,
      loadingStations: this.loadingStations.map((station) => ({
        id: station.id,
        truckPresent: station.operationalData.truckPresent,
        loadingInProgress: station.operationalData.loadingInProgress,
        dailyLoadings: station.operationalData.dailyLoadings,
        totalVolumeLoaded: station.operationalData.totalVolumeLoaded
      })),
      pipeNetworks: Array.from(pipeSystemStatus.values()),
      pipeAlarms: pipeAlarms.filter((alarm) => !alarm.acknowledged),
      systemUptime: Math.floor((Date.now() - this.systemStartTime.getTime()) / 1e3 / 60),
      // minutes
      totalEnergyConsumption: this.hotOilBoiler.operationalData.fuelFlow * this.hotOilBoiler.operationalData.operatingHours
    };
  }
  getPipeNetworks() {
    return this.pipeRoutingService.getNetworks();
  }
  getPipeFlowCalculations() {
    return Array.from(this.pipeRoutingService.getFlowCalculations().entries()).map(([pipeId, calc]) => ({
      pipeId,
      ...calc
    }));
  }
  getPipeAlarms() {
    return this.pipeRoutingService.getAlarms();
  }
  acknowledgePipeAlarm(alarmId) {
    return this.pipeRoutingService.acknowledgeAlarm(alarmId);
  }
  getSCADASensorData(tagName) {
    return this.sensorMappingService.getSensorData(tagName);
  }
  getSCADAAlarms() {
    return this.sensorMappingService.getActiveAlarms();
  }
  acknowledgeSCADAAlarm(alarmId, acknowledgedBy) {
    return this.sensorMappingService.acknowledgeAlarm(alarmId, acknowledgedBy);
  }
  getSCADASensorMappings() {
    return this.sensorMappingService.getAllSensorMappings();
  }
  getSCADATagGroups() {
    return this.sensorMappingService.getTagGroups();
  }
  getSCADASensorReport() {
    return this.sensorMappingService.generateSensorReport();
  }
  getHotOilPhysicsState() {
    return this.hotOilPhysics.getCurrentState();
  }
  getSystemEnergyBalance() {
    return this.hotOilPhysics.getEnergyBalance();
  }
  getSystemEfficiency() {
    return this.hotOilPhysics.getSystemEfficiency();
  }
  setPumpSpeed(pumpId, speed) {
    return this.hotOilPhysics.setPumpSpeed(pumpId, speed);
  }
  setValvePosition(valveId, position) {
    return this.hotOilPhysics.setValvePosition(valveId, position);
  }
  getCoilThermalStates() {
    const thermalStates = {};
    this.coilThermalSimulations.forEach((simulation, tankId) => {
      thermalStates[tankId] = simulation.getCurrentState();
    });
    return thermalStates;
  }
  getCoilThermalState(tankId) {
    const simulation = this.coilThermalSimulations.get(tankId);
    return simulation ? simulation.getCurrentState() : null;
  }
  optimizeCoilFlowRate(tankId, targetHeatTransfer) {
    const simulation = this.coilThermalSimulations.get(tankId);
    return simulation ? simulation.calculateOptimalFlowRate(targetHeatTransfer) : 0;
  }
  getAsphaltFlowState() {
    return this.asphaltFlowSimulation.getCurrentState();
  }
  getAsphaltProperties() {
    return this.asphaltFlowSimulation.getCurrentAsphaltProperties();
  }
  getAsphaltFlowEfficiency() {
    return this.asphaltFlowSimulation.getFlowEfficiency();
  }
  setAsphaltValvePosition(valveId, position) {
    return this.asphaltFlowSimulation.setValvePosition(valveId, position);
  }
  setAsphaltPumpSpeed(pumpId, speed) {
    return this.asphaltFlowSimulation.setPumpSpeed(pumpId, speed);
  }
  calculateOptimalAsphaltTemperature(targetViscosity) {
    return this.asphaltFlowSimulation.calculateOptimalTemperature(targetViscosity);
  }
  getBoilerSimulationState() {
    return this.boilerSimulation.getCurrentState();
  }
  getBoilerAlarms() {
    return this.boilerSimulation.getAlarms();
  }
  getBoilerSpecifications() {
    return this.boilerSimulation.getSpecifications();
  }
  getBoilerPerformanceMetrics() {
    return this.boilerSimulation.getPerformanceMetrics();
  }
  setBoilerTemperatureSetpoint(setpoint) {
    return this.boilerSimulation.setTemperatureSetpoint(setpoint);
  }
  acknowledgeBoilerAlarm(alarmType) {
    return this.boilerSimulation.acknowledgeAlarm(alarmType);
  }
  emergencyShutdownBoiler() {
    this.boilerSimulation.emergencyShutdown();
  }
  resetBoilerEmergencyShutdown() {
    return this.boilerSimulation.resetEmergencyShutdown();
  }
  getLoadingStationOperations() {
    const operations = {};
    this.loadingStationSimulations.forEach((simulation, stationId) => {
      const result = simulation.simulate(0);
      operations[stationId] = result;
    });
    return operations;
  }
  getLoadingStationOperation(stationId) {
    const simulation = this.loadingStationSimulations.get(stationId);
    return simulation ? simulation.simulate(0) : null;
  }
  startLoadingSequence(stationId, truck, operatorId) {
    const simulation = this.loadingStationSimulations.get(stationId);
    return simulation ? simulation.startLoadingSequence(truck, operatorId) : null;
  }
  getLoadingStationMetrics(stationId) {
    const simulation = this.loadingStationSimulations.get(stationId);
    return simulation ? simulation.getOperationalMetrics() : null;
  }
  acknowledgeLoadingStationEmergency(stationId) {
    const simulation = this.loadingStationSimulations.get(stationId);
    return simulation ? simulation.acknowledgeEmergencyStop() : false;
  }
  resetLoadingStation(stationId) {
    const simulation = this.loadingStationSimulations.get(stationId);
    if (simulation) {
      simulation.resetStation();
    }
  }
  updateTankThresholds(tankId, thresholds) {
    const tank = this.tanks.find((t) => t.id === tankId);
    if (tank) {
      tank.targetTemperature = thresholds.targetTemperature;
      console.log(`Updated thresholds for tank ${tankId}:`, thresholds);
    }
  }
};

// server/services/tankMonitor.ts
var TankMonitorService = class {
  tanks = [];
  updateTanks(tanks) {
    this.tanks = tanks;
  }
  getSystemMetrics() {
    if (this.tanks.length === 0) {
      return {
        totalTanks: 0,
        activeTanks: 0,
        averageTemperature: 0,
        totalCapacity: 0,
        totalUsed: 0,
        alertCount: 0,
        lastUpdate: /* @__PURE__ */ new Date()
      };
    }
    const totalTanks = this.tanks.length;
    const activeTanks = this.tanks.filter((tank) => tank.status !== "critical").length;
    const averageTemperature = this.tanks.reduce((sum, tank) => sum + tank.temperature, 0) / totalTanks;
    const totalCapacity = this.tanks.reduce((sum, tank) => sum + tank.capacity, 0);
    const totalUsed = this.tanks.reduce((sum, tank) => sum + tank.currentLevel, 0);
    const alertCount = this.tanks.filter((tank) => tank.status !== "normal").length;
    return {
      totalTanks,
      activeTanks,
      averageTemperature,
      totalCapacity,
      totalUsed,
      alertCount,
      lastUpdate: /* @__PURE__ */ new Date()
    };
  }
  getTankEfficiency() {
    return this.tanks.map((tank) => {
      const tempEfficiency = Math.max(0, 100 - Math.abs(tank.temperature - tank.targetTemperature) * 5);
      const levelEfficiency = tank.currentLevel / tank.capacity * 100;
      const boilerEfficiency = tank.boilerStatus === "active" ? 100 : 50;
      const efficiency = (tempEfficiency + levelEfficiency + boilerEfficiency) / 3;
      return {
        tankId: tank.id,
        efficiency: Math.round(efficiency)
      };
    });
  }
  predictMaintenanceNeeds() {
    return this.tanks.map((tank) => {
      let priority = "low";
      let reason = "Normal operation";
      const tempDiff = Math.abs(tank.temperature - tank.targetTemperature);
      const levelPercentage = tank.currentLevel / tank.capacity * 100;
      if (tank.boilerStatus === "maintenance") {
        priority = "high";
        reason = "Boiler maintenance required";
      } else if (tempDiff > 15) {
        priority = "high";
        reason = "Temperature control issues";
      } else if (levelPercentage < 10) {
        priority = "medium";
        reason = "Low level requires attention";
      } else if (tempDiff > 8) {
        priority = "medium";
        reason = "Temperature variance above normal";
      }
      return {
        tankId: tank.id,
        priority,
        reason
      };
    });
  }
  generateReport() {
    const metrics = this.getSystemMetrics();
    const efficiency = this.getTankEfficiency();
    const maintenance = this.predictMaintenanceNeeds();
    return {
      timestamp: /* @__PURE__ */ new Date(),
      systemMetrics: metrics,
      tankEfficiency: efficiency,
      maintenanceNeeds: maintenance,
      summary: {
        averageEfficiency: efficiency.reduce((sum, e) => sum + e.efficiency, 0) / efficiency.length,
        criticalTanks: this.tanks.filter((tank) => tank.status === "critical").length,
        maintenanceRequired: maintenance.filter((m) => m.priority === "high").length
      }
    };
  }
};

// server/routes.ts
async function registerRoutes(app2) {
  const httpServer = createServer(app2);
  const io = new SocketIOServer(httpServer, {
    cors: {
      origin: "*",
      methods: ["GET", "POST"]
    },
    transports: ["websocket", "polling"]
  });
  const scadaSimulator = new SCADASimulator();
  const tankMonitor = new TankMonitorService();
  const mlService = new MLPredictionService();
  scadaSimulator.setUpdateCallback((tanks) => {
    tankMonitor.updateTanks(tanks);
    io.emit("tankUpdate", tanks);
    const metrics = tankMonitor.getSystemMetrics();
    io.emit("systemMetrics", metrics);
    io.emit("hotOilSystemUpdate", scadaSimulator.getHotOilSystem());
    io.emit("loadingStationsUpdate", scadaSimulator.getLoadingStations());
    io.emit("systemOverviewUpdate", scadaSimulator.getSystemOverview());
    io.emit("pipeNetworksUpdate", scadaSimulator.getPipeNetworks());
    io.emit("pipeFlowUpdate", scadaSimulator.getPipeFlowCalculations());
    const pipeAlarms = scadaSimulator.getPipeAlarms().filter((alarm) => !alarm.acknowledged);
    if (pipeAlarms.length > 0) {
      io.emit("pipeAlarmsUpdate", pipeAlarms);
    }
    io.emit("scadaSensorDataUpdate", scadaSimulator.getSCADASensorData());
    const scadaAlarms = scadaSimulator.getSCADAAlarms().filter((alarm) => !alarm.acknowledged);
    if (scadaAlarms.length > 0) {
      io.emit("scadaAlarmsUpdate", scadaAlarms);
    }
    io.emit("scadaSensorReportUpdate", scadaSimulator.getSCADASensorReport());
    io.emit("asphaltFlowStateUpdate", scadaSimulator.getAsphaltFlowState());
    io.emit("asphaltFlowEfficiencyUpdate", { efficiency: scadaSimulator.getAsphaltFlowEfficiency() });
    io.emit("coilThermalStatesUpdate", scadaSimulator.getCoilThermalStates());
    io.emit("boilerStateUpdate", scadaSimulator.getBoilerSimulationState());
    const boilerAlarms = scadaSimulator.getBoilerAlarms();
    const activeBoilerAlarms = Object.entries(boilerAlarms).filter(([_, active]) => active);
    if (activeBoilerAlarms.length > 0) {
      io.emit("boilerAlarmsUpdate", boilerAlarms);
    }
    io.emit("boilerPerformanceUpdate", scadaSimulator.getBoilerPerformanceMetrics());
    io.emit("loadingStationOperationsUpdate", scadaSimulator.getLoadingStationOperations());
  });
  scadaSimulator.setAlertCallback((alert) => {
    io.emit("newAlert", alert);
  });
  scadaSimulator.startSimulation();
  app2.get("/api/tanks", (req, res) => {
    res.json(scadaSimulator.getTanks());
  });
  app2.get("/api/metrics", (req, res) => {
    res.json(tankMonitor.getSystemMetrics());
  });
  app2.get("/api/report", (req, res) => {
    res.json(tankMonitor.generateReport());
  });
  app2.get("/api/hot-oil-system", (req, res) => {
    res.json(scadaSimulator.getHotOilSystem());
  });
  app2.get("/api/loading-stations", (req, res) => {
    res.json(scadaSimulator.getLoadingStations());
  });
  app2.get("/api/system-overview", (req, res) => {
    res.json(scadaSimulator.getSystemOverview());
  });
  app2.get("/api/pipe-networks", (req, res) => {
    res.json(scadaSimulator.getPipeNetworks());
  });
  app2.get("/api/pipe-flow-calculations", (req, res) => {
    res.json(scadaSimulator.getPipeFlowCalculations());
  });
  app2.get("/api/pipe-alarms", (req, res) => {
    res.json(scadaSimulator.getPipeAlarms());
  });
  app2.post("/api/pipe-alarms/:alarmId/acknowledge", (req, res) => {
    const { alarmId } = req.params;
    const success = scadaSimulator.acknowledgePipeAlarm(alarmId);
    res.json({ success, alarmId });
  });
  app2.get("/api/scada-sensors", (req, res) => {
    const { tagName } = req.query;
    res.json(scadaSimulator.getSCADASensorData(tagName));
  });
  app2.get("/api/scada-alarms", (req, res) => {
    res.json(scadaSimulator.getSCADAAlarms());
  });
  app2.post("/api/scada-alarms/:alarmId/acknowledge", (req, res) => {
    const { alarmId } = req.params;
    const { acknowledgedBy } = req.body;
    const success = scadaSimulator.acknowledgeSCADAAlarm(alarmId, acknowledgedBy || "operator");
    res.json({ success, alarmId, acknowledgedBy });
  });
  app2.get("/api/scada-sensor-mappings", (req, res) => {
    res.json(scadaSimulator.getSCADASensorMappings());
  });
  app2.get("/api/scada-tag-groups", (req, res) => {
    res.json(scadaSimulator.getSCADATagGroups());
  });
  app2.get("/api/scada-sensor-report", (req, res) => {
    res.json(scadaSimulator.getSCADASensorReport());
  });
  app2.get("/api/hot-oil-physics", (req, res) => {
    res.json(scadaSimulator.getHotOilPhysicsState());
  });
  app2.get("/api/energy-balance", (req, res) => {
    res.json(scadaSimulator.getSystemEnergyBalance());
  });
  app2.get("/api/system-efficiency", (req, res) => {
    res.json({ efficiency: scadaSimulator.getSystemEfficiency() });
  });
  app2.post("/api/pump-control/:pumpId", (req, res) => {
    const { pumpId } = req.params;
    const { speed } = req.body;
    const success = scadaSimulator.setPumpSpeed(pumpId, speed);
    res.json({ success, pumpId, speed });
  });
  app2.post("/api/valve-control/:valveId", (req, res) => {
    const { valveId } = req.params;
    const { position } = req.body;
    const success = scadaSimulator.setValvePosition(valveId, position);
    res.json({ success, valveId, position });
  });
  app2.get("/api/coil-thermal-states", (req, res) => {
    res.json(scadaSimulator.getCoilThermalStates());
  });
  app2.get("/api/coil-thermal-state/:tankId", (req, res) => {
    const tankId = parseInt(req.params.tankId);
    const thermalState = scadaSimulator.getCoilThermalState(tankId);
    if (thermalState) {
      res.json(thermalState);
    } else {
      res.status(404).json({ error: "Tank not found" });
    }
  });
  app2.post("/api/optimize-coil-flow/:tankId", (req, res) => {
    const tankId = parseInt(req.params.tankId);
    const { targetHeatTransfer } = req.body;
    const optimalFlowRate = scadaSimulator.optimizeCoilFlowRate(tankId, targetHeatTransfer);
    res.json({ tankId, targetHeatTransfer, optimalFlowRate });
  });
  app2.get("/api/asphalt-flow-state", (req, res) => {
    res.json(scadaSimulator.getAsphaltFlowState());
  });
  app2.get("/api/asphalt-properties", (req, res) => {
    res.json(scadaSimulator.getAsphaltProperties());
  });
  app2.get("/api/asphalt-flow-efficiency", (req, res) => {
    res.json({ efficiency: scadaSimulator.getAsphaltFlowEfficiency() });
  });
  app2.post("/api/asphalt-valve-control/:valveId", (req, res) => {
    const { valveId } = req.params;
    const { position } = req.body;
    const success = scadaSimulator.setAsphaltValvePosition(valveId, position);
    res.json({ success, valveId, position });
  });
  app2.post("/api/asphalt-pump-control/:pumpId", (req, res) => {
    const { pumpId } = req.params;
    const { speed } = req.body;
    const success = scadaSimulator.setAsphaltPumpSpeed(pumpId, speed);
    res.json({ success, pumpId, speed });
  });
  app2.post("/api/optimize-asphalt-temperature", (req, res) => {
    const { targetViscosity } = req.body;
    const optimalTemperature = scadaSimulator.calculateOptimalAsphaltTemperature(targetViscosity);
    res.json({ targetViscosity, optimalTemperature });
  });
  app2.get("/api/boiler-state", (req, res) => {
    res.json(scadaSimulator.getBoilerSimulationState());
  });
  app2.get("/api/boiler-alarms", (req, res) => {
    res.json(scadaSimulator.getBoilerAlarms());
  });
  app2.get("/api/boiler-specifications", (req, res) => {
    res.json(scadaSimulator.getBoilerSpecifications());
  });
  app2.get("/api/boiler-performance", (req, res) => {
    res.json(scadaSimulator.getBoilerPerformanceMetrics());
  });
  app2.post("/api/boiler-setpoint", (req, res) => {
    const { setpoint } = req.body;
    const success = scadaSimulator.setBoilerTemperatureSetpoint(setpoint);
    res.json({ success, setpoint });
  });
  app2.post("/api/boiler-acknowledge-alarm/:alarmType", (req, res) => {
    const { alarmType } = req.params;
    const success = scadaSimulator.acknowledgeBoilerAlarm(alarmType);
    res.json({ success, alarmType });
  });
  app2.post("/api/boiler-emergency-shutdown", (req, res) => {
    scadaSimulator.emergencyShutdownBoiler();
    res.json({ success: true, message: "Emergency shutdown activated" });
  });
  app2.post("/api/boiler-reset-emergency", (req, res) => {
    const success = scadaSimulator.resetBoilerEmergencyShutdown();
    res.json({ success, message: success ? "Emergency reset successful" : "Emergency reset failed" });
  });
  app2.get("/api/loading-station-operations", (req, res) => {
    res.json(scadaSimulator.getLoadingStationOperations());
  });
  app2.get("/api/loading-station-operation/:stationId", (req, res) => {
    const { stationId } = req.params;
    const operation = scadaSimulator.getLoadingStationOperation(stationId);
    if (operation) {
      res.json(operation);
    } else {
      res.status(404).json({ error: "Loading station not found" });
    }
  });
  app2.get("/api/loading-station-metrics/:stationId", (req, res) => {
    const { stationId } = req.params;
    const metrics = scadaSimulator.getLoadingStationMetrics(stationId);
    if (metrics) {
      res.json(metrics);
    } else {
      res.status(404).json({ error: "Loading station not found" });
    }
  });
  app2.post("/api/start-loading-sequence/:stationId", (req, res) => {
    const { stationId } = req.params;
    const { truck, operatorId } = req.body;
    const sequence = scadaSimulator.startLoadingSequence(stationId, truck, operatorId);
    if (sequence) {
      res.json(sequence);
    } else {
      res.status(400).json({ error: "Failed to start loading sequence" });
    }
  });
  app2.post("/api/loading-station-acknowledge-emergency/:stationId", (req, res) => {
    const { stationId } = req.params;
    const success = scadaSimulator.acknowledgeLoadingStationEmergency(stationId);
    res.json({ success, stationId });
  });
  app2.post("/api/loading-station-reset/:stationId", (req, res) => {
    const { stationId } = req.params;
    scadaSimulator.resetLoadingStation(stationId);
    res.json({ success: true, stationId, message: "Loading station reset" });
  });
  app2.post("/api/tanks/:id/thresholds", (req, res) => {
    const tankId = parseInt(req.params.id);
    const thresholds = req.body;
    scadaSimulator.updateTankThresholds(tankId, thresholds);
    res.json({ success: true });
  });
  app2.get("/api/ml/models", (req, res) => {
    res.json(mlService.getModelStatus());
  });
  app2.post("/api/ml/retrain/:modelId", (req, res) => {
    const modelId = req.params.modelId;
    const success = mlService.retrain(modelId);
    res.json({ success, message: success ? "Model retraining started" : "Model not found" });
  });
  app2.get("/api/tanks/:id/prediction", (req, res) => {
    const tankId = parseInt(req.params.id);
    const tanks = scadaSimulator.getTanks();
    const tank = tanks.find((t) => t.id === tankId);
    if (!tank) {
      return res.status(404).json({ error: "Tank not found" });
    }
    const prediction = mlService.generatePrediction(tank);
    res.json(prediction);
  });
  app2.get("/api/digital-twin/comparison/:id", (req, res) => {
    const tankId = parseInt(req.params.id);
    const tanks = scadaSimulator.getTanks();
    const tank = tanks.find((t) => t.id === tankId);
    if (!tank) {
      return res.status(404).json({ error: "Tank not found" });
    }
    const comparison = {
      realData: tank,
      twinData: { ...tank, temperature: tank.temperature + (Math.random() - 0.5) * 2 },
      variance: Math.abs(Math.random() * 3),
      syncStatus: Math.random() > 0.1 ? "synced" : "drift",
      lastSync: /* @__PURE__ */ new Date()
    };
    res.json(comparison);
  });
  io.on("connection", (socket) => {
    console.log("Client connected:", socket.id);
    socket.emit("tankUpdate", scadaSimulator.getTanks());
    socket.emit("systemMetrics", tankMonitor.getSystemMetrics());
    socket.on("requestTankData", () => {
      socket.emit("tankUpdate", scadaSimulator.getTanks());
      socket.emit("systemMetrics", tankMonitor.getSystemMetrics());
    });
    socket.on("acknowledgeAlert", (alertId) => {
      console.log("Alert acknowledged:", alertId);
    });
    socket.on("updateThresholds", ({ tankId, thresholds }) => {
      scadaSimulator.updateTankThresholds(tankId, thresholds);
      socket.emit("tankUpdate", scadaSimulator.getTanks());
    });
    socket.on("disconnect", () => {
      console.log("Client disconnected:", socket.id);
    });
  });
  process.on("SIGTERM", () => {
    console.log("Shutting down SCADA simulation...");
    scadaSimulator.stopSimulation();
  });
  return httpServer;
}

// server/vite.ts
import express from "express";
import fs from "fs";
import path2, { dirname as dirname2 } from "path";
import { fileURLToPath as fileURLToPath2 } from "url";
import { createServer as createViteServer, createLogger } from "vite";

// vite.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path, { dirname } from "path";
import runtimeErrorOverlay from "@replit/vite-plugin-runtime-error-modal";
import { fileURLToPath } from "url";
import glsl from "vite-plugin-glsl";
var __filename = fileURLToPath(import.meta.url);
var __dirname = dirname(__filename);
var vite_config_default = defineConfig({
  plugins: [
    react(),
    runtimeErrorOverlay(),
    glsl()
    // Add GLSL shader support
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "client", "src"),
      "@shared": path.resolve(__dirname, "shared")
    }
  },
  root: path.resolve(__dirname, "client"),
  build: {
    outDir: path.resolve(__dirname, "dist/public"),
    emptyOutDir: true
  },
  // Add support for large models and audio files
  assetsInclude: ["**/*.gltf", "**/*.glb", "**/*.mp3", "**/*.ogg", "**/*.wav"]
});

// server/vite.ts
import { nanoid as nanoid2 } from "nanoid";
var __filename2 = fileURLToPath2(import.meta.url);
var __dirname2 = dirname2(__filename2);
var viteLogger = createLogger();
function log(message, source = "express") {
  const formattedTime = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true
  });
  console.log(`${formattedTime} [${source}] ${message}`);
}
async function setupVite(app2, server) {
  const serverOptions = {
    middlewareMode: true,
    hmr: { server },
    allowedHosts: true
  };
  const vite = await createViteServer({
    ...vite_config_default,
    configFile: false,
    customLogger: {
      ...viteLogger,
      error: (msg, options) => {
        viteLogger.error(msg, options);
        process.exit(1);
      }
    },
    server: serverOptions,
    appType: "custom"
  });
  app2.use(vite.middlewares);
  app2.use("*", async (req, res, next) => {
    const url = req.originalUrl;
    try {
      const clientTemplate = path2.resolve(
        __dirname2,
        "..",
        "client",
        "index.html"
      );
      let template = await fs.promises.readFile(clientTemplate, "utf-8");
      template = template.replace(
        `src="/src/main.tsx"`,
        `src="/src/main.tsx?v=${nanoid2()}"`
      );
      const page = await vite.transformIndexHtml(url, template);
      res.status(200).set({ "Content-Type": "text/html" }).end(page);
    } catch (e) {
      vite.ssrFixStacktrace(e);
      next(e);
    }
  });
}
function serveStatic(app2) {
  const distPath = path2.resolve(__dirname2, "public");
  if (!fs.existsSync(distPath)) {
    throw new Error(
      `Could not find the build directory: ${distPath}, make sure to build the client first`
    );
  }
  app2.use(express.static(distPath));
  app2.use("*", (_req, res) => {
    res.sendFile(path2.resolve(distPath, "index.html"));
  });
}

// server/index.ts
var app = express2();
app.use(express2.json());
app.use(express2.urlencoded({ extended: false }));
app.use((req, res, next) => {
  const start = Date.now();
  const path3 = req.path;
  let capturedJsonResponse = void 0;
  const originalResJson = res.json;
  res.json = function(bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };
  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path3.startsWith("/api")) {
      let logLine = `${req.method} ${path3} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }
      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "\u2026";
      }
      log(logLine);
    }
  });
  next();
});
(async () => {
  const server = await registerRoutes(app);
  app.use((err, _req, res, _next) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";
    res.status(status).json({ message });
    throw err;
  });
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }
  const port = 5e3;
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true
  }, () => {
    log(`serving on port ${port}`);
  });
})();
