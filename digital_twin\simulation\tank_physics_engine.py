"""
Tank Physics Engine
Advanced physics simulation for industrial tank systems including:
- Heat transfer (conduction, convection, radiation)
- Fluid dynamics and mixing
- Mass transfer and chemical reactions
- Energy optimization
"""

import asyncio
import logging
import numpy as np
from typing import Dict, List, Optional, Any, Callable, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import math
from abc import ABC, abstractmethod

from ..core.tank_models import (
    TankConfiguration, TankStateData, TankPhysicsModel,
    TankGeometry, TankType, SensorType, ActuatorType,
    TankState, TankPerformanceMetrics
)

logger = logging.getLogger(__name__)


class SimulationState(Enum):
    """Physics simulation states"""
    STOPPED = "stopped"
    INITIALIZING = "initializing"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"


@dataclass
class SimulationConfiguration:
    """Configuration for physics simulation"""
    real_time_factor: float = 1.0  # 1.0 = real-time, >1.0 = faster than real-time
    time_step: float = 1.0  # seconds
    max_simulation_time: float = 86400.0  # 24 hours
    output_frequency: int = 1  # output every N steps
    
    # Numerical parameters
    spatial_discretization: int = 50  # number of spatial nodes
    temporal_tolerance: float = 1e-6
    max_iterations: int = 1000
    
    # Physics models to enable
    enable_heat_transfer: bool = True
    enable_fluid_dynamics: bool = True
    enable_mass_transfer: bool = False
    enable_chemical_reactions: bool = False
    enable_mixing: bool = True
    
    # Boundary conditions
    ambient_temperature: float = 20.0  # celsius
    ambient_pressure: float = 101325.0  # Pa
    wind_speed: float = 2.0  # m/s
    
    # Advanced features
    enable_uncertainty_quantification: bool = False
    enable_model_adaptation: bool = False
    enable_real_time_optimization: bool = False


class PhysicsModel(ABC):
    """Abstract base class for physics models"""
    
    @abstractmethod
    async def initialize(self, tank_config: TankConfiguration, sim_config: SimulationConfiguration):
        """Initialize the physics model"""
        pass
    
    @abstractmethod
    async def step(self, dt: float, state: TankStateData, inputs: Dict[str, float]) -> TankStateData:
        """Perform one simulation step"""
        pass
    
    @abstractmethod
    def get_model_parameters(self) -> Dict[str, float]:
        """Get current model parameters"""
        pass
    
    @abstractmethod
    async def update_parameters(self, parameters: Dict[str, float]):
        """Update model parameters"""
        pass


class HeatTransferModel(PhysicsModel):
    """Heat transfer physics model for tanks"""
    
    def __init__(self):
        self.tank_config: Optional[TankConfiguration] = None
        self.sim_config: Optional[SimulationConfiguration] = None
        self.physics_model: Optional[TankPhysicsModel] = None
        
        # Discretization
        self.spatial_nodes: int = 50
        self.temperature_field: np.ndarray = None
        self.node_positions: np.ndarray = None
        
        # Heat transfer coefficients
        self.convection_coefficient: float = 10.0  # W/m²/K
        self.radiation_coefficient: float = 5.67e-8  # Stefan-Boltzmann constant
        
        # Boundary conditions
        self.ambient_temperature: float = 20.0
        self.heat_input_zones: List[Tuple[int, int]] = []  # (start_node, end_node) for each heater
        
    async def initialize(self, tank_config: TankConfiguration, sim_config: SimulationConfiguration):
        """Initialize heat transfer model"""
        self.tank_config = tank_config
        self.sim_config = sim_config
        self.spatial_nodes = sim_config.spatial_discretization
        self.ambient_temperature = sim_config.ambient_temperature
        
        # Get physics parameters
        self.physics_model = TankPhysicsModel(
            thermal_conductivity=tank_config.thermal_properties.get('thermal_conductivity', 0.6),
            specific_heat=tank_config.thermal_properties.get('specific_heat', 4186.0),
            density=tank_config.thermal_properties.get('density', 1000.0),
            convection_coefficient=tank_config.thermal_properties.get('convection_coefficient', 10.0)
        )
        
        # Initialize temperature field
        initial_temp = tank_config.control_parameters.temperature_setpoint
        self.temperature_field = np.full(self.spatial_nodes, initial_temp, dtype=np.float64)
        
        # Create node positions (1D along tank height)
        self.node_positions = np.linspace(0, tank_config.geometry.height, self.spatial_nodes)
        
        # Setup heating zones
        self._setup_heating_zones()
        
        logger.info(f"Heat transfer model initialized with {self.spatial_nodes} nodes")
    
    async def step(self, dt: float, state: TankStateData, inputs: Dict[str, float]) -> TankStateData:
        """Perform heat transfer simulation step"""
        try:
            # Extract heating inputs
            heater_powers = []
            for actuator_id, power in state.heater_output.items():
                heater_powers.append(power)
            
            # Add external heating inputs
            for key, value in inputs.items():
                if 'heater' in key.lower() or 'heating' in key.lower():
                    heater_powers.append(value)
            
            # Solve heat equation
            new_temperature_field = await self._solve_heat_equation(dt, heater_powers)
            
            # Update temperature field
            self.temperature_field = new_temperature_field
            
            # Update tank state with calculated temperatures
            state.temperature.clear()
            
            # Calculate zone temperatures (average over zones)
            zones_per_heater = len(self.heat_input_zones)
            if zones_per_heater > 0:
                nodes_per_zone = self.spatial_nodes // zones_per_heater
                for i in range(zones_per_heater):
                    start_idx = i * nodes_per_zone
                    end_idx = min((i + 1) * nodes_per_zone, self.spatial_nodes)
                    zone_temp = np.mean(self.temperature_field[start_idx:end_idx])
                    state.temperature[f"zone_{i+1}"] = zone_temp
            
            # Calculate overall average temperature
            state.temperature['average'] = np.mean(self.temperature_field)
            
            # Calculate heat loss rate
            surface_area = self.tank_config.geometry.surface_area
            avg_temp = np.mean(self.temperature_field)
            heat_loss = self.convection_coefficient * surface_area * (avg_temp - self.ambient_temperature)
            state.heat_loss_rate = heat_loss / 1000.0  # Convert to kW
            
            return state
            
        except Exception as e:
            logger.error(f"Error in heat transfer step: {e}")
            return state
    
    async def _solve_heat_equation(self, dt: float, heater_powers: List[float]) -> np.ndarray:
        """Solve 1D heat equation using finite difference method"""
        try:
            # Physical properties
            k = self.physics_model.thermal_conductivity  # W/m/K
            rho = self.physics_model.density  # kg/m³
            cp = self.physics_model.specific_heat  # J/kg/K
            alpha = k / (rho * cp)  # thermal diffusivity m²/s
            
            # Spatial discretization
            dx = self.tank_config.geometry.height / (self.spatial_nodes - 1)
            
            # Check stability criterion (CFL condition)
            stability_limit = dx**2 / (2 * alpha)
            if dt > stability_limit:
                dt = stability_limit * 0.5
                logger.warning(f"Time step reduced to {dt:.6f}s for stability")
            
            # Finite difference coefficients
            r = alpha * dt / (dx**2)
            
            # Initialize matrices for implicit method (Crank-Nicolson)
            A = np.zeros((self.spatial_nodes, self.spatial_nodes))
            b = np.zeros(self.spatial_nodes)
            
            # Interior nodes
            for i in range(1, self.spatial_nodes - 1):
                A[i, i-1] = -r/2
                A[i, i] = 1 + r
                A[i, i+1] = -r/2
                
                b[i] = (r/2) * self.temperature_field[i-1] + (1 - r) * self.temperature_field[i] + (r/2) * self.temperature_field[i+1]
            
            # Boundary conditions
            # Bottom boundary (insulated or fixed temperature)
            A[0, 0] = 1
            b[0] = self.temperature_field[0]  # Maintain current temperature
            
            # Top boundary (convective heat loss)
            h = self.convection_coefficient  # W/m²/K
            A[-1, -2] = -k/dx
            A[-1, -1] = k/dx + h
            b[-1] = h * self.ambient_temperature
            
            # Add heat source terms
            heat_per_node = self._distribute_heat_sources(heater_powers)
            for i in range(self.spatial_nodes):
                if heat_per_node[i] > 0:
                    volume_per_node = self.tank_config.geometry.volume / self.spatial_nodes
                    mass_per_node = rho * volume_per_node
                    b[i] += (heat_per_node[i] * dt) / (mass_per_node * cp)
            
            # Solve linear system
            new_temperature = np.linalg.solve(A, b)
            
            return new_temperature
            
        except Exception as e:
            logger.error(f"Error solving heat equation: {e}")
            return self.temperature_field.copy()
    
    def _setup_heating_zones(self):
        """Setup heating zones based on tank configuration"""
        try:
            # Get heater actuators
            heater_actuators = self.tank_config.get_actuators_by_type(ActuatorType.HEATER)
            num_heaters = len(heater_actuators)
            
            if num_heaters == 0:
                # Default to 4 zones
                num_heaters = 4
            
            # Distribute heating zones evenly
            nodes_per_zone = self.spatial_nodes // num_heaters
            self.heat_input_zones = []
            
            for i in range(num_heaters):
                start_node = i * nodes_per_zone
                end_node = min((i + 1) * nodes_per_zone, self.spatial_nodes) - 1
                self.heat_input_zones.append((start_node, end_node))
            
            logger.info(f"Setup {num_heaters} heating zones: {self.heat_input_zones}")
            
        except Exception as e:
            logger.error(f"Error setting up heating zones: {e}")
            self.heat_input_zones = [(0, self.spatial_nodes - 1)]
    
    def _distribute_heat_sources(self, heater_powers: List[float]) -> np.ndarray:
        """Distribute heat sources across spatial nodes"""
        heat_per_node = np.zeros(self.spatial_nodes)
        
        try:
            for i, power in enumerate(heater_powers):
                if i < len(self.heat_input_zones):
                    start_node, end_node = self.heat_input_zones[i]
                    nodes_in_zone = end_node - start_node + 1
                    
                    # Distribute power evenly across nodes in zone
                    if nodes_in_zone > 0:
                        power_per_node = power / nodes_in_zone
                        for node in range(start_node, end_node + 1):
                            heat_per_node[node] += power_per_node
            
        except Exception as e:
            logger.error(f"Error distributing heat sources: {e}")
        
        return heat_per_node
    
    def get_model_parameters(self) -> Dict[str, float]:
        """Get current heat transfer model parameters"""
        if self.physics_model:
            return {
                'thermal_conductivity': self.physics_model.thermal_conductivity,
                'specific_heat': self.physics_model.specific_heat,
                'density': self.physics_model.density,
                'convection_coefficient': self.physics_model.convection_coefficient,
                'ambient_temperature': self.physics_model.ambient_temperature
            }
        return {}
    
    async def update_parameters(self, parameters: Dict[str, float]):
        """Update heat transfer model parameters"""
        if self.physics_model:
            for param, value in parameters.items():
                if hasattr(self.physics_model, param):
                    setattr(self.physics_model, param, value)
                    logger.info(f"Updated {param} = {value}")


class FluidDynamicsModel(PhysicsModel):
    """Fluid dynamics model for tank systems"""
    
    def __init__(self):
        self.tank_config: Optional[TankConfiguration] = None
        self.sim_config: Optional[SimulationConfiguration] = None
        self.physics_model: Optional[TankPhysicsModel] = None
        
        # Flow field
        self.velocity_field: np.ndarray = None
        self.pressure_field: np.ndarray = None
        self.mixing_efficiency: float = 0.8
        
    async def initialize(self, tank_config: TankConfiguration, sim_config: SimulationConfiguration):
        """Initialize fluid dynamics model"""
        self.tank_config = tank_config
        self.sim_config = sim_config
        
        # Initialize physics model
        self.physics_model = TankPhysicsModel(
            viscosity=tank_config.fluid_properties.get('viscosity', 0.001),
            density=tank_config.fluid_properties.get('density', 1000.0)
        )
        
        # Initialize flow fields
        num_nodes = sim_config.spatial_discretization
        self.velocity_field = np.zeros(num_nodes)
        self.pressure_field = np.full(num_nodes, sim_config.ambient_pressure)
        
        logger.info("Fluid dynamics model initialized")
    
    async def step(self, dt: float, state: TankStateData, inputs: Dict[str, float]) -> TankStateData:
        """Perform fluid dynamics simulation step"""
        try:
            # Get pump and mixer inputs
            pump_powers = list(state.pump_output.values())
            mixer_speeds = list(state.mixer_speed.values())
            
            # Calculate mixing time
            if mixer_speeds:
                avg_mixer_speed = sum(mixer_speeds) / len(mixer_speeds)
                mixing_time = self.physics_model.calculate_mixing_time(
                    state.volume_current, avg_mixer_speed
                )
                
                # Update mixing efficiency
                if mixing_time > 0:
                    self.mixing_efficiency = min(1.0, dt / mixing_time)
            
            # Update flow rates
            flow_data = {}
            for i, pump_power in enumerate(pump_powers):
                # Simple correlation: flow rate proportional to pump power
                flow_rate = pump_power * 0.1  # L/min per % power
                flow_data[f"pump_{i+1}"] = flow_rate
            
            state.flow.update(flow_data)
            
            return state
            
        except Exception as e:
            logger.error(f"Error in fluid dynamics step: {e}")
            return state
    
    def get_model_parameters(self) -> Dict[str, float]:
        """Get fluid dynamics model parameters"""
        return {
            'viscosity': self.physics_model.viscosity,
            'density': self.physics_model.density,
            'mixing_efficiency': self.mixing_efficiency
        }
    
    async def update_parameters(self, parameters: Dict[str, float]):
        """Update fluid dynamics model parameters"""
        for param, value in parameters.items():
            if hasattr(self.physics_model, param):
                setattr(self.physics_model, param, value)
            elif param == 'mixing_efficiency':
                self.mixing_efficiency = value


class TankPhysicsEngine:
    """Main physics engine for tank simulation"""
    
    def __init__(self, config: SimulationConfiguration):
        self.config = config
        self.state = SimulationState.STOPPED
        
        # Physics models
        self.heat_transfer_model: Optional[HeatTransferModel] = None
        self.fluid_dynamics_model: Optional[FluidDynamicsModel] = None
        
        # Simulation state
        self.current_time: float = 0.0
        self.step_count: int = 0
        self.tank_configs: List[TankConfiguration] = []
        self.tank_states: Dict[str, TankStateData] = {}
        
        # Callbacks
        self.output_callbacks: List[Callable] = []
        self.state_callbacks: List[Callable] = []
        
        # Tasks
        self.simulation_task: Optional[asyncio.Task] = None
        
        # Performance tracking
        self.performance_metrics: Dict[str, TankPerformanceMetrics] = {}
        
    async def initialize(self, tank_configs: List[TankConfiguration]) -> bool:
        """Initialize physics engine with tank configurations"""
        try:
            logger.info("Initializing tank physics engine")
            self.state = SimulationState.INITIALIZING
            
            self.tank_configs = tank_configs
            
            # Initialize physics models
            if self.config.enable_heat_transfer:
                self.heat_transfer_model = HeatTransferModel()
                for tank_config in tank_configs:
                    await self.heat_transfer_model.initialize(tank_config, self.config)
            
            if self.config.enable_fluid_dynamics:
                self.fluid_dynamics_model = FluidDynamicsModel()
                for tank_config in tank_configs:
                    await self.fluid_dynamics_model.initialize(tank_config, self.config)
            
            # Initialize tank states
            for tank_config in tank_configs:
                self.tank_states[tank_config.tank_id] = TankStateData(
                    timestamp=datetime.now(),
                    tank_id=tank_config.tank_id,
                    state=TankState.STOPPED
                )
                
                # Initialize performance metrics
                self.performance_metrics[tank_config.tank_id] = TankPerformanceMetrics(
                    timestamp=datetime.now(),
                    tank_id=tank_config.tank_id
                )
            
            self.state = SimulationState.STOPPED
            logger.info("Tank physics engine initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing physics engine: {e}")
            self.state = SimulationState.ERROR
            return False
    
    async def start(self) -> bool:
        """Start physics simulation"""
        try:
            if self.state not in [SimulationState.STOPPED, SimulationState.PAUSED]:
                logger.warning("Physics engine not in stoppable state")
                return False
            
            logger.info("Starting physics simulation")
            self.state = SimulationState.RUNNING
            
            # Start simulation task
            self.simulation_task = asyncio.create_task(self._simulation_loop())
            
            return True
            
        except Exception as e:
            logger.error(f"Error starting physics simulation: {e}")
            self.state = SimulationState.ERROR
            return False
    
    async def stop(self):
        """Stop physics simulation"""
        try:
            logger.info("Stopping physics simulation")
            self.state = SimulationState.STOPPED
            
            if self.simulation_task:
                self.simulation_task.cancel()
                try:
                    await self.simulation_task
                except asyncio.CancelledError:
                    pass
            
        except Exception as e:
            logger.error(f"Error stopping physics simulation: {e}")
    
    async def pause(self):
        """Pause physics simulation"""
        if self.state == SimulationState.RUNNING:
            self.state = SimulationState.PAUSED
            logger.info("Physics simulation paused")
    
    async def resume(self):
        """Resume physics simulation"""
        if self.state == SimulationState.PAUSED:
            self.state = SimulationState.RUNNING
            logger.info("Physics simulation resumed")
    
    async def step_simulation(self, tank_id: str, inputs: Dict[str, float] = None) -> TankStateData:
        """Perform single simulation step for specific tank"""
        try:
            if tank_id not in self.tank_states:
                logger.error(f"Tank {tank_id} not found")
                return None
            
            tank_config = self._get_tank_config(tank_id)
            if not tank_config:
                return None
            
            tank_state = self.tank_states[tank_id]
            if inputs is None:
                inputs = {}
            
            # Update timestamp
            tank_state.timestamp = datetime.now()
            
            # Run heat transfer model
            if self.heat_transfer_model:
                tank_state = await self.heat_transfer_model.step(
                    self.config.time_step, tank_state, inputs
                )
            
            # Run fluid dynamics model
            if self.fluid_dynamics_model:
                tank_state = await self.fluid_dynamics_model.step(
                    self.config.time_step, tank_state, inputs
                )
            
            # Update derived values
            await self._calculate_derived_values(tank_state, tank_config)
            
            # Update performance metrics
            await self._update_performance_metrics(tank_id, tank_state)
            
            return tank_state
            
        except Exception as e:
            logger.error(f"Error in simulation step for tank {tank_id}: {e}")
            return self.tank_states.get(tank_id)
    
    async def get_tank_state(self, tank_id: str) -> Optional[TankStateData]:
        """Get current tank state"""
        return self.tank_states.get(tank_id)
    
    async def get_performance_metrics(self, tank_id: str) -> Optional[TankPerformanceMetrics]:
        """Get performance metrics for tank"""
        return self.performance_metrics.get(tank_id)
    
    def register_output_callback(self, callback: Callable):
        """Register callback for simulation output"""
        self.output_callbacks.append(callback)
    
    def register_state_callback(self, callback: Callable):
        """Register callback for state changes"""
        self.state_callbacks.append(callback)
    
    def get_simulation_status(self) -> Dict[str, Any]:
        """Get simulation status"""
        return {
            'state': self.state.value,
            'current_time': self.current_time,
            'step_count': self.step_count,
            'tank_count': len(self.tank_configs),
            'models_enabled': {
                'heat_transfer': self.config.enable_heat_transfer,
                'fluid_dynamics': self.config.enable_fluid_dynamics,
                'mass_transfer': self.config.enable_mass_transfer,
                'chemical_reactions': self.config.enable_chemical_reactions
            }
        }
    
    # Private methods
    
    async def _simulation_loop(self):
        """Main simulation loop"""
        last_time = asyncio.get_event_loop().time()
        
        while self.state == SimulationState.RUNNING:
            try:
                # Calculate actual dt based on real-time factor
                current_time = asyncio.get_event_loop().time()
                real_dt = current_time - last_time
                sim_dt = real_dt * self.config.real_time_factor
                
                # Limit dt to configured time step
                dt = min(sim_dt, self.config.time_step)
                
                # Run simulation step for all tanks
                for tank_config in self.tank_configs:
                    await self.step_simulation(tank_config.tank_id)
                
                # Update time and step count
                self.current_time += dt
                self.step_count += 1
                
                # Output results if needed
                if self.step_count % self.config.output_frequency == 0:
                    await self._emit_output()
                
                # Check if simulation should end
                if self.current_time >= self.config.max_simulation_time:
                    logger.info("Maximum simulation time reached")
                    break
                
                # Sleep to maintain real-time factor
                sleep_time = self.config.time_step / self.config.real_time_factor
                await asyncio.sleep(sleep_time)
                
                last_time = current_time
                
            except Exception as e:
                logger.error(f"Error in simulation loop: {e}")
                self.state = SimulationState.ERROR
                break
    
    def _get_tank_config(self, tank_id: str) -> Optional[TankConfiguration]:
        """Get tank configuration by ID"""
        for tank_config in self.tank_configs:
            if tank_config.tank_id == tank_id:
                return tank_config
        return None
    
    async def _calculate_derived_values(self, tank_state: TankStateData, tank_config: TankConfiguration):
        """Calculate derived values for tank state"""
        try:
            # Calculate volume from level sensors
            if tank_state.level:
                avg_level = sum(tank_state.level.values()) / len(tank_state.level)
                tank_state.volume_current = tank_config.geometry.capacity * (avg_level / 100.0)
            
            # Calculate mass
            density = tank_config.fluid_properties.get('density', 1000.0)
            tank_state.mass_current = tank_state.volume_current * density / 1000.0  # Convert to kg
            
            # Calculate energy content
            if tank_state.temperature:
                avg_temp = sum(tank_state.temperature.values()) / len(tank_state.temperature)
                specific_heat = tank_config.thermal_properties.get('specific_heat', 4186.0)
                tank_state.energy_content = tank_state.mass_current * specific_heat * (avg_temp - 20.0) / 1000.0  # kJ
            
        except Exception as e:
            logger.error(f"Error calculating derived values: {e}")
    
    async def _update_performance_metrics(self, tank_id: str, tank_state: TankStateData):
        """Update performance metrics"""
        try:
            metrics = self.performance_metrics.get(tank_id)
            if not metrics:
                return
            
            # Update timestamp
            metrics.timestamp = datetime.now()
            
            # Calculate energy consumption
            total_heater_power = sum(tank_state.heater_output.values())
            dt_hours = self.config.time_step / 3600.0
            energy_consumed = total_heater_power * dt_hours / 1000.0  # kWh
            metrics.energy_consumption += energy_consumed
            
            # Calculate temperature stability (standard deviation)
            if tank_state.temperature:
                temps = list(tank_state.temperature.values())
                metrics.temperature_stability = float(np.std(temps))
            
            # Calculate energy efficiency
            if tank_state.energy_content > 0 and metrics.energy_consumption > 0:
                metrics.energy_efficiency = (tank_state.energy_content / metrics.energy_consumption) * 100.0
            
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")
    
    async def _emit_output(self):
        """Emit simulation output to callbacks"""
        try:
            output_data = {
                'timestamp': datetime.now().isoformat(),
                'simulation_time': self.current_time,
                'step_count': self.step_count,
                'tank_states': {},
                'performance_metrics': {}
            }
            
            # Add tank states
            for tank_id, tank_state in self.tank_states.items():
                output_data['tank_states'][tank_id] = {
                    'temperature': dict(tank_state.temperature),
                    'pressure': dict(tank_state.pressure),
                    'level': dict(tank_state.level),
                    'flow': dict(tank_state.flow),
                    'heater_output': dict(tank_state.heater_output),
                    'volume_current': tank_state.volume_current,
                    'mass_current': tank_state.mass_current,
                    'energy_content': tank_state.energy_content,
                    'heat_loss_rate': tank_state.heat_loss_rate
                }
            
            # Add performance metrics
            for tank_id, metrics in self.performance_metrics.items():
                output_data['performance_metrics'][tank_id] = {
                    'energy_consumption': metrics.energy_consumption,
                    'energy_efficiency': metrics.energy_efficiency,
                    'temperature_stability': metrics.temperature_stability
                }
            
            # Notify callbacks
            for callback in self.output_callbacks:
                try:
                    await callback(output_data)
                except Exception as e:
                    logger.error(f"Error in output callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error emitting output: {e}")


# Factory function for creating physics engines
def create_tank_physics_engine(tank_configs: List[TankConfiguration], 
                               real_time_factor: float = 1.0,
                               enable_advanced_models: bool = True) -> TankPhysicsEngine:
    """Factory function to create tank physics engine"""
    
    sim_config = SimulationConfiguration(
        real_time_factor=real_time_factor,
        time_step=1.0,
        max_simulation_time=86400.0,
        spatial_discretization=50,
        enable_heat_transfer=True,
        enable_fluid_dynamics=enable_advanced_models,
        enable_mass_transfer=enable_advanced_models,
        enable_mixing=True
    )
    
    engine = TankPhysicsEngine(sim_config)
    return engine