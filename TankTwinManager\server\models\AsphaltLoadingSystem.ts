/**
 * Asphalt Loading Station System Models
 * Implements loading operations for truck loading with flow control and automation
 */

export interface LoadingStation {
  id: string;
  name: string;
  position: [number, number, number];
  specifications: {
    numberOfArms: number;
    maxFlowRate: number; // L/min per arm
    maxTruckCapacity: number; // L
    loadingHeight: number; // m
    armReach: number; // m
    automationLevel: 'manual' | 'semi_automatic' | 'fully_automatic';
    safetyFeatures: string[];
  };
  operationalData: {
    currentFlowRate: number; // L/min
    totalVolumeLoaded: number; // L
    loadingTime: number; // minutes
    truckPresent: boolean;
    loadingInProgress: boolean;
    lastLoadingTime: Date;
    dailyLoadings: number;
  };
  truckData: {
    truckId: string;
    capacity: number; // L
    currentVolume: number; // L
    targetVolume: number; // L
    arrivalTime: Date;
    estimatedDepartureTime: Date;
    driverInfo: {
      name: string;
      license: string;
      company: string;
    };
  };
  loadingArms: LoadingArm[];
  controlSystem: {
    mode: 'automatic' | 'manual' | 'remote';
    presetVolumes: number[]; // L
    flowRateSetpoint: number; // L/min
    temperatureSetpoint: number; // °C
    safetyInterlocks: {
      truckPositionOk: boolean;
      emergencyStopActive: boolean;
      armPositionOk: boolean;
      temperatureOk: boolean;
    };
  };
  sensors: {
    truckDetection: string;
    weightScale: string;
    temperatureSensor: string;
    flowMeter: string;
    levelSensor: string;
  };
  status: {
    operational: boolean;
    maintenanceRequired: boolean;
    alarmActive: boolean;
    lastMaintenance: Date;
    nextMaintenance: Date;
  };
}

export interface LoadingArm {
  id: string;
  stationId: string;
  name: string;
  specifications: {
    diameter: number; // mm
    length: number; // m
    maxFlowRate: number; // L/min
    maxTemperature: number; // °C
    maxPressure: number; // bar
    material: 'stainless_steel' | 'carbon_steel';
    insulationType: 'steam_traced' | 'electric_traced' | 'insulated';
  };
  position: {
    basePosition: [number, number, number];
    currentPosition: [number, number, number];
    targetPosition: [number, number, number];
    rotation: number; // degrees
    elevation: number; // degrees
  };
  operationalData: {
    flowRate: number; // L/min
    pressure: number; // bar
    temperature: number; // °C
    isConnected: boolean;
    isFlowing: boolean;
    totalVolumeDelivered: number; // L
  };
  controlSystem: {
    positionControl: {
      mode: 'manual' | 'automatic';
      targetX: number;
      targetY: number;
      targetZ: number;
    };
    flowControl: {
      mode: 'manual' | 'automatic';
      setpoint: number; // L/min
      valve: {
        position: number; // % open
        type: 'ball' | 'butterfly' | 'gate';
        actuator: 'pneumatic' | 'electric' | 'manual';
      };
    };
  };
  sensors: {
    positionSensors: string[];
    flowMeter: string;
    pressureSensor: string;
    temperatureSensor: string;
    connectionSensor: string;
  };
  status: {
    operational: boolean;
    connected: boolean;
    alarmActive: boolean;
    maintenanceRequired: boolean;
  };
}

export interface AsphaltDistributionSystem {
  id: string;
  name: string;
  mainHeader: {
    diameter: number; // mm
    pressure: number; // bar
    temperature: number; // °C
    flowRate: number; // L/min
    material: 'carbon_steel' | 'stainless_steel';
  };
  distributionLines: AsphaltPipe[];
  valves: DistributionValve[];
  pumps: AsphaltPump[];
  heatingSystem: {
    type: 'steam_traced' | 'electric_traced' | 'hot_oil_traced';
    temperature: number; // °C
    powerConsumption: number; // kW
    efficiency: number; // %
  };
  controlSystem: {
    masterController: {
      mode: 'automatic' | 'manual' | 'cascade';
      pressureSetpoint: number; // bar
      temperatureSetpoint: number; // °C
      flowDistribution: { [tankId: number]: number }; // % allocation
    };
    safetySystem: {
      emergencyShutdown: boolean;
      highPressureAlarm: boolean;
      lowTemperatureAlarm: boolean;
      pumpFailureAlarm: boolean;
    };
  };
}

export interface AsphaltPipe {
  id: string;
  name: string;
  startPoint: [number, number, number];
  endPoint: [number, number, number];
  specifications: {
    diameter: number; // mm
    length: number; // m
    material: 'carbon_steel' | 'stainless_steel';
    insulation: {
      type: 'mineral_wool' | 'polyurethane';
      thickness: number; // mm
    };
    tracing: {
      type: 'steam' | 'electric' | 'hot_oil';
      temperature: number; // °C
    };
    maxPressure: number; // bar
    maxTemperature: number; // °C
  };
  operationalData: {
    flowRate: number; // L/min
    pressure: number; // bar
    temperature: number; // °C
    velocity: number; // m/s
    viscosity: number; // cP
  };
  connectedTanks: number[];
  connectedStations: string[];
  sensors: {
    flowMeter: string;
    pressureSensor: string;
    temperatureSensor: string;
  };
}

export interface DistributionValve {
  id: string;
  name: string;
  position: [number, number, number];
  type: 'ball' | 'butterfly' | 'gate' | 'globe';
  specifications: {
    diameter: number; // mm
    maxPressure: number; // bar
    maxTemperature: number; // °C
    material: 'carbon_steel' | 'stainless_steel' | 'bronze';
    actuator: {
      type: 'pneumatic' | 'electric' | 'manual';
      powerRating: number; // W
      responseTime: number; // seconds
    };
  };
  operationalData: {
    position: number; // % open (0-100)
    flowRate: number; // L/min
    pressureDrop: number; // bar
    cycleCount: number;
    operatingHours: number;
  };
  controlParameters: {
    setpoint: number; // % open
    controlMode: 'manual' | 'automatic' | 'remote';
    failSafePosition: 'open' | 'closed' | 'last_position';
  };
  status: {
    operational: boolean;
    alarmActive: boolean;
    maintenanceRequired: boolean;
    calibrationRequired: boolean;
  };
  sensors: {
    positionSensor: string;
    flowMeter: string;
    pressureSensors: string[];
  };
}

export interface AsphaltPump {
  id: string;
  name: string;
  position: [number, number, number];
  type: 'gear' | 'screw' | 'centrifugal';
  specifications: {
    maxFlowRate: number; // L/min
    maxPressure: number; // bar
    powerRating: number; // kW
    efficiency: number; // %
    maxTemperature: number; // °C
    viscosityRange: [number, number]; // cP
  };
  operationalData: {
    flowRate: number; // L/min
    pressure: number; // bar
    powerConsumption: number; // kW
    efficiency: number; // %
    speed: number; // RPM
    temperature: number; // °C
    vibration: number; // mm/s
    operatingHours: number;
  };
  controlParameters: {
    speedSetpoint: number; // RPM or %
    pressureSetpoint: number; // bar
    flowSetpoint: number; // L/min
    controlMode: 'speed' | 'pressure' | 'flow';
    variableFrequencyDrive: {
      enabled: boolean;
      frequency: number; // Hz
      voltage: number; // V
    };
  };
  status: {
    isRunning: boolean;
    alarmActive: boolean;
    maintenanceRequired: boolean;
    cavitationDetected: boolean;
    overheating: boolean;
  };
  sensors: {
    flowMeter: string;
    pressureSensor: string;
    temperatureSensor: string;
    vibrationSensor: string;
    powerMeter: string;
  };
}

// Factory function to create a loading station
export function createLoadingStation(
  id: string, 
  position: [number, number, number], 
  connectedTanks: number[]
): LoadingStation {
  return {
    id,
    name: `Loading Station ${id}`,
    position,
    specifications: {
      numberOfArms: 2,
      maxFlowRate: 1000, // L/min per arm
      maxTruckCapacity: 30000, // L
      loadingHeight: 4, // m
      armReach: 8, // m
      automationLevel: 'semi_automatic',
      safetyFeatures: [
        'Emergency Stop',
        'Truck Position Detection',
        'Overflow Protection',
        'Temperature Monitoring',
        'Grounding System'
      ]
    },
    operationalData: {
      currentFlowRate: 0,
      totalVolumeLoaded: 0,
      loadingTime: 0,
      truckPresent: false,
      loadingInProgress: false,
      lastLoadingTime: new Date(),
      dailyLoadings: 0
    },
    truckData: {
      truckId: '',
      capacity: 0,
      currentVolume: 0,
      targetVolume: 0,
      arrivalTime: new Date(),
      estimatedDepartureTime: new Date(),
      driverInfo: {
        name: '',
        license: '',
        company: ''
      }
    },
    loadingArms: [
      createLoadingArm(`${id}-ARM-01`, id),
      createLoadingArm(`${id}-ARM-02`, id)
    ],
    controlSystem: {
      mode: 'automatic',
      presetVolumes: [10000, 15000, 20000, 25000, 30000],
      flowRateSetpoint: 800,
      temperatureSetpoint: 150,
      safetyInterlocks: {
        truckPositionOk: false,
        emergencyStopActive: false,
        armPositionOk: true,
        temperatureOk: true
      }
    },
    sensors: {
      truckDetection: `TD-${id}`,
      weightScale: `WS-${id}`,
      temperatureSensor: `TT-${id}`,
      flowMeter: `FT-${id}`,
      levelSensor: `LT-${id}`
    },
    status: {
      operational: true,
      maintenanceRequired: false,
      alarmActive: false,
      lastMaintenance: new Date(),
      nextMaintenance: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
    }
  };
}

// Enhanced loading station management system
export interface LoadingStationManager {
  stations: Map<string, LoadingStation>;
  operationalMetrics: {
    totalLoadingsToday: number;
    totalVolumeToday: number; // L
    averageLoadingTime: number; // minutes
    peakFlowRate: number; // L/min
    efficiency: number; // %
    downtime: number; // minutes
  };
  queueManagement: {
    waitingTrucks: TruckInfo[];
    estimatedWaitTime: number; // minutes
    maxQueueLength: number;
  };
  safetySystem: {
    emergencyShutdownActive: boolean;
    spillDetectionActive: boolean;
    fireSuppressionReady: boolean;
    groundingSystemActive: boolean;
  };
}

export interface TruckInfo {
  id: string;
  arrivalTime: Date;
  estimatedServiceTime: number; // minutes
  priority: 'normal' | 'high' | 'emergency';
  company: string;
  driver: {
    name: string;
    license: string;
    certifications: string[];
  };
  vehicle: {
    capacity: number; // L
    type: 'tanker' | 'trailer' | 'truck';
    plateNumber: string;
    lastInspection: Date;
  };
  loadingRequirements: {
    productType: string;
    targetVolume: number; // L
    temperatureRequirement: number; // °C
    specialInstructions: string[];
  };
}

export interface LoadingSequence {
  id: string;
  stationId: string;
  truckId: string;
  startTime: Date;
  estimatedEndTime: Date;
  actualEndTime?: Date;
  steps: LoadingStep[];
  status: 'pending' | 'in_progress' | 'completed' | 'aborted';
  operatorId: string;
}

export interface LoadingStep {
  id: string;
  name: string;
  description: string;
  estimatedDuration: number; // minutes
  actualDuration?: number; // minutes
  status: 'pending' | 'in_progress' | 'completed' | 'skipped' | 'failed';
  requirements: string[];
  safetyChecks: string[];
  automationLevel: 'manual' | 'semi_auto' | 'full_auto';
}

// Factory function to create a loading arm
export function createLoadingArm(id: string, stationId: string): LoadingArm {
  return {
    id,
    stationId,
    name: `Loading Arm ${id}`,
    specifications: {
      diameter: 100, // mm
      length: 8, // m
      maxFlowRate: 500, // L/min
      maxTemperature: 200, // °C
      maxPressure: 10, // bar
      material: 'stainless_steel',
      insulationType: 'steam_traced'
    },
    position: {
      basePosition: [0, 3, 0],
      currentPosition: [0, 3, 0],
      targetPosition: [0, 3, 0],
      rotation: 0,
      elevation: 0
    },
    operationalData: {
      flowRate: 0,
      pressure: 0,
      temperature: 150,
      isConnected: false,
      isFlowing: false,
      totalVolumeDelivered: 0
    },
    controlSystem: {
      positionControl: {
        mode: 'manual',
        targetX: 0,
        targetY: 3,
        targetZ: 0
      },
      flowControl: {
        mode: 'automatic',
        setpoint: 400,
        valve: {
          position: 0,
          type: 'ball',
          actuator: 'pneumatic'
        }
      }
    },
    sensors: {
      positionSensors: [`PS-${id}-X`, `PS-${id}-Y`, `PS-${id}-Z`],
      flowMeter: `FT-${id}`,
      pressureSensor: `PT-${id}`,
      temperatureSensor: `TT-${id}`,
      connectionSensor: `CS-${id}`
    },
    status: {
      operational: true,
      connected: false,
      alarmActive: false,
      maintenanceRequired: false
    }
  };
}

// Factory function to create a loading sequence
export function createLoadingSequence(
  stationId: string,
  truckInfo: TruckInfo,
  operatorId: string
): LoadingSequence {
  const estimatedDuration = truckInfo.loadingRequirements.targetVolume / 800; // minutes at 800 L/min

  return {
    id: `LS-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    stationId,
    truckId: truckInfo.id,
    startTime: new Date(),
    estimatedEndTime: new Date(Date.now() + estimatedDuration * 60 * 1000),
    steps: [
      {
        id: 'STEP-01',
        name: 'Truck Positioning',
        description: 'Position truck at loading bay and engage parking brake',
        estimatedDuration: 2,
        status: 'pending',
        requirements: ['Truck positioned correctly', 'Parking brake engaged'],
        safetyChecks: ['Wheel chocks in place', 'Engine off'],
        automationLevel: 'manual'
      },
      {
        id: 'STEP-02',
        name: 'Safety Verification',
        description: 'Verify all safety systems and grounding',
        estimatedDuration: 3,
        status: 'pending',
        requirements: ['Grounding cable connected', 'Emergency stops tested'],
        safetyChecks: ['Grounding verified', 'Fire suppression ready', 'Spill containment ready'],
        automationLevel: 'semi_auto'
      },
      {
        id: 'STEP-03',
        name: 'Loading Arm Connection',
        description: 'Connect loading arms to truck tank',
        estimatedDuration: 5,
        status: 'pending',
        requirements: ['Arms positioned', 'Connections sealed'],
        safetyChecks: ['Connection integrity verified', 'No leaks detected'],
        automationLevel: 'semi_auto'
      },
      {
        id: 'STEP-04',
        name: 'Pre-Loading Checks',
        description: 'Verify product temperature and system readiness',
        estimatedDuration: 2,
        status: 'pending',
        requirements: ['Temperature within range', 'System pressurized'],
        safetyChecks: ['Temperature verified', 'Pressure within limits'],
        automationLevel: 'full_auto'
      },
      {
        id: 'STEP-05',
        name: 'Loading Operation',
        description: 'Transfer asphalt product to truck tank',
        estimatedDuration: estimatedDuration * 0.8, // 80% of total time
        status: 'pending',
        requirements: ['Flow rate controlled', 'Volume monitored'],
        safetyChecks: ['Continuous monitoring', 'Overflow protection active'],
        automationLevel: 'full_auto'
      },
      {
        id: 'STEP-06',
        name: 'Loading Completion',
        description: 'Complete loading and disconnect equipment',
        estimatedDuration: 3,
        status: 'pending',
        requirements: ['Target volume reached', 'Arms disconnected'],
        safetyChecks: ['No residual pressure', 'Arms properly stowed'],
        automationLevel: 'semi_auto'
      },
      {
        id: 'STEP-07',
        name: 'Documentation',
        description: 'Generate loading documentation and release truck',
        estimatedDuration: 2,
        status: 'pending',
        requirements: ['Bill of lading generated', 'Quality certificate issued'],
        safetyChecks: ['Final safety check', 'Area clear'],
        automationLevel: 'manual'
      }
    ],
    status: 'pending',
    operatorId
  };
}

// Factory function to create loading station manager
export function createLoadingStationManager(): LoadingStationManager {
  return {
    stations: new Map(),
    operationalMetrics: {
      totalLoadingsToday: 0,
      totalVolumeToday: 0,
      averageLoadingTime: 25, // minutes
      peakFlowRate: 0,
      efficiency: 85, // %
      downtime: 0
    },
    queueManagement: {
      waitingTrucks: [],
      estimatedWaitTime: 0,
      maxQueueLength: 5
    },
    safetySystem: {
      emergencyShutdownActive: false,
      spillDetectionActive: true,
      fireSuppressionReady: true,
      groundingSystemActive: true
    }
  };
}

// Utility function to calculate loading efficiency
export function calculateLoadingEfficiency(
  actualTime: number,
  estimatedTime: number,
  actualVolume: number,
  targetVolume: number
): number {
  const timeEfficiency = Math.min(100, (estimatedTime / actualTime) * 100);
  const volumeEfficiency = (actualVolume / targetVolume) * 100;
  return (timeEfficiency + volumeEfficiency) / 2;
}

// Utility function to estimate loading time
export function estimateLoadingTime(
  volume: number,
  flowRate: number,
  setupTime: number = 10
): number {
  return setupTime + (volume / flowRate); // minutes
}

// Utility function to validate loading requirements
export function validateLoadingRequirements(
  station: LoadingStation,
  truckInfo: TruckInfo
): { valid: boolean; issues: string[] } {
  const issues: string[] = [];

  // Check capacity
  if (truckInfo.vehicle.capacity < truckInfo.loadingRequirements.targetVolume) {
    issues.push('Target volume exceeds truck capacity');
  }

  // Check station capacity
  if (truckInfo.loadingRequirements.targetVolume > station.specifications.maxTruckCapacity) {
    issues.push('Target volume exceeds station capacity');
  }

  // Check temperature requirements
  if (truckInfo.loadingRequirements.temperatureRequirement > 200) {
    issues.push('Temperature requirement exceeds system capability');
  }

  // Check station availability
  if (!station.status.operational) {
    issues.push('Loading station not operational');
  }

  // Check maintenance status
  if (station.status.maintenanceRequired) {
    issues.push('Loading station requires maintenance');
  }

  return {
    valid: issues.length === 0,
    issues
  };
}
