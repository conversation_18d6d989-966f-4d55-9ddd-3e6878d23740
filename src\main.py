"""
Digital Twin System Main Entry Point
Orchestrates the complete digital twin system
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path
from typing import Dict, Any
import yaml
import click

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from digital_twin.core import DigitalTwinEngine, TwinConfiguration
from digital_twin.scada import OPCUAClient, ModbusClient, OPCConfiguration, ModbusConfiguration
from digital_twin.simulation import RealTimePhysicsEngine, SimulationConfig, PhysicsParameters, BoundaryConditions
from src.api.main import create_app
from src.services.data_processor import DataProcessor
from src.services.monitoring import MonitoringService

logger = logging.getLogger(__name__)


class DigitalTwinSystem:
    """Main digital twin system orchestrator"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config: Dict[str, Any] = {}
        self.twin_engine: DigitalTwinEngine = None
        self.scada_clients: Dict[str, Any] = {}
        self.physics_engines: Dict[str, RealTimePhysicsEngine] = {}
        self.data_processor: DataProcessor = None
        self.monitoring: MonitoringService = None
        self.api_app = None
        self.is_running = False
        self._shutdown_event = asyncio.Event()
    
    async def initialize(self) -> bool:
        """Initialize the digital twin system"""
        try:
            # Load configuration
            if not self._load_config():
                return False
            
            # Setup logging
            self._setup_logging()
            
            logger.info("Initializing Digital Twin System")
            
            # Initialize core components
            await self._initialize_twin_engine()
            await self._initialize_scada_clients()
            await self._initialize_physics_engines()
            await self._initialize_data_processor()
            await self._initialize_monitoring()
            await self._initialize_api()
            
            logger.info("Digital Twin System initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Digital Twin System: {e}")
            return False
    
    async def start(self) -> bool:
        """Start the digital twin system"""
        try:
            logger.info("Starting Digital Twin System")
            
            # Start SCADA clients
            for name, client in self.scada_clients.items():
                if hasattr(client, 'connect'):
                    success = await client.connect()
                    if success:
                        logger.info(f"Connected to SCADA client: {name}")
                    else:
                        logger.warning(f"Failed to connect to SCADA client: {name}")
            
            # Start physics engines
            for name, engine in self.physics_engines.items():
                success = await engine.start()
                if success:
                    logger.info(f"Started physics engine: {name}")
                else:
                    logger.warning(f"Failed to start physics engine: {name}")
            
            # Start data processor
            if self.data_processor:
                await self.data_processor.start()
                logger.info("Data processor started")
            
            # Start monitoring
            if self.monitoring:
                await self.monitoring.start()
                logger.info("Monitoring service started")
            
            # Create and configure digital twins
            await self._create_digital_twins()
            
            self.is_running = True
            logger.info("Digital Twin System started successfully")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Digital Twin System: {e}")
            return False
    
    async def stop(self):
        """Stop the digital twin system"""
        try:
            logger.info("Stopping Digital Twin System")
            self.is_running = False
            
            # Stop digital twins
            if self.twin_engine:
                for twin_id in list(self.twin_engine.twins.keys()):
                    await self.twin_engine.stop_twin(twin_id)
            
            # Stop physics engines
            for name, engine in self.physics_engines.items():
                await engine.stop()
                logger.info(f"Stopped physics engine: {name}")
            
            # Stop SCADA clients
            for name, client in self.scada_clients.items():
                if hasattr(client, 'disconnect'):
                    await client.disconnect()
                    logger.info(f"Disconnected SCADA client: {name}")
            
            # Stop data processor
            if self.data_processor:
                await self.data_processor.stop()
                logger.info("Data processor stopped")
            
            # Stop monitoring
            if self.monitoring:
                await self.monitoring.stop()
                logger.info("Monitoring service stopped")
            
            # Set shutdown event
            self._shutdown_event.set()
            
            logger.info("Digital Twin System stopped successfully")
            
        except Exception as e:
            logger.error(f"Error stopping Digital Twin System: {e}")
    
    async def run(self):
        """Run the digital twin system main loop"""
        try:
            # Setup signal handlers
            self._setup_signal_handlers()
            
            # Initialize and start
            if not await self.initialize():
                return False
            
            if not await self.start():
                return False
            
            # Main loop
            logger.info("Digital Twin System running. Press Ctrl+C to stop.")
            
            # Run API server in background
            if self.api_app:
                import uvicorn
                config = uvicorn.Config(
                    self.api_app,
                    host=self.config.get('api', {}).get('host', '0.0.0.0'),
                    port=self.config.get('api', {}).get('port', 8000),
                    log_level="info"
                )
                server = uvicorn.Server(config)
                api_task = asyncio.create_task(server.serve())
            
            # Wait for shutdown signal
            await self._shutdown_event.wait()
            
            # Cancel API server
            if 'api_task' in locals():
                api_task.cancel()
                try:
                    await api_task
                except asyncio.CancelledError:
                    pass
            
            return True
            
        except Exception as e:
            logger.error(f"Error running Digital Twin System: {e}")
            return False
        finally:
            await self.stop()
    
    def _load_config(self) -> bool:
        """Load configuration from file"""
        try:
            with open(self.config_path, 'r') as f:
                self.config = yaml.safe_load(f)
            logger.info(f"Configuration loaded from {self.config_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            return False
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_config = self.config.get('monitoring', {}).get('logging', {})
        
        log_level = log_config.get('level', 'INFO')
        log_format = log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        
        logging.basicConfig(
            level=getattr(logging, log_level),
            format=log_format,
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(log_config.get('file', 'digital_twin.log'))
            ]
        )
    
    async def _initialize_twin_engine(self):
        """Initialize digital twin engine"""
        self.twin_engine = DigitalTwinEngine()
        logger.info("Digital twin engine initialized")
    
    async def _initialize_scada_clients(self):
        """Initialize SCADA clients"""
        scada_config = self.config.get('scada', {})
        
        # OPC UA Client
        if scada_config.get('opc_ua', {}).get('enabled', False):
            opc_config = OPCConfiguration(**scada_config['opc_ua'])
            self.scada_clients['opc_ua'] = OPCUAClient(opc_config)
            logger.info("OPC UA client initialized")
        
        # Modbus Client
        if scada_config.get('modbus', {}).get('enabled', False):
            modbus_config = ModbusConfiguration(**scada_config['modbus'])
            self.scada_clients['modbus'] = ModbusClient(modbus_config)
            logger.info("Modbus client initialized")
    
    async def _initialize_physics_engines(self):
        """Initialize physics engines"""
        sim_config = self.config.get('simulation', {})
        
        # Create simulation configuration
        physics_params = PhysicsParameters(**sim_config.get('physics_parameters', {}))
        boundary_conditions = BoundaryConditions(**sim_config.get('boundary_conditions', {}))
        
        config = SimulationConfig(
            physics_params=physics_params,
            boundary_conditions=boundary_conditions,
            **sim_config.get('physics_engine', {})
        )
        
        # Create physics engine
        engine = RealTimePhysicsEngine(config)
        success = await engine.initialize()
        
        if success:
            self.physics_engines['main'] = engine
            logger.info("Physics engine initialized")
        else:
            logger.error("Failed to initialize physics engine")
    
    async def _initialize_data_processor(self):
        """Initialize data processor"""
        # This would be implemented based on data processing requirements
        logger.info("Data processor initialization skipped (placeholder)")
    
    async def _initialize_monitoring(self):
        """Initialize monitoring service"""
        # This would be implemented based on monitoring requirements
        logger.info("Monitoring service initialization skipped (placeholder)")
    
    async def _initialize_api(self):
        """Initialize API application"""
        self.api_app = create_app(self.config, self.twin_engine, self.scada_clients)
        logger.info("API application initialized")
    
    async def _create_digital_twins(self):
        """Create configured digital twins"""
        twins_config = self.config.get('digital_twin', {}).get('twins', {})
        
        for twin_type, twin_config in twins_config.items():
            if twin_type == 'default':
                continue
                
            # Create twin configuration
            config = TwinConfiguration(
                twin_id=f"{twin_type}_001",
                name=twin_config.get('name', twin_type),
                description=twin_config.get('description', ''),
                physical_asset_id=f"asset_{twin_type}_001",
                model_version=twin_config.get('model_version', 'v1.0'),
                update_frequency=twin_config.get('update_frequency', 1.0),
                synchronization_threshold=twin_config.get('synchronization_threshold', 0.1),
                physics_parameters=twin_config.get('physics_parameters', {}),
                scada_tags=twin_config.get('scada_tags', [])
            )
            
            # Create and start twin
            twin = await self.twin_engine.create_twin(config)
            await self.twin_engine.start_twin(config.twin_id)
            
            logger.info(f"Created and started digital twin: {config.twin_id}")
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown...")
            asyncio.create_task(self.stop())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)


@click.command()
@click.option('--config', '-c', default='config/digital_twin_config.yaml', 
              help='Path to configuration file')
@click.option('--log-level', default='INFO', 
              help='Logging level (DEBUG, INFO, WARNING, ERROR)')
def main(config: str, log_level: str):
    """
    Digital Twin System Main Entry Point
    
    This is the main entry point for the digital twin system that orchestrates
    all components including SCADA integration, physics simulation, and API services.
    """
    # Setup basic logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create and run system
    system = DigitalTwinSystem(config)
    
    try:
        # Run the async main loop
        asyncio.run(system.run())
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()