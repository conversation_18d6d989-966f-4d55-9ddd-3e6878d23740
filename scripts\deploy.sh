#!/bin/bash
# Deployment script for MLOps Digital Twin Platform
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="digital-twin"
DOCKER_COMPOSE_FILE="docker-compose.production.yml"
ENV_FILE=".env"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_dependencies() {
    log_info "Checking dependencies..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi
    
    log_success "All dependencies are available"
}

create_env_file() {
    log_info "Creating environment file..."
    
    if [ ! -f "$ENV_FILE" ]; then
        cat > "$ENV_FILE" << EOF
# MLOps Digital Twin Platform Environment Variables

# Application
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# Database
POSTGRES_DB=digital_twin
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
DATABASE_URL=********************************************/digital_twin

# Redis
REDIS_URL=redis://redis:6379/0

# InfluxDB
INFLUXDB_URL=http://influxdb:8086
INFLUXDB_TOKEN=admin-token
INFLUXDB_ORG=digital-twin
INFLUXDB_BUCKET=metrics
INFLUXDB_USERNAME=admin
INFLUXDB_PASSWORD=adminpassword

# MinIO
MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin

# External APIs
OPENWEATHER_API_KEY=your-openweather-api-key-here

# MLflow
MLFLOW_BACKEND_STORE_URI=********************************************/mlflow
MLFLOW_DEFAULT_ARTIFACT_ROOT=s3://mlflow/artifacts
AWS_ACCESS_KEY_ID=minioadmin
AWS_SECRET_ACCESS_KEY=minioadmin
MLFLOW_S3_ENDPOINT_URL=http://minio:9000

# Security
JWT_SECRET_KEY=your-jwt-secret-key-here
ENCRYPTION_KEY=your-encryption-key-here

# Monitoring
GRAFANA_ADMIN_PASSWORD=admin
PROMETHEUS_RETENTION_TIME=15d

# Networking
NGINX_PORT=80
NGINX_SSL_PORT=443
EOF
        log_success "Environment file created: $ENV_FILE"
        log_warning "Please edit $ENV_FILE and update the configuration values"
    else
        log_info "Environment file already exists: $ENV_FILE"
    fi
}

create_directories() {
    log_info "Creating necessary directories..."
    
    directories=(
        "logs"
        "data"
        "models"
        "config/grafana/provisioning/datasources"
        "config/grafana/provisioning/dashboards"
        "config/grafana/dashboards"
        "config/nginx/ssl"
        "config/prometheus"
        "scripts/sql"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_info "Created directory: $dir"
        fi
    done
    
    log_success "All directories created"
}

build_images() {
    log_info "Building Docker images..."
    
    # Build the main application image
    docker-compose -f "$DOCKER_COMPOSE_FILE" build
    
    log_success "Docker images built successfully"
}

start_services() {
    log_info "Starting services..."
    
    # Start core infrastructure services first
    log_info "Starting infrastructure services..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d postgres redis influxdb minio
    
    # Wait for services to be ready
    log_info "Waiting for infrastructure services to be ready..."
    sleep 30
    
    # Start application services
    log_info "Starting application services..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    log_success "All services started"
}

check_services() {
    log_info "Checking service health..."
    
    # Wait a bit for services to fully start
    sleep 30
    
    services=(
        "digital-twin-postgres:5432"
        "digital-twin-redis:6379"
        "digital-twin-influxdb:8086"
        "digital-twin-minio:9000"
        "digital-twin-app:8000"
    )
    
    for service in "${services[@]}"; do
        container_name=$(echo "$service" | cut -d':' -f1)
        port=$(echo "$service" | cut -d':' -f2)
        
        if docker ps | grep -q "$container_name"; then
            log_success "Service $container_name is running"
        else
            log_error "Service $container_name is not running"
            exit 1
        fi
    done
    
    # Check application health endpoint
    log_info "Checking application health..."
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8000/health &> /dev/null; then
            log_success "Application is healthy"
            break
        else
            log_info "Attempt $attempt/$max_attempts: Waiting for application to be ready..."
            sleep 10
            ((attempt++))
        fi
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "Application health check failed"
        exit 1
    fi
}

show_endpoints() {
    log_success "Deployment completed successfully!"
    echo ""
    echo "Available endpoints:"
    echo "  Application API:    http://localhost:8000"
    echo "  Dashboard:          http://localhost:8501"
    echo "  Grafana:            http://localhost:3000 (admin/admin)"
    echo "  Prometheus:         http://localhost:9090"
    echo "  MLflow:             http://localhost:5000"
    echo "  MinIO Console:      http://localhost:9001 (minioadmin/minioadmin)"
    echo "  InfluxDB:           http://localhost:8086 (admin/adminpassword)"
    echo ""
    echo "Main entry point:    http://localhost (via Nginx)"
    echo ""
    echo "Logs: docker-compose -f $DOCKER_COMPOSE_FILE logs -f"
    echo "Stop: docker-compose -f $DOCKER_COMPOSE_FILE down"
    echo ""
}

cleanup() {
    log_info "Cleaning up..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    log_success "Cleanup completed"
}

# Main execution
main() {
    case "${1:-deploy}" in
        "deploy")
            log_info "Starting deployment of MLOps Digital Twin Platform..."
            check_dependencies
            create_env_file
            create_directories
            build_images
            start_services
            check_services
            show_endpoints
            ;;
        "start")
            log_info "Starting services..."
            docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
            check_services
            show_endpoints
            ;;
        "stop")
            log_info "Stopping services..."
            docker-compose -f "$DOCKER_COMPOSE_FILE" down
            log_success "Services stopped"
            ;;
        "restart")
            log_info "Restarting services..."
            docker-compose -f "$DOCKER_COMPOSE_FILE" restart
            check_services
            show_endpoints
            ;;
        "status")
            log_info "Checking service status..."
            docker-compose -f "$DOCKER_COMPOSE_FILE" ps
            ;;
        "logs")
            log_info "Showing logs..."
            docker-compose -f "$DOCKER_COMPOSE_FILE" logs -f "${2:-}"
            ;;
        "cleanup")
            cleanup
            ;;
        "rebuild")
            log_info "Rebuilding and redeploying..."
            docker-compose -f "$DOCKER_COMPOSE_FILE" down
            docker-compose -f "$DOCKER_COMPOSE_FILE" build --no-cache
            start_services
            check_services
            show_endpoints
            ;;
        *)
            echo "Usage: $0 {deploy|start|stop|restart|status|logs|cleanup|rebuild}"
            echo ""
            echo "Commands:"
            echo "  deploy   - Full deployment (default)"
            echo "  start    - Start all services"
            echo "  stop     - Stop all services"
            echo "  restart  - Restart all services"
            echo "  status   - Show service status"
            echo "  logs     - Show logs (optionally for specific service)"
            echo "  cleanup  - Stop and remove all containers"
            echo "  rebuild  - Rebuild and redeploy everything"
            exit 1
            ;;
    esac
}

# Execute main function with all arguments
main "$@"