"""
Event Bus Service - Core Event-Driven Architecture
Implements event sourcing, CQRS, and distributed messaging for the tank control system
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field, asdict
from enum import Enum
from abc import ABC, abstractmethod
import aioredis
import aiokafka
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class EventType(Enum):
    """Event types in the system"""
    # Tank Events
    TANK_CREATED = "tank.created"
    TANK_UPDATED = "tank.updated"
    TANK_DELETED = "tank.deleted"
    TANK_STATE_CHANGED = "tank.state_changed"
    
    # Sensor Events
    SENSOR_DATA_RECEIVED = "sensor.data_received"
    SENSOR_ALARM_TRIGGERED = "sensor.alarm_triggered"
    SENSOR_ALARM_CLEARED = "sensor.alarm_cleared"
    
    # Control Events
    CONTROL_COMMAND_EXECUTED = "control.command_executed"
    CONTROL_SETPOINT_CHANGED = "control.setpoint_changed"
    CONTROL_MODE_CHANGED = "control.mode_changed"
    
    # System Events
    SYSTEM_STARTED = "system.started"
    SYSTEM_STOPPED = "system.stopped"
    SERVICE_HEALTH_CHANGED = "service.health_changed"
    
    # User Events
    USER_LOGGED_IN = "user.logged_in"
    USER_ACTION_PERFORMED = "user.action_performed"


@dataclass
class EventMetadata:
    """Event metadata for tracing and correlation"""
    correlation_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    causation_id: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    source_service: Optional[str] = None
    trace_id: Optional[str] = None
    span_id: Optional[str] = None


@dataclass
class DomainEvent:
    """Base domain event"""
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    event_type: EventType = EventType.SYSTEM_STARTED
    aggregate_id: str = ""
    aggregate_type: str = ""
    aggregate_version: int = 1
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    data: Dict[str, Any] = field(default_factory=dict)
    metadata: EventMetadata = field(default_factory=EventMetadata)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary"""
        return {
            'event_id': self.event_id,
            'event_type': self.event_type.value,
            'aggregate_id': self.aggregate_id,
            'aggregate_type': self.aggregate_type,
            'aggregate_version': self.aggregate_version,
            'timestamp': self.timestamp.isoformat(),
            'data': self.data,
            'metadata': asdict(self.metadata)
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DomainEvent':
        """Create event from dictionary"""
        metadata = EventMetadata(**data['metadata'])
        return cls(
            event_id=data['event_id'],
            event_type=EventType(data['event_type']),
            aggregate_id=data['aggregate_id'],
            aggregate_type=data['aggregate_type'],
            aggregate_version=data['aggregate_version'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            data=data['data'],
            metadata=metadata
        )


# Specific Event Types

@dataclass
class TankCreatedEvent(DomainEvent):
    """Tank created event"""
    event_type: EventType = EventType.TANK_CREATED
    aggregate_type: str = "Tank"


@dataclass
class SensorDataReceivedEvent(DomainEvent):
    """Sensor data received event"""
    event_type: EventType = EventType.SENSOR_DATA_RECEIVED
    aggregate_type: str = "Sensor"


@dataclass
class ControlCommandExecutedEvent(DomainEvent):
    """Control command executed event"""
    event_type: EventType = EventType.CONTROL_COMMAND_EXECUTED
    aggregate_type: str = "Controller"


class EventHandler(ABC):
    """Abstract event handler"""
    
    @abstractmethod
    async def handle(self, event: DomainEvent) -> None:
        """Handle an event"""
        pass
    
    @property
    @abstractmethod
    def event_types(self) -> List[EventType]:
        """Event types this handler can process"""
        pass


class EventStore(ABC):
    """Abstract event store interface"""
    
    @abstractmethod
    async def append_event(self, event: DomainEvent) -> None:
        """Append event to store"""
        pass
    
    @abstractmethod
    async def get_events(self, aggregate_id: str, from_version: int = 0) -> List[DomainEvent]:
        """Get events for aggregate"""
        pass
    
    @abstractmethod
    async def get_events_by_type(self, event_type: EventType, from_time: Optional[datetime] = None) -> List[DomainEvent]:
        """Get events by type"""
        pass


class RedisEventStore(EventStore):
    """Redis-based event store implementation"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.redis: Optional[aioredis.Redis] = None
    
    async def connect(self):
        """Connect to Redis"""
        self.redis = aioredis.from_url(self.redis_url)
    
    async def disconnect(self):
        """Disconnect from Redis"""
        if self.redis:
            await self.redis.close()
    
    async def append_event(self, event: DomainEvent) -> None:
        """Append event to Redis streams"""
        if not self.redis:
            await self.connect()
        
        # Store in aggregate-specific stream
        aggregate_stream = f"events:{event.aggregate_type}:{event.aggregate_id}"
        await self.redis.xadd(aggregate_stream, event.to_dict())
        
        # Store in type-specific stream
        type_stream = f"events:type:{event.event_type.value}"
        await self.redis.xadd(type_stream, event.to_dict())
        
        # Store in global event stream
        global_stream = "events:all"
        await self.redis.xadd(global_stream, event.to_dict())
        
        logger.debug(f"Event {event.event_id} appended to store")
    
    async def get_events(self, aggregate_id: str, from_version: int = 0) -> List[DomainEvent]:
        """Get events for aggregate from Redis"""
        if not self.redis:
            await self.connect()
        
        # For simplicity, we'll get all events and filter by version
        # In production, you'd want more efficient querying
        stream_name = f"events:*:{aggregate_id}"
        events = []
        
        # This is a simplified implementation
        # In practice, you'd need more sophisticated querying
        return events
    
    async def get_events_by_type(self, event_type: EventType, from_time: Optional[datetime] = None) -> List[DomainEvent]:
        """Get events by type from Redis"""
        if not self.redis:
            await self.connect()
        
        stream_name = f"events:type:{event_type.value}"
        try:
            # Read from stream
            results = await self.redis.xread({stream_name: '0'})
            events = []
            
            for stream, messages in results:
                for message_id, fields in messages:
                    event_data = {k.decode(): v.decode() if isinstance(v, bytes) else v for k, v in fields.items()}
                    # Parse JSON fields
                    if 'data' in event_data:
                        event_data['data'] = json.loads(event_data['data'])
                    if 'metadata' in event_data:
                        event_data['metadata'] = json.loads(event_data['metadata'])
                    
                    event = DomainEvent.from_dict(event_data)
                    if not from_time or event.timestamp >= from_time:
                        events.append(event)
            
            return events
        except Exception as e:
            logger.error(f"Error reading events by type: {e}")
            return []


class EventPublisher(ABC):
    """Abstract event publisher"""
    
    @abstractmethod
    async def publish(self, event: DomainEvent) -> None:
        """Publish event"""
        pass


class KafkaEventPublisher(EventPublisher):
    """Kafka-based event publisher"""
    
    def __init__(self, bootstrap_servers: str = "localhost:9092"):
        self.bootstrap_servers = bootstrap_servers
        self.producer: Optional[aiokafka.AIOKafkaProducer] = None
    
    async def start(self):
        """Start Kafka producer"""
        self.producer = aiokafka.AIOKafkaProducer(
            bootstrap_servers=self.bootstrap_servers,
            value_serializer=lambda v: json.dumps(v).encode('utf-8')
        )
        await self.producer.start()
    
    async def stop(self):
        """Stop Kafka producer"""
        if self.producer:
            await self.producer.stop()
    
    async def publish(self, event: DomainEvent) -> None:
        """Publish event to Kafka"""
        if not self.producer:
            await self.start()
        
        topic = f"tank-events-{event.event_type.value.replace('.', '-')}"
        
        await self.producer.send(
            topic,
            value=event.to_dict(),
            key=event.aggregate_id.encode('utf-8')
        )
        
        logger.debug(f"Event {event.event_id} published to topic {topic}")


class EventSubscriber(ABC):
    """Abstract event subscriber"""
    
    @abstractmethod
    async def subscribe(self, event_types: List[EventType], handler: EventHandler) -> None:
        """Subscribe to events"""
        pass


class KafkaEventSubscriber(EventSubscriber):
    """Kafka-based event subscriber"""
    
    def __init__(self, bootstrap_servers: str = "localhost:9092", group_id: str = "tank-system"):
        self.bootstrap_servers = bootstrap_servers
        self.group_id = group_id
        self.consumers: Dict[str, aiokafka.AIOKafkaConsumer] = {}
        self.handlers: Dict[EventType, List[EventHandler]] = {}
        self.running = False
    
    async def subscribe(self, event_types: List[EventType], handler: EventHandler) -> None:
        """Subscribe to events"""
        for event_type in event_types:
            if event_type not in self.handlers:
                self.handlers[event_type] = []
            self.handlers[event_type].append(handler)
    
    async def start(self):
        """Start consuming events"""
        self.running = True
        
        # Create consumers for each event type
        for event_type in self.handlers.keys():
            topic = f"tank-events-{event_type.value.replace('.', '-')}"
            
            consumer = aiokafka.AIOKafkaConsumer(
                topic,
                bootstrap_servers=self.bootstrap_servers,
                group_id=self.group_id,
                value_deserializer=lambda m: json.loads(m.decode('utf-8'))
            )
            
            await consumer.start()
            self.consumers[event_type.value] = consumer
            
            # Start consuming task
            asyncio.create_task(self._consume_events(event_type, consumer))
    
    async def stop(self):
        """Stop consuming events"""
        self.running = False
        
        for consumer in self.consumers.values():
            await consumer.stop()
        
        self.consumers.clear()
    
    async def _consume_events(self, event_type: EventType, consumer: aiokafka.AIOKafkaConsumer):
        """Consume events from Kafka"""
        try:
            async for message in consumer:
                if not self.running:
                    break
                
                try:
                    event_data = message.value
                    event = DomainEvent.from_dict(event_data)
                    
                    # Process event with all registered handlers
                    handlers = self.handlers.get(event_type, [])
                    for handler in handlers:
                        try:
                            await handler.handle(event)
                        except Exception as e:
                            logger.error(f"Error in event handler: {e}")
                
                except Exception as e:
                    logger.error(f"Error processing event: {e}")
        
        except Exception as e:
            logger.error(f"Error in event consumer: {e}")


class EventBus:
    """Main event bus orchestrator"""
    
    def __init__(self, 
                 event_store: EventStore,
                 publisher: EventPublisher,
                 subscriber: EventSubscriber):
        self.event_store = event_store
        self.publisher = publisher
        self.subscriber = subscriber
        self.handlers: Dict[EventType, List[EventHandler]] = {}
        self.middleware: List[Callable] = []
    
    async def publish_event(self, event: DomainEvent) -> None:
        """Publish an event"""
        try:
            # Apply middleware
            for middleware in self.middleware:
                event = await middleware(event)
            
            # Store event
            await self.event_store.append_event(event)
            
            # Publish event
            await self.publisher.publish(event)
            
            logger.info(f"Event published: {event.event_type.value} for {event.aggregate_id}")
            
        except Exception as e:
            logger.error(f"Error publishing event: {e}")
            raise
    
    async def subscribe_to_events(self, event_types: List[EventType], handler: EventHandler) -> None:
        """Subscribe to events"""
        await self.subscriber.subscribe(event_types, handler)
        
        for event_type in event_types:
            if event_type not in self.handlers:
                self.handlers[event_type] = []
            self.handlers[event_type].append(handler)
    
    async def start(self):
        """Start event bus"""
        await self.subscriber.start()
        logger.info("Event bus started")
    
    async def stop(self):
        """Stop event bus"""
        await self.subscriber.stop()
        logger.info("Event bus stopped")
    
    def add_middleware(self, middleware: Callable):
        """Add middleware to event processing pipeline"""
        self.middleware.append(middleware)


# Event Handlers

class TankEventHandler(EventHandler):
    """Handler for tank-related events"""
    
    @property
    def event_types(self) -> List[EventType]:
        return [
            EventType.TANK_CREATED,
            EventType.TANK_UPDATED,
            EventType.TANK_DELETED,
            EventType.TANK_STATE_CHANGED
        ]
    
    async def handle(self, event: DomainEvent) -> None:
        """Handle tank events"""
        logger.info(f"Handling tank event: {event.event_type.value}")
        
        if event.event_type == EventType.TANK_CREATED:
            await self._handle_tank_created(event)
        elif event.event_type == EventType.TANK_STATE_CHANGED:
            await self._handle_tank_state_changed(event)
    
    async def _handle_tank_created(self, event: DomainEvent):
        """Handle tank created event"""
        tank_data = event.data
        logger.info(f"Tank created: {tank_data.get('tank_id')}")
        
        # Initialize tank monitoring
        # Send notification
        # Update dashboards
    
    async def _handle_tank_state_changed(self, event: DomainEvent):
        """Handle tank state changed event"""
        state_data = event.data
        logger.info(f"Tank state changed: {state_data}")
        
        # Check alarms
        # Update metrics
        # Trigger control actions if needed


class SensorEventHandler(EventHandler):
    """Handler for sensor-related events"""
    
    @property
    def event_types(self) -> List[EventType]:
        return [
            EventType.SENSOR_DATA_RECEIVED,
            EventType.SENSOR_ALARM_TRIGGERED,
            EventType.SENSOR_ALARM_CLEARED
        ]
    
    async def handle(self, event: DomainEvent) -> None:
        """Handle sensor events"""
        logger.info(f"Handling sensor event: {event.event_type.value}")
        
        if event.event_type == EventType.SENSOR_DATA_RECEIVED:
            await self._handle_sensor_data(event)
        elif event.event_type == EventType.SENSOR_ALARM_TRIGGERED:
            await self._handle_alarm_triggered(event)
    
    async def _handle_sensor_data(self, event: DomainEvent):
        """Handle sensor data received event"""
        sensor_data = event.data
        logger.debug(f"Sensor data: {sensor_data}")
        
        # Store in time series database
        # Check alarm conditions
        # Update real-time displays
    
    async def _handle_alarm_triggered(self, event: DomainEvent):
        """Handle alarm triggered event"""
        alarm_data = event.data
        logger.warning(f"Alarm triggered: {alarm_data}")
        
        # Send notifications
        # Log alarm
        # Trigger automatic responses if configured


# Middleware

async def correlation_middleware(event: DomainEvent) -> DomainEvent:
    """Add correlation tracking to events"""
    if not event.metadata.correlation_id:
        event.metadata.correlation_id = str(uuid.uuid4())
    return event


async def audit_middleware(event: DomainEvent) -> DomainEvent:
    """Audit logging middleware"""
    logger.info(f"Audit: {event.event_type.value} by {event.metadata.user_id}")
    return event


async def metrics_middleware(event: DomainEvent) -> DomainEvent:
    """Metrics collection middleware"""
    # Increment event counters
    # Track event processing time
    return event


# Factory functions

def create_event_bus(config: Dict[str, Any]) -> EventBus:
    """Create and configure event bus"""
    
    # Create event store
    if config.get('event_store', {}).get('type') == 'redis':
        redis_config = config['event_store']['redis']
        event_store = RedisEventStore(redis_config['url'])
    else:
        raise ValueError("Unsupported event store type")
    
    # Create publisher
    if config.get('publisher', {}).get('type') == 'kafka':
        kafka_config = config['publisher']['kafka']
        publisher = KafkaEventPublisher(kafka_config['bootstrap_servers'])
    else:
        raise ValueError("Unsupported publisher type")
    
    # Create subscriber
    if config.get('subscriber', {}).get('type') == 'kafka':
        kafka_config = config['subscriber']['kafka']
        subscriber = KafkaEventSubscriber(
            kafka_config['bootstrap_servers'],
            kafka_config['group_id']
        )
    else:
        raise ValueError("Unsupported subscriber type")
    
    # Create event bus
    event_bus = EventBus(event_store, publisher, subscriber)
    
    # Add middleware
    event_bus.add_middleware(correlation_middleware)
    event_bus.add_middleware(audit_middleware)
    event_bus.add_middleware(metrics_middleware)
    
    return event_bus


# Usage example
async def main():
    """Example usage of event bus"""
    
    config = {
        'event_store': {
            'type': 'redis',
            'redis': {
                'url': 'redis://localhost:6379'
            }
        },
        'publisher': {
            'type': 'kafka',
            'kafka': {
                'bootstrap_servers': 'localhost:9092'
            }
        },
        'subscriber': {
            'type': 'kafka',
            'kafka': {
                'bootstrap_servers': 'localhost:9092',
                'group_id': 'tank-system'
            }
        }
    }
    
    # Create event bus
    event_bus = create_event_bus(config)
    
    # Register handlers
    tank_handler = TankEventHandler()
    sensor_handler = SensorEventHandler()
    
    await event_bus.subscribe_to_events(tank_handler.event_types, tank_handler)
    await event_bus.subscribe_to_events(sensor_handler.event_types, sensor_handler)
    
    # Start event bus
    await event_bus.start()
    
    try:
        # Publish some events
        tank_created_event = TankCreatedEvent(
            aggregate_id="tank_001",
            data={
                "tank_id": "tank_001",
                "name": "Primary Heating Tank",
                "capacity": 50000
            }
        )
        
        await event_bus.publish_event(tank_created_event)
        
        sensor_data_event = SensorDataReceivedEvent(
            aggregate_id="sensor_001",
            data={
                "sensor_id": "TT_001_01",
                "tank_id": "tank_001",
                "value": 165.5,
                "unit": "celsius",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )
        
        await event_bus.publish_event(sensor_data_event)
        
        # Keep running
        await asyncio.sleep(60)
        
    finally:
        # Stop event bus
        await event_bus.stop()


if __name__ == "__main__":
    asyncio.run(main())