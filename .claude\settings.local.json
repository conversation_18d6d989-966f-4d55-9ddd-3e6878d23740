{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(git init:*)", "Bash(git add:*)", "Bash(ls:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(docker:*)", "Bash(pip3 install:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(source:*)", "Bash(pip install:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./scripts/deploy.sh:*)", "<PERSON><PERSON>(curl:*)", "Bash(python run_dashboard_simple.py)"], "deny": []}}