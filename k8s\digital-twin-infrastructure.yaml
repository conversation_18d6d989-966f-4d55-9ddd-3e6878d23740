# Infrastructure services for MLOps Digital Twin Platform
# PostgreSQL Database
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: digital-twin-postgres
  namespace: digital-twin
  labels:
    app: digital-twin-postgres
    component: database
spec:
  serviceName: digital-twin-postgres
  replicas: 1
  selector:
    matchLabels:
      app: digital-twin-postgres
  template:
    metadata:
      labels:
        app: digital-twin-postgres
        component: database
    spec:
      securityContext:
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
      containers:
      - name: postgres
        image: postgres:15-alpine
        env:
        - name: POSTGRES_DB
          value: "digital_twin"
        - name: POSTGRES_USER
          value: "postgres"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: digital-twin-secrets
              key: postgres-password
        - name: PGDATA
          value: "/var/lib/postgresql/data/pgdata"
        ports:
        - containerPort: 5432
          name: postgres
        resources:
          requests:
            cpu: "250m"
            memory: "512Mi"
          limits:
            cpu: "1"
            memory: "2Gi"
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 15
          periodSeconds: 10
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi

---
# PostgreSQL Service
apiVersion: v1
kind: Service
metadata:
  name: digital-twin-postgres
  namespace: digital-twin
  labels:
    app: digital-twin-postgres
    component: database
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
    name: postgres
  selector:
    app: digital-twin-postgres

---
# Redis Cache
apiVersion: apps/v1
kind: Deployment
metadata:
  name: digital-twin-redis
  namespace: digital-twin
  labels:
    app: digital-twin-redis
    component: cache
spec:
  replicas: 1
  selector:
    matchLabels:
      app: digital-twin-redis
  template:
    metadata:
      labels:
        app: digital-twin-redis
        component: cache
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
          name: redis
        resources:
          requests:
            cpu: "100m"
            memory: "256Mi"
          limits:
            cpu: "500m"
            memory: "1Gi"
        volumeMounts:
        - name: redis-config
          mountPath: /usr/local/etc/redis/redis.conf
          subPath: redis.conf
        - name: redis-storage
          mountPath: /data
        command:
        - redis-server
        - /usr/local/etc/redis/redis.conf
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 15
          periodSeconds: 10
      volumes:
      - name: redis-config
        configMap:
          name: digital-twin-config
          items:
          - key: redis.conf
            path: redis.conf
      - name: redis-storage
        persistentVolumeClaim:
          claimName: digital-twin-redis-pvc

---
# Redis Service
apiVersion: v1
kind: Service
metadata:
  name: digital-twin-redis
  namespace: digital-twin
  labels:
    app: digital-twin-redis
    component: cache
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  selector:
    app: digital-twin-redis

---
# InfluxDB Time-Series Database
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: digital-twin-influxdb
  namespace: digital-twin
  labels:
    app: digital-twin-influxdb
    component: timeseries
spec:
  serviceName: digital-twin-influxdb
  replicas: 1
  selector:
    matchLabels:
      app: digital-twin-influxdb
  template:
    metadata:
      labels:
        app: digital-twin-influxdb
        component: timeseries
    spec:
      containers:
      - name: influxdb
        image: influxdb:2.7-alpine
        env:
        - name: DOCKER_INFLUXDB_INIT_MODE
          value: "setup"
        - name: DOCKER_INFLUXDB_INIT_USERNAME
          value: "admin"
        - name: DOCKER_INFLUXDB_INIT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: digital-twin-secrets
              key: influxdb-password
        - name: DOCKER_INFLUXDB_INIT_ORG
          value: "digital-twin"
        - name: DOCKER_INFLUXDB_INIT_BUCKET
          value: "metrics"
        - name: DOCKER_INFLUXDB_INIT_ADMIN_TOKEN
          valueFrom:
            secretKeyRef:
              name: digital-twin-secrets
              key: influxdb-token
        ports:
        - containerPort: 8086
          name: influxdb
        resources:
          requests:
            cpu: "250m"
            memory: "512Mi"
          limits:
            cpu: "1"
            memory: "2Gi"
        volumeMounts:
        - name: influxdb-storage
          mountPath: /var/lib/influxdb2
        - name: influxdb-config
          mountPath: /etc/influxdb2
        livenessProbe:
          httpGet:
            path: /ping
            port: 8086
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /ping
            port: 8086
          initialDelaySeconds: 15
          periodSeconds: 10
  volumeClaimTemplates:
  - metadata:
      name: influxdb-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 20Gi
  - metadata:
      name: influxdb-config
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 1Gi

---
# InfluxDB Service
apiVersion: v1
kind: Service
metadata:
  name: digital-twin-influxdb
  namespace: digital-twin
  labels:
    app: digital-twin-influxdb
    component: timeseries
spec:
  type: ClusterIP
  ports:
  - port: 8086
    targetPort: 8086
    protocol: TCP
    name: influxdb
  selector:
    app: digital-twin-influxdb

---
# MinIO Object Storage
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: digital-twin-minio
  namespace: digital-twin
  labels:
    app: digital-twin-minio
    component: storage
spec:
  serviceName: digital-twin-minio
  replicas: 1
  selector:
    matchLabels:
      app: digital-twin-minio
  template:
    metadata:
      labels:
        app: digital-twin-minio
        component: storage
    spec:
      containers:
      - name: minio
        image: minio/minio:latest
        env:
        - name: MINIO_ROOT_USER
          valueFrom:
            secretKeyRef:
              name: digital-twin-secrets
              key: minio-access-key
        - name: MINIO_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: digital-twin-secrets
              key: minio-secret-key
        - name: MINIO_DEFAULT_BUCKETS
          value: "datalake,models,artifacts,mlflow"
        ports:
        - containerPort: 9000
          name: minio
        - containerPort: 9001
          name: console
        resources:
          requests:
            cpu: "250m"
            memory: "512Mi"
          limits:
            cpu: "1"
            memory: "2Gi"
        volumeMounts:
        - name: minio-storage
          mountPath: /data
        command:
        - minio
        - server
        - /data
        - --console-address
        - ":9001"
        livenessProbe:
          httpGet:
            path: /minio/health/live
            port: 9000
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /minio/health/ready
            port: 9000
          initialDelaySeconds: 15
          periodSeconds: 10
  volumeClaimTemplates:
  - metadata:
      name: minio-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 50Gi

---
# MinIO Service
apiVersion: v1
kind: Service
metadata:
  name: digital-twin-minio
  namespace: digital-twin
  labels:
    app: digital-twin-minio
    component: storage
spec:
  type: ClusterIP
  ports:
  - port: 9000
    targetPort: 9000
    protocol: TCP
    name: minio
  - port: 9001
    targetPort: 9001
    protocol: TCP
    name: console
  selector:
    app: digital-twin-minio

---
# Persistent Volume Claims for shared storage
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: digital-twin-data-pvc
  namespace: digital-twin
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: digital-twin-models-pvc
  namespace: digital-twin
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 5Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: digital-twin-redis-pvc
  namespace: digital-twin
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi