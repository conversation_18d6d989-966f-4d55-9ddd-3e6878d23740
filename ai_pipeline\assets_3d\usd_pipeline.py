"""
3D Assets Pipeline
USD Simulation, Logo Objects, and NVIDIA AI integration
Based on the architecture diagram's 3D Assets section
"""

import asyncio
import logging
import numpy as np
import torch
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
from typing import Dict, List, Optional, Any, Union, Tuple
import json
import os
from pathlib import Path
import threading

from ..core.architecture_framework import (
    PipelineComponent, ComponentType, ProcessingStage, PipelineData,
    device_manager, create_pipeline_data
)

logger = logging.getLogger(__name__)


class AssetType(Enum):
    """Types of 3D assets"""
    MESH = "mesh"
    TEXTURE = "texture"
    MATERIAL = "material"
    ANIMATION = "animation"
    LIGHT = "light"
    CAMERA = "camera"
    ENVIRONMENT = "environment"
    LOGO = "logo"
    PROCEDURAL = "procedural"


class USDStageType(Enum):
    """USD stage types"""
    MAIN_STAGE = "main_stage"
    SUBLAYER = "sublayer"
    REFERENCE = "reference"
    PAYLOAD = "payload"
    VARIANT_SET = "variant_set"


@dataclass
class Asset3D:
    """3D asset data structure"""
    asset_id: str
    asset_type: AssetType
    name: str
    file_path: Optional[Path] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    geometry_data: Optional[torch.Tensor] = None
    texture_data: Optional[torch.Tensor] = None
    material_properties: Dict[str, Any] = field(default_factory=dict)
    transform_matrix: torch.Tensor = field(default_factory=lambda: torch.eye(4))
    bbox_min: torch.Tensor = field(default_factory=lambda: torch.zeros(3))
    bbox_max: torch.Tensor = field(default_factory=lambda: torch.ones(3))
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    def update_transform(self, translation: torch.Tensor = None, 
                        rotation: torch.Tensor = None, scale: torch.Tensor = None):
        """Update asset transform"""
        if translation is not None:
            self.transform_matrix[:3, 3] = translation
        if rotation is not None:
            # Apply rotation (assuming rotation is a 3x3 matrix)
            self.transform_matrix[:3, :3] = rotation
        if scale is not None:
            # Apply scale
            scale_matrix = torch.diag(torch.cat([scale, torch.ones(1)]))
            self.transform_matrix = self.transform_matrix @ scale_matrix


class USDSimulationEngine:
    """USD (Universal Scene Description) simulation engine"""
    
    def __init__(self):
        self.usd_available = self._check_usd_availability()
        self.stages: Dict[str, Any] = {}
        self.asset_registry: Dict[str, Asset3D] = {}
        self.device = device_manager.get_device()
        
    def _check_usd_availability(self) -> bool:
        """Check if USD is available"""
        try:
            from pxr import Usd, UsdGeom, UsdShade, UsdLux
            return True
        except ImportError:
            logger.warning("USD (Universal Scene Description) not available, using fallback")
            return False
    
    async def initialize(self) -> bool:
        """Initialize USD simulation engine"""
        try:
            if self.usd_available:
                logger.info("USD simulation engine initialized with USD support")
            else:
                logger.info("USD simulation engine initialized with fallback implementation")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize USD simulation engine: {e}")
            return False
    
    async def create_stage(self, stage_id: str, stage_type: USDStageType = USDStageType.MAIN_STAGE) -> bool:
        """Create a new USD stage"""
        try:
            if self.usd_available:
                stage = self._create_usd_stage(stage_id, stage_type)
            else:
                stage = self._create_fallback_stage(stage_id, stage_type)
            
            self.stages[stage_id] = {
                "stage": stage,
                "type": stage_type,
                "assets": [],
                "created_at": datetime.now(timezone.utc)
            }
            
            logger.info(f"Created USD stage: {stage_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating USD stage: {e}")
            return False
    
    def _create_usd_stage(self, stage_id: str, stage_type: USDStageType):
        """Create actual USD stage"""
        if not self.usd_available:
            return None
        
        try:
            from pxr import Usd, UsdGeom
            
            # Create new stage
            stage = Usd.Stage.CreateInMemory(f"{stage_id}.usda")
            
            # Set up root layer
            root_layer = stage.GetRootLayer()
            root_layer.documentation = f"Stage: {stage_id}, Type: {stage_type.value}"
            
            # Define default prim
            UsdGeom.SetStageUpAxis(stage, UsdGeom.Tokens.y)
            UsdGeom.SetStageMetersPerUnit(stage, 1.0)
            
            return stage
            
        except Exception as e:
            logger.error(f"Error creating USD stage: {e}")
            return None
    
    def _create_fallback_stage(self, stage_id: str, stage_type: USDStageType):
        """Create fallback stage implementation"""
        return {
            "stage_id": stage_id,
            "type": stage_type,
            "prims": {},
            "metadata": {},
            "fallback": True
        }
    
    async def add_asset_to_stage(self, stage_id: str, asset: Asset3D) -> bool:
        """Add asset to USD stage"""
        if stage_id not in self.stages:
            logger.error(f"Stage not found: {stage_id}")
            return False
        
        try:
            stage_info = self.stages[stage_id]
            stage = stage_info["stage"]
            
            if self.usd_available and not isinstance(stage, dict):
                success = await self._add_usd_asset(stage, asset)
            else:
                success = await self._add_fallback_asset(stage, asset)
            
            if success:
                stage_info["assets"].append(asset.asset_id)
                self.asset_registry[asset.asset_id] = asset
                logger.info(f"Added asset {asset.asset_id} to stage {stage_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error adding asset to stage: {e}")
            return False
    
    async def _add_usd_asset(self, stage, asset: Asset3D) -> bool:
        """Add asset to actual USD stage"""
        if not self.usd_available:
            return False
        
        try:
            from pxr import Usd, UsdGeom, UsdShade, Sdf
            
            # Create prim path
            prim_path = f"/{asset.name}_{asset.asset_id}"
            
            if asset.asset_type == AssetType.MESH:
                # Create mesh
                mesh_prim = UsdGeom.Mesh.Define(stage, prim_path)
                mesh = mesh_prim.GetPrim()
                
                # Set geometry data if available
                if asset.geometry_data is not None:
                    # Convert tensor to USD format
                    vertices = asset.geometry_data.cpu().numpy()
                    mesh_prim.CreatePointsAttr(vertices)
                
                # Set transform
                if asset.transform_matrix is not None:
                    transform_matrix = asset.transform_matrix.cpu().numpy()
                    xform = UsdGeom.Xformable(mesh)
                    xform.AddTransformOp().Set(transform_matrix)
            
            elif asset.asset_type == AssetType.LIGHT:
                # Create light
                light_prim = UsdLux.RectLight.Define(stage, prim_path)
                light = light_prim.GetPrim()
                
                # Set light properties
                light_prim.CreateIntensityAttr(1000.0)
                light_prim.CreateColorAttr((1.0, 1.0, 1.0))
            
            elif asset.asset_type == AssetType.CAMERA:
                # Create camera
                camera_prim = UsdGeom.Camera.Define(stage, prim_path)
                camera = camera_prim.GetPrim()
                
                # Set camera properties
                camera_prim.CreateFocalLengthAttr(50.0)
                camera_prim.CreateFocusDistanceAttr(5.0)
            
            return True
            
        except Exception as e:
            logger.error(f"Error adding USD asset: {e}")
            return False
    
    async def _add_fallback_asset(self, stage, asset: Asset3D) -> bool:
        """Add asset to fallback stage"""
        if not isinstance(stage, dict):
            return False
        
        stage["prims"][asset.asset_id] = {
            "asset": asset,
            "type": asset.asset_type.value,
            "transform": asset.transform_matrix.tolist() if asset.transform_matrix is not None else None
        }
        return True
    
    async def export_stage(self, stage_id: str, output_path: Path) -> bool:
        """Export USD stage to file"""
        if stage_id not in self.stages:
            return False
        
        try:
            stage_info = self.stages[stage_id]
            stage = stage_info["stage"]
            
            # Ensure output directory exists
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            if self.usd_available and not isinstance(stage, dict):
                # Export actual USD stage
                stage.Export(str(output_path))
            else:
                # Export fallback format (JSON)
                export_data = {
                    "stage_id": stage_id,
                    "type": stage_info["type"].value,
                    "assets": [asset_id for asset_id in stage_info["assets"]],
                    "created_at": stage_info["created_at"].isoformat()
                }
                
                with open(output_path.with_suffix('.json'), 'w') as f:
                    json.dump(export_data, f, indent=2)
            
            logger.info(f"Exported stage {stage_id} to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting stage: {e}")
            return False
    
    async def render_stage(self, stage_id: str, camera_id: str = None, 
                          resolution: Tuple[int, int] = (1920, 1080)) -> Optional[torch.Tensor]:
        """Render USD stage to image"""
        if stage_id not in self.stages:
            return None
        
        try:
            # Simple fallback rendering (would use proper USD/Hydra renderer in production)
            width, height = resolution
            rendered_image = torch.rand(3, height, width, device=self.device)
            
            logger.debug(f"Rendered stage {stage_id} at resolution {resolution}")
            return rendered_image
            
        except Exception as e:
            logger.error(f"Error rendering stage: {e}")
            return None


class LogoObjectsManager:
    """Manager for logo objects and branding assets"""
    
    def __init__(self):
        self.logo_assets: Dict[str, Asset3D] = {}
        self.brand_templates: Dict[str, Dict[str, Any]] = {}
        self.device = device_manager.get_device()
        
    async def initialize(self) -> bool:
        """Initialize logo objects manager"""
        try:
            # Load default brand templates
            self._load_default_templates()
            logger.info("Logo objects manager initialized")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize logo objects manager: {e}")
            return False
    
    def _load_default_templates(self):
        """Load default brand templates"""
        self.brand_templates = {
            "nvidia": {
                "colors": {"primary": "#76B900", "secondary": "#000000"},
                "fonts": ["NVIDIA Sans", "Arial"],
                "logo_variants": ["full", "icon", "text"]
            },
            "hp": {
                "colors": {"primary": "#0096D6", "secondary": "#FFFFFF"},
                "fonts": ["HP Sans", "Helvetica"],
                "logo_variants": ["full", "icon", "text"]
            },
            "custom": {
                "colors": {"primary": "#FF6B35", "secondary": "#004225"},
                "fonts": ["Custom Sans", "Arial"],
                "logo_variants": ["full", "icon", "text"]
            }
        }
    
    async def create_logo_asset(self, logo_id: str, brand: str, variant: str = "full") -> Optional[Asset3D]:
        """Create a logo asset"""
        try:
            if brand not in self.brand_templates:
                logger.error(f"Brand template not found: {brand}")
                return None
            
            template = self.brand_templates[brand]
            
            # Create logo geometry (simplified - would use actual logo mesh in production)
            logo_geometry = self._generate_logo_geometry(template, variant)
            
            logo_asset = Asset3D(
                asset_id=logo_id,
                asset_type=AssetType.LOGO,
                name=f"{brand}_{variant}_logo",
                geometry_data=logo_geometry,
                material_properties={
                    "primary_color": template["colors"]["primary"],
                    "secondary_color": template["colors"]["secondary"],
                    "brand": brand,
                    "variant": variant
                }
            )
            
            self.logo_assets[logo_id] = logo_asset
            logger.info(f"Created logo asset: {logo_id}")
            return logo_asset
            
        except Exception as e:
            logger.error(f"Error creating logo asset: {e}")
            return None
    
    def _generate_logo_geometry(self, template: Dict[str, Any], variant: str) -> torch.Tensor:
        """Generate logo geometry based on template"""
        # Simplified logo geometry generation
        if variant == "full":
            # Full logo with text and icon
            vertices = torch.tensor([
                [-1.0, -0.5, 0.0], [1.0, -0.5, 0.0], [1.0, 0.5, 0.0], [-1.0, 0.5, 0.0],  # Rectangle
                [-0.8, -0.3, 0.0], [-0.2, -0.3, 0.0], [-0.2, 0.3, 0.0], [-0.8, 0.3, 0.0]   # Icon area
            ], device=self.device)
        elif variant == "icon":
            # Icon only
            vertices = torch.tensor([
                [-0.5, -0.5, 0.0], [0.5, -0.5, 0.0], [0.5, 0.5, 0.0], [-0.5, 0.5, 0.0]
            ], device=self.device)
        else:  # text
            # Text only
            vertices = torch.tensor([
                [-1.0, -0.2, 0.0], [1.0, -0.2, 0.0], [1.0, 0.2, 0.0], [-1.0, 0.2, 0.0]
            ], device=self.device)
        
        return vertices
    
    async def apply_brand_material(self, asset: Asset3D, brand: str) -> bool:
        """Apply brand-specific material to asset"""
        try:
            if brand not in self.brand_templates:
                return False
            
            template = self.brand_templates[brand]
            
            # Update material properties
            asset.material_properties.update({
                "diffuse_color": template["colors"]["primary"],
                "specular_color": template["colors"]["secondary"],
                "brand": brand,
                "material_type": "brand_material"
            })
            
            logger.info(f"Applied brand material '{brand}' to asset {asset.asset_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error applying brand material: {e}")
            return False


class NVIDIAAIAssetsEngine:
    """NVIDIA AI-powered assets engine"""
    
    def __init__(self):
        self.ai_models: Dict[str, torch.nn.Module] = {}
        self.device = device_manager.get_device()
        self.inference_cache: Dict[str, Any] = {}
        
    async def initialize(self) -> bool:
        """Initialize NVIDIA AI assets engine"""
        try:
            # Initialize AI models for asset generation
            await self._load_ai_models()
            logger.info("NVIDIA AI assets engine initialized")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize NVIDIA AI assets engine: {e}")
            return False
    
    async def _load_ai_models(self):
        """Load AI models for asset generation"""
        # Placeholder for actual AI model loading
        # In production, would load models like:
        # - Generative models for procedural geometry
        # - Style transfer models for textures
        # - Neural rendering models
        
        # Simple placeholder models
        self.ai_models["geometry_generator"] = torch.nn.Sequential(
            torch.nn.Linear(64, 128),
            torch.nn.ReLU(),
            torch.nn.Linear(128, 256),
            torch.nn.ReLU(),
            torch.nn.Linear(256, 512)  # Output geometry features
        ).to(self.device)
        
        self.ai_models["texture_generator"] = torch.nn.Sequential(
            torch.nn.ConvTranspose2d(64, 128, 4, 2, 1),
            torch.nn.ReLU(),
            torch.nn.ConvTranspose2d(128, 64, 4, 2, 1),
            torch.nn.ReLU(),
            torch.nn.ConvTranspose2d(64, 3, 4, 2, 1),
            torch.nn.Tanh()
        ).to(self.device)
    
    async def generate_procedural_geometry(self, geometry_id: str, params: Dict[str, Any]) -> torch.Tensor:
        """Generate procedural geometry using AI"""
        try:
            # Generate input features from parameters
            input_features = torch.randn(1, 64, device=self.device)
            
            # Use AI model to generate geometry
            with torch.no_grad():
                geometry_features = self.ai_models["geometry_generator"](input_features)
            
            # Convert features to actual geometry (simplified)
            num_vertices = min(geometry_features.shape[-1] // 3, 256)
            geometry = geometry_features[0, :num_vertices*3].reshape(-1, 3)
            
            # Cache result
            self.inference_cache[geometry_id] = {
                "geometry": geometry,
                "params": params,
                "generated_at": datetime.now(timezone.utc)
            }
            
            logger.info(f"Generated procedural geometry: {geometry_id}")
            return geometry
            
        except Exception as e:
            logger.error(f"Error generating procedural geometry: {e}")
            return torch.empty(0, 3, device=self.device)
    
    async def generate_ai_texture(self, texture_id: str, style_params: Dict[str, Any]) -> torch.Tensor:
        """Generate AI-powered texture"""
        try:
            # Generate input noise
            noise = torch.randn(1, 64, 8, 8, device=self.device)
            
            # Use AI model to generate texture
            with torch.no_grad():
                texture = self.ai_models["texture_generator"](noise)
            
            # Normalize to [0, 1]
            texture = (texture + 1.0) / 2.0
            
            # Cache result
            self.inference_cache[texture_id] = {
                "texture": texture,
                "style_params": style_params,
                "generated_at": datetime.now(timezone.utc)
            }
            
            logger.info(f"Generated AI texture: {texture_id}")
            return texture.squeeze(0)  # Remove batch dimension
            
        except Exception as e:
            logger.error(f"Error generating AI texture: {e}")
            return torch.zeros(3, 64, 64, device=self.device)
    
    async def enhance_asset_quality(self, asset: Asset3D) -> Asset3D:
        """Enhance asset quality using AI"""
        try:
            # AI-powered asset enhancement
            enhanced_asset = Asset3D(
                asset_id=f"{asset.asset_id}_enhanced",
                asset_type=asset.asset_type,
                name=f"{asset.name}_enhanced",
                geometry_data=asset.geometry_data,
                texture_data=asset.texture_data,
                material_properties=asset.material_properties.copy(),
                transform_matrix=asset.transform_matrix.clone()
            )
            
            # Apply AI enhancements
            if asset.geometry_data is not None:
                # Enhance geometry (e.g., subdivision, smoothing)
                enhanced_asset.geometry_data = await self._enhance_geometry(asset.geometry_data)
            
            if asset.texture_data is not None:
                # Enhance texture (e.g., super-resolution, denoising)
                enhanced_asset.texture_data = await self._enhance_texture(asset.texture_data)
            
            # Update material properties
            enhanced_asset.material_properties["ai_enhanced"] = True
            enhanced_asset.material_properties["enhancement_timestamp"] = datetime.now(timezone.utc).isoformat()
            
            logger.info(f"Enhanced asset quality: {asset.asset_id}")
            return enhanced_asset
            
        except Exception as e:
            logger.error(f"Error enhancing asset quality: {e}")
            return asset
    
    async def _enhance_geometry(self, geometry: torch.Tensor) -> torch.Tensor:
        """Enhance geometry using AI"""
        # Simplified geometry enhancement
        if geometry.shape[0] > 0:
            # Apply smoothing or subdivision
            enhanced = geometry + torch.randn_like(geometry) * 0.01
            return enhanced
        return geometry
    
    async def _enhance_texture(self, texture: torch.Tensor) -> torch.Tensor:
        """Enhance texture using AI"""
        # Simplified texture enhancement
        if texture.numel() > 0:
            # Apply denoising or super-resolution
            enhanced = torch.clamp(texture + torch.randn_like(texture) * 0.02, 0, 1)
            return enhanced
        return texture


class Assets3DComponent(PipelineComponent):
    """Main 3D Assets pipeline component"""
    
    def __init__(self):
        super().__init__("3d_assets", ComponentType.ASSETS_3D)
        
        # Initialize sub-components
        self.usd_engine = USDSimulationEngine()
        self.logo_manager = LogoObjectsManager()
        self.nvidia_ai_engine = NVIDIAAIAssetsEngine()
        
        # Asset management
        self.asset_registry: Dict[str, Asset3D] = {}
        self.asset_collections: Dict[str, List[str]] = {}
        
    async def initialize(self) -> bool:
        """Initialize 3D Assets component"""
        try:
            # Initialize all sub-components
            components = [
                ("USD Engine", self.usd_engine),
                ("Logo Manager", self.logo_manager),
                ("NVIDIA AI Engine", self.nvidia_ai_engine)
            ]
            
            for name, component in components:
                if await component.initialize():
                    logger.info(f"Initialized {name}")
                else:
                    logger.error(f"Failed to initialize {name}")
                    return False
            
            self.is_initialized = True
            logger.info("3D Assets component initialized")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing 3D Assets component: {e}")
            return False
    
    async def process(self, data: PipelineData) -> PipelineData:
        """Process data through 3D Assets pipeline"""
        try:
            # Update stage
            data.stage = ProcessingStage.PREPROCESSING
            
            # Process based on action
            action = data.data.get("action", "create_asset")
            
            if action == "create_asset":
                await self._handle_create_asset(data)
            elif action == "create_stage":
                await self._handle_create_stage(data)
            elif action == "add_to_stage":
                await self._handle_add_to_stage(data)
            elif action == "render_stage":
                await self._handle_render_stage(data)
            elif action == "generate_procedural":
                await self._handle_generate_procedural(data)
            elif action == "enhance_asset":
                await self._handle_enhance_asset(data)
            
            # Add 3D assets metadata
            data.metadata['3d_assets'] = {
                'processed_at': datetime.now(timezone.utc).isoformat(),
                'total_assets': len(self.asset_registry),
                'usd_stages': len(self.usd_engine.stages)
            }
            
            # Update metrics
            self.update_metrics({
                'data_processed': self.metrics.get('data_processed', 0) + 1,
                'total_assets': len(self.asset_registry),
                'usd_stages': len(self.usd_engine.stages)
            })
            
            logger.debug("Data processed through 3D Assets pipeline")
            return data
            
        except Exception as e:
            logger.error(f"Error processing data in 3D Assets pipeline: {e}")
            raise
    
    async def _handle_create_asset(self, data: PipelineData):
        """Handle asset creation"""
        asset_type = AssetType(data.data.get("asset_type", "mesh"))
        asset_id = data.data.get("asset_id", f"asset_{datetime.now().timestamp()}")
        asset_name = data.data.get("name", asset_id)
        
        # Create asset
        asset = Asset3D(
            asset_id=asset_id,
            asset_type=asset_type,
            name=asset_name
        )
        
        # Add geometry data if provided
        if "geometry" in data.tensors:
            asset.geometry_data = data.tensors["geometry"]
        
        # Add texture data if provided
        if "texture" in data.tensors:
            asset.texture_data = data.tensors["texture"]
        
        # Register asset
        self.asset_registry[asset_id] = asset
        data.data["asset_created"] = True
        data.data["asset_id"] = asset_id
    
    async def _handle_create_stage(self, data: PipelineData):
        """Handle USD stage creation"""
        stage_id = data.data.get("stage_id", f"stage_{datetime.now().timestamp()}")
        stage_type = USDStageType(data.data.get("stage_type", "main_stage"))
        
        success = await self.usd_engine.create_stage(stage_id, stage_type)
        data.data["stage_created"] = success
        data.data["stage_id"] = stage_id
    
    async def _handle_add_to_stage(self, data: PipelineData):
        """Handle adding asset to stage"""
        stage_id = data.data.get("stage_id")
        asset_id = data.data.get("asset_id")
        
        if asset_id in self.asset_registry:
            asset = self.asset_registry[asset_id]
            success = await self.usd_engine.add_asset_to_stage(stage_id, asset)
            data.data["asset_added_to_stage"] = success
        else:
            data.data["error"] = f"Asset not found: {asset_id}"
    
    async def _handle_render_stage(self, data: PipelineData):
        """Handle stage rendering"""
        stage_id = data.data.get("stage_id")
        resolution = data.data.get("resolution", (1920, 1080))
        
        rendered_image = await self.usd_engine.render_stage(stage_id, resolution=resolution)
        if rendered_image is not None:
            data.add_tensor("rendered_image", rendered_image)
            data.data["render_success"] = True
        else:
            data.data["render_success"] = False
    
    async def _handle_generate_procedural(self, data: PipelineData):
        """Handle procedural generation"""
        geometry_id = data.data.get("geometry_id", f"proc_{datetime.now().timestamp()}")
        params = data.data.get("parameters", {})
        
        geometry = await self.nvidia_ai_engine.generate_procedural_geometry(geometry_id, params)
        data.add_tensor("procedural_geometry", geometry)
        data.data["procedural_generated"] = True
    
    async def _handle_enhance_asset(self, data: PipelineData):
        """Handle asset enhancement"""
        asset_id = data.data.get("asset_id")
        
        if asset_id in self.asset_registry:
            asset = self.asset_registry[asset_id]
            enhanced_asset = await self.nvidia_ai_engine.enhance_asset_quality(asset)
            
            # Register enhanced asset
            self.asset_registry[enhanced_asset.asset_id] = enhanced_asset
            data.data["enhanced_asset_id"] = enhanced_asset.asset_id
            data.data["asset_enhanced"] = True
        else:
            data.data["error"] = f"Asset not found: {asset_id}"
    
    async def cleanup(self):
        """Clean up 3D Assets resources"""
        self.asset_registry.clear()
        self.asset_collections.clear()
        logger.info("3D Assets component cleaned up")


# Global 3D assets component instance
assets_3d = Assets3DComponent()