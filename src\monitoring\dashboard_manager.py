"""
Comprehensive Monitoring Dashboard Manager
Real-time analytics dashboard for MLOps Digital Twin Platform
"""

import asyncio
import logging
import json
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
import threading
import time
import warnings
warnings.filterwarnings('ignore')

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.io as pio
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    print("Warning: Plotly not available. Dashboard visualizations will be limited.")

try:
    import dash
    from dash import dcc, html, Input, Output, State, callback_context
    import dash_bootstrap_components as dbc
    DASH_AVAILABLE = True
except ImportError:
    DASH_AVAILABLE = False
    print("Warning: Dash not available. Web dashboard will not be available.")

try:
    import streamlit as st
    STREAMLIT_AVAILABLE = True
except ImportError:
    STREAMLIT_AVAILABLE = False
    print("Warning: Streamlit not available. Alternative dashboard framework will be used.")

logger = logging.getLogger(__name__)


class DashboardType(Enum):
    OPERATIONAL = "operational"
    PERFORMANCE = "performance"
    ENERGY = "energy"
    ANOMALY = "anomaly"
    BUSINESS = "business"
    SYSTEM = "system"


class MetricType(Enum):
    GAUGE = "gauge"
    TIME_SERIES = "time_series"
    BAR_CHART = "bar_chart"
    PIE_CHART = "pie_chart"
    HEATMAP = "heatmap"
    SCATTER = "scatter"
    TABLE = "table"
    KPI = "kpi"


class AlertSeverity(Enum):
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class MetricDefinition:
    """Definition of a dashboard metric"""
    name: str
    title: str
    description: str
    metric_type: MetricType
    data_source: str
    query: str
    refresh_interval: timedelta = timedelta(seconds=30)
    dashboard_types: List[DashboardType] = field(default_factory=list)
    visualization_config: Dict[str, Any] = field(default_factory=dict)
    thresholds: Dict[str, float] = field(default_factory=dict)
    unit: str = ""
    format_string: str = "{:.2f}"


@dataclass
class AlertRule:
    """Alert rule definition"""
    name: str
    description: str
    metric_name: str
    condition: str  # e.g., "> 80", "< 10", "== 0"
    severity: AlertSeverity
    threshold_value: float
    evaluation_window: timedelta = timedelta(minutes=5)
    notification_channels: List[str] = field(default_factory=list)
    enabled: bool = True


@dataclass
class DashboardWidget:
    """Dashboard widget configuration"""
    widget_id: str
    title: str
    metric_name: str
    position: Tuple[int, int]  # (row, column)
    size: Tuple[int, int]  # (width, height)
    widget_type: MetricType
    config: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Dashboard:
    """Dashboard configuration"""
    dashboard_id: str
    name: str
    description: str
    dashboard_type: DashboardType
    widgets: List[DashboardWidget] = field(default_factory=list)
    refresh_interval: timedelta = timedelta(seconds=30)
    auto_refresh: bool = True
    layout_config: Dict[str, Any] = field(default_factory=dict)


class DataSource(ABC):
    """Abstract base class for data sources"""
    
    @abstractmethod
    async def get_metric_data(self, metric_name: str, start_time: datetime, 
                             end_time: datetime) -> Optional[pd.DataFrame]:
        """Get metric data"""
        pass
    
    @abstractmethod
    async def get_current_value(self, metric_name: str) -> Optional[float]:
        """Get current metric value"""
        pass


class MockDataSource(DataSource):
    """Mock data source for testing"""
    
    def __init__(self):
        self.mock_data = {}
        self._generate_mock_data()
    
    def _generate_mock_data(self):
        """Generate mock data for testing"""
        now = datetime.now()
        
        # Temperature data
        temp_data = []
        for i in range(100):
            timestamp = now - timedelta(minutes=100-i)
            value = 150 + 10 * np.sin(i/10) + np.random.normal(0, 2)
            temp_data.append({'timestamp': timestamp, 'value': value})
        
        self.mock_data['temperature'] = pd.DataFrame(temp_data)
        
        # Energy consumption data
        energy_data = []
        for i in range(100):
            timestamp = now - timedelta(minutes=100-i)
            value = 1000 + 200 * np.sin(i/20) + np.random.normal(0, 50)
            energy_data.append({'timestamp': timestamp, 'value': value})
        
        self.mock_data['energy_consumption'] = pd.DataFrame(energy_data)
        
        # Efficiency data
        efficiency_data = []
        for i in range(100):
            timestamp = now - timedelta(minutes=100-i)
            value = 0.85 + 0.1 * np.sin(i/15) + np.random.normal(0, 0.02)
            efficiency_data.append({'timestamp': timestamp, 'value': value})
        
        self.mock_data['efficiency'] = pd.DataFrame(efficiency_data)
    
    async def get_metric_data(self, metric_name: str, start_time: datetime, 
                             end_time: datetime) -> Optional[pd.DataFrame]:
        """Get mock metric data"""
        if metric_name in self.mock_data:
            df = self.mock_data[metric_name].copy()
            df = df[(df['timestamp'] >= start_time) & (df['timestamp'] <= end_time)]
            return df
        return None
    
    async def get_current_value(self, metric_name: str) -> Optional[float]:
        """Get current mock value"""
        if metric_name in self.mock_data:
            return float(self.mock_data[metric_name]['value'].iloc[-1])
        return None


class MetricCalculator:
    """Calculate derived metrics"""
    
    def __init__(self, data_source: DataSource):
        self.data_source = data_source
        self.cache = {}
        self.cache_ttl = timedelta(seconds=30)
    
    async def calculate_metric(self, metric_name: str, **kwargs) -> Optional[float]:
        """Calculate a derived metric"""
        try:
            # Check cache
            cache_key = f"{metric_name}_{kwargs}"
            if cache_key in self.cache:
                cached_time, cached_value = self.cache[cache_key]
                if datetime.now() - cached_time < self.cache_ttl:
                    return cached_value
            
            # Calculate metric
            if metric_name == "temperature_stability":
                result = await self._calculate_temperature_stability(**kwargs)
            elif metric_name == "energy_efficiency":
                result = await self._calculate_energy_efficiency(**kwargs)
            elif metric_name == "system_availability":
                result = await self._calculate_system_availability(**kwargs)
            elif metric_name == "cost_savings":
                result = await self._calculate_cost_savings(**kwargs)
            elif metric_name == "prediction_accuracy":
                result = await self._calculate_prediction_accuracy(**kwargs)
            else:
                # Try to get directly from data source
                result = await self.data_source.get_current_value(metric_name)
            
            # Cache result
            if result is not None:
                self.cache[cache_key] = (datetime.now(), result)
            
            return result
        
        except Exception as e:
            logger.error(f"Error calculating metric {metric_name}: {e}")
            return None
    
    async def _calculate_temperature_stability(self, window_minutes: int = 60) -> Optional[float]:
        """Calculate temperature stability metric"""
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(minutes=window_minutes)
            
            df = await self.data_source.get_metric_data("temperature", start_time, end_time)
            if df is not None and not df.empty:
                std_dev = df['value'].std()
                mean_temp = df['value'].mean()
                # Stability = 1 - (std_dev / mean_temp), bounded between 0 and 1
                stability = max(0, 1 - (std_dev / mean_temp))
                return min(1.0, stability)
            
            return None
        
        except Exception as e:
            logger.error(f"Error calculating temperature stability: {e}")
            return None
    
    async def _calculate_energy_efficiency(self, window_minutes: int = 60) -> Optional[float]:
        """Calculate energy efficiency metric"""
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(minutes=window_minutes)
            
            energy_df = await self.data_source.get_metric_data("energy_consumption", start_time, end_time)
            temp_df = await self.data_source.get_metric_data("temperature", start_time, end_time)
            
            if energy_df is not None and temp_df is not None and not energy_df.empty and not temp_df.empty:
                # Simplified efficiency calculation
                avg_energy = energy_df['value'].mean()
                avg_temp = temp_df['value'].mean()
                
                # Efficiency = temperature / energy (normalized)
                if avg_energy > 0:
                    efficiency = min(1.0, avg_temp / (avg_energy / 1000))  # Normalize by 1000
                    return efficiency
            
            return None
        
        except Exception as e:
            logger.error(f"Error calculating energy efficiency: {e}")
            return None
    
    async def _calculate_system_availability(self, window_hours: int = 24) -> Optional[float]:
        """Calculate system availability metric"""
        try:
            # Mock calculation - in real system, this would check system status
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=window_hours)
            
            # Simulate 99.5% availability
            availability = 0.995 + np.random.normal(0, 0.002)
            return max(0.0, min(1.0, availability))
        
        except Exception as e:
            logger.error(f"Error calculating system availability: {e}")
            return None
    
    async def _calculate_cost_savings(self, baseline_cost: float = 1000) -> Optional[float]:
        """Calculate cost savings percentage"""
        try:
            # Mock calculation - in real system, this would compare actual vs baseline costs
            current_cost = await self.data_source.get_current_value("energy_consumption")
            if current_cost is not None:
                # Convert energy to cost and calculate savings
                estimated_cost = current_cost * 0.1  # $0.1 per kWh
                savings_percent = ((baseline_cost - estimated_cost) / baseline_cost) * 100
                return max(0.0, savings_percent)
            
            return None
        
        except Exception as e:
            logger.error(f"Error calculating cost savings: {e}")
            return None
    
    async def _calculate_prediction_accuracy(self, window_hours: int = 24) -> Optional[float]:
        """Calculate prediction accuracy metric"""
        try:
            # Mock calculation - in real system, this would compare predictions vs actual
            accuracy = 0.92 + np.random.normal(0, 0.05)
            return max(0.0, min(1.0, accuracy))
        
        except Exception as e:
            logger.error(f"Error calculating prediction accuracy: {e}")
            return None


class AlertManager:
    """Manage alerts and notifications"""
    
    def __init__(self, metric_calculator: MetricCalculator):
        self.metric_calculator = metric_calculator
        self.alert_rules = {}
        self.active_alerts = {}
        self.alert_history = []
        self.notification_channels = {}
    
    def add_alert_rule(self, rule: AlertRule) -> bool:
        """Add an alert rule"""
        try:
            self.alert_rules[rule.name] = rule
            logger.info(f"Added alert rule: {rule.name}")
            return True
        except Exception as e:
            logger.error(f"Error adding alert rule: {e}")
            return False
    
    async def evaluate_alerts(self) -> List[Dict[str, Any]]:
        """Evaluate all alert rules"""
        new_alerts = []
        
        try:
            for rule_name, rule in self.alert_rules.items():
                if not rule.enabled:
                    continue
                
                # Get current metric value
                current_value = await self.metric_calculator.calculate_metric(rule.metric_name)
                
                if current_value is not None:
                    # Evaluate condition
                    alert_triggered = self._evaluate_condition(current_value, rule.condition, rule.threshold_value)
                    
                    if alert_triggered:
                        if rule_name not in self.active_alerts:
                            # New alert
                            alert = {
                                'rule_name': rule_name,
                                'metric_name': rule.metric_name,
                                'current_value': current_value,
                                'threshold_value': rule.threshold_value,
                                'severity': rule.severity.value,
                                'description': rule.description,
                                'timestamp': datetime.now(),
                                'status': 'active'
                            }
                            
                            self.active_alerts[rule_name] = alert
                            self.alert_history.append(alert.copy())
                            new_alerts.append(alert)
                            
                            logger.warning(f"Alert triggered: {rule_name} - {rule.description}")
                    
                    else:
                        if rule_name in self.active_alerts:
                            # Alert resolved
                            resolved_alert = self.active_alerts[rule_name].copy()
                            resolved_alert['status'] = 'resolved'
                            resolved_alert['resolved_timestamp'] = datetime.now()
                            
                            self.alert_history.append(resolved_alert)
                            del self.active_alerts[rule_name]
                            
                            logger.info(f"Alert resolved: {rule_name}")
        
        except Exception as e:
            logger.error(f"Error evaluating alerts: {e}")
        
        return new_alerts
    
    def _evaluate_condition(self, value: float, condition: str, threshold: float) -> bool:
        """Evaluate alert condition"""
        try:
            if condition.startswith('>'):
                return value > threshold
            elif condition.startswith('<'):
                return value < threshold
            elif condition.startswith('>='):
                return value >= threshold
            elif condition.startswith('<='):
                return value <= threshold
            elif condition.startswith('=='):
                return abs(value - threshold) < 0.001
            elif condition.startswith('!='):
                return abs(value - threshold) >= 0.001
            else:
                return False
        except Exception as e:
            logger.error(f"Error evaluating condition: {e}")
            return False
    
    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """Get list of active alerts"""
        return list(self.active_alerts.values())
    
    def get_alert_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get alert history"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [alert for alert in self.alert_history if alert['timestamp'] >= cutoff_time]


class VisualizationEngine:
    """Create visualizations for dashboard"""
    
    def __init__(self, data_source: DataSource):
        self.data_source = data_source
    
    async def create_time_series_chart(self, metric_name: str, title: str, 
                                      window_hours: int = 1) -> Optional[Dict[str, Any]]:
        """Create time series chart"""
        try:
            if not PLOTLY_AVAILABLE:
                return {"error": "Plotly not available"}
            
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=window_hours)
            
            df = await self.data_source.get_metric_data(metric_name, start_time, end_time)
            
            if df is not None and not df.empty:
                fig = go.Figure()
                fig.add_trace(go.Scatter(
                    x=df['timestamp'],
                    y=df['value'],
                    mode='lines+markers',
                    name=metric_name,
                    line=dict(width=2)
                ))
                
                fig.update_layout(
                    title=title,
                    xaxis_title="Time",
                    yaxis_title="Value",
                    hovermode='x unified',
                    showlegend=True
                )
                
                return fig.to_dict()
            
            return None
        
        except Exception as e:
            logger.error(f"Error creating time series chart: {e}")
            return None
    
    async def create_gauge_chart(self, metric_name: str, title: str, 
                                min_val: float = 0, max_val: float = 100) -> Optional[Dict[str, Any]]:
        """Create gauge chart"""
        try:
            if not PLOTLY_AVAILABLE:
                return {"error": "Plotly not available"}
            
            current_value = await self.data_source.get_current_value(metric_name)
            
            if current_value is not None:
                fig = go.Figure(go.Indicator(
                    mode="gauge+number+delta",
                    value=current_value,
                    domain={'x': [0, 1], 'y': [0, 1]},
                    title={'text': title},
                    gauge={
                        'axis': {'range': [None, max_val]},
                        'bar': {'color': "darkblue"},
                        'steps': [
                            {'range': [0, max_val*0.5], 'color': "lightgray"},
                            {'range': [max_val*0.5, max_val*0.8], 'color': "gray"}
                        ],
                        'threshold': {
                            'line': {'color': "red", 'width': 4},
                            'thickness': 0.75,
                            'value': max_val*0.9
                        }
                    }
                ))
                
                return fig.to_dict()
            
            return None
        
        except Exception as e:
            logger.error(f"Error creating gauge chart: {e}")
            return None
    
    async def create_kpi_widget(self, metric_name: str, title: str, 
                               format_string: str = "{:.2f}", unit: str = "") -> Optional[Dict[str, Any]]:
        """Create KPI widget"""
        try:
            current_value = await self.data_source.get_current_value(metric_name)
            
            if current_value is not None:
                formatted_value = format_string.format(current_value)
                return {
                    'type': 'kpi',
                    'title': title,
                    'value': formatted_value,
                    'unit': unit,
                    'raw_value': current_value
                }
            
            return None
        
        except Exception as e:
            logger.error(f"Error creating KPI widget: {e}")
            return None
    
    async def create_status_table(self, metrics: List[str]) -> Optional[Dict[str, Any]]:
        """Create status table"""
        try:
            data = []
            for metric in metrics:
                current_value = await self.data_source.get_current_value(metric)
                if current_value is not None:
                    data.append({
                        'Metric': metric,
                        'Current Value': f"{current_value:.2f}",
                        'Status': 'OK' if current_value > 0 else 'Warning',
                        'Last Updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
            
            if data:
                return {
                    'type': 'table',
                    'data': data,
                    'columns': ['Metric', 'Current Value', 'Status', 'Last Updated']
                }
            
            return None
        
        except Exception as e:
            logger.error(f"Error creating status table: {e}")
            return None


class DashboardManager:
    """Main dashboard manager"""
    
    def __init__(self, data_source: Optional[DataSource] = None):
        self.data_source = data_source or MockDataSource()
        self.metric_calculator = MetricCalculator(self.data_source)
        self.alert_manager = AlertManager(self.metric_calculator)
        self.visualization_engine = VisualizationEngine(self.data_source)
        self.dashboards = {}
        self.metrics = {}
        self.is_running = False
        self.update_tasks = []
        
        # Initialize with default metrics and dashboards
        self._initialize_default_metrics()
        self._initialize_default_dashboards()
        self._initialize_default_alerts()
    
    def _initialize_default_metrics(self):
        """Initialize default metrics"""
        default_metrics = [
            MetricDefinition(
                name="temperature",
                title="Tank Temperature",
                description="Current tank temperature",
                metric_type=MetricType.TIME_SERIES,
                data_source="timeseries",
                query="SELECT * FROM temperature",
                dashboard_types=[DashboardType.OPERATIONAL, DashboardType.PERFORMANCE],
                unit="°C",
                thresholds={"warning": 160, "critical": 180}
            ),
            MetricDefinition(
                name="energy_consumption",
                title="Energy Consumption",
                description="Current energy consumption",
                metric_type=MetricType.TIME_SERIES,
                data_source="timeseries",
                query="SELECT * FROM energy",
                dashboard_types=[DashboardType.ENERGY, DashboardType.BUSINESS],
                unit="kW",
                thresholds={"warning": 1200, "critical": 1500}
            ),
            MetricDefinition(
                name="efficiency",
                title="System Efficiency",
                description="Overall system efficiency",
                metric_type=MetricType.GAUGE,
                data_source="calculated",
                query="efficiency_calculation",
                dashboard_types=[DashboardType.PERFORMANCE, DashboardType.BUSINESS],
                unit="%",
                format_string="{:.1f}",
                thresholds={"warning": 0.8, "critical": 0.7}
            )
        ]
        
        for metric in default_metrics:
            self.metrics[metric.name] = metric
    
    def _initialize_default_dashboards(self):
        """Initialize default dashboards"""
        # Operational Dashboard
        operational_widgets = [
            DashboardWidget(
                widget_id="temp_gauge",
                title="Tank Temperature",
                metric_name="temperature",
                position=(0, 0),
                size=(6, 4),
                widget_type=MetricType.GAUGE,
                config={"min_val": 0, "max_val": 200}
            ),
            DashboardWidget(
                widget_id="temp_chart",
                title="Temperature Trend",
                metric_name="temperature",
                position=(0, 6),
                size=(6, 4),
                widget_type=MetricType.TIME_SERIES,
                config={"window_hours": 2}
            ),
            DashboardWidget(
                widget_id="energy_kpi",
                title="Energy Consumption",
                metric_name="energy_consumption",
                position=(4, 0),
                size=(3, 2),
                widget_type=MetricType.KPI
            ),
            DashboardWidget(
                widget_id="efficiency_kpi",
                title="Efficiency",
                metric_name="efficiency",
                position=(4, 3),
                size=(3, 2),
                widget_type=MetricType.KPI
            )
        ]
        
        operational_dashboard = Dashboard(
            dashboard_id="operational",
            name="Operational Dashboard",
            description="Real-time operational monitoring",
            dashboard_type=DashboardType.OPERATIONAL,
            widgets=operational_widgets
        )
        
        self.dashboards["operational"] = operational_dashboard
        
        # Performance Dashboard
        performance_widgets = [
            DashboardWidget(
                widget_id="efficiency_trend",
                title="Efficiency Trend",
                metric_name="efficiency",
                position=(0, 0),
                size=(12, 6),
                widget_type=MetricType.TIME_SERIES,
                config={"window_hours": 24}
            ),
            DashboardWidget(
                widget_id="status_table",
                title="System Status",
                metric_name="status",
                position=(6, 0),
                size=(6, 6),
                widget_type=MetricType.TABLE
            )
        ]
        
        performance_dashboard = Dashboard(
            dashboard_id="performance",
            name="Performance Dashboard",
            description="System performance monitoring",
            dashboard_type=DashboardType.PERFORMANCE,
            widgets=performance_widgets
        )
        
        self.dashboards["performance"] = performance_dashboard
    
    def _initialize_default_alerts(self):
        """Initialize default alert rules"""
        default_alerts = [
            AlertRule(
                name="high_temperature",
                description="Tank temperature is too high",
                metric_name="temperature",
                condition="> 160",
                severity=AlertSeverity.WARNING,
                threshold_value=160.0
            ),
            AlertRule(
                name="critical_temperature",
                description="Tank temperature is critically high",
                metric_name="temperature",
                condition="> 180",
                severity=AlertSeverity.CRITICAL,
                threshold_value=180.0
            ),
            AlertRule(
                name="high_energy_consumption",
                description="Energy consumption is above normal",
                metric_name="energy_consumption",
                condition="> 1200",
                severity=AlertSeverity.WARNING,
                threshold_value=1200.0
            ),
            AlertRule(
                name="low_efficiency",
                description="System efficiency is below acceptable level",
                metric_name="efficiency",
                condition="< 0.8",
                severity=AlertSeverity.WARNING,
                threshold_value=0.8
            )
        ]
        
        for alert in default_alerts:
            self.alert_manager.add_alert_rule(alert)
    
    async def start_monitoring(self):
        """Start the monitoring system"""
        try:
            if self.is_running:
                logger.warning("Monitoring already running")
                return
            
            self.is_running = True
            
            # Start alert evaluation task
            alert_task = asyncio.create_task(self._alert_evaluation_loop())
            self.update_tasks.append(alert_task)
            
            logger.info("Dashboard monitoring started")
        
        except Exception as e:
            logger.error(f"Error starting monitoring: {e}")
    
    async def stop_monitoring(self):
        """Stop the monitoring system"""
        try:
            self.is_running = False
            
            # Cancel all tasks
            for task in self.update_tasks:
                task.cancel()
            
            await asyncio.gather(*self.update_tasks, return_exceptions=True)
            self.update_tasks.clear()
            
            logger.info("Dashboard monitoring stopped")
        
        except Exception as e:
            logger.error(f"Error stopping monitoring: {e}")
    
    async def _alert_evaluation_loop(self):
        """Alert evaluation loop"""
        while self.is_running:
            try:
                await self.alert_manager.evaluate_alerts()
                await asyncio.sleep(30)  # Evaluate every 30 seconds
            
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in alert evaluation loop: {e}")
                await asyncio.sleep(60)
    
    async def get_dashboard_data(self, dashboard_id: str) -> Optional[Dict[str, Any]]:
        """Get complete dashboard data"""
        try:
            if dashboard_id not in self.dashboards:
                return None
            
            dashboard = self.dashboards[dashboard_id]
            dashboard_data = {
                'dashboard_id': dashboard_id,
                'name': dashboard.name,
                'description': dashboard.description,
                'type': dashboard.dashboard_type.value,
                'widgets': [],
                'alerts': self.alert_manager.get_active_alerts(),
                'last_updated': datetime.now().isoformat()
            }
            
            # Get widget data
            for widget in dashboard.widgets:
                widget_data = await self._get_widget_data(widget)
                if widget_data:
                    dashboard_data['widgets'].append(widget_data)
            
            return dashboard_data
        
        except Exception as e:
            logger.error(f"Error getting dashboard data: {e}")
            return None
    
    async def _get_widget_data(self, widget: DashboardWidget) -> Optional[Dict[str, Any]]:
        """Get data for a specific widget"""
        try:
            widget_data = {
                'widget_id': widget.widget_id,
                'title': widget.title,
                'position': widget.position,
                'size': widget.size,
                'type': widget.widget_type.value,
                'config': widget.config
            }
            
            if widget.widget_type == MetricType.TIME_SERIES:
                chart_data = await self.visualization_engine.create_time_series_chart(
                    widget.metric_name,
                    widget.title,
                    widget.config.get('window_hours', 1)
                )
                widget_data['chart_data'] = chart_data
            
            elif widget.widget_type == MetricType.GAUGE:
                gauge_data = await self.visualization_engine.create_gauge_chart(
                    widget.metric_name,
                    widget.title,
                    widget.config.get('min_val', 0),
                    widget.config.get('max_val', 100)
                )
                widget_data['chart_data'] = gauge_data
            
            elif widget.widget_type == MetricType.KPI:
                metric = self.metrics.get(widget.metric_name)
                kpi_data = await self.visualization_engine.create_kpi_widget(
                    widget.metric_name,
                    widget.title,
                    metric.format_string if metric else "{:.2f}",
                    metric.unit if metric else ""
                )
                widget_data['kpi_data'] = kpi_data
            
            elif widget.widget_type == MetricType.TABLE:
                table_data = await self.visualization_engine.create_status_table(
                    list(self.metrics.keys())
                )
                widget_data['table_data'] = table_data
            
            return widget_data
        
        except Exception as e:
            logger.error(f"Error getting widget data: {e}")
            return None
    
    async def get_system_overview(self) -> Dict[str, Any]:
        """Get system overview data"""
        try:
            overview = {
                'timestamp': datetime.now().isoformat(),
                'system_status': 'operational',
                'active_alerts': len(self.alert_manager.get_active_alerts()),
                'total_dashboards': len(self.dashboards),
                'total_metrics': len(self.metrics),
                'key_metrics': {}
            }
            
            # Get key metrics
            key_metric_names = ['temperature', 'energy_consumption', 'efficiency']
            for metric_name in key_metric_names:
                if metric_name in self.metrics:
                    value = await self.metric_calculator.calculate_metric(metric_name)
                    if value is not None:
                        metric = self.metrics[metric_name]
                        overview['key_metrics'][metric_name] = {
                            'value': value,
                            'unit': metric.unit,
                            'title': metric.title
                        }
            
            # Add calculated metrics
            stability = await self.metric_calculator.calculate_metric('temperature_stability')
            if stability is not None:
                overview['key_metrics']['temperature_stability'] = {
                    'value': stability,
                    'unit': '',
                    'title': 'Temperature Stability'
                }
            
            availability = await self.metric_calculator.calculate_metric('system_availability')
            if availability is not None:
                overview['key_metrics']['system_availability'] = {
                    'value': availability * 100,
                    'unit': '%',
                    'title': 'System Availability'
                }
            
            return overview
        
        except Exception as e:
            logger.error(f"Error getting system overview: {e}")
            return {}
    
    async def get_alert_summary(self) -> Dict[str, Any]:
        """Get alert summary"""
        try:
            active_alerts = self.alert_manager.get_active_alerts()
            alert_history = self.alert_manager.get_alert_history(24)
            
            severity_counts = {'info': 0, 'warning': 0, 'error': 0, 'critical': 0}
            for alert in active_alerts:
                severity_counts[alert['severity']] += 1
            
            return {
                'active_alerts_count': len(active_alerts),
                'alerts_last_24h': len(alert_history),
                'severity_breakdown': severity_counts,
                'recent_alerts': active_alerts[:5],  # Last 5 alerts
                'alert_trends': {
                    'total_today': len([a for a in alert_history if a['timestamp'].date() == datetime.now().date()]),
                    'resolved_today': len([a for a in alert_history if a.get('status') == 'resolved' and a['timestamp'].date() == datetime.now().date()])
                }
            }
        
        except Exception as e:
            logger.error(f"Error getting alert summary: {e}")
            return {}
    
    def get_available_dashboards(self) -> List[Dict[str, str]]:
        """Get list of available dashboards"""
        return [
            {
                'dashboard_id': dashboard_id,
                'name': dashboard.name,
                'description': dashboard.description,
                'type': dashboard.dashboard_type.value
            }
            for dashboard_id, dashboard in self.dashboards.items()
        ]


# Factory function
def create_dashboard_manager(data_source: Optional[DataSource] = None) -> DashboardManager:
    """Create a dashboard manager instance"""
    return DashboardManager(data_source)


# Example usage
if __name__ == "__main__":
    async def test_dashboard_manager():
        # Create dashboard manager
        dashboard_manager = create_dashboard_manager()
        
        # Start monitoring
        await dashboard_manager.start_monitoring()
        
        # Wait a bit for data to be generated
        await asyncio.sleep(5)
        
        # Get system overview
        overview = await dashboard_manager.get_system_overview()
        print(f"System Overview:\n{json.dumps(overview, indent=2)}")
        
        # Get operational dashboard data
        dashboard_data = await dashboard_manager.get_dashboard_data("operational")
        print(f"\nOperational Dashboard:\n{json.dumps(dashboard_data, indent=2, default=str)}")
        
        # Get alert summary
        alert_summary = await dashboard_manager.get_alert_summary()
        print(f"\nAlert Summary:\n{json.dumps(alert_summary, indent=2, default=str)}")
        
        # Get available dashboards
        dashboards = dashboard_manager.get_available_dashboards()
        print(f"\nAvailable Dashboards:\n{json.dumps(dashboards, indent=2)}")
        
        # Stop monitoring
        await dashboard_manager.stop_monitoring()
    
    # Run test
    asyncio.run(test_dashboard_manager())