#!/bin/bash
# Kubernetes deployment script for MLOps Digital Twin Platform
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="digital-twin"
K8S_DIR="k8s"
DOCKER_IMAGE="digital-twin:latest"
DOCKER_REGISTRY=""  # Set if using a registry
KUBECTL_TIMEOUT="300s"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_dependencies() {
    log_info "Checking dependencies..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed. Please install kubectl first."
        exit 1
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Kubernetes cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster. Please check your kubeconfig."
        exit 1
    fi
    
    log_success "All dependencies are available"
}

check_ingress_controller() {
    log_info "Checking for ingress controller..."
    
    if kubectl get pods -n ingress-nginx | grep -q nginx-controller; then
        log_success "Nginx ingress controller found"
    elif kubectl get pods -A | grep -q ingress; then
        log_warning "Ingress controller found but may not be nginx"
    else
        log_warning "No ingress controller found. You may need to install one."
        log_info "To install nginx ingress controller, run:"
        log_info "kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.8.1/deploy/static/provider/cloud/deploy.yaml"
    fi
}

build_and_push_image() {
    log_info "Building Docker image..."
    
    # Build the image
    docker build -f Dockerfile.production -t "$DOCKER_IMAGE" .
    
    if [ -n "$DOCKER_REGISTRY" ]; then
        log_info "Pushing image to registry..."
        docker tag "$DOCKER_IMAGE" "$DOCKER_REGISTRY/$DOCKER_IMAGE"
        docker push "$DOCKER_REGISTRY/$DOCKER_IMAGE"
    else
        log_info "No registry configured. Using local image."
    fi
    
    log_success "Docker image ready: $DOCKER_IMAGE"
}

create_namespace() {
    log_info "Creating namespace..."
    
    if kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_info "Namespace $NAMESPACE already exists"
    else
        kubectl apply -f "$K8S_DIR/digital-twin-namespace.yaml"
        log_success "Namespace $NAMESPACE created"
    fi
}

generate_secrets() {
    log_info "Generating secrets..."
    
    # Check if secrets already exist
    if kubectl get secret digital-twin-secrets -n "$NAMESPACE" &> /dev/null; then
        log_warning "Secrets already exist. Skipping generation."
        return
    fi
    
    # Generate random passwords
    POSTGRES_PASSWORD=$(openssl rand -base64 32)
    INFLUXDB_PASSWORD=$(openssl rand -base64 32)
    INFLUXDB_TOKEN=$(openssl rand -base64 32)
    MINIO_ACCESS_KEY="minioadmin"
    MINIO_SECRET_KEY=$(openssl rand -base64 32)
    JWT_SECRET=$(openssl rand -base64 32)
    ENCRYPTION_KEY=$(openssl rand -base64 32)
    GRAFANA_PASSWORD=$(openssl rand -base64 16)
    
    # Create secret
    kubectl create secret generic digital-twin-secrets \
        --namespace="$NAMESPACE" \
        --from-literal=postgres-password="$POSTGRES_PASSWORD" \
        --from-literal=database-url="*******************************************************************/digital_twin" \
        --from-literal=influxdb-token="$INFLUXDB_TOKEN" \
        --from-literal=influxdb-password="$INFLUXDB_PASSWORD" \
        --from-literal=minio-access-key="$MINIO_ACCESS_KEY" \
        --from-literal=minio-secret-key="$MINIO_SECRET_KEY" \
        --from-literal=openweather-api-key="your-openweather-api-key-here" \
        --from-literal=jwt-secret-key="$JWT_SECRET" \
        --from-literal=encryption-key="$ENCRYPTION_KEY" \
        --from-literal=grafana-admin-password="$GRAFANA_PASSWORD"
    
    log_success "Secrets generated and created"
    log_warning "Save these credentials securely:"
    echo "  Postgres Password: $POSTGRES_PASSWORD"
    echo "  InfluxDB Password: $INFLUXDB_PASSWORD"
    echo "  InfluxDB Token: $INFLUXDB_TOKEN"
    echo "  MinIO Secret Key: $MINIO_SECRET_KEY"
    echo "  Grafana Password: $GRAFANA_PASSWORD"
}

deploy_configmaps() {
    log_info "Deploying ConfigMaps..."
    kubectl apply -f "$K8S_DIR/digital-twin-configmap.yaml"
    log_success "ConfigMaps deployed"
}

deploy_rbac() {
    log_info "Deploying RBAC resources..."
    
    # Extract RBAC from secrets file
    kubectl apply -f "$K8S_DIR/digital-twin-secrets.yaml" --dry-run=client -o yaml | \
    kubectl apply -f -
    
    log_success "RBAC resources deployed"
}

deploy_infrastructure() {
    log_info "Deploying infrastructure services..."
    kubectl apply -f "$K8S_DIR/digital-twin-infrastructure.yaml"
    
    log_info "Waiting for infrastructure services to be ready..."
    kubectl wait --for=condition=ready pod -l app=digital-twin-postgres -n "$NAMESPACE" --timeout="$KUBECTL_TIMEOUT" || true
    kubectl wait --for=condition=ready pod -l app=digital-twin-redis -n "$NAMESPACE" --timeout="$KUBECTL_TIMEOUT" || true
    kubectl wait --for=condition=ready pod -l app=digital-twin-influxdb -n "$NAMESPACE" --timeout="$KUBECTL_TIMEOUT" || true
    kubectl wait --for=condition=ready pod -l app=digital-twin-minio -n "$NAMESPACE" --timeout="$KUBECTL_TIMEOUT" || true
    
    log_success "Infrastructure services deployed"
}

deploy_application() {
    log_info "Deploying application services..."
    
    # Update image in deployment if registry is set
    if [ -n "$DOCKER_REGISTRY" ]; then
        sed -i "s|image: digital-twin:latest|image: $DOCKER_REGISTRY/$DOCKER_IMAGE|g" "$K8S_DIR/digital-twin-app-deployment.yaml"
    fi
    
    kubectl apply -f "$K8S_DIR/digital-twin-app-deployment.yaml"
    
    log_info "Waiting for application to be ready..."
    kubectl wait --for=condition=available deployment/digital-twin-app -n "$NAMESPACE" --timeout="$KUBECTL_TIMEOUT"
    
    log_success "Application services deployed"
}

deploy_ingress() {
    log_info "Deploying ingress..."
    kubectl apply -f "$K8S_DIR/digital-twin-ingress.yaml"
    log_success "Ingress deployed"
}

check_deployment() {
    log_info "Checking deployment status..."
    
    # Check pods
    log_info "Pod status:"
    kubectl get pods -n "$NAMESPACE" -o wide
    
    # Check services
    log_info "Service status:"
    kubectl get services -n "$NAMESPACE"
    
    # Check ingress
    log_info "Ingress status:"
    kubectl get ingress -n "$NAMESPACE"
    
    # Check application health
    log_info "Checking application health..."
    
    # Port forward for health check
    kubectl port-forward service/digital-twin-app 8000:8000 -n "$NAMESPACE" &
    PF_PID=$!
    sleep 10
    
    if curl -f http://localhost:8000/health &> /dev/null; then
        log_success "Application is healthy"
    else
        log_error "Application health check failed"
    fi
    
    # Clean up port forward
    kill $PF_PID &> /dev/null || true
}

show_endpoints() {
    log_success "Deployment completed successfully!"
    echo ""
    echo "Available endpoints (update /etc/hosts or use port-forward):"
    echo "  Application:        https://digital-twin.local"
    echo "  API:                https://api.digital-twin.local"
    echo "  Dashboard:          https://dashboard.digital-twin.local"
    echo "  Grafana:            https://grafana.digital-twin.local"
    echo "  Prometheus:         https://prometheus.digital-twin.local"
    echo ""
    echo "Port-forward commands for local access:"
    echo "  kubectl port-forward service/digital-twin-app 8000:8000 -n $NAMESPACE"
    echo "  kubectl port-forward service/digital-twin-dashboard 8501:8501 -n $NAMESPACE"
    echo "  kubectl port-forward service/digital-twin-grafana 3000:3000 -n $NAMESPACE"
    echo ""
    echo "Useful commands:"
    echo "  kubectl get pods -n $NAMESPACE"
    echo "  kubectl logs -f deployment/digital-twin-app -n $NAMESPACE"
    echo "  kubectl describe pod <pod-name> -n $NAMESPACE"
    echo ""
    
    # Show ingress IP if available
    INGRESS_IP=$(kubectl get ingress digital-twin-ingress -n "$NAMESPACE" -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
    if [ -n "$INGRESS_IP" ]; then
        echo "Ingress IP: $INGRESS_IP"
        echo "Add to /etc/hosts:"
        echo "$INGRESS_IP digital-twin.local api.digital-twin.local dashboard.digital-twin.local"
    fi
}

cleanup() {
    log_info "Cleaning up resources..."
    kubectl delete namespace "$NAMESPACE" --ignore-not-found=true
    log_success "Resources cleaned up"
}

rollback() {
    log_info "Rolling back deployment..."
    kubectl rollout undo deployment/digital-twin-app -n "$NAMESPACE"
    kubectl rollout status deployment/digital-twin-app -n "$NAMESPACE"
    log_success "Rollback completed"
}

# Main execution
main() {
    case "${1:-deploy}" in
        "deploy")
            log_info "Starting Kubernetes deployment of MLOps Digital Twin Platform..."
            check_dependencies
            check_ingress_controller
            build_and_push_image
            create_namespace
            generate_secrets
            deploy_configmaps
            deploy_rbac
            deploy_infrastructure
            deploy_application
            deploy_ingress
            check_deployment
            show_endpoints
            ;;
        "build")
            log_info "Building and pushing Docker image..."
            check_dependencies
            build_and_push_image
            ;;
        "infrastructure")
            log_info "Deploying infrastructure only..."
            check_dependencies
            create_namespace
            generate_secrets
            deploy_configmaps
            deploy_rbac
            deploy_infrastructure
            ;;
        "app")
            log_info "Deploying application only..."
            check_dependencies
            deploy_application
            deploy_ingress
            ;;
        "status")
            log_info "Checking deployment status..."
            check_deployment
            ;;
        "logs")
            log_info "Showing application logs..."
            kubectl logs -f deployment/digital-twin-app -n "$NAMESPACE" --max-log-requests=10
            ;;
        "restart")
            log_info "Restarting application..."
            kubectl rollout restart deployment/digital-twin-app -n "$NAMESPACE"
            kubectl rollout status deployment/digital-twin-app -n "$NAMESPACE"
            log_success "Application restarted"
            ;;
        "scale")
            replicas="${2:-3}"
            log_info "Scaling application to $replicas replicas..."
            kubectl scale deployment/digital-twin-app --replicas="$replicas" -n "$NAMESPACE"
            kubectl rollout status deployment/digital-twin-app -n "$NAMESPACE"
            log_success "Application scaled to $replicas replicas"
            ;;
        "rollback")
            rollback
            ;;
        "cleanup")
            cleanup
            ;;
        "port-forward")
            service="${2:-digital-twin-app}"
            port="${3:-8000}"
            log_info "Port forwarding $service on port $port..."
            kubectl port-forward "service/$service" "$port:$port" -n "$NAMESPACE"
            ;;
        *)
            echo "Usage: $0 {deploy|build|infrastructure|app|status|logs|restart|scale|rollback|cleanup|port-forward}"
            echo ""
            echo "Commands:"
            echo "  deploy         - Full deployment (default)"
            echo "  build          - Build and push Docker image only"
            echo "  infrastructure - Deploy infrastructure services only"
            echo "  app            - Deploy application services only"
            echo "  status         - Check deployment status"
            echo "  logs           - Show application logs"
            echo "  restart        - Restart application"
            echo "  scale [N]      - Scale application to N replicas"
            echo "  rollback       - Rollback to previous version"
            echo "  cleanup        - Remove all resources"
            echo "  port-forward [service] [port] - Port forward to service"
            exit 1
            ;;
    esac
}

# Execute main function with all arguments
main "$@"