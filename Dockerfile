# Multi-stage Docker build for Industrial Tank Control System
FROM nvidia/cuda:11.8-devel-ubuntu20.04 as base

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PATH="/opt/conda/bin:$PATH"
ENV PYTHONPATH="/app:$PYTHONPATH"

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    pkg-config \
    cmake \
    libssl-dev \
    libffi-dev \
    libxml2-dev \
    libxslt1-dev \
    zlib1g-dev \
    libjpeg-dev \
    libpng-dev \
    libfreetype6-dev \
    libblas-dev \
    liblapack-dev \
    libatlas-base-dev \
    gfortran \
    redis-tools \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Install Miniconda
RUN wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O /tmp/miniconda.sh && \
    bash /tmp/miniconda.sh -b -p /opt/conda && \
    rm /tmp/miniconda.sh && \
    /opt/conda/bin/conda clean -ya

# Create conda environment
RUN conda create -n tank-system python=3.11 -y
ENV PATH="/opt/conda/envs/tank-system/bin:$PATH"
RUN echo "source activate tank-system" > ~/.bashrc

# Install PyTorch with CUDA support
RUN pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Development stage
FROM base as development

WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Install additional development tools
RUN pip install --no-cache-dir \
    black \
    isort \
    flake8 \
    mypy \
    pytest \
    pytest-asyncio \
    pytest-cov \
    jupyter \
    jupyterlab \
    ipywidgets

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/checkpoints

# Set proper permissions
RUN chmod +x /app/scripts/*.sh 2>/dev/null || true

# Expose ports
EXPOSE 8000 8001 8888

# Default command for development
CMD ["python", "-m", "src.main"]

# Production stage
FROM base as production

WORKDIR /app

# Create non-root user
RUN groupadd -r dtuser && useradd -r -g dtuser dtuser

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY --chown=dtuser:dtuser . .

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/checkpoints && \
    chown -R dtuser:dtuser /app

# Switch to non-root user
USER dtuser

# Expose ports
EXPOSE 8000 8001

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Production command - Main Tank System
CMD ["python", "tank_system_main.py"]

# Microservice-specific stages

# Tank Management Service
FROM base as tank-management-service

WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy only required modules for tank management
COPY digital_twin/core/ digital_twin/core/
COPY digital_twin/scada/ digital_twin/scada/
COPY src/api/tank_endpoints.py src/api/
COPY config/ config/

RUN groupadd -r tankuser && useradd -r -g tankuser tankuser
RUN chown -R tankuser:tankuser /app
USER tankuser

EXPOSE 8080
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

CMD ["uvicorn", "src.api.tank_endpoints:app", "--host", "0.0.0.0", "--port", "8080"]

# Data Ingestion Service
FROM base as data-ingestion-service

WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt \
    aiokafka==0.8.10 \
    aioredis==2.0.1 \
    influxdb-client==1.37.0

COPY digital_twin/scada/ digital_twin/scada/
COPY services/data-ingestion/ services/data-ingestion/

RUN groupadd -r datauser && useradd -r -g datauser datauser
RUN chown -R datauser:datauser /app
USER datauser

EXPOSE 8081
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8081/health || exit 1

CMD ["python", "services/data-ingestion/ingestion_service.py"]

# Control Service
FROM base as control-service

WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt \
    numpy==1.24.3 \
    scipy==1.11.1 \
    control==0.9.4

COPY digital_twin/core/ digital_twin/core/
COPY services/control/ services/control/

RUN groupadd -r controluser && useradd -r -g controluser controluser
RUN chown -R controluser:controluser /app
USER controluser

EXPOSE 8082
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8082/health || exit 1

CMD ["python", "services/control/control_service.py"]

# Event Bus Service
FROM base as event-bus-service

WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt \
    aiokafka==0.8.10 \
    aioredis==2.0.1

COPY services/event-bus/ services/event-bus/

RUN groupadd -r eventuser && useradd -r -g eventuser eventuser
RUN chown -R eventuser:eventuser /app
USER eventuser

EXPOSE 8083
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8083/health || exit 1

CMD ["python", "services/event-bus/event_bus.py"]

# API Gateway Service
FROM base as api-gateway-service

WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt \
    fastapi==0.104.1 \
    uvicorn==0.24.0 \
    httpx==0.25.0

COPY services/api-gateway/ services/api-gateway/

RUN groupadd -r gatewayuser && useradd -r -g gatewayuser gatewayuser
RUN chown -R gatewayuser:gatewayuser /app
USER gatewayuser

EXPOSE 8000
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

CMD ["uvicorn", "services.api-gateway.gateway:app", "--host", "0.0.0.0", "--port", "8000"]

# ML/AI Service
FROM base as ml-service

WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt \
    scikit-learn==1.3.0 \
    tensorflow==2.13.0 \
    mlflow==2.6.0

COPY services/ml/ services/ml/
COPY digital_twin/simulation/ digital_twin/simulation/

RUN groupadd -r mluser && useradd -r -g mluser mluser
RUN chown -R mluser:mluser /app
USER mluser

EXPOSE 8084
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8084/health || exit 1

CMD ["python", "services/ml/ml_service.py"]

# Monitoring Service
FROM base as monitoring-service

WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt \
    prometheus-client==0.17.1 \
    grafana-api==1.0.3

COPY services/monitoring/ services/monitoring/

RUN groupadd -r monitoruser && useradd -r -g monitoruser monitoruser
RUN chown -R monitoruser:monitoruser /app
USER monitoruser

EXPOSE 8085
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8085/health || exit 1

CMD ["python", "services/monitoring/monitoring_service.py"]

# Testing stage
FROM development as testing

# Install testing dependencies
RUN pip install --no-cache-dir \
    pytest-xdist \
    pytest-benchmark \
    coverage \
    bandit \
    safety

# Run tests
COPY tests/ tests/
RUN python -m pytest tests/ -v --cov=src --cov-report=html --cov-report=term

# Security scanning
RUN bandit -r src/ -f json -o security_report.json || true
RUN safety check --json --output safety_report.json || true

CMD ["python", "-m", "pytest", "tests/", "-v"]