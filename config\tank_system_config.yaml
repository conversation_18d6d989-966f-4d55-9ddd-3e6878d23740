# Tank System Configuration
# Complete configuration for industrial tank control system
# Based on the HMI screenshot showing multiple tanks with control parameters

# System Information
system:
  name: "Industrial Tank Control System"
  version: "1.0.0"
  description: "Multi-tank heating and control system for industrial applications"
  location: "Plant A - Building 1"
  operator: "Control Room"
  
# Tank Configurations
tanks:
  tank_001:
    name: "Primary Heating Tank 1"
    description: "Main heating tank for material processing"
    tank_type: "heating"
    is_active: true
    
    # Physical Properties
    geometry:
      capacity: 50000.0  # liters
      diameter: 6.0      # meters
      height: 8.0        # meters
      insulation_thickness: 0.15  # meters
      material: "steel"
      design_pressure: 5.0  # bar
      design_temperature: 200.0  # celsius
    
    # Thermal Properties
    thermal_properties:
      thermal_conductivity: 0.5    # W/m/K
      specific_heat: 920.0         # J/kg/K
      density: 2400.0              # kg/m³
      convection_coefficient: 25.0  # W/m²/K
      emissivity: 0.9
    
    # Fluid Properties
    fluid_properties:
      density: 1000.0      # kg/m³
      viscosity: 0.001     # Pa·s
      specific_heat: 4186.0 # J/kg/K
    
    # Control Parameters
    control_parameters:
      temperature_setpoint: 160.0      # celsius
      temperature_deadband: 2.0        # celsius
      pressure_setpoint: 1.5           # bar
      pressure_deadband: 0.1           # bar
      level_setpoint: 75.0             # percent
      level_deadband: 5.0              # percent
      heating_power_limit: 100.0       # percent
      control_mode: "auto"
      auto_start_enabled: false
      safety_interlock_enabled: true
    
    # Sensors
    sensors:
      - sensor_id: "TT_001_01"
        sensor_type: "temperature"
        description: "Zone 1 Temperature"
        unit: "°C"
        min_value: 0.0
        max_value: 300.0
        accuracy: 1.0
        location: "zone_1"
        node_id: "ns=2;s=Tank001.Zone1.Temperature"
        alarm_high: 180.0
        alarm_low: 140.0
        
      - sensor_id: "TT_001_02"
        sensor_type: "temperature"
        description: "Zone 2 Temperature"
        unit: "°C"
        min_value: 0.0
        max_value: 300.0
        accuracy: 1.0
        location: "zone_2"
        node_id: "ns=2;s=Tank001.Zone2.Temperature"
        alarm_high: 180.0
        alarm_low: 140.0
        
      - sensor_id: "TT_001_03"
        sensor_type: "temperature"
        description: "Zone 3 Temperature"
        unit: "°C"
        min_value: 0.0
        max_value: 300.0
        accuracy: 1.0
        location: "zone_3"
        node_id: "ns=2;s=Tank001.Zone3.Temperature"
        alarm_high: 180.0
        alarm_low: 140.0
        
      - sensor_id: "TT_001_04"
        sensor_type: "temperature"
        description: "Zone 4 Temperature"
        unit: "°C"
        min_value: 0.0
        max_value: 300.0
        accuracy: 1.0
        location: "zone_4"
        node_id: "ns=2;s=Tank001.Zone4.Temperature"
        alarm_high: 180.0
        alarm_low: 140.0
        
      - sensor_id: "PT_001_01"
        sensor_type: "pressure"
        description: "Tank Pressure"
        unit: "bar"
        min_value: 0.0
        max_value: 10.0
        accuracy: 0.1
        location: "top"
        node_id: "ns=2;s=Tank001.Pressure"
        alarm_high: 3.0
        alarm_low: 0.5
        
      - sensor_id: "LT_001_01"
        sensor_type: "level"
        description: "Tank Level"
        unit: "%"
        min_value: 0.0
        max_value: 100.0
        accuracy: 1.0
        location: "side"
        node_id: "ns=2;s=Tank001.Level"
        alarm_high: 90.0
        alarm_low: 10.0
    
    # Actuators
    actuators:
      - actuator_id: "HT_001_01"
        actuator_type: "heater"
        description: "Zone 1 Heater"
        unit: "%"
        min_value: 0.0
        max_value: 100.0
        location: "zone_1"
        node_id: "ns=2;s=Tank001.Zone1.Heater"
        is_enabled: true
        
      - actuator_id: "HT_001_02"
        actuator_type: "heater"
        description: "Zone 2 Heater"
        unit: "%"
        min_value: 0.0
        max_value: 100.0
        location: "zone_2"
        node_id: "ns=2;s=Tank001.Zone2.Heater"
        is_enabled: true
        
      - actuator_id: "HT_001_03"
        actuator_type: "heater"
        description: "Zone 3 Heater"
        unit: "%"
        min_value: 0.0
        max_value: 100.0
        location: "zone_3"
        node_id: "ns=2;s=Tank001.Zone3.Heater"
        is_enabled: true
        
      - actuator_id: "HT_001_04"
        actuator_type: "heater"
        description: "Zone 4 Heater"
        unit: "%"
        min_value: 0.0
        max_value: 100.0
        location: "zone_4"
        node_id: "ns=2;s=Tank001.Zone4.Heater"
        is_enabled: true
        
      - actuator_id: "PU_001_01"
        actuator_type: "pump"
        description: "Circulation Pump"
        unit: "%"
        min_value: 0.0
        max_value: 100.0
        location: "inlet"
        node_id: "ns=2;s=Tank001.Pump"
        is_enabled: true
        
      - actuator_id: "VL_001_01"
        actuator_type: "valve"
        description: "Outlet Valve"
        unit: "%"
        min_value: 0.0
        max_value: 100.0
        location: "outlet"
        node_id: "ns=2;s=Tank001.OutletValve"
        is_enabled: true
    
    # Alarms
    alarms:
      - alarm_id: "ALM_001_TEMP_HIGH"
        description: "High Temperature Alarm"
        severity: "high"
        condition: "temperature > 180"
        enabled: true
        
      - alarm_id: "ALM_001_TEMP_LOW"
        description: "Low Temperature Alarm"
        severity: "medium"
        condition: "temperature < 140"
        enabled: true
        
      - alarm_id: "ALM_001_PRESS_HIGH"
        description: "High Pressure Alarm"
        severity: "critical"
        condition: "pressure > 3.0"
        enabled: true
        
      - alarm_id: "ALM_001_LEVEL_LOW"
        description: "Low Level Alarm"
        severity: "medium"
        condition: "level < 10"
        enabled: true

  tank_002:
    name: "Secondary Heating Tank 2"
    description: "Secondary heating tank for material processing"
    tank_type: "heating"
    is_active: true
    
    # Similar configuration to tank_001 but with different IDs
    geometry:
      capacity: 30000.0
      diameter: 5.0
      height: 7.0
      insulation_thickness: 0.15
      material: "steel"
      design_pressure: 5.0
      design_temperature: 200.0
    
    thermal_properties:
      thermal_conductivity: 0.5
      specific_heat: 920.0
      density: 2400.0
      convection_coefficient: 25.0
      emissivity: 0.9
    
    fluid_properties:
      density: 1000.0
      viscosity: 0.001
      specific_heat: 4186.0
    
    control_parameters:
      temperature_setpoint: 155.0
      temperature_deadband: 2.0
      pressure_setpoint: 1.2
      pressure_deadband: 0.1
      level_setpoint: 70.0
      level_deadband: 5.0
      heating_power_limit: 100.0
      control_mode: "auto"
      auto_start_enabled: false
      safety_interlock_enabled: true
    
    # Sensors and actuators similar to tank_001 but with different node IDs
    sensors:
      - sensor_id: "TT_002_01"
        sensor_type: "temperature"
        description: "Zone 1 Temperature"
        unit: "°C"
        min_value: 0.0
        max_value: 300.0
        accuracy: 1.0
        location: "zone_1"
        node_id: "ns=2;s=Tank002.Zone1.Temperature"
        alarm_high: 175.0
        alarm_low: 135.0
    
    actuators:
      - actuator_id: "HT_002_01"
        actuator_type: "heater"
        description: "Zone 1 Heater"
        unit: "%"
        min_value: 0.0
        max_value: 100.0
        location: "zone_1"
        node_id: "ns=2;s=Tank002.Zone1.Heater"
        is_enabled: true

# SCADA Configuration
scada:
  opc_ua:
    enabled: true
    endpoint_url: "opc.tcp://*************:4840"
    username: "operator"
    password: "password123"
    security_policy: "Basic256Sha256"
    security_mode: "SignAndEncrypt"
    session_timeout: 60000
    connection_timeout: 10000
    keep_alive_interval: 5000
    
  modbus:
    enabled: false
    host: "*************"
    port: 502
    unit_id: 1
    timeout: 3.0
    
  historian:
    enabled: true
    type: "pi"  # PI, Wonderware, etc.
    connection_string: "Data Source=*************;Initial Catalog=PIArchive"

# Control System Configuration
control:
  # Global control parameters
  scan_rate: 1.0  # seconds
  data_retention: 168  # hours (1 week)
  enable_auto_control: true
  enable_data_logging: true
  
  # PID Controller Settings
  pid_controllers:
    default_temperature:
      kp: 2.0
      ki: 0.1
      kd: 0.05
      output_limits: [0.0, 100.0]
      enable_anti_windup: true
      
    default_pressure:
      kp: 1.5
      ki: 0.05
      kd: 0.02
      output_limits: [0.0, 100.0]
      
    default_level:
      kp: 1.0
      ki: 0.08
      kd: 0.01
      output_limits: [0.0, 100.0]
  
  # Safety Configuration
  safety:
    emergency_stop_enabled: true
    safety_interlocks:
      - name: "High Temperature Interlock"
        condition: "any_temperature > 200"
        action: "emergency_stop"
        enabled: true
        
      - name: "High Pressure Interlock"
        condition: "any_pressure > 4.0"
        action: "emergency_stop"
        enabled: true
        
      - name: "Low Level Interlock"
        condition: "any_level < 5"
        action: "stop_heating"
        enabled: true

# Physics Simulation Configuration
simulation:
  enabled: true
  real_time_factor: 1.0
  time_step: 1.0
  max_simulation_time: 86400.0  # 24 hours
  spatial_discretization: 50
  
  # Physics Models
  models:
    heat_transfer:
      enabled: true
      solver_type: "finite_difference"
      temporal_tolerance: 1e-6
      max_iterations: 1000
      
    fluid_dynamics:
      enabled: true
      reynolds_number: 10000
      prandtl_number: 7.0
      
    mixing:
      enabled: true
      mixing_time_constant: 60.0
      mixing_efficiency: 0.8

# API Configuration
api:
  host: "0.0.0.0"
  port: 8000
  title: "Tank Control System API"
  description: "REST API for industrial tank control and monitoring"
  version: "1.0.0"
  
  # CORS settings
  cors:
    allow_origins: ["*"]
    allow_methods: ["GET", "POST", "PUT", "DELETE"]
    allow_headers: ["*"]
  
  # Authentication
  authentication:
    enabled: false
    type: "bearer"  # bearer, basic, oauth2
    secret_key: "your-secret-key-here"

# Monitoring and Alerting
monitoring:
  # Prometheus metrics
  prometheus:
    enabled: true
    port: 8001
    metrics_path: "/metrics"
    
  # Grafana dashboard
  grafana:
    enabled: true
    url: "http://localhost:3000"
    username: "admin"
    password: "admin_password"
    
  # Logging configuration
  logging:
    level: "INFO"
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: "logs/tank_system.log"
    max_file_size: "10MB"
    backup_count: 5
    
  # Alerting
  alerting:
    enabled: true
    email:
      enabled: true
      smtp_server: "smtp.company.com"
      smtp_port: 587
      username: "<EMAIL>"
      password: "email_password"
      recipients: ["<EMAIL>", "<EMAIL>"]
      
    slack:
      enabled: false
      webhook_url: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
      channel: "#tank-alerts"

# Data Storage Configuration
data_storage:
  # Time series database
  influxdb:
    enabled: true
    url: "http://localhost:8086"
    database: "tank_system"
    username: "tank_user"
    password: "tank_password"
    retention_policy: "30d"
    
  # Object storage for files
  minio:
    enabled: true
    endpoint: "localhost:9000"
    access_key: "minioadmin"
    secret_key: "minioadmin"
    bucket: "tank-data"
    
  # Relational database for configuration
  postgres:
    enabled: true
    host: "localhost"
    port: 5432
    database: "tank_config"
    username: "postgres"
    password: "postgres"

# Deployment Configuration
deployment:
  environment: "production"  # development, staging, production
  
  # Docker configuration
  docker:
    registry: "your-registry.com"
    image_tag: "latest"
    replicas: 2
    
  # Kubernetes configuration
  kubernetes:
    namespace: "tank-system"
    service_type: "LoadBalancer"
    resources:
      requests:
        cpu: "500m"
        memory: "1Gi"
      limits:
        cpu: "2000m"
        memory: "4Gi"

# Feature Flags
features:
  enable_machine_learning: true
  enable_predictive_maintenance: true
  enable_energy_optimization: true
  enable_advanced_physics: true
  enable_digital_twin: true
  enable_real_time_optimization: false