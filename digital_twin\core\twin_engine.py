"""
Digital Twin Core Engine
Manages the lifecycle and state of digital twins for asphalt heating systems
"""

import asyncio
import json
import logging
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
import numpy as np
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class TwinState(Enum):
    INITIALIZING = "initializing"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPED = "stopped"
    ERROR = "error"
    SYNCHRONIZED = "synchronized"


@dataclass
class TwinConfiguration:
    """Configuration for a digital twin instance"""
    twin_id: str
    name: str
    description: str
    physical_asset_id: str
    model_version: str
    update_frequency: float = 1.0  # Hz
    synchronization_threshold: float = 0.1
    physics_parameters: Dict[str, Any] = field(default_factory=dict)
    scada_tags: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


@dataclass
class TwinStateData:
    """Current state data of a digital twin"""
    timestamp: datetime
    temperature_profile: np.ndarray
    heat_flux: np.ndarray
    control_inputs: Dict[str, float]
    measured_outputs: Dict[str, float]
    predicted_outputs: Dict[str, float]
    deviation_metrics: Dict[str, float]
    model_confidence: float
    simulation_step: int


class StateReconciler(ABC):
    """Abstract base class for state reconciliation algorithms"""
    
    @abstractmethod
    async def reconcile(self, 
                       twin_state: TwinStateData, 
                       physical_state: Dict[str, Any]) -> TwinStateData:
        """Reconcile twin state with physical measurements"""
        pass


class KalmanStateReconciler(StateReconciler):
    """Kalman filter-based state reconciliation"""
    
    def __init__(self, process_noise: float = 0.1, measurement_noise: float = 0.1):
        self.process_noise = process_noise
        self.measurement_noise = measurement_noise
        self.state_covariance = None
        self.initialized = False
    
    async def reconcile(self, 
                       twin_state: TwinStateData, 
                       physical_state: Dict[str, Any]) -> TwinStateData:
        """Apply Kalman filtering for state reconciliation"""
        if not self.initialized:
            self._initialize_filter(twin_state)
        
        # Prediction step
        predicted_state = self._predict(twin_state)
        
        # Update step with physical measurements
        updated_state = self._update(predicted_state, physical_state)
        
        return updated_state
    
    def _initialize_filter(self, initial_state: TwinStateData):
        """Initialize Kalman filter parameters"""
        state_size = len(initial_state.temperature_profile)
        self.state_covariance = np.eye(state_size) * 0.1
        self.initialized = True
    
    def _predict(self, state: TwinStateData) -> TwinStateData:
        """Prediction step of Kalman filter"""
        # Simple prediction model - can be replaced with physics model
        return state
    
    def _update(self, predicted_state: TwinStateData, 
                measurements: Dict[str, Any]) -> TwinStateData:
        """Update step of Kalman filter"""
        # Update state with measurements
        updated_state = predicted_state
        
        # Calculate deviation metrics
        deviations = {}
        for key, measured_value in measurements.items():
            if key in predicted_state.predicted_outputs:
                predicted_value = predicted_state.predicted_outputs[key]
                deviations[key] = abs(measured_value - predicted_value)
        
        updated_state.deviation_metrics = deviations
        updated_state.timestamp = datetime.now()
        
        return updated_state


class DigitalTwinEngine:
    """Core digital twin engine for managing twin lifecycle and operations"""
    
    def __init__(self, reconciler: StateReconciler = None):
        self.twins: Dict[str, 'DigitalTwin'] = {}
        self.reconciler = reconciler or KalmanStateReconciler()
        self.is_running = False
        self._update_tasks: Dict[str, asyncio.Task] = {}
        self._event_handlers: Dict[str, List[Callable]] = {}
        self._sync_tasks: Dict[str, asyncio.Task] = {}
        self._federation_enabled = False
        self._twin_registry = {}
        self._performance_metrics = {}
    
    async def create_twin(self, config: TwinConfiguration) -> 'DigitalTwin':
        """Create a new digital twin instance"""
        logger.info(f"Creating digital twin: {config.twin_id}")
        
        twin = DigitalTwin(config, self.reconciler)
        self.twins[config.twin_id] = twin
        
        # Emit twin created event
        await self._emit_event('twin_created', {'twin_id': config.twin_id})
        
        return twin
    
    async def start_twin(self, twin_id: str) -> bool:
        """Start a digital twin"""
        if twin_id not in self.twins:
            logger.error(f"Twin {twin_id} not found")
            return False
        
        twin = self.twins[twin_id]
        await twin.start()
        
        # Start update task
        self._update_tasks[twin_id] = asyncio.create_task(
            self._twin_update_loop(twin)
        )
        
        logger.info(f"Started digital twin: {twin_id}")
        return True
    
    async def stop_twin(self, twin_id: str) -> bool:
        """Stop a digital twin"""
        if twin_id not in self.twins:
            return False
        
        twin = self.twins[twin_id]
        await twin.stop()
        
        # Cancel update task
        if twin_id in self._update_tasks:
            self._update_tasks[twin_id].cancel()
            del self._update_tasks[twin_id]
        
        logger.info(f"Stopped digital twin: {twin_id}")
        return True
    
    async def update_twin_state(self, twin_id: str, 
                               physical_measurements: Dict[str, Any]) -> bool:
        """Update twin state with physical measurements"""
        if twin_id not in self.twins:
            return False
        
        twin = self.twins[twin_id]
        await twin.update_with_measurements(physical_measurements)
        
        return True
    
    async def get_twin_state(self, twin_id: str) -> Optional[TwinStateData]:
        """Get current state of a digital twin"""
        if twin_id not in self.twins:
            return None
        
        return self.twins[twin_id].current_state
    
    async def _twin_update_loop(self, twin: 'DigitalTwin'):
        """Main update loop for a digital twin"""
        while twin.state == TwinState.RUNNING:
            try:
                await twin.simulate_step()
                await asyncio.sleep(1.0 / twin.config.update_frequency)
            except Exception as e:
                logger.error(f"Error in twin update loop: {e}")
                await twin.set_state(TwinState.ERROR)
                break
    
    def register_event_handler(self, event_type: str, handler: Callable):
        """Register an event handler"""
        if event_type not in self._event_handlers:
            self._event_handlers[event_type] = []
        self._event_handlers[event_type].append(handler)
    
    async def _emit_event(self, event_type: str, data: Dict[str, Any]):
        """Emit an event to registered handlers"""
        if event_type in self._event_handlers:
            for handler in self._event_handlers[event_type]:
                try:
                    await handler(data)
                except Exception as e:
                    logger.error(f"Error in event handler: {e}")
    
    async def enable_federation(self, registry_endpoint: str = None):
        """Enable multi-site twin federation"""
        self._federation_enabled = True
        if registry_endpoint:
            self._twin_registry['endpoint'] = registry_endpoint
        logger.info("Twin federation enabled")
    
    async def sync_twins_with_federation(self):
        """Synchronize twins across federation"""
        if not self._federation_enabled:
            return
        
        for twin_id, twin in self.twins.items():
            if twin.state == TwinState.RUNNING:
                # Sync with federated twins
                await self._sync_federated_twin(twin_id, twin)
    
    async def _sync_federated_twin(self, twin_id: str, twin: 'DigitalTwin'):
        """Synchronize individual twin with federation"""
        try:
            # Placeholder for federation sync logic
            sync_data = {
                'twin_id': twin_id,
                'state': twin.current_state,
                'performance': self._performance_metrics.get(twin_id, {})
            }
            logger.debug(f"Syncing twin {twin_id} with federation")
        except Exception as e:
            logger.error(f"Error syncing twin {twin_id}: {e}")
    
    async def get_twin_performance_metrics(self, twin_id: str) -> Dict[str, Any]:
        """Get performance metrics for a twin"""
        if twin_id not in self._performance_metrics:
            self._performance_metrics[twin_id] = {
                'simulation_frequency': 0,
                'sync_latency': 0,
                'prediction_accuracy': 0,
                'energy_efficiency': 0,
                'last_updated': datetime.now()
            }
        return self._performance_metrics[twin_id]
    
    async def optimize_twin_performance(self, twin_id: str):
        """Optimize twin performance based on metrics"""
        metrics = await self.get_twin_performance_metrics(twin_id)
        
        if twin_id in self.twins:
            twin = self.twins[twin_id]
            
            # Adjust update frequency based on performance
            if metrics['sync_latency'] > 100:  # ms
                twin.config.update_frequency = max(0.5, twin.config.update_frequency * 0.8)
            elif metrics['sync_latency'] < 50:
                twin.config.update_frequency = min(10.0, twin.config.update_frequency * 1.2)
            
            logger.info(f"Optimized twin {twin_id} performance")


class DigitalTwin:
    """Individual digital twin instance"""
    
    def __init__(self, config: TwinConfiguration, reconciler: StateReconciler):
        self.config = config
        self.reconciler = reconciler
        self.state = TwinState.INITIALIZING
        self.current_state: Optional[TwinStateData] = None
        self.simulation_step = 0
        self._physics_model = None
        self._last_sync_time = datetime.now()
    
    async def start(self):
        """Start the digital twin"""
        await self._initialize_physics_model()
        await self._initialize_state()
        self.state = TwinState.RUNNING
        logger.info(f"Digital twin {self.config.twin_id} started")
    
    async def stop(self):
        """Stop the digital twin"""
        self.state = TwinState.STOPPED
        logger.info(f"Digital twin {self.config.twin_id} stopped")
    
    async def pause(self):
        """Pause the digital twin"""
        self.state = TwinState.PAUSED
        logger.info(f"Digital twin {self.config.twin_id} paused")
    
    async def resume(self):
        """Resume the digital twin"""
        self.state = TwinState.RUNNING
        logger.info(f"Digital twin {self.config.twin_id} resumed")
    
    async def set_state(self, new_state: TwinState):
        """Set twin state"""
        old_state = self.state
        self.state = new_state
        logger.info(f"Twin {self.config.twin_id} state changed: {old_state} -> {new_state}")
    
    async def simulate_step(self):
        """Perform one simulation step"""
        if self.state != TwinState.RUNNING:
            return
        
        # Run physics simulation
        await self._run_physics_simulation()
        
        # Update simulation step
        self.simulation_step += 1
        
        # Update timestamp
        if self.current_state:
            self.current_state.timestamp = datetime.now()
            self.current_state.simulation_step = self.simulation_step
    
    async def update_with_measurements(self, measurements: Dict[str, Any]):
        """Update twin state with physical measurements"""
        if not self.current_state:
            return
        
        # Apply state reconciliation
        updated_state = await self.reconciler.reconcile(
            self.current_state, measurements
        )
        
        self.current_state = updated_state
        self._last_sync_time = datetime.now()
        
        # Check synchronization status
        max_deviation = max(updated_state.deviation_metrics.values()) if updated_state.deviation_metrics else 0
        if max_deviation < self.config.synchronization_threshold:
            await self.set_state(TwinState.SYNCHRONIZED)
    
    async def _initialize_physics_model(self):
        """Initialize the physics model"""
        # Placeholder for physics model initialization
        logger.info(f"Initializing physics model for twin {self.config.twin_id}")
    
    async def _initialize_state(self):
        """Initialize twin state"""
        self.current_state = TwinStateData(
            timestamp=datetime.now(),
            temperature_profile=np.zeros(10),  # 10 temperature sensors
            heat_flux=np.zeros(5),  # 5 heat flux sensors
            control_inputs={},
            measured_outputs={},
            predicted_outputs={},
            deviation_metrics={},
            model_confidence=1.0,
            simulation_step=0
        )
    
    async def _run_physics_simulation(self):
        """Run physics simulation step"""
        if not self.current_state:
            return
        
        try:
            # Enhanced physics simulation with NVIDIA Modulus integration
            from ..simulation.physics_engine import PhysicsEngine
            
            if not self._physics_model:
                self._physics_model = PhysicsEngine(self.config.physics_parameters)
            
            # Run physics step
            simulation_results = await self._physics_model.simulate_step(
                current_state=self.current_state,
                control_inputs=self.current_state.control_inputs,
                ambient_conditions={
                    'temperature': self.current_state.measured_outputs.get('ambient_temp', 20.0),
                    'wind_speed': self.current_state.measured_outputs.get('wind_speed', 0.0)
                }
            )
            
            # Update current state with simulation results
            if simulation_results:
                self.current_state.temperature_profile = simulation_results.get('temperature_profile', self.current_state.temperature_profile)
                self.current_state.heat_flux = simulation_results.get('heat_flux', self.current_state.heat_flux)
                self.current_state.predicted_outputs = simulation_results.get('predicted_outputs', {})
                self.current_state.model_confidence = simulation_results.get('confidence', 1.0)
                
        except ImportError:
            # Fallback to simple physics model
            await self._run_simple_physics_simulation()
        except Exception as e:
            logger.error(f"Error in physics simulation: {e}")
            await self._run_simple_physics_simulation()
    
    async def _run_simple_physics_simulation(self):
        """Simplified physics simulation fallback"""
        if not self.current_state:
            return
        
        # Simple thermal dynamics simulation
        dt = 1.0 / self.config.update_frequency
        
        # Heat transfer simulation
        for i in range(len(self.current_state.temperature_profile)):
            current_temp = self.current_state.temperature_profile[i]
            target_temp = self.current_state.control_inputs.get(f'setpoint_{i}', 150.0)
            
            # Simple PID-like control
            error = target_temp - current_temp
            heating_power = self.current_state.control_inputs.get(f'power_{i}', 0.0)
            
            # Heat transfer coefficient (simplified)
            k = 0.1  # thermal conductivity
            heat_loss = k * (current_temp - self.current_state.measured_outputs.get('ambient_temp', 20.0))
            
            # Temperature update
            temp_change = (heating_power * 0.001 - heat_loss) * dt
            self.current_state.temperature_profile[i] = current_temp + temp_change
            
            # Update predicted outputs
            self.current_state.predicted_outputs[f'temp_{i}'] = self.current_state.temperature_profile[i]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert twin to dictionary representation"""
        return {
            'config': {
                'twin_id': self.config.twin_id,
                'name': self.config.name,
                'description': self.config.description,
                'physical_asset_id': self.config.physical_asset_id,
                'model_version': self.config.model_version,
                'update_frequency': self.config.update_frequency,
                'synchronization_threshold': self.config.synchronization_threshold,
                'created_at': self.config.created_at.isoformat(),
                'updated_at': self.config.updated_at.isoformat()
            },
            'state': self.state.value,
            'simulation_step': self.simulation_step,
            'last_sync_time': self._last_sync_time.isoformat()
        }