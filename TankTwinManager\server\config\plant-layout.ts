/**
 * Real Plant Layout Configuration
 * Based on actual asphalt plant facility layout (scale 1:20m)
 * Includes tank positioning, hot-oil circulation, and asphalt loading systems
 */

export interface TankPosition {
  id: number;
  name: string;
  position: [number, number, number]; // [x, y, z] coordinates
  capacity: number; // liters
  type: 'storage' | 'heating' | 'loading';
  hasHeatingCoil: boolean;
  coilTurns: number;
  connections: {
    hotOilInlet: string;
    hotOilOutlet: string;
    asphaltOutlet: string;
    sensors: string[];
  };
  // Enhanced heating coil configuration
  heatingCoilConfig: HeatingCoilConfiguration;
  // Enhanced thermal properties
  thermalProperties: TankThermalProperties;
  // Enhanced geometry
  geometry: TankGeometry;
  // Control parameters
  controlParameters: TankControlParameters;
}

export interface HeatingCoilConfiguration {
  coilId: string;
  type: '3-turn' | 'spiral' | 'serpentine';
  specifications: {
    turns: number;
    coilDiameter: number; // mm - diameter of the coil spiral
    pipeDiameter: number; // mm - diameter of the coil pipe
    totalLength: number; // m - total pipe length
    material: 'stainless_steel' | 'carbon_steel' | 'alloy_steel';
    wallThickness: number; // mm
    surfaceFinish: 'smooth' | 'enhanced' | 'finned';
  };
  positioning: {
    elevationFromBottom: number; // m
    centerOffset: [number, number]; // [x, y] offset from tank center
    supportType: 'suspended' | 'bottom_mounted' | 'side_mounted';
    accessPoints: string[];
  };
  thermalCharacteristics: {
    heatTransferArea: number; // m²
    heatTransferCoefficient: number; // W/m²K
    foulingFactor: number; // m²K/W
    thermalEfficiency: number; // %
    maxHeatTransferRate: number; // kW
  };
  fluidDynamics: {
    maxFlowRate: number; // L/min
    minFlowRate: number; // L/min
    pressureDrop: number; // bar at nominal flow
    reynoldsNumber: number;
    prandtlNumber: number;
  };
  operationalLimits: {
    maxTemperature: number; // °C
    maxPressure: number; // bar
    maxThermalStress: number; // MPa
    fatigueLifeCycles: number;
  };
}

export interface TankThermalProperties {
  fluidProperties: {
    density: number; // kg/m³
    specificHeat: number; // J/kg·K
    thermalConductivity: number; // W/m·K
    viscosity: number; // Pa·s
    expansionCoefficient: number; // 1/K
  };
  tankMaterial: {
    density: number; // kg/m³
    specificHeat: number; // J/kg·K
    thermalConductivity: number; // W/m·K
    thermalDiffusivity: number; // m²/s
  };
  insulation: {
    type: 'mineral_wool' | 'polyurethane' | 'ceramic' | 'vacuum';
    thickness: number; // mm
    thermalConductivity: number; // W/m·K
    temperatureRating: number; // °C
    weatherResistant: boolean;
  };
  heatLoss: {
    ambientTemperature: number; // °C
    windSpeed: number; // m/s
    convectionCoefficient: number; // W/m²·K
    radiationEmissivity: number;
    totalHeatLossCoefficient: number; // W/m²·K
  };
}

export interface TankGeometry {
  shape: 'cylindrical' | 'rectangular' | 'spherical';
  dimensions: {
    diameter?: number; // m (for cylindrical)
    height: number; // m
    length?: number; // m (for rectangular)
    width?: number; // m (for rectangular)
    wallThickness: number; // mm
  };
  volumes: {
    totalVolume: number; // L
    workingVolume: number; // L
    deadVolume: number; // L
    vaporSpace: number; // L
  };
  surfaces: {
    totalSurfaceArea: number; // m²
    heatedSurfaceArea: number; // m²
    insulatedSurfaceArea: number; // m²
  };
  structural: {
    designPressure: number; // bar
    designTemperature: number; // °C
    materialGrade: string;
    corrosionAllowance: number; // mm
    windLoadRating: number; // kPa
    seismicRating: string;
  };
}

export interface TankControlParameters {
  temperature: {
    setpoint: number; // °C
    deadband: number; // °C
    rampRate: number; // °C/min
    maxRampRate: number; // °C/min
    alarmLimits: {
      highHigh: number; // °C
      high: number; // °C
      low: number; // °C
      lowLow: number; // °C
    };
  };
  level: {
    setpoint: number; // %
    deadband: number; // %
    alarmLimits: {
      highHigh: number; // %
      high: number; // %
      low: number; // %
      lowLow: number; // %
    };
  };
  pressure: {
    setpoint: number; // bar
    deadband: number; // bar
    alarmLimits: {
      highHigh: number; // bar
      high: number; // bar
      low: number; // bar
      lowLow: number; // bar
    };
  };
  heating: {
    pidParameters: {
      kp: number; // Proportional gain
      ki: number; // Integral gain
      kd: number; // Derivative gain
      outputLimits: [number, number]; // [min, max] %
    };
    heatingStrategy: 'continuous' | 'batch' | 'demand' | 'scheduled';
    energyOptimization: boolean;
    loadFollowing: boolean;
  };
}

export interface HotOilSystem {
  boiler: {
    position: [number, number, number];
    capacity: number; // kW heating capacity
    fuelType: 'gas' | 'oil' | 'electric';
    maxTemperature: number; // °C
    efficiency: number; // %
  };
  circulation: {
    mainSupplyLine: PipeSegment[];
    mainReturnLine: PipeSegment[];
    tankConnections: TankConnection[];
    pumps: PumpConfiguration[];
  };
}

export interface AsphaltLoadingSystem {
  loadingStations: LoadingStation[];
  distributionLines: PipeSegment[];
  controlSystems: ControlSystem[];
}

export interface PipeSegment {
  id: string;
  startPosition: [number, number, number];
  endPosition: [number, number, number];
  diameter: number; // mm
  material: 'steel' | 'stainless' | 'insulated';
  insulated: boolean;
  flowDirection: 'bidirectional' | 'supply' | 'return';
  maxPressure: number; // bar
  maxTemperature: number; // °C
}

export interface TankConnection {
  tankId: number;
  coilConfiguration: {
    turns: number;
    diameter: number; // mm
    length: number; // m
    heatTransferCoefficient: number; // W/m²K
  };
  inletPipe: string;
  outletPipe: string;
}

export interface LoadingStation {
  id: string;
  position: [number, number, number];
  loadingArms: number;
  maxFlowRate: number; // L/min
  truckCapacity: number; // L
  automationLevel: 'manual' | 'semi-auto' | 'full-auto';
  connectedTanks: number[];
}

export interface PumpConfiguration {
  id: string;
  position: [number, number, number];
  type: 'centrifugal' | 'positive-displacement';
  maxFlowRate: number; // L/min
  maxPressure: number; // bar
  powerRating: number; // kW
  efficiency: number; // %
}

export interface ControlSystem {
  id: string;
  type: 'temperature' | 'flow' | 'level' | 'pressure';
  controllerType: 'PID' | 'ON_OFF' | 'MODULATING';
  setpoint: number;
  deadband: number;
  outputRange: [number, number];
}

// Real plant layout configuration based on provided image
export const PLANT_LAYOUT_CONFIG = {
  // Tank positions matching real facility layout (scale 1:20m)
  tanks: [
    {
      id: 1,
      name: 'ASP-01',
      position: [-6, 0, -8] as [number, number, number],
      capacity: 50000,
      type: 'storage' as const,
      hasHeatingCoil: true,
      coilTurns: 3,
      connections: {
        hotOilInlet: 'HO-SUPPLY-01',
        hotOilOutlet: 'HO-RETURN-01',
        asphaltOutlet: 'ASP-OUT-01',
        sensors: ['TT-01', 'LT-01', 'PT-01']
      },
      heatingCoilConfig: {
        coilId: 'HC-ASP-01',
        type: '3-turn',
        specifications: {
          turns: 3,
          coilDiameter: 1800, // mm (1.8m diameter)
          pipeDiameter: 50, // mm (2" pipe)
          totalLength: 17, // m (3 turns × π × 1.8m)
          material: 'stainless_steel',
          wallThickness: 3, // mm
          surfaceFinish: 'smooth'
        },
        positioning: {
          elevationFromBottom: 0.5, // m
          centerOffset: [0, 0],
          supportType: 'bottom_mounted',
          accessPoints: ['AP-HC-01-INLET', 'AP-HC-01-OUTLET', 'AP-HC-01-DRAIN']
        },
        thermalCharacteristics: {
          heatTransferArea: 2.67, // m² (π × 0.05 × 17)
          heatTransferCoefficient: 500, // W/m²K
          foulingFactor: 0.0002, // m²K/W
          thermalEfficiency: 85, // %
          maxHeatTransferRate: 75 // kW
        },
        fluidDynamics: {
          maxFlowRate: 200, // L/min
          minFlowRate: 20, // L/min
          pressureDrop: 0.5, // bar at nominal flow
          reynoldsNumber: 15000,
          prandtlNumber: 7.5
        },
        operationalLimits: {
          maxTemperature: 320, // °C
          maxPressure: 10, // bar
          maxThermalStress: 150, // MPa
          fatigueLifeCycles: 100000
        }
      },
      thermalProperties: {
        fluidProperties: {
          density: 1000, // kg/m³ (asphalt)
          specificHeat: 2100, // J/kg·K
          thermalConductivity: 0.15, // W/m·K
          viscosity: 0.5, // Pa·s at operating temperature
          expansionCoefficient: 0.0007 // 1/K
        },
        tankMaterial: {
          density: 7850, // kg/m³ (steel)
          specificHeat: 460, // J/kg·K
          thermalConductivity: 50, // W/m·K
          thermalDiffusivity: 1.4e-5 // m²/s
        },
        insulation: {
          type: 'mineral_wool',
          thickness: 100, // mm
          thermalConductivity: 0.04, // W/m·K
          temperatureRating: 200, // °C
          weatherResistant: true
        },
        heatLoss: {
          ambientTemperature: 20, // °C
          windSpeed: 3, // m/s
          convectionCoefficient: 15, // W/m²·K
          radiationEmissivity: 0.9,
          totalHeatLossCoefficient: 2.5 // W/m²·K
        }
      },
      geometry: {
        shape: 'cylindrical',
        dimensions: {
          diameter: 4.5, // m
          height: 6, // m
          wallThickness: 12 // mm
        },
        volumes: {
          totalVolume: 50000, // L
          workingVolume: 45000, // L
          deadVolume: 2000, // L
          vaporSpace: 3000 // L
        },
        surfaces: {
          totalSurfaceArea: 120, // m²
          heatedSurfaceArea: 95, // m²
          insulatedSurfaceArea: 120 // m²
        },
        structural: {
          designPressure: 3, // bar
          designTemperature: 200, // °C
          materialGrade: 'A516-70',
          corrosionAllowance: 3, // mm
          windLoadRating: 1.5, // kPa
          seismicRating: 'Zone 2'
        }
      },
      controlParameters: {
        temperature: {
          setpoint: 150, // °C
          deadband: 2, // °C
          rampRate: 1, // °C/min
          maxRampRate: 5, // °C/min
          alarmLimits: {
            highHigh: 180, // °C
            high: 170, // °C
            low: 130, // °C
            lowLow: 120 // °C
          }
        },
        level: {
          setpoint: 75, // %
          deadband: 5, // %
          alarmLimits: {
            highHigh: 95, // %
            high: 90, // %
            low: 20, // %
            lowLow: 10 // %
          }
        },
        pressure: {
          setpoint: 1.5, // bar
          deadband: 0.2, // bar
          alarmLimits: {
            highHigh: 2.8, // bar
            high: 2.5, // bar
            low: 0.5, // bar
            lowLow: 0.2 // bar
          }
        },
        heating: {
          pidParameters: {
            kp: 2.0,
            ki: 0.1,
            kd: 0.05,
            outputLimits: [0, 100]
          },
          heatingStrategy: 'continuous',
          energyOptimization: true,
          loadFollowing: true
        }
      }
    },
    {
      id: 2,
      name: 'ASP-02',
      position: [-2, 0, -8] as [number, number, number],
      capacity: 50000,
      type: 'storage' as const,
      hasHeatingCoil: true,
      coilTurns: 3,
      connections: {
        hotOilInlet: 'HO-SUPPLY-02',
        hotOilOutlet: 'HO-RETURN-02',
        asphaltOutlet: 'ASP-OUT-02',
        sensors: ['TT-02', 'LT-02', 'PT-02']
      }
    },
    {
      id: 3,
      name: 'ASP-03',
      position: [2, 0, -8] as [number, number, number],
      capacity: 50000,
      type: 'storage' as const,
      hasHeatingCoil: true,
      coilTurns: 3,
      connections: {
        hotOilInlet: 'HO-SUPPLY-03',
        hotOilOutlet: 'HO-RETURN-03',
        asphaltOutlet: 'ASP-OUT-03',
        sensors: ['TT-03', 'LT-03', 'PT-03']
      }
    },
    {
      id: 4,
      name: 'ASP-04',
      position: [6, 0, -8] as [number, number, number],
      capacity: 50000,
      type: 'storage' as const,
      hasHeatingCoil: true,
      coilTurns: 3,
      connections: {
        hotOilInlet: 'HO-SUPPLY-04',
        hotOilOutlet: 'HO-RETURN-04',
        asphaltOutlet: 'ASP-OUT-04',
        sensors: ['TT-04', 'LT-04', 'PT-04']
      }
    },
    {
      id: 5,
      name: 'ASP-05',
      position: [-4, 0, -4] as [number, number, number],
      capacity: 50000,
      type: 'storage' as const,
      hasHeatingCoil: true,
      coilTurns: 3,
      connections: {
        hotOilInlet: 'HO-SUPPLY-05',
        hotOilOutlet: 'HO-RETURN-05',
        asphaltOutlet: 'ASP-OUT-05',
        sensors: ['TT-05', 'LT-05', 'PT-05']
      }
    },
    {
      id: 6,
      name: 'ASP-06',
      position: [0, 0, -4] as [number, number, number],
      capacity: 50000,
      type: 'storage' as const,
      hasHeatingCoil: true,
      coilTurns: 3,
      connections: {
        hotOilInlet: 'HO-SUPPLY-06',
        hotOilOutlet: 'HO-RETURN-06',
        asphaltOutlet: 'ASP-OUT-06',
        sensors: ['TT-06', 'LT-06', 'PT-06']
      }
    },
    {
      id: 7,
      name: 'ASP-07',
      position: [4, 0, -4] as [number, number, number],
      capacity: 50000,
      type: 'storage' as const,
      hasHeatingCoil: true,
      coilTurns: 3,
      connections: {
        hotOilInlet: 'HO-SUPPLY-07',
        hotOilOutlet: 'HO-RETURN-07',
        asphaltOutlet: 'ASP-OUT-07',
        sensors: ['TT-07', 'LT-07', 'PT-07']
      }
    },
    {
      id: 8,
      name: 'ASP-08',
      position: [-2, 0, 0] as [number, number, number],
      capacity: 50000,
      type: 'storage' as const,
      hasHeatingCoil: true,
      coilTurns: 3,
      connections: {
        hotOilInlet: 'HO-SUPPLY-08',
        hotOilOutlet: 'HO-RETURN-08',
        asphaltOutlet: 'ASP-OUT-08',
        sensors: ['TT-08', 'LT-08', 'PT-08']
      }
    },
    {
      id: 9,
      name: 'ASP-09',
      position: [2, 0, 0] as [number, number, number],
      capacity: 50000,
      type: 'storage' as const,
      hasHeatingCoil: true,
      coilTurns: 3,
      connections: {
        hotOilInlet: 'HO-SUPPLY-09',
        hotOilOutlet: 'HO-RETURN-09',
        asphaltOutlet: 'ASP-OUT-09',
        sensors: ['TT-09', 'LT-09', 'PT-09']
      }
    },
    {
      id: 10,
      name: 'ASP-10',
      position: [0, 0, 4] as [number, number, number],
      capacity: 50000,
      type: 'storage' as const,
      hasHeatingCoil: true,
      coilTurns: 3,
      connections: {
        hotOilInlet: 'HO-SUPPLY-10',
        hotOilOutlet: 'HO-RETURN-10',
        asphaltOutlet: 'ASP-OUT-10',
        sensors: ['TT-10', 'LT-10', 'PT-10']
      }
    }
  ] as TankPosition[],

  // Hot-oil circulation system
  hotOilSystem: {
    boiler: {
      position: [-12, 0, 2] as [number, number, number],
      capacity: 2000, // kW
      fuelType: 'gas' as const,
      maxTemperature: 300, // °C
      efficiency: 85 // %
    }
  } as HotOilSystem,

  // Asphalt loading stations
  loadingSystem: {
    loadingStations: [
      {
        id: 'LS-01',
        position: [8, 0, -10] as [number, number, number],
        loadingArms: 2,
        maxFlowRate: 1000, // L/min
        truckCapacity: 30000, // L
        automationLevel: 'semi-auto' as const,
        connectedTanks: [1, 2, 3, 4]
      },
      {
        id: 'LS-02',
        position: [12, 0, -6] as [number, number, number],
        loadingArms: 2,
        maxFlowRate: 1000, // L/min
        truckCapacity: 30000, // L
        automationLevel: 'semi-auto' as const,
        connectedTanks: [5, 6, 7]
      }
    ]
  } as AsphaltLoadingSystem
};
