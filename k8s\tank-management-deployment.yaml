apiVersion: apps/v1
kind: Deployment
metadata:
  name: tank-management-service
  namespace: tank-system
  labels:
    app: tank-management-service
    version: v1.0.0
    component: tank-management
spec:
  replicas: 3
  selector:
    matchLabels:
      app: tank-management-service
  template:
    metadata:
      labels:
        app: tank-management-service
        version: v1.0.0
        component: tank-management
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: tank-service-account
      containers:
      - name: tank-management
        image: tank-registry.io/tank-management:v1.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: postgres-credentials
              key: url
        - name: REDIS_URL
          value: "redis://redis-service:6379"
        - name: EVENT_BUS_URL
          value: "http://event-bus-service:8083"
        - name: LOG_LEVEL
          value: "INFO"
        - name: ENVIRONMENT
          value: "production"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        - name: temp-storage
          mountPath: /tmp
      volumes:
      - name: config-volume
        configMap:
          name: tank-system-config
      - name: temp-storage
        emptyDir: {}
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
---
apiVersion: v1
kind: Service
metadata:
  name: tank-management-service
  namespace: tank-system
  labels:
    app: tank-management-service
    component: tank-management
spec:
  selector:
    app: tank-management-service
  ports:
  - name: http
    port: 8080
    targetPort: 8080
    protocol: TCP
  type: ClusterIP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: tank-management-hpa
  namespace: tank-system
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tank-management-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80