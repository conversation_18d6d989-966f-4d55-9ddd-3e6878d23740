# Main application deployment for MLOps Digital Twin Platform
apiVersion: apps/v1
kind: Deployment
metadata:
  name: digital-twin-app
  namespace: digital-twin
  labels:
    app: digital-twin-app
    component: backend
    version: "1.0.0"
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: digital-twin-app
  template:
    metadata:
      labels:
        app: digital-twin-app
        component: backend
        version: "1.0.0"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: digital-twin-service-account
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: digital-twin-app
        image: digital-twin:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
          protocol: TCP
        - containerPort: 8001
          name: metrics
          protocol: TCP
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: DEBUG
          value: "false"
        - name: LO<PERSON>_LEVEL
          value: "INFO"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: digital-twin-secrets
              key: database-url
        - name: REDIS_URL
          value: "redis://digital-twin-redis:6379/0"
        - name: INFLUXDB_URL
          value: "http://digital-twin-influxdb:8086"
        - name: INFLUXDB_TOKEN
          valueFrom:
            secretKeyRef:
              name: digital-twin-secrets
              key: influxdb-token
        - name: INFLUXDB_ORG
          value: "digital-twin"
        - name: INFLUXDB_BUCKET
          value: "metrics"
        - name: MINIO_ENDPOINT
          value: "digital-twin-minio:9000"
        - name: MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: digital-twin-secrets
              key: minio-access-key
        - name: MINIO_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: digital-twin-secrets
              key: minio-secret-key
        - name: OPENWEATHER_API_KEY
          valueFrom:
            secretKeyRef:
              name: digital-twin-secrets
              key: openweather-api-key
        - name: PYTHONPATH
          value: "/app"
        resources:
          requests:
            cpu: "500m"
            memory: "1Gi"
          limits:
            cpu: "2"
            memory: "4Gi"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: app-config
          mountPath: /app/config
          readOnly: true
        - name: app-logs
          mountPath: /app/logs
        - name: app-data
          mountPath: /app/data
        - name: app-models
          mountPath: /app/models
      volumes:
      - name: app-config
        configMap:
          name: digital-twin-config
      - name: app-logs
        emptyDir: {}
      - name: app-data
        persistentVolumeClaim:
          claimName: digital-twin-data-pvc
      - name: app-models
        persistentVolumeClaim:
          claimName: digital-twin-models-pvc
      nodeSelector:
        kubernetes.io/os: linux
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300

---
# Service for the main application
apiVersion: v1
kind: Service
metadata:
  name: digital-twin-app
  namespace: digital-twin
  labels:
    app: digital-twin-app
    component: backend
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  - port: 8001
    targetPort: 8001
    protocol: TCP
    name: metrics
  selector:
    app: digital-twin-app

---
# Horizontal Pod Autoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: digital-twin-app-hpa
  namespace: digital-twin
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: digital-twin-app
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60

---
# Pod Disruption Budget
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: digital-twin-app-pdb
  namespace: digital-twin
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: digital-twin-app