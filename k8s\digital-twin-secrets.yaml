# Secrets for MLOps Digital Twin Platform
# Note: In production, these should be created securely and not stored in Git
apiVersion: v1
kind: Secret
metadata:
  name: digital-twin-secrets
  namespace: digital-twin
  labels:
    app: digital-twin
    component: secrets
type: Opaque
data:
  # Database credentials (base64 encoded)
  postgres-password: cG9zdGdyZXM=  # postgres
  database-url: ************************************************************************************************

  # InfluxDB credentials
  influxdb-token: YWRtaW4tdG9rZW4=  # admin-token
  influxdb-password: YWRtaW5wYXNzd29yZA==  # adminpassword

  # MinIO credentials
  minio-access-key: bWluaW9hZG1pbg==  # minioadmin
  minio-secret-key: bWluaW9hZG1pbg==  # minioadmin

  # External API keys
  openweather-api-key: eW91ci1vcGVud2VhdGhlci1hcGkta2V5LWhlcmU=  # your-openweather-api-key-here

  # Application secrets
  jwt-secret-key: eW91ci1qd3Qtc2VjcmV0LWtleS1oZXJl  # your-jwt-secret-key-here
  encryption-key: eW91ci1lbmNyeXB0aW9uLWtleS1oZXJl  # your-encryption-key-here

  # Grafana credentials
  grafana-admin-password: YWRtaW4=  # admin

---
# Service Account for the application
apiVersion: v1
kind: ServiceAccount
metadata:
  name: digital-twin-service-account
  namespace: digital-twin
  labels:
    app: digital-twin
    component: rbac
automountServiceAccountToken: true

---
# Role for the service account
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: digital-twin-role
  namespace: digital-twin
  labels:
    app: digital-twin
    component: rbac
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create"]

---
# Role binding
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: digital-twin-role-binding
  namespace: digital-twin
  labels:
    app: digital-twin
    component: rbac
subjects:
- kind: ServiceAccount
  name: digital-twin-service-account
  namespace: digital-twin
roleRef:
  kind: Role
  name: digital-twin-role
  apiGroup: rbac.authorization.k8s.io

---
# TLS Secret for HTTPS (placeholder - should be properly generated)
apiVersion: v1
kind: Secret
metadata:
  name: digital-twin-tls
  namespace: digital-twin
  labels:
    app: digital-twin
    component: tls
type: kubernetes.io/tls
data:
  # These are placeholder certificates - replace with real ones in production
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t  # placeholder
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t  # placeholder