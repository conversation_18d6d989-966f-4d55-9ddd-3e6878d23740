"""
MLOps Pipeline Orchestrator
Comprehensive MLOps pipeline with CI/CD, model monitoring, and deployment automation
"""

import asyncio
import logging
import json
import pickle
import hashlib
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from pathlib import Path
import numpy as np
import pandas as pd

try:
    import mlflow
    import mlflow.tracking
    from mlflow.models.signature import infer_signature
    MLFLOW_AVAILABLE = True
except ImportError:
    MLFLOW_AVAILABLE = False
    print("Warning: MLflow not available. Model tracking will be limited.")

try:
    import docker
    DOCKER_AVAILABLE = True
except ImportError:
    DOCKER_AVAILABLE = False
    print("Warning: Docker not available. Containerized deployment will be limited.")

logger = logging.getLogger(__name__)


class PipelineStage(Enum):
    DATA_VALIDATION = "data_validation"
    FEATURE_ENGINEERING = "feature_engineering"
    MODEL_TRAINING = "model_training"
    MODEL_VALIDATION = "model_validation"
    MODEL_TESTING = "model_testing"
    MODEL_DEPLOYMENT = "model_deployment"
    MODEL_MONITORING = "model_monitoring"
    MODEL_RETRAINING = "model_retraining"


class DeploymentStage(Enum):
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    CANARY = "canary"
    ROLLBACK = "rollback"


class ModelStatus(Enum):
    TRAINING = "training"
    VALIDATION = "validation"
    TESTING = "testing"
    DEPLOYED = "deployed"
    DEPRECATED = "deprecated"
    FAILED = "failed"
    MONITORING = "monitoring"


@dataclass
class ModelMetrics:
    """Model performance metrics"""
    accuracy: float = 0.0
    precision: float = 0.0
    recall: float = 0.0
    f1_score: float = 0.0
    mae: float = 0.0
    mse: float = 0.0
    rmse: float = 0.0
    r2_score: float = 0.0
    custom_metrics: Dict[str, float] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, float]:
        metrics = {
            'accuracy': self.accuracy,
            'precision': self.precision,
            'recall': self.recall,
            'f1_score': self.f1_score,
            'mae': self.mae,
            'mse': self.mse,
            'rmse': self.rmse,
            'r2_score': self.r2_score
        }
        metrics.update(self.custom_metrics)
        return metrics


@dataclass
class ModelArtifact:
    """Model artifact information"""
    model_id: str
    model_name: str
    version: str
    stage: DeploymentStage
    status: ModelStatus
    metrics: ModelMetrics
    model_path: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    tags: Dict[str, str] = field(default_factory=dict)
    
    def get_model_uri(self) -> str:
        """Get MLflow model URI"""
        return f"models:/{self.model_name}/{self.stage.value}"


@dataclass
class PipelineConfig:
    """MLOps pipeline configuration"""
    project_name: str
    model_name: str
    tracking_uri: str = "sqlite:///mlflow.db"
    artifact_location: str = "./mlruns"
    experiment_name: str = "default"
    
    # Training configuration
    train_test_split: float = 0.8
    validation_split: float = 0.2
    cross_validation_folds: int = 5
    
    # Model validation thresholds
    min_accuracy: float = 0.8
    min_precision: float = 0.7
    min_recall: float = 0.7
    max_drift_threshold: float = 0.1
    
    # Deployment configuration
    staging_traffic_percentage: float = 0.1
    canary_traffic_percentage: float = 0.05
    rollback_threshold: float = 0.05  # Performance degradation threshold
    
    # Monitoring configuration
    monitoring_interval: int = 3600  # seconds
    retraining_threshold: float = 0.15  # Model drift threshold for retraining
    data_quality_threshold: float = 0.9
    
    # Infrastructure
    enable_docker: bool = True
    enable_kubernetes: bool = False
    container_registry: str = "localhost:5000"


class DataValidator:
    """Data validation component"""
    
    def __init__(self):
        self.validation_rules = {}
        self.data_schema = {}
    
    async def validate_data(self, data: pd.DataFrame, dataset_type: str = "training") -> Dict[str, Any]:
        """Validate data quality and schema"""
        validation_results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'metrics': {}
        }
        
        try:
            # Schema validation
            schema_validation = self._validate_schema(data)
            validation_results['metrics'].update(schema_validation)
            
            # Data quality validation
            quality_validation = self._validate_data_quality(data)
            validation_results['metrics'].update(quality_validation)
            
            # Statistical validation
            statistical_validation = self._validate_statistics(data, dataset_type)
            validation_results['metrics'].update(statistical_validation)
            
            # Determine overall validity
            quality_score = quality_validation.get('quality_score', 0.0)
            if quality_score < 0.9:
                validation_results['is_valid'] = False
                validation_results['errors'].append(f"Data quality score {quality_score:.2f} below threshold")
            
            logger.info(f"Data validation completed. Valid: {validation_results['is_valid']}")
            
        except Exception as e:
            logger.error(f"Error in data validation: {e}")
            validation_results['is_valid'] = False
            validation_results['errors'].append(str(e))
        
        return validation_results
    
    def _validate_schema(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Validate data schema"""
        expected_columns = ['temperature', 'power_consumption', 'efficiency', 'timestamp']
        
        missing_columns = [col for col in expected_columns if col not in data.columns]
        extra_columns = [col for col in data.columns if col not in expected_columns and not col.startswith('feature_')]
        
        return {
            'schema_valid': len(missing_columns) == 0,
            'missing_columns': missing_columns,
            'extra_columns': extra_columns,
            'total_columns': len(data.columns)
        }
    
    def _validate_data_quality(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Validate data quality"""
        total_rows = len(data)
        if total_rows == 0:
            return {'quality_score': 0.0, 'total_rows': 0}
        
        # Calculate quality metrics
        missing_values = data.isnull().sum().sum()
        duplicate_rows = data.duplicated().sum()
        
        # Detect outliers (simple Z-score method)
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        outliers = 0
        for col in numeric_columns:
            if col in data.columns:
                z_scores = np.abs((data[col] - data[col].mean()) / data[col].std())
                outliers += (z_scores > 3).sum()
        
        # Calculate quality score
        missing_rate = missing_values / (total_rows * len(data.columns))
        duplicate_rate = duplicate_rows / total_rows
        outlier_rate = outliers / (total_rows * len(numeric_columns)) if numeric_columns.any() else 0
        
        quality_score = 1.0 - (missing_rate * 0.4 + duplicate_rate * 0.3 + outlier_rate * 0.3)
        
        return {
            'quality_score': max(0.0, quality_score),
            'missing_values': missing_values,
            'missing_rate': missing_rate,
            'duplicate_rows': duplicate_rows,
            'duplicate_rate': duplicate_rate,
            'outliers': outliers,
            'outlier_rate': outlier_rate,
            'total_rows': total_rows
        }
    
    def _validate_statistics(self, data: pd.DataFrame, dataset_type: str) -> Dict[str, Any]:
        """Validate statistical properties"""
        numeric_data = data.select_dtypes(include=[np.number])
        
        if numeric_data.empty:
            return {'statistical_validation': 'no_numeric_data'}
        
        stats = {
            'mean_values': numeric_data.mean().to_dict(),
            'std_values': numeric_data.std().to_dict(),
            'min_values': numeric_data.min().to_dict(),
            'max_values': numeric_data.max().to_dict(),
            'skewness': numeric_data.skew().to_dict(),
            'kurtosis': numeric_data.kurtosis().to_dict()
        }
        
        # Add dataset-specific validation
        stats['dataset_type'] = dataset_type
        stats['validation_timestamp'] = datetime.now().isoformat()
        
        return stats


class ModelTrainer:
    """Model training component"""
    
    def __init__(self, config: PipelineConfig):
        self.config = config
        self.training_history = []
    
    async def train_model(self, training_data: pd.DataFrame, model_type: str = "temperature_prediction") -> Dict[str, Any]:
        """Train machine learning model"""
        try:
            logger.info(f"Starting model training for {model_type}")
            
            # Start MLflow run
            if MLFLOW_AVAILABLE:
                mlflow.set_tracking_uri(self.config.tracking_uri)
                mlflow.set_experiment(self.config.experiment_name)
                
                with mlflow.start_run():
                    return await self._train_with_mlflow(training_data, model_type)
            else:
                return await self._train_without_mlflow(training_data, model_type)
                
        except Exception as e:
            logger.error(f"Error in model training: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _train_with_mlflow(self, training_data: pd.DataFrame, model_type: str) -> Dict[str, Any]:
        """Train model with MLflow tracking"""
        # Prepare data
        X, y = self._prepare_training_data(training_data, model_type)
        
        # Split data
        from sklearn.model_selection import train_test_split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=1-self.config.train_test_split, random_state=42
        )
        
        # Log parameters
        mlflow.log_param("model_type", model_type)
        mlflow.log_param("train_size", len(X_train))
        mlflow.log_param("test_size", len(X_test))
        mlflow.log_param("features", X.shape[1])
        
        # Train model
        if model_type == "temperature_prediction":
            model = await self._train_temperature_model(X_train, y_train)
        elif model_type == "energy_optimization":
            model = await self._train_energy_model(X_train, y_train)
        elif model_type == "anomaly_detection":
            model = await self._train_anomaly_model(X_train, y_train)
        else:
            raise ValueError(f"Unknown model type: {model_type}")
        
        # Evaluate model
        metrics = await self._evaluate_model(model, X_test, y_test, model_type)
        
        # Log metrics
        for metric_name, metric_value in metrics.to_dict().items():
            mlflow.log_metric(metric_name, metric_value)
        
        # Log model
        signature = infer_signature(X_train, y_train)
        mlflow.sklearn.log_model(
            model, 
            "model",
            signature=signature,
            registered_model_name=self.config.model_name
        )
        
        # Get run info
        run = mlflow.active_run()
        model_uri = f"runs:/{run.info.run_id}/model"
        
        logger.info(f"Model training completed. MLflow run: {run.info.run_id}")
        
        return {
            'success': True,
            'model': model,
            'metrics': metrics,
            'model_uri': model_uri,
            'run_id': run.info.run_id,
            'mlflow_tracking': True
        }
    
    async def _train_without_mlflow(self, training_data: pd.DataFrame, model_type: str) -> Dict[str, Any]:
        """Train model without MLflow (fallback)"""
        logger.warning("Training without MLflow tracking")
        
        # Prepare data
        X, y = self._prepare_training_data(training_data, model_type)
        
        # Split data
        split_idx = int(len(X) * self.config.train_test_split)
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        # Train model
        if model_type == "temperature_prediction":
            model = await self._train_temperature_model(X_train, y_train)
        elif model_type == "energy_optimization":
            model = await self._train_energy_model(X_train, y_train)
        else:
            raise ValueError(f"Unknown model type: {model_type}")
        
        # Evaluate model
        metrics = await self._evaluate_model(model, X_test, y_test, model_type)
        
        # Save model locally
        model_path = f"./models/{model_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
        Path("./models").mkdir(exist_ok=True)
        
        with open(model_path, 'wb') as f:
            pickle.dump(model, f)
        
        return {
            'success': True,
            'model': model,
            'metrics': metrics,
            'model_path': model_path,
            'mlflow_tracking': False
        }
    
    def _prepare_training_data(self, data: pd.DataFrame, model_type: str) -> tuple:
        """Prepare training data based on model type"""
        # Feature selection based on model type
        if model_type == "temperature_prediction":
            feature_columns = ['ambient_temperature', 'power_consumption', 'efficiency', 'wind_speed']
            target_column = 'temperature'
        elif model_type == "energy_optimization":
            feature_columns = ['temperature', 'ambient_temperature', 'time_of_day', 'day_of_week']
            target_column = 'power_consumption'
        elif model_type == "anomaly_detection":
            feature_columns = data.select_dtypes(include=[np.number]).columns.tolist()
            if 'timestamp' in feature_columns:
                feature_columns.remove('timestamp')
            target_column = None  # Unsupervised learning
        else:
            raise ValueError(f"Unknown model type: {model_type}")
        
        # Filter available columns
        available_features = [col for col in feature_columns if col in data.columns]
        
        if not available_features:
            raise ValueError(f"No required features found for {model_type}")
        
        X = data[available_features].fillna(0).values
        
        if target_column and target_column in data.columns:
            y = data[target_column].fillna(0).values
        else:
            y = np.zeros(len(X))  # Dummy target for unsupervised
        
        return X, y
    
    async def _train_temperature_model(self, X_train: np.ndarray, y_train: np.ndarray):
        """Train temperature prediction model"""
        from sklearn.ensemble import RandomForestRegressor
        from sklearn.preprocessing import StandardScaler
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        
        # Train model
        model = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            n_jobs=-1
        )
        model.fit(X_train_scaled, y_train)
        
        # Return model with scaler
        return {'model': model, 'scaler': scaler, 'type': 'temperature_prediction'}
    
    async def _train_energy_model(self, X_train: np.ndarray, y_train: np.ndarray):
        """Train energy optimization model"""
        from sklearn.ensemble import GradientBoostingRegressor
        from sklearn.preprocessing import StandardScaler
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        
        # Train model
        model = GradientBoostingRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=6,
            random_state=42
        )
        model.fit(X_train_scaled, y_train)
        
        return {'model': model, 'scaler': scaler, 'type': 'energy_optimization'}
    
    async def _train_anomaly_model(self, X_train: np.ndarray, y_train: np.ndarray):
        """Train anomaly detection model"""
        from sklearn.ensemble import IsolationForest
        from sklearn.preprocessing import StandardScaler
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        
        # Train model
        model = IsolationForest(
            contamination=0.1,
            random_state=42,
            n_estimators=100
        )
        model.fit(X_train_scaled)
        
        return {'model': model, 'scaler': scaler, 'type': 'anomaly_detection'}
    
    async def _evaluate_model(self, model_dict: Dict[str, Any], X_test: np.ndarray, 
                            y_test: np.ndarray, model_type: str) -> ModelMetrics:
        """Evaluate trained model"""
        model = model_dict['model']
        scaler = model_dict['scaler']
        
        # Scale test data
        X_test_scaled = scaler.transform(X_test)
        
        # Make predictions
        if model_type == "anomaly_detection":
            predictions = model.predict(X_test_scaled)
            # For anomaly detection, create dummy metrics
            return ModelMetrics(
                accuracy=0.9,  # Placeholder
                custom_metrics={'anomaly_score': float(np.mean(predictions == 1))}
            )
        else:
            predictions = model.predict(X_test_scaled)
            
            # Calculate regression metrics
            from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
            
            mae = mean_absolute_error(y_test, predictions)
            mse = mean_squared_error(y_test, predictions)
            rmse = np.sqrt(mse)
            r2 = r2_score(y_test, predictions)
            
            return ModelMetrics(
                mae=mae,
                mse=mse,
                rmse=rmse,
                r2_score=r2
            )


class ModelValidator:
    """Model validation component"""
    
    def __init__(self, config: PipelineConfig):
        self.config = config
    
    async def validate_model(self, model_artifact: ModelArtifact, validation_data: pd.DataFrame) -> Dict[str, Any]:
        """Validate model performance"""
        try:
            logger.info(f"Validating model {model_artifact.model_name}")
            
            validation_results = {
                'is_valid': True,
                'validation_metrics': {},
                'business_metrics': {},
                'errors': [],
                'warnings': []
            }
            
            # Performance validation
            performance_validation = await self._validate_performance(model_artifact, validation_data)
            validation_results['validation_metrics'] = performance_validation
            
            # Business rules validation
            business_validation = await self._validate_business_rules(model_artifact, validation_data)
            validation_results['business_metrics'] = business_validation
            
            # Check validation thresholds
            if performance_validation.get('r2_score', 0) < self.config.min_accuracy:
                validation_results['is_valid'] = False
                validation_results['errors'].append(f"R2 score below threshold: {performance_validation.get('r2_score', 0):.3f} < {self.config.min_accuracy}")
            
            logger.info(f"Model validation completed. Valid: {validation_results['is_valid']}")
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Error in model validation: {e}")
            return {
                'is_valid': False,
                'errors': [str(e)],
                'validation_metrics': {},
                'business_metrics': {}
            }
    
    async def _validate_performance(self, model_artifact: ModelArtifact, 
                                  validation_data: pd.DataFrame) -> Dict[str, float]:
        """Validate model performance metrics"""
        # For simplicity, return stored metrics
        # In practice, would re-evaluate on validation data
        return model_artifact.metrics.to_dict()
    
    async def _validate_business_rules(self, model_artifact: ModelArtifact,
                                     validation_data: pd.DataFrame) -> Dict[str, Any]:
        """Validate business-specific rules"""
        business_metrics = {
            'prediction_stability': 0.9,  # Placeholder
            'prediction_range_valid': True,
            'bias_score': 0.05,
            'fairness_score': 0.95
        }
        
        return business_metrics


class ModelDeployer:
    """Model deployment component"""
    
    def __init__(self, config: PipelineConfig):
        self.config = config
        self.deployment_history = []
    
    async def deploy_model(self, model_artifact: ModelArtifact, 
                          target_stage: DeploymentStage) -> Dict[str, Any]:
        """Deploy model to target stage"""
        try:
            logger.info(f"Deploying model {model_artifact.model_name} to {target_stage.value}")
            
            deployment_result = {
                'success': False,
                'deployment_id': None,
                'endpoint_url': None,
                'rollback_version': None
            }
            
            # Choose deployment strategy
            if target_stage == DeploymentStage.DEVELOPMENT:
                result = await self._deploy_to_development(model_artifact)
            elif target_stage == DeploymentStage.STAGING:
                result = await self._deploy_to_staging(model_artifact)
            elif target_stage == DeploymentStage.PRODUCTION:
                result = await self._deploy_to_production(model_artifact)
            elif target_stage == DeploymentStage.CANARY:
                result = await self._deploy_canary(model_artifact)
            else:
                raise ValueError(f"Unknown deployment stage: {target_stage}")
            
            deployment_result.update(result)
            
            # Record deployment
            if deployment_result['success']:
                self.deployment_history.append({
                    'model_artifact': model_artifact,
                    'target_stage': target_stage,
                    'deployment_time': datetime.now(),
                    'deployment_id': deployment_result['deployment_id']
                })
            
            logger.info(f"Deployment completed. Success: {deployment_result['success']}")
            
            return deployment_result
            
        except Exception as e:
            logger.error(f"Error in model deployment: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _deploy_to_development(self, model_artifact: ModelArtifact) -> Dict[str, Any]:
        """Deploy to development environment"""
        # Simple local deployment for development
        deployment_id = f"dev_{model_artifact.model_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        endpoint_url = f"http://localhost:5000/models/{model_artifact.model_name}"
        
        return {
            'success': True,
            'deployment_id': deployment_id,
            'endpoint_url': endpoint_url,
            'environment': 'development'
        }
    
    async def _deploy_to_staging(self, model_artifact: ModelArtifact) -> Dict[str, Any]:
        """Deploy to staging environment"""
        deployment_id = f"staging_{model_artifact.model_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        if DOCKER_AVAILABLE and self.config.enable_docker:
            # Deploy using Docker
            container_result = await self._deploy_docker_container(model_artifact, 'staging')
            if container_result['success']:
                return {
                    'success': True,
                    'deployment_id': deployment_id,
                    'endpoint_url': container_result['endpoint_url'],
                    'container_id': container_result['container_id'],
                    'environment': 'staging'
                }
        
        # Fallback deployment
        return {
            'success': True,
            'deployment_id': deployment_id,
            'endpoint_url': f"http://staging.example.com/models/{model_artifact.model_name}",
            'environment': 'staging'
        }
    
    async def _deploy_to_production(self, model_artifact: ModelArtifact) -> Dict[str, Any]:
        """Deploy to production environment"""
        deployment_id = f"prod_{model_artifact.model_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Production deployment with additional safety checks
        safety_checks = await self._perform_safety_checks(model_artifact)
        if not safety_checks['passed']:
            return {
                'success': False,
                'error': 'Safety checks failed',
                'safety_checks': safety_checks
            }
        
        if DOCKER_AVAILABLE and self.config.enable_docker:
            container_result = await self._deploy_docker_container(model_artifact, 'production')
            if container_result['success']:
                return {
                    'success': True,
                    'deployment_id': deployment_id,
                    'endpoint_url': container_result['endpoint_url'],
                    'container_id': container_result['container_id'],
                    'environment': 'production'
                }
        
        return {
            'success': True,
            'deployment_id': deployment_id,
            'endpoint_url': f"http://production.example.com/models/{model_artifact.model_name}",
            'environment': 'production'
        }
    
    async def _deploy_canary(self, model_artifact: ModelArtifact) -> Dict[str, Any]:
        """Deploy canary version"""
        deployment_id = f"canary_{model_artifact.model_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        return {
            'success': True,
            'deployment_id': deployment_id,
            'endpoint_url': f"http://canary.example.com/models/{model_artifact.model_name}",
            'traffic_percentage': self.config.canary_traffic_percentage,
            'environment': 'canary'
        }
    
    async def _deploy_docker_container(self, model_artifact: ModelArtifact, 
                                     environment: str) -> Dict[str, Any]:
        """Deploy model in Docker container"""
        try:
            client = docker.from_env()
            
            # Build container image
            image_name = f"{self.config.container_registry}/{model_artifact.model_name}:{model_artifact.version}"
            
            # For simplicity, assume image exists or create dummy deployment
            container_id = f"container_{model_artifact.model_id}_{environment}"
            endpoint_url = f"http://{environment}.example.com:8080/predict"
            
            return {
                'success': True,
                'container_id': container_id,
                'endpoint_url': endpoint_url,
                'image_name': image_name
            }
            
        except Exception as e:
            logger.error(f"Error deploying Docker container: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _perform_safety_checks(self, model_artifact: ModelArtifact) -> Dict[str, Any]:
        """Perform safety checks before production deployment"""
        checks = {
            'passed': True,
            'checks': []
        }
        
        # Check model metrics
        if model_artifact.metrics.r2_score < self.config.min_accuracy:
            checks['passed'] = False
            checks['checks'].append(f"R2 score too low: {model_artifact.metrics.r2_score}")
        
        # Check model age
        model_age = datetime.now() - model_artifact.created_at
        if model_age.days > 30:
            checks['checks'].append(f"Model is {model_age.days} days old")
        
        # Add more safety checks as needed
        
        return checks


class ModelMonitor:
    """Model monitoring component"""
    
    def __init__(self, config: PipelineConfig):
        self.config = config
        self.monitoring_data = []
        self.drift_detectors = {}
        self.performance_history = {}
    
    async def monitor_model(self, model_artifact: ModelArtifact, 
                          monitoring_data: pd.DataFrame) -> Dict[str, Any]:
        """Monitor model performance and detect drift"""
        try:
            logger.info(f"Monitoring model {model_artifact.model_name}")
            
            monitoring_results = {
                'model_health': 'healthy',
                'drift_detected': False,
                'performance_degradation': False,
                'alerts': [],
                'metrics': {}
            }
            
            # Performance monitoring
            performance_results = await self._monitor_performance(model_artifact, monitoring_data)
            monitoring_results['metrics'].update(performance_results)
            
            # Drift detection
            drift_results = await self._detect_drift(model_artifact, monitoring_data)
            monitoring_results.update(drift_results)
            
            # Data quality monitoring
            quality_results = await self._monitor_data_quality(monitoring_data)
            monitoring_results['metrics'].update(quality_results)
            
            # Generate alerts
            alerts = await self._generate_alerts(monitoring_results)
            monitoring_results['alerts'] = alerts
            
            # Determine overall health
            if monitoring_results['drift_detected'] or monitoring_results['performance_degradation']:
                monitoring_results['model_health'] = 'degraded'
            
            if len(alerts) > 0:
                if any(alert['severity'] == 'critical' for alert in alerts):
                    monitoring_results['model_health'] = 'critical'
                elif monitoring_results['model_health'] == 'healthy':
                    monitoring_results['model_health'] = 'warning'
            
            logger.info(f"Model monitoring completed. Health: {monitoring_results['model_health']}")
            
            return monitoring_results
            
        except Exception as e:
            logger.error(f"Error in model monitoring: {e}")
            return {
                'model_health': 'error',
                'error': str(e),
                'alerts': [{'message': f"Monitoring error: {e}", 'severity': 'critical'}]
            }
    
    async def _monitor_performance(self, model_artifact: ModelArtifact,
                                 data: pd.DataFrame) -> Dict[str, Any]:
        """Monitor model performance metrics"""
        # Simplified performance monitoring
        # In practice, would make predictions and compare with actual values
        
        current_metrics = {
            'prediction_count': len(data),
            'avg_prediction_time': 0.1,  # Placeholder
            'memory_usage': 128,  # MB, placeholder
            'cpu_usage': 25,  # %, placeholder
            'error_rate': 0.02  # Placeholder
        }
        
        # Check for performance degradation
        if model_artifact.model_name in self.performance_history:
            historical_metrics = self.performance_history[model_artifact.model_name]
            
            # Compare error rates
            if current_metrics['error_rate'] > historical_metrics.get('error_rate', 0) * (1 + self.config.rollback_threshold):
                current_metrics['performance_degraded'] = True
            else:
                current_metrics['performance_degraded'] = False
        else:
            current_metrics['performance_degraded'] = False
        
        # Store current metrics
        self.performance_history[model_artifact.model_name] = current_metrics
        
        return current_metrics
    
    async def _detect_drift(self, model_artifact: ModelArtifact,
                          data: pd.DataFrame) -> Dict[str, Any]:
        """Detect model and data drift"""
        drift_results = {
            'drift_detected': False,
            'drift_score': 0.0,
            'feature_drift': {}
        }
        
        try:
            # Simple drift detection using statistical methods
            numeric_columns = data.select_dtypes(include=[np.number]).columns
            
            for column in numeric_columns:
                if column in data.columns:
                    # Calculate basic statistics
                    current_mean = data[column].mean()
                    current_std = data[column].std()
                    
                    # Compare with stored baseline (simplified)
                    # In practice, would use more sophisticated drift detection
                    baseline_mean = model_artifact.metadata.get(f'{column}_mean', current_mean)
                    baseline_std = model_artifact.metadata.get(f'{column}_std', current_std)
                    
                    # Calculate drift score
                    mean_drift = abs(current_mean - baseline_mean) / max(baseline_std, 0.1)
                    std_drift = abs(current_std - baseline_std) / max(baseline_std, 0.1)
                    
                    feature_drift_score = max(mean_drift, std_drift)
                    drift_results['feature_drift'][column] = feature_drift_score
                    
                    if feature_drift_score > self.config.max_drift_threshold:
                        drift_results['drift_detected'] = True
            
            # Calculate overall drift score
            if drift_results['feature_drift']:
                drift_results['drift_score'] = max(drift_results['feature_drift'].values())
            
        except Exception as e:
            logger.error(f"Error in drift detection: {e}")
            drift_results['error'] = str(e)
        
        return drift_results
    
    async def _monitor_data_quality(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Monitor data quality metrics"""
        total_rows = len(data)
        if total_rows == 0:
            return {'data_quality_score': 0.0}
        
        missing_values = data.isnull().sum().sum()
        missing_rate = missing_values / (total_rows * len(data.columns))
        
        data_quality_score = 1.0 - missing_rate
        
        return {
            'data_quality_score': data_quality_score,
            'missing_values': missing_values,
            'missing_rate': missing_rate,
            'total_rows': total_rows
        }
    
    async def _generate_alerts(self, monitoring_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate alerts based on monitoring results"""
        alerts = []
        
        # Drift alerts
        if monitoring_results['drift_detected']:
            alerts.append({
                'type': 'model_drift',
                'message': f"Model drift detected. Score: {monitoring_results['drift_score']:.3f}",
                'severity': 'high' if monitoring_results['drift_score'] > 0.2 else 'medium',
                'timestamp': datetime.now().isoformat(),
                'action_required': 'Consider model retraining'
            })
        
        # Performance alerts
        if monitoring_results.get('performance_degradation', False):
            alerts.append({
                'type': 'performance_degradation',
                'message': "Model performance degradation detected",
                'severity': 'high',
                'timestamp': datetime.now().isoformat(),
                'action_required': 'Investigate performance issues'
            })
        
        # Data quality alerts
        data_quality = monitoring_results['metrics'].get('data_quality_score', 1.0)
        if data_quality < self.config.data_quality_threshold:
            alerts.append({
                'type': 'data_quality',
                'message': f"Data quality below threshold: {data_quality:.3f}",
                'severity': 'medium',
                'timestamp': datetime.now().isoformat(),
                'action_required': 'Check data pipeline'
            })
        
        return alerts


class MLOpsPipeline:
    """Complete MLOps pipeline orchestrator"""
    
    def __init__(self, config: PipelineConfig):
        self.config = config
        self.data_validator = DataValidator()
        self.model_trainer = ModelTrainer(config)
        self.model_validator = ModelValidator(config)
        self.model_deployer = ModelDeployer(config)
        self.model_monitor = ModelMonitor(config)
        
        self.pipeline_runs = []
        self.active_models = {}
        
        # Initialize MLflow if available
        if MLFLOW_AVAILABLE:
            mlflow.set_tracking_uri(config.tracking_uri)
            try:
                mlflow.create_experiment(config.experiment_name)
            except:
                pass  # Experiment might already exist
    
    async def run_pipeline(self, training_data: pd.DataFrame, 
                         model_type: str = "temperature_prediction",
                         target_stage: DeploymentStage = DeploymentStage.STAGING) -> Dict[str, Any]:
        """Run complete MLOps pipeline"""
        pipeline_id = f"pipeline_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        logger.info(f"Starting MLOps pipeline run: {pipeline_id}")
        
        pipeline_result = {
            'pipeline_id': pipeline_id,
            'success': False,
            'stages': {},
            'model_artifact': None,
            'deployment_info': None,
            'errors': []
        }
        
        try:
            # Stage 1: Data Validation
            logger.info("Stage 1: Data Validation")
            data_validation = await self.data_validator.validate_data(training_data)
            pipeline_result['stages']['data_validation'] = data_validation
            
            if not data_validation['is_valid']:
                pipeline_result['errors'].append("Data validation failed")
                return pipeline_result
            
            # Stage 2: Model Training
            logger.info("Stage 2: Model Training")
            training_result = await self.model_trainer.train_model(training_data, model_type)
            pipeline_result['stages']['model_training'] = training_result
            
            if not training_result['success']:
                pipeline_result['errors'].append("Model training failed")
                return pipeline_result
            
            # Create model artifact
            model_artifact = ModelArtifact(
                model_id=pipeline_id,
                model_name=self.config.model_name,
                version=f"v{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                stage=DeploymentStage.DEVELOPMENT,
                status=ModelStatus.TRAINING,
                metrics=training_result['metrics'],
                model_path=training_result.get('model_uri', training_result.get('model_path', '')),
                metadata={
                    'model_type': model_type,
                    'training_data_size': len(training_data),
                    'pipeline_id': pipeline_id
                }
            )
            
            # Stage 3: Model Validation
            logger.info("Stage 3: Model Validation")
            # Use a subset of training data for validation (in practice, would use separate validation set)
            validation_data = training_data.sample(frac=0.2, random_state=42)
            validation_result = await self.model_validator.validate_model(model_artifact, validation_data)
            pipeline_result['stages']['model_validation'] = validation_result
            
            if not validation_result['is_valid']:
                pipeline_result['errors'].append("Model validation failed")
                model_artifact.status = ModelStatus.FAILED
                return pipeline_result
            
            model_artifact.status = ModelStatus.VALIDATION
            
            # Stage 4: Model Deployment
            logger.info(f"Stage 4: Model Deployment to {target_stage.value}")
            deployment_result = await self.model_deployer.deploy_model(model_artifact, target_stage)
            pipeline_result['stages']['model_deployment'] = deployment_result
            
            if not deployment_result['success']:
                pipeline_result['errors'].append("Model deployment failed")
                model_artifact.status = ModelStatus.FAILED
                return pipeline_result
            
            model_artifact.status = ModelStatus.DEPLOYED
            model_artifact.stage = target_stage
            
            # Store model artifact
            self.active_models[model_artifact.model_id] = model_artifact
            pipeline_result['model_artifact'] = model_artifact
            pipeline_result['deployment_info'] = deployment_result
            
            # Stage 5: Initial Monitoring Setup
            logger.info("Stage 5: Monitoring Setup")
            monitoring_result = await self.model_monitor.monitor_model(model_artifact, training_data.tail(100))
            pipeline_result['stages']['initial_monitoring'] = monitoring_result
            
            pipeline_result['success'] = True
            logger.info(f"MLOps pipeline completed successfully: {pipeline_id}")
            
        except Exception as e:
            logger.error(f"Error in MLOps pipeline: {e}")
            pipeline_result['errors'].append(str(e))
        
        # Store pipeline run
        self.pipeline_runs.append(pipeline_result)
        
        return pipeline_result
    
    async def monitor_active_models(self, monitoring_data: pd.DataFrame) -> Dict[str, Any]:
        """Monitor all active models"""
        monitoring_results = {}
        
        for model_id, model_artifact in self.active_models.items():
            if model_artifact.status == ModelStatus.DEPLOYED:
                try:
                    result = await self.model_monitor.monitor_model(model_artifact, monitoring_data)
                    monitoring_results[model_id] = result
                    
                    # Check if retraining is needed
                    if result['drift_detected'] and result['drift_score'] > self.config.retraining_threshold:
                        logger.warning(f"Model {model_id} requires retraining due to drift")
                        monitoring_results[model_id]['retraining_recommended'] = True
                        
                except Exception as e:
                    logger.error(f"Error monitoring model {model_id}: {e}")
                    monitoring_results[model_id] = {'error': str(e)}
        
        return monitoring_results
    
    async def retrain_model(self, model_id: str, new_training_data: pd.DataFrame) -> Dict[str, Any]:
        """Retrain a specific model"""
        if model_id not in self.active_models:
            return {'success': False, 'error': 'Model not found'}
        
        model_artifact = self.active_models[model_id]
        model_type = model_artifact.metadata.get('model_type', 'temperature_prediction')
        
        logger.info(f"Retraining model {model_id}")
        
        # Run pipeline for retraining
        retraining_result = await self.run_pipeline(
            new_training_data, 
            model_type, 
            model_artifact.stage
        )
        
        if retraining_result['success']:
            # Update model reference
            new_model_artifact = retraining_result['model_artifact']
            new_model_artifact.metadata['retrained_from'] = model_id
            
            # Mark old model as deprecated
            model_artifact.status = ModelStatus.DEPRECATED
            
            logger.info(f"Model {model_id} successfully retrained")
        
        return retraining_result
    
    def get_pipeline_status(self) -> Dict[str, Any]:
        """Get overall pipeline status"""
        total_runs = len(self.pipeline_runs)
        successful_runs = len([r for r in self.pipeline_runs if r['success']])
        active_models_count = len([m for m in self.active_models.values() if m.status == ModelStatus.DEPLOYED])
        
        return {
            'total_pipeline_runs': total_runs,
            'successful_runs': successful_runs,
            'success_rate': successful_runs / max(total_runs, 1),
            'active_models': active_models_count,
            'last_run_time': self.pipeline_runs[-1]['stages'].get('model_training', {}).get('timestamp') if self.pipeline_runs else None,
            'config': {
                'project_name': self.config.project_name,
                'model_name': self.config.model_name,
                'mlflow_enabled': MLFLOW_AVAILABLE,
                'docker_enabled': DOCKER_AVAILABLE and self.config.enable_docker
            }
        }


# Example usage and testing
if __name__ == "__main__":
    async def test_mlops_pipeline():
        # Create test configuration
        config = PipelineConfig(
            project_name="asphalt_tank_control",
            model_name="temperature_predictor",
            experiment_name="temperature_prediction_experiment"
        )
        
        # Create test data
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=1000, freq='H')
        
        training_data = pd.DataFrame({
            'timestamp': dates,
            'temperature': 150 + 5 * np.random.normal(0, 1, 1000),
            'ambient_temperature': 20 + 10 * np.sin(2 * np.pi * np.arange(1000) / 24) + np.random.normal(0, 2, 1000),
            'power_consumption': 100 + 10 * np.random.normal(0, 1, 1000),
            'efficiency': 0.85 + 0.05 * np.random.normal(0, 1, 1000),
            'wind_speed': 5 + 3 * np.random.random(1000)
        })
        
        print("Testing MLOps Pipeline...")
        
        # Create pipeline
        pipeline = MLOpsPipeline(config)
        
        # Run pipeline
        print("Running MLOps pipeline...")
        result = await pipeline.run_pipeline(
            training_data, 
            model_type="temperature_prediction",
            target_stage=DeploymentStage.STAGING
        )
        
        print(f"Pipeline success: {result['success']}")
        if result['success']:
            print(f"Model deployed: {result['deployment_info']['deployment_id']}")
            print(f"Model metrics: R2={result['model_artifact'].metrics.r2_score:.3f}")
        else:
            print(f"Pipeline errors: {result['errors']}")
        
        # Test monitoring
        print("\nTesting model monitoring...")
        monitoring_data = training_data.tail(100)  # Recent data for monitoring
        monitoring_results = await pipeline.monitor_active_models(monitoring_data)
        
        for model_id, monitoring_result in monitoring_results.items():
            print(f"Model {model_id}: Health={monitoring_result['model_health']}")
            if monitoring_result['alerts']:
                print(f"  Alerts: {len(monitoring_result['alerts'])}")
        
        # Get pipeline status
        status = pipeline.get_pipeline_status()
        print(f"\nPipeline Status:")
        print(f"  Total runs: {status['total_pipeline_runs']}")
        print(f"  Success rate: {status['success_rate']:.1%}")
        print(f"  Active models: {status['active_models']}")
        
        print("\nMLOps pipeline testing completed!")
    
    # Run test
    asyncio.run(test_mlops_pipeline())