"""
Web Dashboard Interface
Simple web-based dashboard for monitoring MLOps Digital Twin Platform
"""

import asyncio
import logging
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import threading
import time

try:
    import streamlit as st
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import pandas as pd
    import requests
    STREAMLIT_AVAILABLE = True
except ImportError:
    STREAMLIT_AVAILABLE = False
    print("Warning: Streamlit or required packages not available.")

try:
    import dash
    from dash import dcc, html, Input, Output, State, callback
    import dash_bootstrap_components as dbc
    import plotly.graph_objects as go
    DASH_AVAILABLE = True
except ImportError:
    DASH_AVAILABLE = False
    print("Warning: Dash not available.")

# Import dashboard manager
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

try:
    from monitoring.dashboard_manager import DashboardManager, create_dashboard_manager
    DASHBOARD_AVAILABLE = True
except ImportError:
    DASHBOARD_AVAILABLE = False
    print("Warning: Dashboard manager not available.")

logger = logging.getLogger(__name__)


class StreamlitDashboard:
    """Streamlit-based web dashboard"""
    
    def __init__(self, dashboard_manager: Optional[DashboardManager] = None, api_url: Optional[str] = None):
        self.dashboard_manager = dashboard_manager
        self.api_url = api_url or "http://localhost:8000"
        self.last_update = datetime.now()
        
        # Configure Streamlit page
        if STREAMLIT_AVAILABLE:
            st.set_page_config(
                page_title="MLOps Digital Twin Dashboard",
                page_icon="🏭",
                layout="wide",
                initial_sidebar_state="expanded"
            )
    
    def run_dashboard(self):
        """Run the Streamlit dashboard"""
        if not STREAMLIT_AVAILABLE:
            st.error("Streamlit not available")
            return
        
        # Sidebar
        self._render_sidebar()
        
        # Main content
        selected_dashboard = st.session_state.get('selected_dashboard', 'operational')
        
        if selected_dashboard == 'overview':
            self._render_overview_page()
        elif selected_dashboard == 'operational':
            self._render_operational_page()
        elif selected_dashboard == 'performance':
            self._render_performance_page()
        elif selected_dashboard == 'alerts':
            self._render_alerts_page()
        elif selected_dashboard == 'energy':
            self._render_energy_page()
        else:
            self._render_overview_page()
    
    def _render_sidebar(self):
        """Render sidebar navigation"""
        st.sidebar.title("🏭 MLOps Digital Twin")
        st.sidebar.markdown("---")
        
        # Dashboard selection
        dashboard_options = {
            'overview': '📊 System Overview',
            'operational': '⚙️ Operational',
            'performance': '📈 Performance',
            'alerts': '🚨 Alerts',
            'energy': '⚡ Energy'
        }
        
        selected = st.sidebar.radio(
            "Select Dashboard",
            list(dashboard_options.keys()),
            format_func=lambda x: dashboard_options[x],
            key='selected_dashboard'
        )
        
        st.sidebar.markdown("---")
        
        # Auto-refresh controls
        auto_refresh = st.sidebar.checkbox("Auto Refresh", value=True)
        refresh_interval = st.sidebar.selectbox(
            "Refresh Interval",
            [5, 10, 30, 60],
            index=2,
            format_func=lambda x: f"{x} seconds"
        )
        
        # Manual refresh button
        if st.sidebar.button("🔄 Refresh Now"):
            st.rerun()
        
        # System status
        st.sidebar.markdown("### System Status")
        try:
            if self.dashboard_manager:
                is_running = self.dashboard_manager.is_running
                status_color = "🟢" if is_running else "🔴"
                status_text = "Running" if is_running else "Stopped"
                st.sidebar.markdown(f"{status_color} **{status_text}**")
            else:
                st.sidebar.markdown("🟡 **API Mode**")
        except:
            st.sidebar.markdown("🔴 **Unknown**")
        
        # Last update time
        st.sidebar.markdown(f"**Last Update:** {self.last_update.strftime('%H:%M:%S')}")
        
        # Auto-refresh logic
        if auto_refresh:
            time.sleep(refresh_interval)
            st.rerun()
    
    def _render_overview_page(self):
        """Render system overview page"""
        st.title("📊 System Overview")
        
        # Get system overview data
        overview_data = self._get_system_overview()
        
        if overview_data:
            # Key metrics row
            col1, col2, col3, col4 = st.columns(4)
            
            key_metrics = overview_data.get('key_metrics', {})
            
            with col1:
                temp_data = key_metrics.get('temperature', {})
                st.metric(
                    label="🌡️ Temperature",
                    value=f"{temp_data.get('value', 0):.1f}°C",
                    delta=None
                )
            
            with col2:
                energy_data = key_metrics.get('energy_consumption', {})
                st.metric(
                    label="⚡ Energy",
                    value=f"{energy_data.get('value', 0):.0f}kW",
                    delta=None
                )
            
            with col3:
                efficiency_data = key_metrics.get('efficiency', {})
                st.metric(
                    label="📊 Efficiency",
                    value=f"{efficiency_data.get('value', 0):.1f}%",
                    delta=None
                )
            
            with col4:
                availability_data = key_metrics.get('system_availability', {})
                st.metric(
                    label="🔧 Availability",
                    value=f"{availability_data.get('value', 0):.1f}%",
                    delta=None
                )
            
            st.markdown("---")
            
            # System status and alerts
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("System Status")
                status_data = {
                    'Active Alerts': overview_data.get('active_alerts', 0),
                    'Total Dashboards': overview_data.get('total_dashboards', 0),
                    'Total Metrics': overview_data.get('total_metrics', 0)
                }
                
                for key, value in status_data.items():
                    st.write(f"**{key}:** {value}")
            
            with col2:
                st.subheader("Quick Actions")
                
                if st.button("🔄 Restart Monitoring"):
                    st.info("Monitoring restart requested")
                
                if st.button("📋 Export Report"):
                    st.info("Report export requested")
                
                if st.button("⚙️ System Settings"):
                    st.info("Settings page would open here")
        
        else:
            st.error("Unable to load system overview data")
    
    def _render_operational_page(self):
        """Render operational dashboard"""
        st.title("⚙️ Operational Dashboard")
        
        # Get operational data
        dashboard_data = self._get_dashboard_data('operational')
        
        if dashboard_data:
            # Temperature section
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("🌡️ Temperature Monitoring")
                temp_chart = self._create_temperature_chart()
                if temp_chart:
                    st.plotly_chart(temp_chart, use_container_width=True)
            
            with col2:
                st.subheader("⚡ Energy Consumption")
                energy_chart = self._create_energy_chart()
                if energy_chart:
                    st.plotly_chart(energy_chart, use_container_width=True)
            
            # Real-time metrics
            st.subheader("📊 Real-time Metrics")
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                current_temp = self._get_current_metric('temperature')
                if current_temp:
                    st.metric(
                        label="Current Temperature",
                        value=f"{current_temp:.1f}°C",
                        delta=f"{current_temp - 150:.1f}" if current_temp else None
                    )
            
            with col2:
                current_energy = self._get_current_metric('energy_consumption')
                if current_energy:
                    st.metric(
                        label="Power Consumption",
                        value=f"{current_energy:.0f}kW",
                        delta=f"{current_energy - 1000:.0f}" if current_energy else None
                    )
            
            with col3:
                current_efficiency = self._get_current_metric('efficiency')
                if current_efficiency:
                    st.metric(
                        label="System Efficiency",
                        value=f"{current_efficiency:.1f}%",
                        delta=f"{current_efficiency - 85:.1f}" if current_efficiency else None
                    )
        
        else:
            st.error("Unable to load operational dashboard data")
    
    def _render_performance_page(self):
        """Render performance dashboard"""
        st.title("📈 Performance Dashboard")
        
        # Performance metrics
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("System Performance Trends")
            performance_chart = self._create_performance_chart()
            if performance_chart:
                st.plotly_chart(performance_chart, use_container_width=True)
        
        with col2:
            st.subheader("Efficiency Analysis")
            efficiency_chart = self._create_efficiency_chart()
            if efficiency_chart:
                st.plotly_chart(efficiency_chart, use_container_width=True)
        
        # Performance table
        st.subheader("Performance Summary")
        performance_data = self._get_performance_data()
        if performance_data:
            df = pd.DataFrame(performance_data)
            st.dataframe(df, use_container_width=True)
    
    def _render_alerts_page(self):
        """Render alerts dashboard"""
        st.title("🚨 Alerts & Notifications")
        
        # Get alert data
        alert_data = self._get_alert_summary()
        
        if alert_data:
            # Alert summary
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("Active Alerts", alert_data.get('active_alerts_count', 0))
            
            with col2:
                st.metric("Alerts (24h)", alert_data.get('alerts_last_24h', 0))
            
            with col3:
                critical_count = alert_data.get('severity_breakdown', {}).get('critical', 0)
                st.metric("Critical", critical_count)
            
            with col4:
                warning_count = alert_data.get('severity_breakdown', {}).get('warning', 0)
                st.metric("Warnings", warning_count)
            
            st.markdown("---")
            
            # Active alerts
            st.subheader("🔴 Active Alerts")
            recent_alerts = alert_data.get('recent_alerts', [])
            
            if recent_alerts:
                for alert in recent_alerts:
                    severity_icon = {
                        'critical': '🔴',
                        'error': '🟠',
                        'warning': '🟡',
                        'info': '🔵'
                    }.get(alert.get('severity', 'info'), '🔵')
                    
                    st.warning(f"{severity_icon} **{alert.get('rule_name', 'Unknown')}**: {alert.get('description', 'No description')}")
            else:
                st.success("✅ No active alerts")
            
            # Alert trends
            st.subheader("📈 Alert Trends")
            alert_trends = alert_data.get('alert_trends', {})
            
            col1, col2 = st.columns(2)
            with col1:
                st.metric("Alerts Today", alert_trends.get('total_today', 0))
            with col2:
                st.metric("Resolved Today", alert_trends.get('resolved_today', 0))
        
        else:
            st.error("Unable to load alert data")
    
    def _render_energy_page(self):
        """Render energy dashboard"""
        st.title("⚡ Energy Management")
        
        # Energy metrics
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("Energy Consumption")
            energy_chart = self._create_energy_consumption_chart()
            if energy_chart:
                st.plotly_chart(energy_chart, use_container_width=True)
        
        with col2:
            st.subheader("Cost Analysis")
            cost_chart = self._create_cost_analysis_chart()
            if cost_chart:
                st.plotly_chart(cost_chart, use_container_width=True)
        
        # Energy efficiency
        st.subheader("Energy Efficiency Metrics")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            efficiency = self._get_current_metric('efficiency')
            if efficiency:
                st.metric("Current Efficiency", f"{efficiency:.1f}%")
        
        with col2:
            st.metric("Energy Saved Today", "15.2%")
        
        with col3:
            st.metric("Cost Savings", "$342")
    
    def _get_system_overview(self) -> Optional[Dict[str, Any]]:
        """Get system overview data"""
        try:
            if self.dashboard_manager:
                return asyncio.run(self.dashboard_manager.get_system_overview())
            elif self.api_url:
                response = requests.get(f"{self.api_url}/api/v1/overview", timeout=5)
                if response.status_code == 200:
                    return response.json()
            return None
        except Exception as e:
            logger.error(f"Error getting system overview: {e}")
            return None
    
    def _get_dashboard_data(self, dashboard_id: str) -> Optional[Dict[str, Any]]:
        """Get dashboard data"""
        try:
            if self.dashboard_manager:
                return asyncio.run(self.dashboard_manager.get_dashboard_data(dashboard_id))
            elif self.api_url:
                response = requests.get(f"{self.api_url}/api/v1/dashboards/{dashboard_id}", timeout=5)
                if response.status_code == 200:
                    return response.json()
            return None
        except Exception as e:
            logger.error(f"Error getting dashboard data: {e}")
            return None
    
    def _get_alert_summary(self) -> Optional[Dict[str, Any]]:
        """Get alert summary"""
        try:
            if self.dashboard_manager:
                return asyncio.run(self.dashboard_manager.get_alert_summary())
            elif self.api_url:
                response = requests.get(f"{self.api_url}/api/v1/alerts", timeout=5)
                if response.status_code == 200:
                    return response.json()
            return None
        except Exception as e:
            logger.error(f"Error getting alert summary: {e}")
            return None
    
    def _get_current_metric(self, metric_name: str) -> Optional[float]:
        """Get current metric value"""
        try:
            if self.dashboard_manager:
                return asyncio.run(self.dashboard_manager.metric_calculator.calculate_metric(metric_name))
            elif self.api_url:
                response = requests.get(f"{self.api_url}/api/v1/metrics/{metric_name}/current", timeout=5)
                if response.status_code == 200:
                    return response.json().get('value')
            return None
        except Exception as e:
            logger.error(f"Error getting current metric: {e}")
            return None
    
    def _create_temperature_chart(self) -> Optional[go.Figure]:
        """Create temperature chart"""
        try:
            # Mock data for demonstration
            timestamps = pd.date_range(start=datetime.now() - timedelta(hours=2), 
                                     end=datetime.now(), freq='5min')
            temps = [150 + 5 * np.sin(i/10) + np.random.normal(0, 1) for i in range(len(timestamps))]
            
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=timestamps,
                y=temps,
                mode='lines+markers',
                name='Temperature',
                line=dict(color='red', width=2)
            ))
            
            fig.update_layout(
                title="Tank Temperature (°C)",
                xaxis_title="Time",
                yaxis_title="Temperature (°C)",
                height=300
            )
            
            return fig
        except Exception as e:
            logger.error(f"Error creating temperature chart: {e}")
            return None
    
    def _create_energy_chart(self) -> Optional[go.Figure]:
        """Create energy consumption chart"""
        try:
            import numpy as np
            
            # Mock data
            timestamps = pd.date_range(start=datetime.now() - timedelta(hours=2), 
                                     end=datetime.now(), freq='5min')
            energy = [1000 + 100 * np.sin(i/15) + np.random.normal(0, 20) for i in range(len(timestamps))]
            
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=timestamps,
                y=energy,
                mode='lines+markers',
                name='Energy',
                line=dict(color='blue', width=2)
            ))
            
            fig.update_layout(
                title="Energy Consumption (kW)",
                xaxis_title="Time",
                yaxis_title="Power (kW)",
                height=300
            )
            
            return fig
        except Exception as e:
            logger.error(f"Error creating energy chart: {e}")
            return None
    
    def _create_performance_chart(self) -> Optional[go.Figure]:
        """Create performance chart"""
        try:
            import numpy as np
            
            # Mock performance data
            timestamps = pd.date_range(start=datetime.now() - timedelta(days=7), 
                                     end=datetime.now(), freq='1H')
            performance = [85 + 10 * np.sin(i/24) + np.random.normal(0, 2) for i in range(len(timestamps))]
            
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=timestamps,
                y=performance,
                mode='lines',
                name='Performance',
                line=dict(color='green', width=2)
            ))
            
            fig.update_layout(
                title="System Performance (7 days)",
                xaxis_title="Time",
                yaxis_title="Performance (%)",
                height=400
            )
            
            return fig
        except Exception as e:
            logger.error(f"Error creating performance chart: {e}")
            return None
    
    def _create_efficiency_chart(self) -> Optional[go.Figure]:
        """Create efficiency chart"""
        try:
            import numpy as np
            
            # Mock efficiency data
            categories = ['Heating', 'Thermal Loss', 'Control', 'Overall']
            efficiency_values = [92.5, 87.3, 95.1, 89.2]
            
            fig = go.Figure(data=[
                go.Bar(x=categories, y=efficiency_values, 
                      marker_color=['red', 'orange', 'blue', 'green'])
            ])
            
            fig.update_layout(
                title="System Efficiency by Component",
                xaxis_title="Component",
                yaxis_title="Efficiency (%)",
                height=400
            )
            
            return fig
        except Exception as e:
            logger.error(f"Error creating efficiency chart: {e}")
            return None
    
    def _create_energy_consumption_chart(self) -> Optional[go.Figure]:
        """Create energy consumption chart"""
        try:
            import numpy as np
            
            # Mock hourly energy data
            hours = list(range(24))
            consumption = [800 + 400 * np.sin(h/4) + np.random.normal(0, 50) for h in hours]
            
            fig = go.Figure()
            fig.add_trace(go.Bar(
                x=hours,
                y=consumption,
                name='Energy Consumption',
                marker_color='blue'
            ))
            
            fig.update_layout(
                title="Daily Energy Consumption Pattern",
                xaxis_title="Hour of Day",
                yaxis_title="Energy (kWh)",
                height=400
            )
            
            return fig
        except Exception as e:
            logger.error(f"Error creating energy consumption chart: {e}")
            return None
    
    def _create_cost_analysis_chart(self) -> Optional[go.Figure]:
        """Create cost analysis chart"""
        try:
            # Mock cost data
            categories = ['Baseline', 'Current', 'Optimized']
            costs = [1200, 950, 800]
            colors = ['gray', 'blue', 'green']
            
            fig = go.Figure(data=[
                go.Bar(x=categories, y=costs, marker_color=colors)
            ])
            
            fig.update_layout(
                title="Cost Comparison ($)",
                xaxis_title="Scenario",
                yaxis_title="Daily Cost ($)",
                height=400
            )
            
            return fig
        except Exception as e:
            logger.error(f"Error creating cost analysis chart: {e}")
            return None
    
    def _get_performance_data(self) -> List[Dict[str, Any]]:
        """Get performance data for table"""
        try:
            return [
                {'Metric': 'Temperature Stability', 'Value': '94.2%', 'Target': '95%', 'Status': '🟡'},
                {'Metric': 'Energy Efficiency', 'Value': '89.2%', 'Target': '90%', 'Status': '🟡'},
                {'Metric': 'System Uptime', 'Value': '99.8%', 'Target': '99.5%', 'Status': '🟢'},
                {'Metric': 'Prediction Accuracy', 'Value': '92.1%', 'Target': '90%', 'Status': '🟢'},
                {'Metric': 'Response Time', 'Value': '45ms', 'Target': '< 100ms', 'Status': '🟢'}
            ]
        except Exception as e:
            logger.error(f"Error getting performance data: {e}")
            return []


def create_streamlit_dashboard(dashboard_manager: Optional[DashboardManager] = None, 
                              api_url: Optional[str] = None) -> StreamlitDashboard:
    """Create a Streamlit dashboard instance"""
    return StreamlitDashboard(dashboard_manager, api_url)


# Main Streamlit app
def main():
    """Main Streamlit application"""
    if not STREAMLIT_AVAILABLE:
        print("Streamlit not available. Please install streamlit and required dependencies.")
        return
    
    # Create dashboard
    dashboard = create_streamlit_dashboard(api_url="http://localhost:8000")
    
    # Run dashboard
    dashboard.run_dashboard()


if __name__ == "__main__":
    main()