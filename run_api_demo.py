#!/usr/bin/env python3
"""
Digital Twin API Demo
Demonstrates the full system with API endpoints
"""

import asyncio
import logging
import sys
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def create_mock_config():
    """Create a mock configuration for demo"""
    return {
        'digital_twin': {
            'engine': {
                'reconciler_type': 'kalman',
                'update_frequency': 10.0
            },
            'twins': {
                'asphalt_heating': {
                    'name': 'Asphalt Heating System',
                    'description': 'Demo digital twin for asphalt heating control',
                    'update_frequency': 5.0,
                    'synchronization_threshold': 0.05,
                    'physics_parameters': {
                        'thermal_conductivity': 0.5,
                        'specific_heat': 920.0,
                        'density': 2400.0
                    },
                    'scada_tags': ['temperature_01', 'heat_flux_01']
                }
            }
        },
        'api': {
            'host': '0.0.0.0',
            'port': 8000,
            'cors_enabled': True
        }
    }

async def demo_digital_twin_system():
    """Demonstrate the digital twin system"""
    try:
        from digital_twin.core import DigitalTwinEngine, TwinConfiguration
        from src.api.main import create_app
        
        logger.info("🚀 Starting Digital Twin System Demo...")
        
        # Create components
        config = await create_mock_config()
        twin_engine = DigitalTwinEngine()
        scada_clients = {}  # Mock SCADA clients
        
        # Create FastAPI app
        app = create_app(config, twin_engine, scada_clients)
        
        # Create a demo twin
        twin_config = TwinConfiguration(
            twin_id="demo_asphalt_heater_001",
            name="Demo Asphalt Heating System",
            description="Demonstration digital twin for asphalt heating control",
            physical_asset_id="demo_heater_001",
            model_version="v1.0",
            update_frequency=2.0,
            synchronization_threshold=0.1,
            physics_parameters={
                'thermal_conductivity': 0.5,
                'specific_heat': 920.0,
                'density': 2400.0,
                'ambient_temperature': 20.0
            },
            scada_tags=['temperature_01', 'temperature_02', 'heat_flux_01']
        )
        
        # Create and start twin
        twin = await twin_engine.create_twin(twin_config)
        await twin_engine.start_twin(twin_config.twin_id)
        
        logger.info(f"✅ Created and started demo twin: {twin_config.twin_id}")
        
        # Simulate some measurements
        for i in range(5):
            measurements = {
                'temperature_01': 85.0 + i * 2.5,
                'temperature_02': 82.0 + i * 2.0,
                'heat_flux_01': 1250.0 + i * 50.0
            }
            
            await twin_engine.update_twin_state(twin_config.twin_id, measurements)
            
            state = await twin_engine.get_twin_state(twin_config.twin_id)
            if state:
                logger.info(f"📊 Twin state update {i+1}: "
                           f"confidence={state.model_confidence:.3f}, "
                           f"step={state.simulation_step}")
            
            await asyncio.sleep(1)
        
        logger.info("✅ Demo completed successfully!")
        
        # Show API information
        logger.info("🌐 API Server Configuration:")
        logger.info(f"   Host: {config['api']['host']}")
        logger.info(f"   Port: {config['api']['port']}")
        logger.info("   Available endpoints:")
        logger.info("   - GET  /health (Health check)")
        logger.info("   - GET  /api/v1/twins (List all twins)")
        logger.info(f"   - GET  /api/v1/twins/{twin_config.twin_id} (Get twin details)")
        logger.info(f"   - GET  /api/v1/twins/{twin_config.twin_id}/state (Get twin state)")
        logger.info("   - POST /api/v1/twins (Create new twin)")
        logger.info("   - GET  /docs (API documentation)")
        
        return app, twin_engine, twin_config
        
    except Exception as e:
        logger.error(f"❌ Demo failed: {e}")
        return None, None, None

async def run_api_server():
    """Run the API server with demo data"""
    try:
        import uvicorn
        
        # Create demo system
        app, twin_engine, twin_config = await demo_digital_twin_system()
        
        if not app:
            logger.error("Failed to create demo system")
            return False
        
        logger.info("🚀 Starting API Server...")
        logger.info("📡 Server will be available at: http://localhost:8000")
        logger.info("📚 API Documentation: http://localhost:8000/docs")
        logger.info("🔍 Health Check: http://localhost:8000/health")
        logger.info("")
        logger.info("Press Ctrl+C to stop the server")
        
        # Configure and run server
        config = uvicorn.Config(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            reload=False
        )
        
        server = uvicorn.Server(config)
        await server.serve()
        
        return True
        
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
        return True
    except Exception as e:
        logger.error(f"❌ API server error: {e}")
        return False

def main():
    """Main entry point"""
    logger.info("🎯 Digital Twin System API Demo")
    logger.info("=" * 50)
    
    try:
        success = asyncio.run(run_api_server())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("👋 Demo interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()