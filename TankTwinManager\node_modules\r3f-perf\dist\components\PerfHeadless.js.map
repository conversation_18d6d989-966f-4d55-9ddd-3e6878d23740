{"version": 3, "file": "PerfHeadless.js", "sources": ["../../src/components/PerfHeadless.tsx"], "sourcesContent": null, "names": ["THREE", "useThree", "set<PERSON>erf", "useMemo", "PerfLib", "G<PERSON><PERSON><PERSON>", "chart", "get<PERSON>erf", "emitEvent", "useEffect", "overLimitFps", "addEffect", "addAfterEffect", "countGeoDrawCalls", "addTail"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,MAAM,wBAAwBA,iBAAM,SAAS,UAAU;AACvD,MAAM,wBAAwBA,iBAAM,SAAS,UAAU;AACvD,MAAM,mBAAmBA,iBAAM,SAAS,UAAU;AAElD,MAAM,QAAQ,CAAC,SAAS,aAAa,UAAU,OAAO;AACtD,MAAM,SAAS,CAAC,OAAO,OAAO,OAAO,KAAK;AAEnC,IAAI,oBAAoB;AAAA,EAC7B,OAAO;AACT;AACO,IAAI,eAAe;AAAA,EACxB,OAAO;AACT;AAEA,MAAM,SAAS,CAAC,SAAiB;AAC/B,MAAI,IAAS,KAAK;AAEd,MAAA,EAAE,MAAM,+EAA+E;AAC3F,MAAI,MAAM,MAAM;AACP,WAAA;AAAA,EACT;AACO,SAAA;AACT;AAEA,MAAM,eAAe,CAAC,UAA0B,+BAAoC;AAC9E,MAAA,CAAC,SAAS,SAAS;AACrB,aAAS,UAAU;EACrB;AAEA,MAAI,SAAS,WAAW,CAAC,SAAS,QAAQ,SAAS;AACjD,aAAS,UAAU,OAAO,OAAO,SAAS,WAAW,IAAI;AAAA,MACvD,SAAS,SAAS;AAAA,IAAA,CACnB;AAAA,EACH;AAEA,QAAM,OAAO,SAAS;AAElB,MAAA,CAAC,2BAA2B,IAAI,GAAG;AACrC,+BAA2B,IAAI,IAAI;AAAA,MACjC,QAAQ,CAAC;AAAA,MACT;AAAA,IAAA;AAEF,aAAS,cAAc;AAAA,EACzB;AACA,WAAS,cAAc;AAChB,SAAA;AACT;AAUA,MAAM,cAAc,CAAC,SAAiB,SAAS;AAOlC,MAAA,eAA8B,CAAC,EAAE,WAAW,eAAe,OAAO,aAAa,mBAAmB;AAC7G,QAAM,EAAE,IAAI,MAAM,IAAIC,MAAS,SAAA;AACvBC,QAAAA,QAAA,EAAE,IAAI,MAAA,CAAO;AAEf,QAAA,UAAUC,MAAAA,QAAQ,MAAM;AACtBC,UAAAA,WAAU,IAAIC,gBAAO;AAAA,MACzB,UAAU;AAAA,MACV;AAAA,MACA,UAAU,QAAQ,MAAM,SAAS;AAAA,MACjC,SAAS,QAAQ,MAAM,KAAK;AAAA,MAC5B,eAAe,iBAAiB;AAAA,MAChC,IAAI,GAAG,WAAW;AAAA,MAClB,aAAa,CAACC,WAAiB;AACrBJ,cAAAA,QAAA,EAAE,OAAAI,OAAAA,CAAO;AAAA,MACnB;AAAA,MACA,aAAa,CAAC,WAAgB;AAC5B,cAAM,MAAM;AAAA,UACV,WAAW,OAAO;AAAA,UAClB,KAAK,OAAO;AAAA,UACZ,KAAK,OAAO;AAAA,UACZ,KAAK,OAAO;AAAA,UACZ,KAAK,OAAO;AAAA,UACZ,WAAW,OAAO;AAAA,UAClB,YAAY,OAAO;AAAA,QAAA;AAEbJ,sBAAA;AAAA,UACN;AAAA,QAAA,CACD;AACK,cAAA,EAAE,gBAAqBK,MAAAA;AACvB,cAAA,WAAgB,GAAG,KAAK;AAElB,oBAAA;AACA,oBAAA,GAAG,SAAS,SAAS;AACrB,oBAAA,GAAG,aAAa,SAAS;AACzB,oBAAA,GAAG,UAAU,SAAS;AACtB,oBAAA,GAAG,SAAS,SAAS;AAErB,oBAAA,IAAI,OAAO,OAAO;AAClB,oBAAA,IAAI,OAAO,OAAO;AAClB,oBAAA,IAAI,OAAO,OAAO;AAClB,oBAAA,IAAI,OAAO,OAAO;AAE9B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC/B,gBAAA,MAAM,MAAM,CAAC;AACb,gBAAA,QAAQ,SAAS,GAAG;AAC1B,cAAI,QAAQ,YAAY,IAAI,GAAG,GAAG,GAAG;AACvB,wBAAA,IAAI,GAAG,GAAG,IAAI;AAAA,UAC5B;AAAA,QACF;AAEA,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAChC,gBAAA,MAAM,OAAO,CAAC;AACd,gBAAA,QAAQ,OAAO,GAAG;AACxB,cAAI,QAAQ,YAAY,IAAI,IAAI,GAAG,GAAG;AACxB,wBAAA,IAAI,IAAI,GAAG,IAAI;AAAA,UAC7B;AAAA,QACF;AAGQL,sBAAA,EAAE,aAAa;AAEvBM,eAAAA,UAAU,OAAO,CAAC,KAAK,EAAE,CAAC;AAAA,MAC5B;AAAA,IAAA,CACD;AAIK,UAAA,MAAM,GAAG;AACf,QAAI,aAAa;AACjB,QAAI,WAAW;AAET,UAAA,eAAoB,IAAI,aAAa,2BAA2B;AACtE,UAAM,YAAY,IAAI,aAAa,IAAI,OAAO;AAE9C,QAAI,gBAAgB,MAAM;AACX,mBAAA,IAAI,aAAa,aAAa,uBAAuB;AACvD,iBAAA,IAAI,aAAa,aAAa,qBAAqB;AAAA,IAChE;AAEA,QAAI,CAAC,UAAU;AACF,iBAAA;AAAA,IACb;AAEA,QAAI,CAAC,YAAY;AACF,mBAAA,IAAI,aAAa,IAAI,QAAQ;AAAA,IAC5C;AAEQN,kBAAA;AAAA,MACN,WAAW,OAAO,YAAY,IAAI;AAAA,MAClC,OAAO;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,QACV,QAAQ;AAAA,MACV;AAAA,IAAA,CACD;AAEK,UAAA,gCAAgB;AAChB,UAAA,qCAAqB;AAC3B,WAAO,eAAeF,iBAAM,MAAM,WAAW,kBAAkB;AAAA,MAC7D,MAAM;AACJ,eAAO,IAAI,SAAc;;AACvB,cAAII,UAAS;AACXA,qBAAQ,MAAM,UAAU;AAAA,UAC1B;AACA,0BAAU,IAAI,IAAI,MAAlB,mBAAsB,GAAG;AAAA,QAAI;AAAA,MAEjC;AAAA,MACA,IAAI,UAAU;AACF,kBAAA,IAAI,MAAM,QAAQ;AAAA,MAC9B;AAAA,MACA,cAAc;AAAA,IAAA,CACf;AAED,WAAO,eAAeJ,iBAAM,MAAM,WAAW,iBAAiB;AAAA,MAC5D,MAAM;AACJ,eAAO,IAAI,SAAc;;AACvB,cAAII,UAAS;AACXA,qBAAQ,IAAI,UAAU;AAAA,UACxB;AACA,+BAAe,IAAI,IAAI,MAAvB,mBAA2B,GAAG;AAAA,QAAI;AAAA,MAEtC;AAAA,MACA,IAAI,UAAU;AACG,uBAAA,IAAI,MAAM,QAAQ;AAAA,MACnC;AAAA,MACA,cAAc;AAAA,IAAA,CACf;AAEMA,WAAAA;AAAAA,EACT,GAAG,CAAE,CAAA;AAELK,QAAAA,UAAU,MAAM;AACd,QAAI,SAAS;AACX,cAAQ,YAAY,aAAa;AACjC,UAAI,cAAc,OAAO;AACfP,cAAAA,QAAA,EAAE,iBAAiB,MAAA,CAAO;AAClCQ,iBAAA,aAAa,QAAQ;AACrBA,iBAAA,aAAa,cAAc;AAAA,MAC7B;AACQ,cAAA,WAAU,+BAAO,OAAM;AACvB,cAAA,YAAW,+BAAO,WAAU;AAAA,IACtC;AAAA,EAAA,GACC,CAAC,WAAW,SAAS,+BAAO,QAAQ,+BAAO,EAAE,CAAC;AAEjDD,QAAAA,UAAU,MAAM;AACd,QAAI,cAAc;AACVT,uBAAA,SAAS,UAAU,oBAAoB,WAAY;AACvD,YAAI,KAAK,0BAA0B,UAAU,CAAC,GAAa;AACvC,4BAAA;AAAA,QACpB;AAEsB,8BAAA,MAAM,MAAM,SAAS;AAAA,MAAA;AAEvCA,uBAAA,SAAS,UAAU,oBAAoB,WAAY;AACrC,0BAAA;AAEI,8BAAA,MAAM,MAAM,SAAS;AAAA,MAAA;AAEvCA,uBAAA,SAAS,UAAU,eAAe,WAAY;AACrC,qBAAA;AAEI,yBAAA,MAAM,MAAM,SAAS;AAAA,MAAA;AAAA,IAE1C;AAEA,OAAG,KAAK,YAAY;AACpB,QAAI,YAAiB;AACrB,QAAI,iBAAsB;AACtB,QAAA,CAAC,GAAG,KAAM;AAEF,gBAAAW,MAAAA,UAAU,SAAS,gBAAgB;AACzC,UAAAJ,MAAAA,UAAU,QAAQ;AACZL,cAAAA,QAAA,EAAE,QAAQ,MAAA,CAAO;AAAA,MAC3B;AAEA,UAAI,OAAO,aAAa;AACf,eAAA,YAAY,KAAK,aAAa;AACrC,gBAAQ,oBAAoB;AAAA,MAC9B;AAEA,mBAAa,SAAS;AACtB,wBAAkB,QAAQ;AAC1B,mBAAa,QAAQ;AAErB,UAAI,GAAG,MAAM;AACX,WAAG,KAAK;MACV;AAAA,IAAA,CACD;AAEgB,qBAAAU,MAAAA,eAAe,SAAS,iBAAiB;;AACpD,UAAA,WAAW,CAAC,QAAQ,QAAQ;AAC9B,gBAAQ,UAAU,OAAO,YAAY,IAAK,CAAA;AAE1C,YAAI,aAAa,OAAO,OAAO,wBAAwB,aAAa;AAC1D,kBAAA,WAAW,oBAAoB,QAAQ,OAAO;AAAA,QACxD;AAAA,MACF;AACA,UAAI,aAAa;AACf,cAAM,6BAAkC,CAAA;AAClC,cAAA,+BAA8B;AAE9B,cAAA,SAAS,SAAS,mBAAmB,QAAQ;AACjD,cAAI,kBAAkBZ,iBAAM,QAAQ,kBAAkBA,iBAAM,QAAQ;AAClE,gBAAI,OAAO,UAAU;AACf,kBAAA,OAAO,OAAO,SAAS;AAErB,oBAAA,WAAW,MAAM,QAAQ,OAAO,QAAQ,KAAK,OAAO,SAAS,SAAS;AAC5E,kBAAI,UAAU;AACZ,uBAAO,aAAa,OAAO,SAAS,CAAC,GAAG,0BAA0B;AAAA,cAAA,OAC7D;AACE,uBAAA,aAAa,OAAO,UAAU,0BAA0B;AAAA,cACjE;AAEA,yCAA2B,IAAI,EAAE,OAAO,OAAO,IAAI,IAAI;AAAA,YACzD;AAAA,UACF;AAAA,QAAA,CACD;AAED,6CAAI,SAAJ,mBAAU,aAAV,mBAAoB,QAAQ,CAAC,YAAiB;AAC5C,gBAAM,kBAAkB,QAAQ,SAAS,MAAM,GAAG;AAClD,gBAAM,iBAAiB,gBAAgB,gBAAgB,UAAU,WAAW,IAAI,CAAC;AACjF,cAAI,OAAO,cAAc,KAAK,2BAA2B,cAAc,GAAG;AACxE,kBAAM,EAAE,UAAU,OAAO,IAAI,2BAA2B,cAAc;AACtE,qBAAS,IAAI,gBAAgB;AAAA,cAC3B;AAAA,cACA;AAAA,cACA;AAAA,cACA,YAAY;AAAA,gBACV,OAAO;AAAA,gBACP,MAAM;AAAA,gBACN,MAAM,CAAC;AAAA,cACT;AAAA,cACA,QAAQ;AAAA,cACR,SAAS;AAAA,YAAA,CACV;AAAA,UACH;AAAA,QAAA;AAGF,YAAI,SAAS,SAASO,MAAQ,QAAA,EAAE,SAAS,MAAM;AAC7CM,4BAAA,kBAAkB,QAAQ;AAClBX,wBAAA;AAAA,YACN;AAAA,YACA,uBAAuBK,MAAAA,QAAU,EAAA;AAAA,UAAA,CAClC;AAAA,QACH;AAAA,MACF;AAAA,IAAA,CACD;AAED,WAAO,MAAM;AACX,UAAI,SAAS;AACP,YAAA,OAAO,OAAO,uBAAuB,aAAa;AAC7C,iBAAA,mBAAmB,QAAQ,QAAQ;AAAA,QAC5C;AACO,eAAA,qBAAqB,QAAQ,KAAK;AAClC,eAAA,qBAAqB,QAAQ,YAAY;AAAA,MAClD;AAEA,UAAI,cAAc;AACVP,yBAAA,SAAS,UAAU,oBAAoB;AAAA,MAC/C;AAEU;AACK;IAAA;AAAA,KAEhB,CAAC,SAAS,IAAI,OAAO,YAAY,CAAC;AAErCS,QAAAA,UAAU,MAAM;AACR,UAAA,QAAQK,cAAQ,SAAS,qBAAqB;AAClD,UAAI,SAAS;AACX,gBAAQ,SAAS;AACjB,qBAAa,QAAQ;AACrB,0BAAkB,QAAQ;AAClBZ,sBAAA;AAAA,UACN,QAAQ;AAAA,UACR,KAAK;AAAA,YACH,WAAW;AAAA,YACX,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,WAAW;AAAA,YACX,YAAY;AAAA,UACd;AAAA,QAAA,CACD;AAAA,MACH;AACO,aAAA;AAAA,IAAA,CACR;AAED,WAAO,MAAM;AACL;IAAA;AAAA,EAEV,GAAG,CAAE,CAAA;AAEE,SAAA;AACT;;;;"}