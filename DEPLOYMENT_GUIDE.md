# MLOps Digital Twin Platform - Deployment Guide

This guide provides comprehensive instructions for deploying the MLOps Digital Twin Platform for Predictive Asphalt Tank Control using various deployment methods.

## 🏗️ Architecture Overview

The system is built as a microservices architecture with the following components:

### Core Microservices
- **API Gateway** (Port 8000) - Main entry point and request routing
- **Tank Management Service** (Port 8080) - Tank configuration and lifecycle
- **Control Service** (Port 8082) - Control algorithms and automation
- **Data Ingestion Service** (Port 8081) - SCADA data collection
- **Event Bus Service** (Port 8083) - Event-driven messaging
- **ML Service** (Port 8084) - Machine learning and predictions
- **Monitoring Service** (Port 8085) - System monitoring and metrics

### Infrastructure Services
- **Redis** - Caching and real-time data
- **PostgreSQL** - Configuration and relational data
- **InfluxDB** - Time-series sensor data
- **Apache Kafka** - Event streaming
- **MinIO** - Object storage
- **Prometheus** - Metrics collection
- **Grafana** - Dashboards and visualization

## 🚀 Deployment Options

### Option 1: Docker Compose (Recommended for Development)

#### Prerequisites
- Docker 20.10+
- Docker Compose 2.0+
- 8GB+ RAM
- 50GB+ disk space

#### Quick Start
```bash
# 1. Build all microservices
./build-microservices.sh

# 2. Start the entire system
docker-compose -f docker-compose.microservices.yml up -d

# 3. Check service status
docker-compose -f docker-compose.microservices.yml ps

# 4. View logs
docker-compose -f docker-compose.microservices.yml logs -f
```

#### Access Points
- **API Gateway**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Grafana Dashboard**: http://localhost:3000 (admin/admin123)
- **Prometheus**: http://localhost:9090
- **MinIO Console**: http://localhost:9001 (minioadmin/minioadmin)

### Option 2: Kubernetes (Recommended for Production)

#### Prerequisites
- Kubernetes 1.24+
- kubectl configured
- Helm 3.0+
- 16GB+ RAM
- 100GB+ storage
- GPU nodes (optional, for ML service)

#### Method A: Raw Kubernetes Manifests
```bash
# 1. Create namespace
kubectl apply -f k8s/namespace.yaml

# 2. Deploy infrastructure services
kubectl apply -f k8s/secrets-configmaps.yaml
kubectl apply -f k8s/infrastructure-deployments.yaml

# 3. Wait for infrastructure to be ready
kubectl wait --for=condition=ready pod -l component=database -n tank-system --timeout=300s

# 4. Deploy application services
kubectl apply -f k8s/tank-management-deployment.yaml
kubectl apply -f k8s/api-gateway-deployment.yaml

# 5. Check deployment status
kubectl get pods -n tank-system
kubectl get services -n tank-system
```

#### Method B: Helm Chart (Recommended)
```bash
# 1. Add dependencies
helm dependency update ./helm/tank-system

# 2. Install with default values
helm install tank-system ./helm/tank-system \
  --namespace tank-system \
  --create-namespace

# 3. Install with custom values
helm install tank-system ./helm/tank-system \
  --namespace tank-system \
  --create-namespace \
  --values production-values.yaml

# 4. Check deployment
helm status tank-system -n tank-system
kubectl get pods -n tank-system
```

#### Production Values Example
```yaml
# production-values.yaml
global:
  imageRegistry: "your-registry.com"
  storageClass: "fast-ssd"

apiGateway:
  replicaCount: 5
  ingress:
    enabled: true
    hosts:
      - host: tank-api.company.com

tankManagement:
  replicaCount: 3
  resources:
    requests:
      memory: "1Gi"
      cpu: "500m"
    limits:
      memory: "2Gi"
      cpu: "1000m"

postgresql:
  primary:
    persistence:
      size: 100Gi
    resources:
      requests:
        memory: 1Gi
        cpu: 500m

monitoring:
  prometheus:
    enabled: true
    server:
      persistence:
        size: 100Gi
  grafana:
    enabled: true
```

### Option 3: Cloud Deployment

#### AWS EKS
```bash
# 1. Create EKS cluster
eksctl create cluster --name tank-system-cluster \
  --region us-west-2 \
  --nodegroup-name tank-nodes \
  --node-type m5.2xlarge \
  --nodes 3 \
  --nodes-min 2 \
  --nodes-max 6

# 2. Install load balancer controller
kubectl apply -f https://raw.githubusercontent.com/kubernetes-sigs/aws-load-balancer-controller/v2.5.4/docs/install/iam_policy.json

# 3. Deploy using Helm
helm install tank-system ./helm/tank-system \
  --namespace tank-system \
  --create-namespace \
  --set global.storageClass=gp3 \
  --set apiGateway.service.type=LoadBalancer
```

#### Azure AKS
```bash
# 1. Create AKS cluster
az aks create \
  --resource-group tank-system-rg \
  --name tank-system-cluster \
  --node-count 3 \
  --node-vm-size Standard_D4s_v3 \
  --enable-addons monitoring

# 2. Get credentials
az aks get-credentials --resource-group tank-system-rg --name tank-system-cluster

# 3. Deploy
helm install tank-system ./helm/tank-system \
  --namespace tank-system \
  --create-namespace \
  --set global.storageClass=managed-premium
```

#### Google GKE
```bash
# 1. Create GKE cluster
gcloud container clusters create tank-system-cluster \
  --machine-type=e2-standard-4 \
  --num-nodes=3 \
  --enable-autoscaling \
  --min-nodes=2 \
  --max-nodes=6

# 2. Get credentials
gcloud container clusters get-credentials tank-system-cluster

# 3. Deploy
helm install tank-system ./helm/tank-system \
  --namespace tank-system \
  --create-namespace \
  --set global.storageClass=ssd
```

## 🔧 Configuration

### Environment Variables
```bash
# Database Configuration
DATABASE_URL=********************************************/tank_config
REDIS_URL=redis://redis:6379
INFLUXDB_URL=http://influxdb:8086
INFLUXDB_TOKEN=tank-admin-token

# Messaging
KAFKA_BOOTSTRAP_SERVERS=kafka:29092
EVENT_BUS_URL=http://event-bus:8083

# SCADA Integration
OPCUA_ENDPOINT=opc.tcp://scada-server:4840
OPCUA_USERNAME=operator
OPCUA_PASSWORD=password123

# Security
JWT_SECRET_KEY=your-jwt-secret
CORS_ORIGINS=*

# Features
ENABLE_ML=true
ENABLE_DIGITAL_TWIN=true
ENABLE_PREDICTIVE_MAINTENANCE=true
```

### SCADA Configuration
Update `config/tank_system_config.yaml`:

```yaml
scada:
  opc_ua:
    enabled: true
    endpoint_url: "opc.tcp://your-scada-server:4840"
    username: "your-username"
    password: "your-password"
    security_policy: "Basic256Sha256"
    security_mode: "SignAndEncrypt"
```

### Tank Configuration
Define your tanks in the configuration:

```yaml
tanks:
  tank_001:
    name: "Primary Heating Tank"
    geometry:
      capacity: 50000.0
      diameter: 6.0
      height: 8.0
    sensors:
      - sensor_id: "TT_001_01"
        sensor_type: "temperature"
        node_id: "ns=2;s=Tank001.Zone1.Temperature"
```

## 🔍 Monitoring and Troubleshooting

### Health Checks
```bash
# Check all services
curl http://localhost:8000/health
curl http://localhost:8080/health
curl http://localhost:8082/health

# Kubernetes health checks
kubectl get pods -n tank-system
kubectl describe pod <pod-name> -n tank-system
```

### Logs
```bash
# Docker Compose
docker-compose -f docker-compose.microservices.yml logs -f <service-name>

# Kubernetes
kubectl logs -f deployment/tank-management-service -n tank-system
kubectl logs -f deployment/api-gateway -n tank-system
```

### Common Issues

#### 1. Services Can't Connect to Database
```bash
# Check database status
kubectl get pods -l app=postgres -n tank-system
kubectl logs -l app=postgres -n tank-system

# Verify connection string
kubectl get secret postgres-credentials -n tank-system -o yaml
```

#### 2. OPC UA Connection Failures
```bash
# Check SCADA configuration
kubectl get configmap tank-system-config -n tank-system -o yaml

# Test OPC UA connectivity
kubectl exec -it deployment/data-ingestion-service -n tank-system -- \
  python -c "from opcua import Client; client = Client('opc.tcp://scada-server:4840'); client.connect()"
```

#### 3. High Memory Usage
```bash
# Check resource usage
kubectl top pods -n tank-system
kubectl describe node

# Scale down if needed
kubectl scale deployment tank-management-service --replicas=2 -n tank-system
```

## 🔒 Security

### SSL/TLS Configuration
```bash
# Install cert-manager
kubectl apply -f https://github.com/jetstack/cert-manager/releases/download/v1.12.0/cert-manager.yaml

# Create cluster issuer
kubectl apply -f - <<EOF
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
EOF
```

### Network Policies
```bash
# Apply network policies
kubectl apply -f k8s/network-policies.yaml
```

### RBAC
```bash
# Create service accounts and roles
kubectl apply -f k8s/rbac.yaml
```

## 📊 Performance Tuning

### Resource Allocation
- **API Gateway**: 2-4 CPU cores, 1-2GB RAM
- **Tank Management**: 1-2 CPU cores, 1-2GB RAM
- **Control Service**: 2-4 CPU cores, 1-2GB RAM
- **ML Service**: 4-8 CPU cores, 4-8GB RAM, 1 GPU (optional)
- **Data Ingestion**: 1-2 CPU cores, 1-2GB RAM

### Scaling Guidelines
```bash
# Auto-scaling based on metrics
kubectl autoscale deployment api-gateway --cpu-percent=70 --min=2 --max=10 -n tank-system

# Manual scaling
kubectl scale deployment tank-management-service --replicas=5 -n tank-system
```

### Storage Optimization
- Use SSD storage for databases
- Configure appropriate retention policies
- Enable compression for time-series data

## 🔄 Backup and Recovery

### Database Backup
```bash
# PostgreSQL backup
kubectl exec -it postgres-0 -n tank-system -- \
  pg_dump -U postgres tank_config > backup.sql

# InfluxDB backup
kubectl exec -it influxdb-0 -n tank-system -- \
  influx backup /backup --bucket tank-data
```

### Configuration Backup
```bash
# Export configurations
kubectl get configmap -o yaml -n tank-system > config-backup.yaml
kubectl get secret -o yaml -n tank-system > secrets-backup.yaml
```

## 📈 Upgrade Process

### Rolling Updates
```bash
# Update image tag
helm upgrade tank-system ./helm/tank-system \
  --namespace tank-system \
  --set global.imageTag=v1.1.0 \
  --reuse-values

# Monitor rollout
kubectl rollout status deployment/api-gateway -n tank-system
```

### Rollback
```bash
# Rollback to previous version
helm rollback tank-system 1 -n tank-system

# Or rollback specific deployment
kubectl rollout undo deployment/tank-management-service -n tank-system
```

## 🆘 Support

### Getting Help
1. Check logs: `kubectl logs -f deployment/<service> -n tank-system`
2. Check events: `kubectl get events -n tank-system`
3. Check resource usage: `kubectl top pods -n tank-system`
4. Review configuration: `kubectl get configmap tank-system-config -o yaml`

### Useful Commands
```bash
# Port forwarding for debugging
kubectl port-forward service/api-gateway-service 8000:80 -n tank-system

# Execute commands in pods
kubectl exec -it deployment/tank-management-service -n tank-system -- /bin/bash

# Copy files from pods
kubectl cp tank-system/tank-management-service-xxx:/app/logs/app.log ./local-logs/
```

This deployment guide provides comprehensive instructions for deploying the Tank Control System in various environments. Choose the deployment method that best fits your infrastructure and requirements.