version: '3.8'

services:
  # Infrastructure Services
  redis:
    image: redis:7-alpine
    container_name: tank-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: tank-kafka
    restart: unless-stopped
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
    volumes:
      - kafka_data:/var/lib/kafka/data
    depends_on:
      - zookeeper
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 5

  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: tank-zookeeper
    restart: unless-stopped
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log

  postgres:
    image: postgres:15-alpine
    container_name: tank-postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: tank_config
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  influxdb:
    image: influxdb:2.7-alpine
    container_name: tank-influxdb
    restart: unless-stopped
    ports:
      - "8086:8086"
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: admin
      DOCKER_INFLUXDB_INIT_PASSWORD: admin123
      DOCKER_INFLUXDB_INIT_ORG: tank-org
      DOCKER_INFLUXDB_INIT_BUCKET: tank-data
      DOCKER_INFLUXDB_INIT_ADMIN_TOKEN: tank-admin-token
    volumes:
      - influxdb_data:/var/lib/influxdb2
    healthcheck:
      test: ["CMD", "influx", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  minio:
    image: minio/minio:RELEASE.2023-09-04T19-57-37Z
    container_name: tank-minio
    restart: unless-stopped
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Core Microservices
  event-bus:
    build:
      context: .
      target: event-bus-service
    container_name: tank-event-bus
    restart: unless-stopped
    ports:
      - "8083:8083"
    environment:
      REDIS_URL: redis://redis:6379
      KAFKA_BOOTSTRAP_SERVERS: kafka:29092
    depends_on:
      redis:
        condition: service_healthy
      kafka:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8083/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  data-ingestion:
    build:
      context: .
      target: data-ingestion-service
    container_name: tank-data-ingestion
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      REDIS_URL: redis://redis:6379
      KAFKA_BOOTSTRAP_SERVERS: kafka:29092
      INFLUXDB_URL: http://influxdb:8086
      INFLUXDB_TOKEN: tank-admin-token
      INFLUXDB_ORG: tank-org
      INFLUXDB_BUCKET: tank-data
    depends_on:
      redis:
        condition: service_healthy
      kafka:
        condition: service_healthy
      influxdb:
        condition: service_healthy
      event-bus:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  tank-management:
    build:
      context: .
      target: tank-management-service
    container_name: tank-management
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      DATABASE_URL: ********************************************/tank_config
      REDIS_URL: redis://redis:6379
      EVENT_BUS_URL: http://event-bus:8083
    volumes:
      - ./config:/app/config:ro
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      event-bus:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  control-service:
    build:
      context: .
      target: control-service
    container_name: tank-control
    restart: unless-stopped
    ports:
      - "8082:8082"
    environment:
      REDIS_URL: redis://redis:6379
      EVENT_BUS_URL: http://event-bus:8083
      TANK_MANAGEMENT_URL: http://tank-management:8080
    depends_on:
      redis:
        condition: service_healthy
      event-bus:
        condition: service_healthy
      tank-management:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  ml-service:
    build:
      context: .
      target: ml-service
    container_name: tank-ml
    restart: unless-stopped
    ports:
      - "8084:8084"
    environment:
      REDIS_URL: redis://redis:6379
      MINIO_ENDPOINT: minio:9000
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
      INFLUXDB_URL: http://influxdb:8086
      INFLUXDB_TOKEN: tank-admin-token
    volumes:
      - ml_models:/app/models
    depends_on:
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      influxdb:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8084/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  api-gateway:
    build:
      context: .
      target: api-gateway-service
    container_name: tank-api-gateway
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      TANK_MANAGEMENT_URL: http://tank-management:8080
      CONTROL_SERVICE_URL: http://control-service:8082
      DATA_INGESTION_URL: http://data-ingestion:8081
      ML_SERVICE_URL: http://ml-service:8084
      MONITORING_URL: http://monitoring:8085
      REDIS_URL: redis://redis:6379
    depends_on:
      tank-management:
        condition: service_healthy
      control-service:
        condition: service_healthy
      data-ingestion:
        condition: service_healthy
      ml-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  monitoring:
    build:
      context: .
      target: monitoring-service
    container_name: tank-monitoring
    restart: unless-stopped
    ports:
      - "8085:8085"
    environment:
      PROMETHEUS_URL: http://prometheus:9090
      GRAFANA_URL: http://grafana:3000
      REDIS_URL: redis://redis:6379
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8085/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: tank-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  grafana:
    image: grafana/grafana:10.0.3
    container_name: tank-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus

  # Development Tools
  jupyter:
    build:
      context: .
      target: development
    container_name: tank-jupyter
    restart: unless-stopped
    ports:
      - "8888:8888"
    environment:
      JUPYTER_ENABLE_LAB: yes
      JUPYTER_TOKEN: tank-jupyter-token
    volumes:
      - ./notebooks:/app/notebooks
      - jupyter_data:/app/data
    command: ["jupyter", "lab", "--ip=0.0.0.0", "--port=8888", "--no-browser", "--allow-root"]
    profiles:
      - development

volumes:
  redis_data:
  kafka_data:
  zookeeper_data:
  zookeeper_logs:
  postgres_data:
  influxdb_data:
  minio_data:
  prometheus_data:
  grafana_data:
  ml_models:
  jupyter_data:

networks:
  default:
    name: tank-network
    driver: bridge