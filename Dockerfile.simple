# Simple Docker build for Digital Twin Application Testing
FROM python:3.12-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH="/app:$PYTHONPATH"

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Install essential dependencies for testing
RUN pip install --no-cache-dir \
    numpy \
    scipy \
    pandas \
    matplotlib \
    pydantic \
    fastapi \
    uvicorn \
    pyyaml \
    click \
    asyncio-mqtt \
    aiohttp \
    requests \
    streamlit \
    plotly

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p /app/logs /app/data

# Expose port
EXPOSE 8000

# Default command
CMD ["python", "run_api_simple.py"]