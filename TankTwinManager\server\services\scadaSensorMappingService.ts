/**
 * SCADA Sensor Mapping Service
 * Handles mapping between real SCADA sensors and digital twin components
 * Provides data validation, transformation, and real-time synchronization
 */

import { 
  SCADASensor, 
  SCADATagGroup, 
  SCADAAlarmConfiguration,
  SCADA_SENSOR_CONFIG 
} from '../config/scada-sensor-mapping';

export interface SensorDataPoint {
  tagName: string;
  value: number | boolean | string;
  timestamp: Date;
  quality: 'good' | 'bad' | 'uncertain';
  source: 'real_sensor' | 'simulation' | 'manual';
}

export interface AlarmEvent {
  alarmId: string;
  tagName: string;
  alarmType: string;
  currentValue: number;
  setpoint: number;
  message: string;
  timestamp: Date;
  priority: 'critical' | 'high' | 'medium' | 'low';
  acknowledged: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: Date;
}

export interface SensorValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  transformedValue?: number | boolean | string;
}

export class SCADASensorMappingService {
  private sensorMap: Map<string, SCADASensor> = new Map();
  private alarmConfigs: Map<string, SCADAAlarmConfiguration> = new Map();
  private activeAlarms: Map<string, AlarmEvent> = new Map();
  private sensorData: Map<string, SensorDataPoint> = new Map();
  private lastUpdateTimes: Map<string, Date> = new Map();

  constructor() {
    this.initializeSensorMappings();
    this.initializeAlarmConfigurations();
  }

  private initializeSensorMappings() {
    // Initialize sensor mappings from configuration
    SCADA_SENSOR_CONFIG.tagGroups.forEach(group => {
      group.tags.forEach(sensor => {
        this.sensorMap.set(sensor.tagName, sensor);
      });
    });

    console.log(`Initialized ${this.sensorMap.size} SCADA sensor mappings`);
  }

  private initializeAlarmConfigurations() {
    // Create alarm configurations for sensors with alarm limits
    this.sensorMap.forEach((sensor, tagName) => {
      if (sensor.alarmLimits) {
        // High-High alarm
        if (sensor.alarmLimits.highHigh !== undefined) {
          this.alarmConfigs.set(`${tagName}_HH`, {
            alarmId: `${tagName}_HH`,
            tagName,
            alarmType: 'high',
            setpoint: sensor.alarmLimits.highHigh,
            deadband: (sensor.alarmLimits.highHigh - sensor.alarmLimits.high!) / 2,
            delay: 5,
            priority: 'critical',
            message: `${sensor.description} - High High Alarm`,
            actions: ['log', 'notify', 'shutdown'],
            acknowledgmentRequired: true,
            autoReset: false
          });
        }

        // High alarm
        if (sensor.alarmLimits.high !== undefined) {
          this.alarmConfigs.set(`${tagName}_H`, {
            alarmId: `${tagName}_H`,
            tagName,
            alarmType: 'high',
            setpoint: sensor.alarmLimits.high,
            deadband: 2,
            delay: 10,
            priority: 'high',
            message: `${sensor.description} - High Alarm`,
            actions: ['log', 'notify'],
            acknowledgmentRequired: true,
            autoReset: true
          });
        }

        // Low alarm
        if (sensor.alarmLimits.low !== undefined) {
          this.alarmConfigs.set(`${tagName}_L`, {
            alarmId: `${tagName}_L`,
            tagName,
            alarmType: 'low',
            setpoint: sensor.alarmLimits.low,
            deadband: 2,
            delay: 10,
            priority: 'high',
            message: `${sensor.description} - Low Alarm`,
            actions: ['log', 'notify'],
            acknowledgmentRequired: true,
            autoReset: true
          });
        }

        // Low-Low alarm
        if (sensor.alarmLimits.lowLow !== undefined) {
          this.alarmConfigs.set(`${tagName}_LL`, {
            alarmId: `${tagName}_LL`,
            tagName,
            alarmType: 'low',
            setpoint: sensor.alarmLimits.lowLow,
            deadband: (sensor.alarmLimits.low! - sensor.alarmLimits.lowLow) / 2,
            delay: 5,
            priority: 'critical',
            message: `${sensor.description} - Low Low Alarm`,
            actions: ['log', 'notify', 'shutdown'],
            acknowledgmentRequired: true,
            autoReset: false
          });
        }
      }
    });

    console.log(`Initialized ${this.alarmConfigs.size} alarm configurations`);
  }

  public updateSensorData(tagName: string, value: number | boolean | string, source: 'real_sensor' | 'simulation' | 'manual' = 'simulation'): boolean {
    const sensor = this.sensorMap.get(tagName);
    if (!sensor) {
      console.warn(`Unknown sensor tag: ${tagName}`);
      return false;
    }

    // Validate sensor data
    const validation = this.validateSensorData(sensor, value);
    if (!validation.isValid) {
      console.error(`Sensor validation failed for ${tagName}:`, validation.errors);
      return false;
    }

    // Create data point
    const dataPoint: SensorDataPoint = {
      tagName,
      value: validation.transformedValue !== undefined ? validation.transformedValue : value,
      timestamp: new Date(),
      quality: 'good',
      source
    };

    // Store sensor data
    this.sensorData.set(tagName, dataPoint);
    this.lastUpdateTimes.set(tagName, dataPoint.timestamp);

    // Check for alarms
    this.checkAlarms(tagName, dataPoint.value as number);

    return true;
  }

  private validateSensorData(sensor: SCADASensor, value: number | boolean | string): SensorValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    let transformedValue: number | boolean | string | undefined;

    // Type validation
    if (sensor.dataType === 'float' || sensor.dataType === 'integer') {
      const numValue = typeof value === 'number' ? value : parseFloat(value as string);
      if (isNaN(numValue)) {
        errors.push(`Invalid numeric value: ${value}`);
        return { isValid: false, errors, warnings };
      }

      // Range validation
      if (numValue < sensor.range.min || numValue > sensor.range.max) {
        warnings.push(`Value ${numValue} outside range [${sensor.range.min}, ${sensor.range.max}]`);
      }

      // Apply precision
      transformedValue = sensor.dataType === 'integer' ? 
        Math.round(numValue) : 
        parseFloat(numValue.toFixed(sensor.precision));

    } else if (sensor.dataType === 'boolean') {
      if (typeof value === 'boolean') {
        transformedValue = value;
      } else if (typeof value === 'number') {
        transformedValue = value !== 0;
      } else if (typeof value === 'string') {
        transformedValue = value.toLowerCase() === 'true' || value === '1';
      } else {
        errors.push(`Invalid boolean value: ${value}`);
      }
    } else if (sensor.dataType === 'string') {
      transformedValue = String(value);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      transformedValue
    };
  }

  private checkAlarms(tagName: string, value: number) {
    // Check all alarm configurations for this tag
    this.alarmConfigs.forEach((alarmConfig, alarmId) => {
      if (alarmConfig.tagName !== tagName) return;

      const isAlarmActive = this.activeAlarms.has(alarmId);
      let shouldTrigger = false;

      if (alarmConfig.alarmType === 'high') {
        shouldTrigger = value > alarmConfig.setpoint;
      } else if (alarmConfig.alarmType === 'low') {
        shouldTrigger = value < alarmConfig.setpoint;
      }

      if (shouldTrigger && !isAlarmActive) {
        // Trigger new alarm
        const alarmEvent: AlarmEvent = {
          alarmId,
          tagName,
          alarmType: alarmConfig.alarmType,
          currentValue: value,
          setpoint: alarmConfig.setpoint,
          message: alarmConfig.message,
          timestamp: new Date(),
          priority: alarmConfig.priority,
          acknowledged: false
        };

        this.activeAlarms.set(alarmId, alarmEvent);
        console.log(`Alarm triggered: ${alarmConfig.message} (Value: ${value})`);

      } else if (!shouldTrigger && isAlarmActive) {
        // Check for auto-reset
        const activeAlarm = this.activeAlarms.get(alarmId)!;
        const deadbandValue = alarmConfig.alarmType === 'high' ? 
          alarmConfig.setpoint - alarmConfig.deadband :
          alarmConfig.setpoint + alarmConfig.deadband;

        const shouldReset = alarmConfig.alarmType === 'high' ? 
          value < deadbandValue : 
          value > deadbandValue;

        if (shouldReset && alarmConfig.autoReset) {
          this.activeAlarms.delete(alarmId);
          console.log(`Alarm auto-reset: ${alarmConfig.message} (Value: ${value})`);
        }
      }
    });
  }

  public acknowledgeAlarm(alarmId: string, acknowledgedBy: string): boolean {
    const alarm = this.activeAlarms.get(alarmId);
    if (!alarm) {
      return false;
    }

    alarm.acknowledged = true;
    alarm.acknowledgedBy = acknowledgedBy;
    alarm.acknowledgedAt = new Date();

    console.log(`Alarm acknowledged: ${alarm.message} by ${acknowledgedBy}`);
    return true;
  }

  public getSensorData(tagName?: string): SensorDataPoint[] {
    if (tagName) {
      const data = this.sensorData.get(tagName);
      return data ? [data] : [];
    }
    return Array.from(this.sensorData.values());
  }

  public getActiveAlarms(): AlarmEvent[] {
    return Array.from(this.activeAlarms.values());
  }

  public getSensorMapping(tagName: string): SCADASensor | undefined {
    return this.sensorMap.get(tagName);
  }

  public getAllSensorMappings(): SCADASensor[] {
    return Array.from(this.sensorMap.values());
  }

  public getTagGroups(): SCADATagGroup[] {
    return SCADA_SENSOR_CONFIG.tagGroups;
  }

  public mapToDigitalTwin(tagName: string, value: number | boolean | string): {
    componentType: string;
    componentId: string;
    property: string;
    value: number | boolean | string;
  } | null {
    const sensor = this.sensorMap.get(tagName);
    if (!sensor) {
      return null;
    }

    return {
      componentType: sensor.digitalTwinMapping.componentType,
      componentId: sensor.digitalTwinMapping.componentId,
      property: sensor.digitalTwinMapping.property,
      value
    };
  }

  public generateSensorReport(): {
    totalSensors: number;
    activeSensors: number;
    alarmCount: number;
    criticalAlarms: number;
    lastUpdateSummary: { [tagName: string]: Date };
    sensorHealth: { [tagName: string]: 'healthy' | 'stale' | 'failed' };
  } {
    const now = new Date();
    const staleThreshold = 60000; // 1 minute
    const failedThreshold = 300000; // 5 minutes

    const sensorHealth: { [tagName: string]: 'healthy' | 'stale' | 'failed' } = {};
    let activeSensors = 0;

    this.sensorMap.forEach((sensor, tagName) => {
      const lastUpdate = this.lastUpdateTimes.get(tagName);
      if (!lastUpdate) {
        sensorHealth[tagName] = 'failed';
      } else {
        const timeSinceUpdate = now.getTime() - lastUpdate.getTime();
        if (timeSinceUpdate > failedThreshold) {
          sensorHealth[tagName] = 'failed';
        } else if (timeSinceUpdate > staleThreshold) {
          sensorHealth[tagName] = 'stale';
        } else {
          sensorHealth[tagName] = 'healthy';
          activeSensors++;
        }
      }
    });

    const activeAlarms = Array.from(this.activeAlarms.values());
    const criticalAlarms = activeAlarms.filter(alarm => alarm.priority === 'critical').length;

    return {
      totalSensors: this.sensorMap.size,
      activeSensors,
      alarmCount: activeAlarms.length,
      criticalAlarms,
      lastUpdateSummary: Object.fromEntries(this.lastUpdateTimes),
      sensorHealth
    };
  }
}
