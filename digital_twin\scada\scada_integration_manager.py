"""
SCADA Integration Manager
Comprehensive SCADA integration with OPC UA, Modbus, and historian data interfaces
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
import pandas as pd

from .opc_client import OPCUAClient, OPCConfiguration, OPCTag
from .modbus_client import ModbusClient, ModbusConfiguration, ModbusTag
from ..core.twin_engine import TwinStateData, TankDataPoint

logger = logging.getLogger(__name__)


class SCADAProtocol(Enum):
    OPC_UA = "opc_ua"
    MODBUS_TCP = "modbus_tcp"
    MODBUS_RTU = "modbus_rtu"
    HISTORIAN = "historian"
    MQTT = "mqtt"


@dataclass
class SCADADeviceConfig:
    """Configuration for a SCADA device"""
    device_id: str
    name: str
    protocol: SCADAProtocol
    connection_config: Union[OPCConfiguration, ModbusConfiguration]
    tags: List[Union[OPCTag, ModbusTag]]
    enabled: bool = True
    priority: int = 1
    data_collection_interval: float = 1.0  # seconds
    retry_attempts: int = 3
    timeout: float = 10.0


@dataclass
class SCADADataPoint:
    """SCADA data point with metadata"""
    device_id: str
    tag_name: str
    value: Any
    timestamp: datetime
    quality: str = "GOOD"
    alarm_state: Optional[str] = None
    source_address: Optional[str] = None
    data_type: Optional[str] = None


class SCADAIntegrationManager:
    """Central manager for all SCADA integrations"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.devices: Dict[str, SCADADeviceConfig] = {}
        self.clients: Dict[str, Union[OPCUAClient, ModbusClient]] = {}
        self.data_handlers: List[Callable] = []
        self.alarm_handlers: List[Callable] = []
        self.is_running = False
        self._collection_tasks: Dict[str, asyncio.Task] = {}
        self._data_buffer: List[SCADADataPoint] = []
        self._buffer_lock = asyncio.Lock()
        self.historian_interface = None
        self.mqtt_client = None
        
        # Performance metrics
        self.metrics = {
            'total_reads': 0,
            'failed_reads': 0,
            'connection_errors': 0,
            'data_points_collected': 0,
            'last_collection_time': None
        }
        
        if config_file:
            self.load_configuration(config_file)
    
    def add_device(self, device_config: SCADADeviceConfig):
        """Add a SCADA device configuration"""
        self.devices[device_config.device_id] = device_config
        logger.info(f"Added SCADA device: {device_config.name} ({device_config.protocol.value})")
    
    def remove_device(self, device_id: str):
        """Remove a SCADA device"""
        if device_id in self.devices:
            del self.devices[device_id]
            if device_id in self.clients:
                asyncio.create_task(self.clients[device_id].disconnect())
                del self.clients[device_id]
            logger.info(f"Removed SCADA device: {device_id}")
    
    async def start_all_devices(self):
        """Start all configured SCADA devices"""
        self.is_running = True
        
        for device_id, config in self.devices.items():
            if config.enabled:
                await self._start_device(device_id)
        
        logger.info("Started all SCADA devices")
    
    async def stop_all_devices(self):
        """Stop all SCADA devices"""
        self.is_running = False
        
        # Cancel all collection tasks
        for task in self._collection_tasks.values():
            task.cancel()
        
        # Disconnect all clients
        for client in self.clients.values():
            await client.disconnect()
        
        self.clients.clear()
        self._collection_tasks.clear()
        
        logger.info("Stopped all SCADA devices")
    
    async def _start_device(self, device_id: str):
        """Start a specific SCADA device"""
        config = self.devices[device_id]
        
        try:
            # Create client based on protocol
            if config.protocol == SCADAProtocol.OPC_UA:
                client = OPCUAClient(config.connection_config)
            elif config.protocol in [SCADAProtocol.MODBUS_TCP, SCADAProtocol.MODBUS_RTU]:
                client = ModbusClient(config.connection_config)
            else:
                logger.error(f"Unsupported protocol: {config.protocol}")
                return
            
            # Connect
            if await client.connect():
                self.clients[device_id] = client
                
                # Register data handlers
                if hasattr(client, 'register_subscription_handler'):
                    client.register_subscription_handler(
                        lambda tag, value, timestamp: self._handle_device_data(
                            device_id, tag, value, timestamp
                        )
                    )
                
                # Start data collection task
                self._collection_tasks[device_id] = asyncio.create_task(
                    self._device_collection_loop(device_id)
                )
                
                logger.info(f"Started SCADA device: {config.name}")
            else:
                logger.error(f"Failed to connect to device: {config.name}")
        
        except Exception as e:
            logger.error(f"Error starting device {device_id}: {e}")
    
    async def _device_collection_loop(self, device_id: str):
        """Data collection loop for a device"""
        config = self.devices[device_id]
        client = self.clients[device_id]
        
        while self.is_running:
            try:
                # Collect data from all tags
                if hasattr(client, 'read_all_tags'):
                    data = await client.read_all_tags()
                    
                    # Process collected data
                    for tag_name, value in data.items():
                        await self._handle_device_data(device_id, tag_name, value, datetime.now())
                
                self.metrics['total_reads'] += 1
                self.metrics['last_collection_time'] = datetime.now()
                
                # Wait for next collection
                await asyncio.sleep(config.data_collection_interval)
                
            except Exception as e:
                logger.error(f"Error in collection loop for device {device_id}: {e}")
                self.metrics['failed_reads'] += 1
                await asyncio.sleep(config.data_collection_interval)
    
    async def _handle_device_data(self, device_id: str, tag_name: str, value: Any, timestamp: datetime):
        """Handle data from a SCADA device"""
        try:
            # Create data point
            data_point = SCADADataPoint(
                device_id=device_id,
                tag_name=tag_name,
                value=value,
                timestamp=timestamp,
                quality="GOOD"
            )
            
            # Check for alarms
            await self._check_alarms(data_point)
            
            # Buffer data point
            async with self._buffer_lock:
                self._data_buffer.append(data_point)
                self.metrics['data_points_collected'] += 1
            
            # Notify data handlers
            for handler in self.data_handlers:
                await handler(data_point)
        
        except Exception as e:
            logger.error(f"Error handling device data: {e}")
    
    async def _check_alarms(self, data_point: SCADADataPoint):
        """Check for alarm conditions"""
        device_config = self.devices[data_point.device_id]
        
        # Find tag configuration
        tag_config = None
        for tag in device_config.tags:
            if tag.name == data_point.tag_name:
                tag_config = tag
                break
        
        if not tag_config:
            return
        
        # Check alarm conditions
        if isinstance(data_point.value, (int, float)):
            if hasattr(tag_config, 'alarm_high') and tag_config.alarm_high:
                if data_point.value > tag_config.alarm_high:
                    data_point.alarm_state = "HIGH"
                    await self._trigger_alarm(data_point, "HIGH_ALARM")
            
            if hasattr(tag_config, 'alarm_low') and tag_config.alarm_low:
                if data_point.value < tag_config.alarm_low:
                    data_point.alarm_state = "LOW"
                    await self._trigger_alarm(data_point, "LOW_ALARM")
    
    async def _trigger_alarm(self, data_point: SCADADataPoint, alarm_type: str):
        """Trigger an alarm"""
        alarm_info = {
            'device_id': data_point.device_id,
            'tag_name': data_point.tag_name,
            'value': data_point.value,
            'timestamp': data_point.timestamp,
            'alarm_type': alarm_type
        }
        
        logger.warning(f"ALARM: {alarm_type} for {data_point.device_id}.{data_point.tag_name} = {data_point.value}")
        
        # Notify alarm handlers
        for handler in self.alarm_handlers:
            await handler(alarm_info)
    
    def register_data_handler(self, handler: Callable):
        """Register a data handler"""
        self.data_handlers.append(handler)
    
    def register_alarm_handler(self, handler: Callable):
        """Register an alarm handler"""
        self.alarm_handlers.append(handler)
    
    async def read_tag(self, device_id: str, tag_name: str) -> Optional[Any]:
        """Read a specific tag from a device"""
        if device_id not in self.clients:
            return None
        
        client = self.clients[device_id]
        return await client.read_tag(tag_name)
    
    async def write_tag(self, device_id: str, tag_name: str, value: Any) -> bool:
        """Write a value to a specific tag"""
        if device_id not in self.clients:
            return False
        
        client = self.clients[device_id]
        return await client.write_tag(tag_name, value)
    
    async def get_device_status(self, device_id: str) -> Dict[str, Any]:
        """Get status of a SCADA device"""
        if device_id not in self.devices:
            return {}
        
        config = self.devices[device_id]
        client = self.clients.get(device_id)
        
        status = {
            'device_id': device_id,
            'name': config.name,
            'protocol': config.protocol.value,
            'enabled': config.enabled,
            'connected': client.is_connected() if client else False,
            'last_collection': self.metrics['last_collection_time']
        }
        
        if client and hasattr(client, 'get_tag_status'):
            status['tags'] = client.get_tag_status()
        
        return status
    
    async def get_all_device_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all devices"""
        status = {}
        for device_id in self.devices:
            status[device_id] = await self.get_device_status(device_id)
        return status
    
    async def get_recent_data(self, device_id: str, tag_name: str, 
                             duration: timedelta = timedelta(hours=1)) -> List[SCADADataPoint]:
        """Get recent data for a specific tag"""
        cutoff_time = datetime.now() - duration
        
        async with self._buffer_lock:
            filtered_data = [
                dp for dp in self._data_buffer
                if (dp.device_id == device_id and 
                    dp.tag_name == tag_name and 
                    dp.timestamp >= cutoff_time)
            ]
        
        return sorted(filtered_data, key=lambda x: x.timestamp)
    
    async def get_data_statistics(self) -> Dict[str, Any]:
        """Get data collection statistics"""
        return {
            'total_reads': self.metrics['total_reads'],
            'failed_reads': self.metrics['failed_reads'],
            'success_rate': (self.metrics['total_reads'] - self.metrics['failed_reads']) / max(1, self.metrics['total_reads']),
            'data_points_collected': self.metrics['data_points_collected'],
            'last_collection_time': self.metrics['last_collection_time'],
            'active_devices': len([d for d in self.devices.values() if d.enabled]),
            'connected_devices': len(self.clients),
            'buffer_size': len(self._data_buffer)
        }
    
    async def export_data_to_historian(self, historian_config: Dict[str, Any]) -> bool:
        """Export buffered data to historian system"""
        try:
            # Implementation depends on historian type (PI, Wonderware, etc.)
            logger.info("Exporting data to historian...")
            
            async with self._buffer_lock:
                if not self._data_buffer:
                    return True
                
                # Convert to historian format
                historian_data = []
                for dp in self._data_buffer:
                    historian_data.append({
                        'timestamp': dp.timestamp.isoformat(),
                        'tag': f"{dp.device_id}.{dp.tag_name}",
                        'value': dp.value,
                        'quality': dp.quality
                    })
                
                # Clear buffer after export
                self._data_buffer.clear()
            
            logger.info(f"Exported {len(historian_data)} data points to historian")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting data to historian: {e}")
            return False
    
    async def setup_mqtt_bridge(self, mqtt_config: Dict[str, Any]):
        """Setup MQTT bridge for real-time data streaming"""
        try:
            import paho.mqtt.client as mqtt
            
            def on_connect(client, userdata, flags, rc):
                logger.info(f"MQTT connected with result code {rc}")
            
            def on_publish(client, userdata, mid):
                logger.debug(f"MQTT message published: {mid}")
            
            self.mqtt_client = mqtt.Client()
            self.mqtt_client.on_connect = on_connect
            self.mqtt_client.on_publish = on_publish
            
            if mqtt_config.get('username'):
                self.mqtt_client.username_pw_set(
                    mqtt_config['username'],
                    mqtt_config.get('password', '')
                )
            
            self.mqtt_client.connect(
                mqtt_config['host'],
                mqtt_config.get('port', 1883),
                mqtt_config.get('keepalive', 60)
            )
            
            self.mqtt_client.loop_start()
            
            # Register MQTT data handler
            self.register_data_handler(self._mqtt_data_handler)
            
            logger.info("MQTT bridge setup completed")
            
        except Exception as e:
            logger.error(f"Error setting up MQTT bridge: {e}")
    
    async def _mqtt_data_handler(self, data_point: SCADADataPoint):
        """Handle data publishing to MQTT"""
        if not self.mqtt_client:
            return
        
        try:
            topic = f"scada/{data_point.device_id}/{data_point.tag_name}"
            payload = {
                'value': data_point.value,
                'timestamp': data_point.timestamp.isoformat(),
                'quality': data_point.quality
            }
            
            self.mqtt_client.publish(topic, str(payload))
            
        except Exception as e:
            logger.error(f"Error publishing to MQTT: {e}")
    
    def load_configuration(self, config_file: str):
        """Load SCADA configuration from file"""
        try:
            import json
            
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            for device_config in config.get('devices', []):
                # Parse device configuration
                # Implementation depends on config format
                pass
            
            logger.info(f"Loaded SCADA configuration from {config_file}")
            
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
    
    def save_configuration(self, config_file: str):
        """Save current SCADA configuration to file"""
        try:
            import json
            
            config = {
                'devices': [
                    {
                        'device_id': device.device_id,
                        'name': device.name,
                        'protocol': device.protocol.value,
                        'enabled': device.enabled,
                        'data_collection_interval': device.data_collection_interval
                    }
                    for device in self.devices.values()
                ]
            }
            
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            logger.info(f"Saved SCADA configuration to {config_file}")
            
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")


class SCADAToTwinBridge:
    """Bridge between SCADA data and Digital Twins"""
    
    def __init__(self, scada_manager: SCADAIntegrationManager, twin_engine):
        self.scada_manager = scada_manager
        self.twin_engine = twin_engine
        self.tag_mappings: Dict[str, str] = {}  # SCADA tag -> Twin ID
        self.data_transformers: Dict[str, Callable] = {}
        
        # Register as SCADA data handler
        self.scada_manager.register_data_handler(self._handle_scada_data)
    
    def add_tag_mapping(self, scada_tag: str, twin_id: str, transformer: Callable = None):
        """Add mapping between SCADA tag and twin"""
        self.tag_mappings[scada_tag] = twin_id
        if transformer:
            self.data_transformers[scada_tag] = transformer
        
        logger.info(f"Added tag mapping: {scada_tag} -> {twin_id}")
    
    async def _handle_scada_data(self, data_point: SCADADataPoint):
        """Handle SCADA data and update digital twins"""
        tag_key = f"{data_point.device_id}.{data_point.tag_name}"
        
        if tag_key in self.tag_mappings:
            twin_id = self.tag_mappings[tag_key]
            
            # Transform data if needed
            value = data_point.value
            if tag_key in self.data_transformers:
                value = self.data_transformers[tag_key](value)
            
            # Update twin
            measurements = {
                data_point.tag_name: value,
                'timestamp': data_point.timestamp,
                'quality': data_point.quality
            }
            
            await self.twin_engine.update_twin_state(twin_id, measurements)


# Factory functions
def create_scada_manager(config_file: str = None) -> SCADAIntegrationManager:
    """Create SCADA integration manager"""
    return SCADAIntegrationManager(config_file)


def create_opc_ua_device(device_id: str, name: str, endpoint_url: str, 
                        tags: List[OPCTag]) -> SCADADeviceConfig:
    """Create OPC UA device configuration"""
    opc_config = OPCConfiguration(
        endpoint_url=endpoint_url,
        tags=tags
    )
    
    return SCADADeviceConfig(
        device_id=device_id,
        name=name,
        protocol=SCADAProtocol.OPC_UA,
        connection_config=opc_config,
        tags=tags
    )


def create_modbus_device(device_id: str, name: str, host: str, port: int,
                        tags: List[ModbusTag]) -> SCADADeviceConfig:
    """Create Modbus device configuration"""
    modbus_config = ModbusConfiguration(
        host=host,
        port=port,
        tags=tags
    )
    
    return SCADADeviceConfig(
        device_id=device_id,
        name=name,
        protocol=SCADAProtocol.MODBUS_TCP,
        connection_config=modbus_config,
        tags=tags
    )


# Example usage
if __name__ == "__main__":
    async def test_scada_integration():
        # Create SCADA manager
        scada_manager = create_scada_manager()
        
        # Create OPC UA device
        opc_tags = [
            OPCTag(name="tank_temp_1", node_id="ns=2;s=Tank1.Temperature", data_type="float"),
            OPCTag(name="tank_temp_2", node_id="ns=2;s=Tank2.Temperature", data_type="float"),
            OPCTag(name="heating_power", node_id="ns=2;s=Heater.Power", data_type="float")
        ]
        
        opc_device = create_opc_ua_device(
            device_id="plant_opc",
            name="Plant OPC Server",
            endpoint_url="opc.tcp://localhost:4840",
            tags=opc_tags
        )
        
        # Add device to manager
        scada_manager.add_device(opc_device)
        
        # Start data collection
        await scada_manager.start_all_devices()
        
        # Let it run for a while
        await asyncio.sleep(10)
        
        # Check status
        status = await scada_manager.get_all_device_status()
        print(f"Device status: {status}")
        
        # Get statistics
        stats = await scada_manager.get_data_statistics()
        print(f"Statistics: {stats}")
        
        # Stop
        await scada_manager.stop_all_devices()
    
    # Run test
    asyncio.run(test_scada_integration())