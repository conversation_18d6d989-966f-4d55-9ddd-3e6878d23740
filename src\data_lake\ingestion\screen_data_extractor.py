"""
Screen Data Extractor for SCADA Systems
Captures and extracts data from SCADA screens when direct API access is restricted
"""

import asyncio
import logging
import cv2
import numpy as np
from typing import Dict, List, Optional, Any, Tuple, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import json
import re
import time

try:
    import mss
    import PIL.Image
    import pytesseract
    import easyocr
    MSS_AVAILABLE = True
except ImportError:
    MSS_AVAILABLE = False
    print("Warning: Screen capture libraries not available. Install pillow, mss, pytesseract, easyocr")

logger = logging.getLogger(__name__)


@dataclass
class ScreenRegion:
    """Define a region of the screen to monitor"""
    name: str
    x: int
    y: int
    width: int
    height: int
    data_type: str = "text"  # text, number, color, gauge
    preprocessing: str = "default"  # default, threshold, blur, sharpen
    expected_format: Optional[str] = None  # regex pattern for validation
    description: str = ""


@dataclass
class TankScreenLayout:
    """Layout definition for tank SCADA screen"""
    tank_id: str
    window_title: str = ""
    
    # Temperature regions
    zone_temp_regions: List[ScreenRegion] = field(default_factory=list)
    outlet_temp_region: Optional[ScreenRegion] = None
    ambient_temp_region: Optional[ScreenRegion] = None
    
    # Control regions
    heating_power_regions: List[ScreenRegion] = field(default_factory=list)
    setpoint_regions: List[ScreenRegion] = field(default_factory=list)
    valve_status_regions: List[ScreenRegion] = field(default_factory=list)
    
    # Status indicators
    alarm_regions: List[ScreenRegion] = field(default_factory=list)
    mode_region: Optional[ScreenRegion] = None
    level_region: Optional[ScreenRegion] = None
    
    # Energy and cost
    power_consumption_region: Optional[ScreenRegion] = None
    energy_cost_region: Optional[ScreenRegion] = None


class ScreenDataExtractor:
    """Extract data from SCADA screens using computer vision"""
    
    def __init__(self, tank_layout: TankScreenLayout):
        if not MSS_AVAILABLE:
            raise ImportError("Screen capture libraries required. Install: pip install pillow mss pytesseract easyocr opencv-python")
        
        self.tank_layout = tank_layout
        self.sct = mss.mss()
        self.ocr_reader = easyocr.Reader(['en'])  # Initialize EasyOCR
        
        # Configuration
        self.capture_interval = 1.0  # seconds
        self.data_validation_enabled = True
        self.preprocessing_enabled = True
        
        # State tracking
        self.last_capture_time = None
        self.previous_data = {}
        self.extraction_errors = 0
        self.successful_extractions = 0
        
        # Callbacks
        self.data_callbacks: List[Callable] = []
        self.error_callbacks: List[Callable] = []
        
    async def start_monitoring(self) -> bool:
        """Start monitoring the SCADA screen"""
        try:
            logger.info(f"Starting screen monitoring for tank {self.tank_layout.tank_id}")
            
            # Test screen capture
            test_capture = self._capture_screen_region(
                ScreenRegion("test", 0, 0, 100, 100)
            )
            
            if test_capture is None:
                logger.error("Failed to capture test screen region")
                return False
            
            # Start monitoring loop
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())
            
            logger.info("Screen monitoring started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start screen monitoring: {e}")
            return False
    
    async def stop_monitoring(self):
        """Stop screen monitoring"""
        try:
            if hasattr(self, '_monitoring_task'):
                self._monitoring_task.cancel()
            
            logger.info("Screen monitoring stopped")
            
        except Exception as e:
            logger.error(f"Error stopping screen monitoring: {e}")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while True:
            try:
                # Capture and extract data
                extracted_data = await self._extract_tank_data()
                
                if extracted_data:
                    self.successful_extractions += 1
                    self.last_capture_time = datetime.now()
                    
                    # Validate data
                    if self.data_validation_enabled:
                        if self._validate_extracted_data(extracted_data):
                            await self._notify_data_callbacks(extracted_data)
                        else:
                            logger.warning("Data validation failed, skipping update")
                    else:
                        await self._notify_data_callbacks(extracted_data)
                else:
                    self.extraction_errors += 1
                    logger.warning("Failed to extract data from screen")
                
                # Wait for next capture
                await asyncio.sleep(self.capture_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                self.extraction_errors += 1
                await self._notify_error_callbacks(e)
                await asyncio.sleep(self.capture_interval)
    
    async def _extract_tank_data(self) -> Optional[Dict[str, Any]]:
        """Extract all tank data from screen"""
        try:
            extracted_data = {
                'tank_id': self.tank_layout.tank_id,
                'timestamp': datetime.now(),
                'zone_temperatures': {},
                'heating_powers': {},
                'setpoints': {},
                'valve_statuses': {},
                'alarms': [],
                'system_data': {}
            }
            
            # Extract zone temperatures
            for i, region in enumerate(self.tank_layout.zone_temp_regions):
                temp_value = await self._extract_numeric_value(region)
                if temp_value is not None:
                    extracted_data['zone_temperatures'][i] = temp_value
            
            # Extract heating powers
            for i, region in enumerate(self.tank_layout.heating_power_regions):
                power_value = await self._extract_numeric_value(region)
                if power_value is not None:
                    extracted_data['heating_powers'][i] = power_value
            
            # Extract setpoints
            for i, region in enumerate(self.tank_layout.setpoint_regions):
                setpoint_value = await self._extract_numeric_value(region)
                if setpoint_value is not None:
                    extracted_data['setpoints'][i] = setpoint_value
            
            # Extract valve statuses
            for i, region in enumerate(self.tank_layout.valve_status_regions):
                status_value = await self._extract_text_value(region)
                if status_value is not None:
                    extracted_data['valve_statuses'][i] = status_value
            
            # Extract system-level data
            if self.tank_layout.outlet_temp_region:
                outlet_temp = await self._extract_numeric_value(self.tank_layout.outlet_temp_region)
                if outlet_temp is not None:
                    extracted_data['system_data']['outlet_temperature'] = outlet_temp
            
            if self.tank_layout.ambient_temp_region:
                ambient_temp = await self._extract_numeric_value(self.tank_layout.ambient_temp_region)
                if ambient_temp is not None:
                    extracted_data['system_data']['ambient_temperature'] = ambient_temp
            
            if self.tank_layout.power_consumption_region:
                power_consumption = await self._extract_numeric_value(self.tank_layout.power_consumption_region)
                if power_consumption is not None:
                    extracted_data['system_data']['power_consumption'] = power_consumption
            
            if self.tank_layout.level_region:
                level = await self._extract_numeric_value(self.tank_layout.level_region)
                if level is not None:
                    extracted_data['system_data']['tank_level'] = level
            
            if self.tank_layout.mode_region:
                mode = await self._extract_text_value(self.tank_layout.mode_region)
                if mode is not None:
                    extracted_data['system_data']['control_mode'] = mode
            
            # Extract alarms
            for region in self.tank_layout.alarm_regions:
                alarm_status = await self._extract_text_value(region)
                if alarm_status and alarm_status.lower() not in ['ok', 'normal', 'off', '']:
                    extracted_data['alarms'].append(f"{region.name}: {alarm_status}")
            
            return extracted_data
            
        except Exception as e:
            logger.error(f"Error extracting tank data: {e}")
            return None
    
    async def _extract_numeric_value(self, region: ScreenRegion) -> Optional[float]:
        """Extract numeric value from screen region"""
        try:
            # Capture screen region
            img = self._capture_screen_region(region)
            if img is None:
                return None
            
            # Preprocess image
            if self.preprocessing_enabled:
                img = self._preprocess_image(img, region.preprocessing)
            
            # Extract text using OCR
            text = self._extract_text_from_image(img)
            
            # Parse numeric value
            numeric_value = self._parse_numeric_value(text, region.expected_format)
            
            return numeric_value
            
        except Exception as e:
            logger.error(f"Error extracting numeric value from region {region.name}: {e}")
            return None
    
    async def _extract_text_value(self, region: ScreenRegion) -> Optional[str]:
        """Extract text value from screen region"""
        try:
            # Capture screen region
            img = self._capture_screen_region(region)
            if img is None:
                return None
            
            # Preprocess image
            if self.preprocessing_enabled:
                img = self._preprocess_image(img, region.preprocessing)
            
            # Extract text using OCR
            text = self._extract_text_from_image(img)
            
            # Clean and validate text
            cleaned_text = self._clean_extracted_text(text)
            
            return cleaned_text if cleaned_text else None
            
        except Exception as e:
            logger.error(f"Error extracting text value from region {region.name}: {e}")
            return None
    
    def _capture_screen_region(self, region: ScreenRegion) -> Optional[np.ndarray]:
        """Capture specific screen region"""
        try:
            # Define capture area
            monitor = {
                'top': region.y,
                'left': region.x,
                'width': region.width,
                'height': region.height
            }
            
            # Capture screenshot
            screenshot = self.sct.grab(monitor)
            
            # Convert to numpy array
            img = np.array(screenshot)
            
            # Convert from BGRA to RGB
            img = cv2.cvtColor(img, cv2.COLOR_BGRA2RGB)
            
            return img
            
        except Exception as e:
            logger.error(f"Error capturing screen region {region.name}: {e}")
            return None
    
    def _preprocess_image(self, img: np.ndarray, preprocessing_type: str) -> np.ndarray:
        """Preprocess image for better OCR results"""
        try:
            if preprocessing_type == "threshold":
                # Convert to grayscale and apply threshold
                gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
                _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
                return cv2.cvtColor(thresh, cv2.COLOR_GRAY2RGB)
            
            elif preprocessing_type == "blur":
                # Apply Gaussian blur to reduce noise
                return cv2.GaussianBlur(img, (3, 3), 0)
            
            elif preprocessing_type == "sharpen":
                # Apply sharpening kernel
                kernel = np.array([[-1, -1, -1],
                                 [-1,  9, -1],
                                 [-1, -1, -1]])
                return cv2.filter2D(img, -1, kernel)
            
            elif preprocessing_type == "enhance_contrast":
                # Enhance contrast
                lab = cv2.cvtColor(img, cv2.COLOR_RGB2LAB)
                l, a, b = cv2.split(lab)
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                cl = clahe.apply(l)
                enhanced = cv2.merge((cl, a, b))
                return cv2.cvtColor(enhanced, cv2.COLOR_LAB2RGB)
            
            else:  # default
                return img
                
        except Exception as e:
            logger.error(f"Error preprocessing image: {e}")
            return img
    
    def _extract_text_from_image(self, img: np.ndarray) -> str:
        """Extract text from image using OCR"""
        try:
            # Try EasyOCR first (better for complex backgrounds)
            try:
                results = self.ocr_reader.readtext(img)
                if results:
                    # Combine all detected text
                    text_parts = [result[1] for result in results if result[2] > 0.5]  # confidence > 0.5
                    return ' '.join(text_parts)
            except Exception as e:
                logger.debug(f"EasyOCR failed: {e}")
            
            # Fallback to Tesseract
            try:
                # Convert to PIL Image for tesseract
                pil_img = PIL.Image.fromarray(img)
                text = pytesseract.image_to_string(pil_img, config='--psm 8')
                return text.strip()
            except Exception as e:
                logger.debug(f"Tesseract failed: {e}")
            
            return ""
            
        except Exception as e:
            logger.error(f"Error extracting text from image: {e}")
            return ""
    
    def _parse_numeric_value(self, text: str, expected_format: Optional[str] = None) -> Optional[float]:
        """Parse numeric value from extracted text"""
        try:
            # Clean text
            cleaned_text = re.sub(r'[^\d\.-]+', '', text)
            
            if not cleaned_text:
                return None
            
            # Apply expected format if provided
            if expected_format:
                match = re.search(expected_format, text)
                if match:
                    cleaned_text = match.group(1) if match.groups() else match.group(0)
            
            # Try to convert to float
            try:
                value = float(cleaned_text)
                
                # Basic validation (temperature ranges, power ranges, etc.)
                if -50 <= value <= 500:  # Reasonable range for temperatures/power
                    return value
                else:
                    logger.warning(f"Value {value} outside reasonable range")
                    return None
            
            except ValueError:
                logger.warning(f"Could not convert '{cleaned_text}' to numeric value")
                return None
                
        except Exception as e:
            logger.error(f"Error parsing numeric value from '{text}': {e}")
            return None
    
    def _clean_extracted_text(self, text: str) -> str:
        """Clean extracted text"""
        try:
            # Remove extra whitespace
            cleaned = re.sub(r'\s+', ' ', text.strip())
            
            # Remove common OCR artifacts
            cleaned = re.sub(r'[^\w\s\.-]', '', cleaned)
            
            return cleaned
            
        except Exception as e:
            logger.error(f"Error cleaning text: {e}")
            return text
    
    def _validate_extracted_data(self, data: Dict[str, Any]) -> bool:
        """Validate extracted data for reasonableness"""
        try:
            # Check temperature ranges
            for zone, temp in data.get('zone_temperatures', {}).items():
                if not (0 <= temp <= 300):  # °C
                    logger.warning(f"Zone {zone} temperature {temp}°C out of range")
                    return False
            
            # Check power ranges
            for zone, power in data.get('heating_powers', {}).items():
                if not (0 <= power <= 500):  # kW
                    logger.warning(f"Zone {zone} power {power}kW out of range")
                    return False
            
            # Check for reasonable data completeness
            temp_count = len(data.get('zone_temperatures', {}))
            if temp_count == 0:
                logger.warning("No temperature data extracted")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating data: {e}")
            return False
    
    async def _notify_data_callbacks(self, data: Dict[str, Any]):
        """Notify data callbacks"""
        for callback in self.data_callbacks:
            try:
                await callback(data)
            except Exception as e:
                logger.error(f"Error in data callback: {e}")
    
    async def _notify_error_callbacks(self, error: Exception):
        """Notify error callbacks"""
        for callback in self.error_callbacks:
            try:
                await callback(error)
            except Exception as e:
                logger.error(f"Error in error callback: {e}")
    
    def register_data_callback(self, callback: Callable):
        """Register data callback"""
        self.data_callbacks.append(callback)
    
    def register_error_callback(self, callback: Callable):
        """Register error callback"""
        self.error_callbacks.append(callback)
    
    def get_extraction_statistics(self) -> Dict[str, Any]:
        """Get extraction performance statistics"""
        total_attempts = self.successful_extractions + self.extraction_errors
        success_rate = (self.successful_extractions / total_attempts) if total_attempts > 0 else 0
        
        return {
            'successful_extractions': self.successful_extractions,
            'extraction_errors': self.extraction_errors,
            'success_rate': success_rate,
            'last_capture_time': self.last_capture_time.isoformat() if self.last_capture_time else None,
            'tank_id': self.tank_layout.tank_id
        }


class ScreenLayoutCalibrator:
    """Tool to help calibrate screen regions for data extraction"""
    
    def __init__(self):
        if not MSS_AVAILABLE:
            raise ImportError("Screen capture libraries required")
        
        self.sct = mss.mss()
    
    def capture_full_screen(self, save_path: str = "screen_capture.png") -> bool:
        """Capture full screen to help identify regions"""
        try:
            # Capture full screen
            screenshot = self.sct.grab(self.sct.monitors[1])  # Monitor 1 (primary)
            
            # Save screenshot
            mss.tools.to_png(screenshot.rgb, screenshot.size, output=save_path)
            
            logger.info(f"Full screen captured and saved to {save_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error capturing full screen: {e}")
            return False
    
    def test_region(self, region: ScreenRegion) -> Dict[str, Any]:
        """Test OCR extraction on a specific region"""
        try:
            extractor = ScreenDataExtractor(TankScreenLayout(tank_id="test"))
            
            # Capture region
            img = extractor._capture_screen_region(region)
            if img is None:
                return {'success': False, 'error': 'Failed to capture region'}
            
            # Test OCR
            text = extractor._extract_text_from_image(img)
            
            # Try numeric parsing
            numeric_value = extractor._parse_numeric_value(text)
            
            return {
                'success': True,
                'raw_text': text,
                'numeric_value': numeric_value,
                'region': {
                    'name': region.name,
                    'x': region.x,
                    'y': region.y,
                    'width': region.width,
                    'height': region.height
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}


def create_asphalt_tank_layout(tank_id: str = "tank_001") -> TankScreenLayout:
    """Create a default layout for asphalt tank SCADA screen"""
    
    # These coordinates will need to be adjusted based on your actual SCADA screen
    # Use ScreenLayoutCalibrator to help identify the correct regions
    
    layout = TankScreenLayout(
        tank_id=tank_id,
        window_title="SCADA - Tank Control",
        
        # Zone temperature regions (adjust coordinates as needed)
        zone_temp_regions=[
            ScreenRegion("zone_1_temp", 100, 150, 80, 30, "number", "threshold", r"(\d+\.?\d*)"),
            ScreenRegion("zone_2_temp", 200, 150, 80, 30, "number", "threshold", r"(\d+\.?\d*)"),
            ScreenRegion("zone_3_temp", 300, 150, 80, 30, "number", "threshold", r"(\d+\.?\d*)"),
            ScreenRegion("zone_4_temp", 400, 150, 80, 30, "number", "threshold", r"(\d+\.?\d*)"),
        ],
        
        # Heating power regions
        heating_power_regions=[
            ScreenRegion("zone_1_power", 100, 250, 80, 30, "number", "threshold", r"(\d+\.?\d*)"),
            ScreenRegion("zone_2_power", 200, 250, 80, 30, "number", "threshold", r"(\d+\.?\d*)"),
            ScreenRegion("zone_3_power", 300, 250, 80, 30, "number", "threshold", r"(\d+\.?\d*)"),
            ScreenRegion("zone_4_power", 400, 250, 80, 30, "number", "threshold", r"(\d+\.?\d*)"),
        ],
        
        # Setpoint regions
        setpoint_regions=[
            ScreenRegion("zone_1_setpoint", 100, 200, 80, 30, "number", "threshold", r"(\d+\.?\d*)"),
            ScreenRegion("zone_2_setpoint", 200, 200, 80, 30, "number", "threshold", r"(\d+\.?\d*)"),
            ScreenRegion("zone_3_setpoint", 300, 200, 80, 30, "number", "threshold", r"(\d+\.?\d*)"),
            ScreenRegion("zone_4_setpoint", 400, 200, 80, 30, "number", "threshold", r"(\d+\.?\d*)"),
        ],
        
        # System data regions
        outlet_temp_region=ScreenRegion("outlet_temp", 500, 150, 80, 30, "number", "threshold", r"(\d+\.?\d*)"),
        ambient_temp_region=ScreenRegion("ambient_temp", 500, 200, 80, 30, "number", "threshold", r"(\d+\.?\d*)"),
        power_consumption_region=ScreenRegion("total_power", 500, 250, 100, 30, "number", "threshold", r"(\d+\.?\d*)"),
        level_region=ScreenRegion("tank_level", 500, 300, 80, 30, "number", "threshold", r"(\d+\.?\d*)"),
        mode_region=ScreenRegion("control_mode", 600, 100, 100, 30, "text", "default"),
        
        # Alarm regions
        alarm_regions=[
            ScreenRegion("alarm_1", 700, 150, 120, 30, "text", "default"),
            ScreenRegion("alarm_2", 700, 180, 120, 30, "text", "default"),
            ScreenRegion("alarm_3", 700, 210, 120, 30, "text", "default"),
        ]
    )
    
    return layout


# Example usage
if __name__ == "__main__":
    import asyncio
    
    async def test_screen_extraction():
        """Test screen data extraction"""
        
        # First, capture full screen to identify regions
        calibrator = ScreenLayoutCalibrator()
        calibrator.capture_full_screen("scada_screen.png")
        print("Full screen captured. Check 'scada_screen.png' to identify regions.")
        
        # Create layout (adjust coordinates based on your screen)
        layout = create_asphalt_tank_layout("tank_001")
        
        # Test a specific region
        test_region = ScreenRegion("test_temp", 200, 200, 100, 40, "number", "threshold")
        test_result = calibrator.test_region(test_region)
        print(f"Test region result: {test_result}")
        
        # Create extractor
        extractor = ScreenDataExtractor(layout)
        
        # Register data callback
        async def data_callback(data):
            print(f"Extracted data: {data}")
        
        extractor.register_data_callback(data_callback)
        
        # Start monitoring (run for 30 seconds)
        success = await extractor.start_monitoring()
        if success:
            print("Screen monitoring started. Monitoring for 30 seconds...")
            await asyncio.sleep(30)
            await extractor.stop_monitoring()
            
            # Print statistics
            stats = extractor.get_extraction_statistics()
            print(f"Extraction statistics: {stats}")
        else:
            print("Failed to start screen monitoring")
    
    # Run test if libraries are available
    if MSS_AVAILABLE:
        asyncio.run(test_screen_extraction())
    else:
        print("Screen capture libraries not available. Install requirements first.")