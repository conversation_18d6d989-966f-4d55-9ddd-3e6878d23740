# Prometheus configuration for MLOps Digital Twin Platform
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'digital-twin'
    replica: 'prometheus'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "alert_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Digital Twin Application
  - job_name: 'digital-twin-app'
    static_configs:
      - targets: ['digital-twin-app:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # InfluxDB
  - job_name: 'influxdb'
    static_configs:
      - targets: ['digital-twin-influxdb:8086']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['digital-twin-redis:6379']
    scrape_interval: 30s

  # PostgreSQL
  - job_name: 'postgres'
    static_configs:
      - targets: ['digital-twin-postgres:5432']
    scrape_interval: 30s

  # MinIO
  - job_name: 'minio'
    static_configs:
      - targets: ['digital-twin-minio:9000']
    metrics_path: '/minio/prometheus/metrics'
    scrape_interval: 30s

  # Nginx
  - job_name: 'nginx'
    static_configs:
      - targets: ['digital-twin-nginx:80']
    metrics_path: '/nginx_status'
    scrape_interval: 30s

  # Node Exporter (if deployed)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # cAdvisor (container metrics)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s

# Remote write configuration (for external systems)
remote_write:
  # - url: "https://your-remote-prometheus/api/v1/write"
  #   basic_auth:
  #     username: "user"
  #     password: "password"

# Remote read configuration (for external systems)
remote_read:
  # - url: "https://your-remote-prometheus/api/v1/read"
  #   basic_auth:
  #     username: "user"
  #     password: "password"