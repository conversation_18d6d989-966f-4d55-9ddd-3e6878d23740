version: '3.8'

services:
  # Digital Twin Application (simplified)
  digital-twin-app:
    build:
      context: .
      dockerfile: Dockerfile.simple
    container_name: digital-twin-simple
    ports:
      - "8000:8000"  # API port
    environment:
      - PYTHONPATH=/app
      - CONFIG_FILE=/app/config/digital_twin_config.yaml
      - LOG_LEVEL=INFO
    volumes:
      - .:/app
    networks:
      - digital-twin-network
    command: python3 /app/run_api_simple.py

  # Redis for basic caching
  redis:
    image: redis:7-alpine
    container_name: digital-twin-redis-simple
    ports:
      - "6380:6379"
    networks:
      - digital-twin-network
    restart: unless-stopped

networks:
  digital-twin-network:
    driver: bridge