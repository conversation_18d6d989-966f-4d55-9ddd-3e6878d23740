---
# Service Account
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tank-service-account
  namespace: tank-system
  labels:
    app.kubernetes.io/name: tank-control-system
    app.kubernetes.io/component: security
---
# Role for tank services
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: tank-system
  name: tank-service-role
rules:
- apiGroups: [""]
  resources: ["secrets", "configmaps"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "watch"]
---
# Role Binding
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: tank-service-role-binding
  namespace: tank-system
subjects:
- kind: ServiceAccount
  name: tank-service-account
  namespace: tank-system
roleRef:
  kind: Role
  name: tank-service-role
  apiGroup: rbac.authorization.k8s.io
---
# PostgreSQL Credentials Secret
apiVersion: v1
kind: Secret
metadata:
  name: postgres-credentials
  namespace: tank-system
  labels:
    app: postgres
    component: database
type: Opaque
data:
  username: cG9zdGdyZXM=  # postgres (base64)
  password: cG9zdGdyZXM=  # postgres (base64)
  url: ****************************************************************************************  # ****************************************************/tank_config (base64)
---
# InfluxDB Credentials Secret
apiVersion: v1
kind: Secret
metadata:
  name: influxdb-credentials
  namespace: tank-system
  labels:
    app: influxdb
    component: timeseries-db
type: Opaque
data:
  username: YWRtaW4=  # admin (base64)
  password: YWRtaW4xMjM=  # admin123 (base64)
  admin-token: dGFuay1hZG1pbi10b2tlbg==  # tank-admin-token (base64)
---
# JWT Secret for API Authentication
apiVersion: v1
kind: Secret
metadata:
  name: jwt-secret
  namespace: tank-system
  labels:
    app.kubernetes.io/name: tank-control-system
    app.kubernetes.io/component: security
type: Opaque
data:
  secret-key: dGFuay1zeXN0ZW0tanV0LXNlY3JldC1rZXktMjAyNA==  # tank-system-jwt-secret-key-2024 (base64)
---
# OPC UA Credentials Secret
apiVersion: v1
kind: Secret
metadata:
  name: opcua-credentials
  namespace: tank-system
  labels:
    app.kubernetes.io/name: tank-control-system
    app.kubernetes.io/component: scada
type: Opaque
data:
  username: b3BlcmF0b3I=  # operator (base64)
  password: cGFzc3dvcmQxMjM=  # password123 (base64)
---
# Email/SMTP Credentials Secret for Alerts
apiVersion: v1
kind: Secret
metadata:
  name: smtp-credentials
  namespace: tank-system
  labels:
    app.kubernetes.io/name: tank-control-system
    app.kubernetes.io/component: alerting
type: Opaque
data:
  username: ************************  # <EMAIL> (base64)
  password: ZW1haWxfcGFzc3dvcmQ=  # email_password (base64)
---
# Tank System Configuration ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: tank-system-config
  namespace: tank-system
  labels:
    app.kubernetes.io/name: tank-control-system
    app.kubernetes.io/component: configuration
data:
  config.yaml: |
    system:
      name: "Industrial Tank Control System"
      version: "1.0.0"
      description: "Multi-tank heating and control system for industrial applications"
      location: "Production Kubernetes Cluster"
      operator: "Control Room"
    
    # SCADA Configuration
    scada:
      opc_ua:
        enabled: true
        endpoint_url: "opc.tcp://scada-server.company.com:4840"
        security_policy: "Basic256Sha256"
        security_mode: "SignAndEncrypt"
        session_timeout: 60000
        connection_timeout: 10000
        keep_alive_interval: 5000
      
      historian:
        enabled: true
        type: "pi"
        connection_string: "Data Source=historian.company.com;Initial Catalog=PIArchive"
    
    # Control System Configuration
    control:
      scan_rate: 1.0
      data_retention: 168
      enable_auto_control: true
      enable_data_logging: true
      
      # PID Controller Settings
      pid_controllers:
        default_temperature:
          kp: 2.0
          ki: 0.1
          kd: 0.05
          output_limits: [0.0, 100.0]
          enable_anti_windup: true
        
        default_pressure:
          kp: 1.5
          ki: 0.05
          kd: 0.02
          output_limits: [0.0, 100.0]
        
        default_level:
          kp: 1.0
          ki: 0.08
          kd: 0.01
          output_limits: [0.0, 100.0]
      
      # Safety Configuration
      safety:
        emergency_stop_enabled: true
        safety_interlocks:
          - name: "High Temperature Interlock"
            condition: "any_temperature > 200"
            action: "emergency_stop"
            enabled: true
          - name: "High Pressure Interlock"
            condition: "any_pressure > 4.0"
            action: "emergency_stop"
            enabled: true
          - name: "Low Level Interlock"
            condition: "any_level < 5"
            action: "stop_heating"
            enabled: true
    
    # Physics Simulation Configuration
    simulation:
      enabled: true
      real_time_factor: 1.0
      time_step: 1.0
      max_simulation_time: 86400.0
      spatial_discretization: 50
      
      models:
        heat_transfer:
          enabled: true
          solver_type: "finite_difference"
          temporal_tolerance: 1e-6
          max_iterations: 1000
        
        fluid_dynamics:
          enabled: true
          reynolds_number: 10000
          prandtl_number: 7.0
        
        mixing:
          enabled: true
          mixing_time_constant: 60.0
          mixing_efficiency: 0.8
    
    # API Configuration
    api:
      host: "0.0.0.0"
      port: 8000
      title: "Tank Control System API"
      description: "REST API for industrial tank control and monitoring"
      version: "1.0.0"
      
      cors:
        allow_origins: ["*"]
        allow_methods: ["GET", "POST", "PUT", "DELETE"]
        allow_headers: ["*"]
      
      authentication:
        enabled: true
        type: "bearer"
    
    # Monitoring and Alerting
    monitoring:
      prometheus:
        enabled: true
        port: 8001
        metrics_path: "/metrics"
      
      logging:
        level: "INFO"
        format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        max_file_size: "10MB"
        backup_count: 5
      
      alerting:
        enabled: true
        email:
          enabled: true
          smtp_server: "smtp.company.com"
          smtp_port: 587
          recipients: ["<EMAIL>", "<EMAIL>"]
    
    # Feature Flags
    features:
      enable_machine_learning: true
      enable_predictive_maintenance: true
      enable_energy_optimization: true
      enable_advanced_physics: true
      enable_digital_twin: true
      enable_real_time_optimization: false
---
# Prometheus Configuration ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: tank-system
  labels:
    app: prometheus
    component: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    rule_files:
      - "tank_rules.yml"
    
    scrape_configs:
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
                - tank-system
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: kubernetes_namespace
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: kubernetes_pod_name
      
      - job_name: 'tank-management-service'
        static_configs:
          - targets: ['tank-management-service:8080']
        metrics_path: '/metrics'
      
      - job_name: 'api-gateway'
        static_configs:
          - targets: ['api-gateway-service:80']
        metrics_path: '/metrics'
      
      - job_name: 'control-service'
        static_configs:
          - targets: ['control-service:8082']
        metrics_path: '/metrics'
      
      - job_name: 'data-ingestion-service'
        static_configs:
          - targets: ['data-ingestion-service:8081']
        metrics_path: '/metrics'
  
  tank_rules.yml: |
    groups:
    - name: tank.rules
      rules:
      - alert: TankHighTemperature
        expr: tank_temperature_celsius > 180
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Tank {{ $labels.tank_id }} temperature too high"
          description: "Temperature is {{ $value }}°C"
      
      - alert: TankServiceDown
        expr: up{job=~"tank-.*"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Tank service {{ $labels.job }} is down"
          description: "Service has been down for more than 1 minute"
      
      - alert: HighMemoryUsage
        expr: (container_memory_usage_bytes / container_spec_memory_limit_bytes) > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on {{ $labels.pod }}"
          description: "Memory usage is above 90%"
      
      - alert: HighCPUUsage
        expr: rate(container_cpu_usage_seconds_total[5m]) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage on {{ $labels.pod }}"
          description: "CPU usage is above 80%"