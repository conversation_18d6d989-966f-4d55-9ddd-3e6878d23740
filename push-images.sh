#!/bin/bash

# Push script for Tank Control System Microservices
# This script pushes all microservice Docker images to the registry

set -e  # Exit on any error

# Configuration
REGISTRY="${DOCKER_REGISTRY:-tank-registry.io}"
TAG="${BUILD_TAG:-v1.0.0}"

echo "🚀 Pushing Tank Control System Images"
echo "Registry: $REGISTRY"
echo "Tag: $TAG"
echo "=============================================="

# Check if user is logged in to registry
echo "🔐 Checking registry authentication..."
if ! docker info | grep -q "Registry:"; then
    echo "⚠️  Please login to your Docker registry first:"
    echo "   docker login $REGISTRY"
    exit 1
fi

# Push all microservices
SERVICES=(
    "tank-management-service"
    "data-ingestion-service"
    "control-service"
    "event-bus-service"
    "api-gateway-service"
    "ml-service"
    "monitoring-service"
    "tank-development"
    "tank-system"
)

for service in "${SERVICES[@]}"; do
    echo "📤 Pushing $service..."
    
    # Push tagged version
    docker push "$REGISTRY/${service}:$TAG"
    
    # Push latest tag
    docker push "$REGISTRY/${service}:latest"
    
    echo "✅ Pushed $REGISTRY/${service}:$TAG"
done

echo ""
echo "🎉 All images pushed successfully!"
echo "=============================================="

# Show pushed images
echo "📋 Pushed images:"
for service in "${SERVICES[@]}"; do
    echo "  - $REGISTRY/${service}:$TAG"
    echo "  - $REGISTRY/${service}:latest"
done

echo ""
echo "💡 Images are now available in registry: $REGISTRY"
echo "   You can now deploy using:"
echo "   - Docker Compose: docker-compose -f docker-compose.microservices.yml up"
echo "   - Kubernetes: kubectl apply -f k8s/"
echo "   - Helm: helm install tank-system ./helm/tank-system"