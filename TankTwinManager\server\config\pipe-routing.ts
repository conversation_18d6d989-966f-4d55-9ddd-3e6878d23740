/**
 * Pipe Routing Configuration System
 * Defines the complete pipe network for hot-oil circulation and asphalt distribution
 * Based on real plant layout with proper flow directions and connections
 */

export interface PipeConnection {
  id: string;
  name: string;
  type: 'hot_oil_supply' | 'hot_oil_return' | 'asphalt_product' | 'asphalt_loading';
  startPoint: {
    type: 'boiler' | 'tank' | 'pump' | 'valve' | 'junction' | 'loading_station';
    id: string;
    position: [number, number, number];
    connectionPort: string;
  };
  endPoint: {
    type: 'boiler' | 'tank' | 'pump' | 'valve' | 'junction' | 'loading_station';
    id: string;
    position: [number, number, number];
    connectionPort: string;
  };
  specifications: {
    diameter: number; // mm
    length: number; // m
    material: 'carbon_steel' | 'stainless_steel' | 'alloy_steel';
    insulation: boolean;
    insulationType?: 'mineral_wool' | 'polyurethane' | 'ceramic';
    insulationThickness?: number; // mm
    tracing?: 'steam' | 'electric' | 'hot_oil';
    maxPressure: number; // bar
    maxTemperature: number; // °C
    flowDirection: 'unidirectional' | 'bidirectional';
  };
  routing: {
    waypoints: [number, number, number][]; // Intermediate points for complex routing
    elevation: number; // m above ground
    supportType: 'pipe_rack' | 'underground' | 'overhead' | 'ground_level';
    accessPoints: string[]; // Maintenance access points
  };
  instrumentation: {
    flowMeter?: string;
    pressureSensors?: string[];
    temperatureSensors?: string[];
    valves?: string[];
    isolationValves?: string[];
  };
}

export interface PipeNetwork {
  id: string;
  name: string;
  type: 'hot_oil_circulation' | 'asphalt_distribution';
  connections: PipeConnection[];
  mainHeaders: {
    supply?: PipeConnection;
    return?: PipeConnection;
    distribution?: PipeConnection[];
  };
  operationalData: {
    totalLength: number; // m
    totalVolume: number; // L
    operatingPressure: number; // bar
    operatingTemperature: number; // °C
    flowRate: number; // L/min
    pressureDrop: number; // bar
  };
  controlSystems: {
    pressureControl: boolean;
    temperatureControl: boolean;
    flowControl: boolean;
    automaticValves: string[];
  };
}

// Hot-Oil Circulation Network Configuration
export const HOT_OIL_PIPE_NETWORK: PipeNetwork = {
  id: 'HOT_OIL_CIRCULATION',
  name: 'Hot-Oil Circulation System',
  type: 'hot_oil_circulation',
  connections: [
    // Main supply line from boiler
    {
      id: 'HO-MAIN-SUPPLY',
      name: 'Hot-Oil Main Supply Line',
      type: 'hot_oil_supply',
      startPoint: {
        type: 'boiler',
        id: 'HOB-01',
        position: [-12, 0, 2],
        connectionPort: 'outlet'
      },
      endPoint: {
        type: 'junction',
        id: 'HO-JUNCTION-01',
        position: [-8, 1, 0],
        connectionPort: 'inlet'
      },
      specifications: {
        diameter: 150, // mm
        length: 25, // m
        material: 'carbon_steel',
        insulation: true,
        insulationType: 'mineral_wool',
        insulationThickness: 100,
        tracing: 'electric',
        maxPressure: 10,
        maxTemperature: 320,
        flowDirection: 'unidirectional'
      },
      routing: {
        waypoints: [[-12, 1, 2], [-10, 1, 1], [-8, 1, 0]],
        elevation: 1,
        supportType: 'pipe_rack',
        accessPoints: ['AP-HO-01', 'AP-HO-02']
      },
      instrumentation: {
        flowMeter: 'FT-HO-MAIN',
        pressureSensors: ['PT-HO-01', 'PT-HO-02'],
        temperatureSensors: ['TT-HO-01'],
        valves: ['HV-HO-01'],
        isolationValves: ['IV-HO-01', 'IV-HO-02']
      }
    },
    // Distribution lines to tanks (bottom row)
    {
      id: 'HO-DIST-01',
      name: 'Hot-Oil Distribution to Tank 01',
      type: 'hot_oil_supply',
      startPoint: {
        type: 'junction',
        id: 'HO-JUNCTION-01',
        position: [-8, 1, 0],
        connectionPort: 'outlet_01'
      },
      endPoint: {
        type: 'tank',
        id: 'ASP-01',
        position: [-6, -2, -8],
        connectionPort: 'heating_coil_inlet'
      },
      specifications: {
        diameter: 50,
        length: 12,
        material: 'stainless_steel',
        insulation: true,
        insulationType: 'mineral_wool',
        insulationThickness: 50,
        maxPressure: 8,
        maxTemperature: 300,
        flowDirection: 'unidirectional'
      },
      routing: {
        waypoints: [[-8, 1, 0], [-7, 1, -4], [-6, 0, -8], [-6, -2, -8]],
        elevation: 0,
        supportType: 'underground',
        accessPoints: ['AP-HO-T01']
      },
      instrumentation: {
        flowMeter: 'FT-HO-T01',
        temperatureSensors: ['TT-HO-T01-IN'],
        valves: ['HV-HO-T01']
      }
    },
    // Main return line to boiler
    {
      id: 'HO-MAIN-RETURN',
      name: 'Hot-Oil Main Return Line',
      type: 'hot_oil_return',
      startPoint: {
        type: 'junction',
        id: 'HO-RETURN-JUNCTION',
        position: [-8, 0.5, 0],
        connectionPort: 'inlet'
      },
      endPoint: {
        type: 'boiler',
        id: 'HOB-01',
        position: [-12, 0, 2],
        connectionPort: 'inlet'
      },
      specifications: {
        diameter: 150,
        length: 25,
        material: 'carbon_steel',
        insulation: true,
        insulationType: 'mineral_wool',
        insulationThickness: 100,
        maxPressure: 6,
        maxTemperature: 280,
        flowDirection: 'unidirectional'
      },
      routing: {
        waypoints: [[-8, 0.5, 0], [-10, 0.5, 1], [-12, 0.5, 2]],
        elevation: 0.5,
        supportType: 'pipe_rack',
        accessPoints: ['AP-HO-RET-01', 'AP-HO-RET-02']
      },
      instrumentation: {
        flowMeter: 'FT-HO-RETURN',
        pressureSensors: ['PT-HO-RET-01'],
        temperatureSensors: ['TT-HO-RET-01'],
        valves: ['HV-HO-RET-01']
      }
    }
  ],
  mainHeaders: {
    supply: undefined, // Will be set to main supply line
    return: undefined  // Will be set to main return line
  },
  operationalData: {
    totalLength: 250, // m
    totalVolume: 3500, // L
    operatingPressure: 4.5, // bar
    operatingTemperature: 280, // °C
    flowRate: 1000, // L/min
    pressureDrop: 1.5 // bar
  },
  controlSystems: {
    pressureControl: true,
    temperatureControl: true,
    flowControl: true,
    automaticValves: ['HV-HO-01', 'HV-HO-RET-01']
  }
};

// Asphalt Distribution Network Configuration
export const ASPHALT_PIPE_NETWORK: PipeNetwork = {
  id: 'ASPHALT_DISTRIBUTION',
  name: 'Asphalt Product Distribution System',
  type: 'asphalt_distribution',
  connections: [
    // Main distribution header
    {
      id: 'ASP-MAIN-HEADER',
      name: 'Asphalt Main Distribution Header',
      type: 'asphalt_product',
      startPoint: {
        type: 'junction',
        id: 'ASP-JUNCTION-MAIN',
        position: [0, 0.5, -6],
        connectionPort: 'inlet'
      },
      endPoint: {
        type: 'junction',
        id: 'ASP-JUNCTION-LOADING',
        position: [8, 0.5, -8],
        connectionPort: 'outlet'
      },
      specifications: {
        diameter: 200,
        length: 15,
        material: 'carbon_steel',
        insulation: true,
        insulationType: 'polyurethane',
        insulationThickness: 75,
        tracing: 'steam',
        maxPressure: 6,
        maxTemperature: 180,
        flowDirection: 'unidirectional'
      },
      routing: {
        waypoints: [[0, 0.5, -6], [4, 0.5, -7], [8, 0.5, -8]],
        elevation: 0.5,
        supportType: 'pipe_rack',
        accessPoints: ['AP-ASP-01', 'AP-ASP-02']
      },
      instrumentation: {
        flowMeter: 'FT-ASP-MAIN',
        pressureSensors: ['PT-ASP-01', 'PT-ASP-02'],
        temperatureSensors: ['TT-ASP-01'],
        valves: ['AV-ASP-MAIN-01', 'AV-ASP-MAIN-02']
      }
    },
    // Loading station connections
    {
      id: 'ASP-LOADING-01',
      name: 'Asphalt Line to Loading Station 01',
      type: 'asphalt_loading',
      startPoint: {
        type: 'junction',
        id: 'ASP-JUNCTION-LOADING',
        position: [8, 0.5, -8],
        connectionPort: 'outlet_01'
      },
      endPoint: {
        type: 'loading_station',
        id: 'LS-01',
        position: [8, 0, -10],
        connectionPort: 'inlet'
      },
      specifications: {
        diameter: 100,
        length: 5,
        material: 'stainless_steel',
        insulation: true,
        insulationType: 'polyurethane',
        insulationThickness: 50,
        tracing: 'steam',
        maxPressure: 5,
        maxTemperature: 170,
        flowDirection: 'unidirectional'
      },
      routing: {
        waypoints: [[8, 0.5, -8], [8, 0.2, -9], [8, 0, -10]],
        elevation: 0,
        supportType: 'ground_level',
        accessPoints: ['AP-ASP-LS01']
      },
      instrumentation: {
        flowMeter: 'FT-ASP-LS01',
        temperatureSensors: ['TT-ASP-LS01'],
        valves: ['AV-ASP-LS01']
      }
    }
  ],
  mainHeaders: {
    distribution: [] // Will be populated with distribution lines
  },
  operationalData: {
    totalLength: 180, // m
    totalVolume: 2800, // L
    operatingPressure: 3.5, // bar
    operatingTemperature: 150, // °C
    flowRate: 800, // L/min
    pressureDrop: 1.0 // bar
  },
  controlSystems: {
    pressureControl: true,
    temperatureControl: true,
    flowControl: true,
    automaticValves: ['AV-ASP-MAIN-01', 'AV-ASP-LS01']
  }
};

// Complete pipe routing configuration
export const PIPE_ROUTING_CONFIG = {
  networks: [HOT_OIL_PIPE_NETWORK, ASPHALT_PIPE_NETWORK],
  junctions: [
    {
      id: 'HO-JUNCTION-01',
      name: 'Hot-Oil Main Distribution Junction',
      position: [-8, 1, 0],
      type: 'distribution',
      connections: ['HO-MAIN-SUPPLY', 'HO-DIST-01', 'HO-DIST-02', 'HO-DIST-03']
    },
    {
      id: 'ASP-JUNCTION-MAIN',
      name: 'Asphalt Main Collection Junction',
      position: [0, 0.5, -6],
      type: 'collection',
      connections: ['ASP-T01-OUT', 'ASP-T02-OUT', 'ASP-T03-OUT', 'ASP-MAIN-HEADER']
    }
  ],
  valves: [
    {
      id: 'HV-HO-01',
      name: 'Hot-Oil Main Supply Valve',
      position: [-10, 1, 1.5],
      type: 'control',
      actuator: 'pneumatic',
      size: 150 // mm
    },
    {
      id: 'AV-ASP-MAIN-01',
      name: 'Asphalt Main Header Valve',
      position: [2, 0.5, -6.5],
      type: 'control',
      actuator: 'electric',
      size: 200 // mm
    }
  ],
  pumps: [
    {
      id: 'HO-PUMP-01',
      name: 'Hot-Oil Circulation Pump',
      position: [-11, 0, 2],
      type: 'centrifugal',
      specifications: {
        flowRate: 1200, // L/min
        head: 50, // m
        power: 15 // kW
      }
    },
    {
      id: 'ASP-PUMP-01',
      name: 'Asphalt Transfer Pump',
      position: [-1, 0, -6],
      type: 'positive_displacement',
      specifications: {
        flowRate: 800, // L/min
        pressure: 6, // bar
        power: 12 // kW
      }
    }
  ]
};
