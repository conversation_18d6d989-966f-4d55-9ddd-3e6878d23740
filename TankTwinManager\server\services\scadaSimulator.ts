import { Tank<PERSON><PERSON>, Alert } from '@shared/types';
import { nanoid } from 'nanoid';
import { MLPredictionService } from './mlPredictionService';
import { PLANT_LAYOUT_CONFIG } from '../config/plant-layout';
import { createHotOilBoiler, createHeatingCoil, HotOilBoiler, HeatingCoil } from '../models/HotOilSystem';
import { createLoadingStation, LoadingStation } from '../models/AsphaltLoadingSystem';
import { PipeRoutingService } from './pipeRoutingService';
import { SCADASensorMappingService } from './scadaSensorMappingService';
import { HotOilCirculationPhysics } from '../physics/HotOilCirculationPhysics';
import { ThreeTurnCoilThermalSimulation, CoilGeometry, FluidProperties } from '../physics/ThreeTurnCoilThermalSimulation';

export class SCADASimulator {
  private tanks: TankData[] = [];
  private intervalId: NodeJS.Timeout | null = null;
  private alertCallback?: (alert: Alert) => void;
  private updateCallback?: (tanks: TankData[]) => void;
  private mlService: MLPredictionService;

  // Enhanced plant systems
  private hotOilBoiler: HotOilBoiler;
  private heatingCoils: Map<number, HeatingCoil> = new Map();
  private loadingStations: LoadingStation[] = [];
  private pipeRoutingService: PipeRoutingService;
  private sensorMappingService: SCADASensorMappingService;
  private hotOilPhysics: HotOilCirculationPhysics;
  private coilThermalSimulations: Map<number, ThreeTurnCoilThermalSimulation> = new Map();
  private systemStartTime: Date = new Date();

  // Hot-oil circulation system operational data
  private hotOilSystemData = {
    supplyTemperature: 280, // °C
    returnTemperature: 260, // °C
    flowRate: 1000, // L/min total system flow
    systemPressure: 4.5, // bar
    totalHeatLoad: 500, // kW
    pumpSpeed: 1450, // RPM
    fuelConsumption: 150, // m³/h
    systemEfficiency: 83 // %
  };

  constructor() {
    this.mlService = new MLPredictionService();
    this.pipeRoutingService = new PipeRoutingService();
    this.sensorMappingService = new SCADASensorMappingService();
    this.hotOilPhysics = new HotOilCirculationPhysics();
    this.initializePlantSystems();
    this.initializeTanks();
  }

  private initializePlantSystems() {
    // Initialize hot-oil boiler
    this.hotOilBoiler = createHotOilBoiler();

    // Initialize heating coils for each tank
    PLANT_LAYOUT_CONFIG.tanks.forEach(tankConfig => {
      const heatingCoil = createHeatingCoil(tankConfig.id, [
        tankConfig.position[0],
        tankConfig.position[1] - 2, // Under the tank
        tankConfig.position[2]
      ]);
      this.heatingCoils.set(tankConfig.id, heatingCoil);
    });

    // Initialize loading stations
    this.loadingStations = PLANT_LAYOUT_CONFIG.loadingSystem.loadingStations.map(stationConfig =>
      createLoadingStation(stationConfig.id, stationConfig.position, stationConfig.connectedTanks)
    );

    // Initialize thermal simulations for each heating coil
    this.initializeCoilThermalSimulations();

    console.log(`Initialized plant systems: ${this.heatingCoils.size} heating coils, ${this.loadingStations.length} loading stations, ${this.coilThermalSimulations.size} thermal simulations`);
  }

  private initializeCoilThermalSimulations() {
    PLANT_LAYOUT_CONFIG.tanks.forEach(tankConfig => {
      const coilGeometry: CoilGeometry = {
        turns: 3,
        coilDiameter: tankConfig.heatingCoilConfig.specifications.coilDiameter / 1000, // Convert mm to m
        pipeDiameter: tankConfig.heatingCoilConfig.specifications.pipeDiameter / 1000, // Convert mm to m
        pipeWallThickness: tankConfig.heatingCoilConfig.specifications.wallThickness / 1000, // Convert mm to m
        totalLength: tankConfig.heatingCoilConfig.specifications.totalLength,
        elevationFromBottom: tankConfig.heatingCoilConfig.positioning.elevationFromBottom,
        material: {
          thermalConductivity: 16, // W/m·K for stainless steel
          density: 8000, // kg/m³
          specificHeat: 500 // J/kg·K
        }
      };

      const fluidProperties: FluidProperties = {
        hotOil: {
          density: 850,
          viscosity: 0.001,
          specificHeat: 2200,
          thermalConductivity: 0.12,
          temperature: 280
        },
        tankFluid: {
          density: tankConfig.thermalProperties.fluidProperties.density,
          viscosity: tankConfig.thermalProperties.fluidProperties.viscosity,
          specificHeat: tankConfig.thermalProperties.fluidProperties.specificHeat,
          thermalConductivity: tankConfig.thermalProperties.fluidProperties.thermalConductivity,
          temperature: 150
        }
      };

      const thermalSimulation = new ThreeTurnCoilThermalSimulation(coilGeometry, fluidProperties);
      this.coilThermalSimulations.set(tankConfig.id, thermalSimulation);
    });
  }

  private simulateHotOilCirculation() {
    // Run physics simulation
    const physicsState = this.hotOilPhysics.simulateTimeStep(2.0); // 2-second time step

    // Update boiler based on physics simulation
    const boiler = this.hotOilBoiler;
    const supplyTemp = physicsState.temperatures.get('HO-SUPPLY') || 280;
    const returnTemp = physicsState.temperatures.get('HO-RETURN') || 260;
    const mainFlow = physicsState.flowRates.get('HO-MAIN-SUPPLY') || 1000;

    // Update boiler operational data from physics
    boiler.operationalData.currentTemperature = supplyTemp;

    // Calculate heat demand from physics
    const totalHeatTransfer = Array.from(physicsState.heatTransferRates.values())
      .reduce((sum, rate) => sum + rate, 0);

    // Update fuel consumption based on actual heat transfer
    const heatDemandRatio = totalHeatTransfer / boiler.specifications.heatingCapacity;
    boiler.operationalData.fuelFlow = heatDemandRatio * boiler.specifications.fuelConsumption;
    boiler.operationalData.heatOutput = totalHeatTransfer;
    boiler.operationalData.efficiency = this.hotOilPhysics.getEnergyBalance().efficiency;

    // Update system-wide hot-oil data from physics
    this.hotOilSystemData.supplyTemperature = supplyTemp;
    this.hotOilSystemData.returnTemperature = returnTemp;
    this.hotOilSystemData.flowRate = mainFlow;
    this.hotOilSystemData.totalHeatLoad = totalHeatTransfer;
    this.hotOilSystemData.systemEfficiency = boiler.operationalData.efficiency;
    this.hotOilSystemData.fuelConsumption = boiler.operationalData.fuelFlow;

    // Update system pressure from physics
    const pumpPressure = physicsState.pressures.get('HO-PUMP-01-DISCHARGE') || 4.5;
    this.hotOilSystemData.systemPressure = pumpPressure;

    // Update heating coils with detailed thermal simulation
    this.heatingCoils.forEach((coil, tankId) => {
      const tank = this.tanks.find(t => t.id === tankId);
      const thermalSim = this.coilThermalSimulations.get(tankId);
      if (!tank || !thermalSim) return;

      const coilId = `HC-ASP-${tankId.toString().padStart(2, '0')}`;
      const coilFlow = physicsState.flowRates.get(coilId) || 100;

      // Set boundary conditions for detailed thermal simulation
      thermalSim.setBoundaryConditions({
        hotOilInletTemperature: supplyTemp,
        hotOilFlowRate: coilFlow,
        tankTemperature: tank.temperature,
        ambientTemperature: 20,
        tankLevel: tank.currentLevel,
        foulingResistance: 0.0002
      });

      // Run detailed thermal simulation
      const thermalState = thermalSim.simulate(thermalSim.getBoundaryConditions(), 2.0);

      // Update coil thermal data from detailed simulation
      coil.thermalData.hotOilInletTemp = supplyTemp;
      coil.thermalData.hotOilOutletTemp = thermalState.hotOilOutletTemperature;
      coil.thermalData.heatTransferRate = thermalState.totalHeatTransfer;
      coil.thermalData.tankTemperature = tank.temperature;
      coil.thermalData.efficiency = thermalState.thermalEfficiency;

      // Update operational data from detailed simulation
      coil.operationalData.deltaTemperature = supplyTemp - thermalState.hotOilOutletTemperature;
      coil.operationalData.heatDuty = thermalState.totalHeatTransfer;
      coil.operationalData.flowRate = coilFlow;
      coil.operationalData.pressure = 3.5 + thermalState.pressureDrop;
      coil.operationalData.operatingHours += 2.0 / 3600; // 2-second time step

      // Apply realistic heating effect to tank temperature using detailed simulation
      if (tank.temperature < tank.targetTemperature && thermalState.totalHeatTransfer > 0) {
        const tankConfig = PLANT_LAYOUT_CONFIG.tanks.find(t => t.id === tankId);
        if (tankConfig) {
          const tankMass = tank.capacity * tankConfig.thermalProperties.fluidProperties.density / 1000; // kg
          const specificHeat = tankConfig.thermalProperties.fluidProperties.specificHeat; // J/kg·K
          const heatingRate = (thermalState.totalHeatTransfer * 1000) / (tankMass * specificHeat); // °C/s

          tank.temperature += heatingRate * 2.0; // 2-second time step
          tank.temperature = Math.min(tank.targetTemperature + 2, tank.temperature); // Prevent overheating
        }
      }
    });

    // Update boiler operating hours
    boiler.operationalData.operatingHours += deltaTime / 3600;
  }

  private updateSCADASensorData() {
    // Update tank sensor data
    this.tanks.forEach(tank => {
      // Temperature sensors
      this.sensorMappingService.updateSensorData(
        `TT_ASP_${tank.id.toString().padStart(2, '0')}`,
        tank.temperature,
        'simulation'
      );

      // Level sensors
      this.sensorMappingService.updateSensorData(
        `LT_ASP_${tank.id.toString().padStart(2, '0')}`,
        tank.currentLevel,
        'simulation'
      );

      // Pressure sensors
      this.sensorMappingService.updateSensorData(
        `PT_ASP_${tank.id.toString().padStart(2, '0')}`,
        tank.pressure,
        'simulation'
      );
    });

    // Update hot-oil boiler sensor data
    const boiler = this.hotOilBoiler;
    this.sensorMappingService.updateSensorData(
      'TT_HOB_SUPPLY',
      boiler.operationalData.currentTemperature,
      'simulation'
    );

    this.sensorMappingService.updateSensorData(
      'FT_HOB_FUEL',
      boiler.operationalData.fuelFlow,
      'simulation'
    );

    // Update hot-oil circulation sensor data
    this.sensorMappingService.updateSensorData(
      'FT_HO_MAIN_SUPPLY',
      this.hotOilSystemData.flowRate,
      'simulation'
    );

    // Update loading station sensor data
    this.loadingStations.forEach(station => {
      this.sensorMappingService.updateSensorData(
        `FT_${station.id}_FLOW`,
        station.operationalData.currentFlowRate,
        'simulation'
      );

      this.sensorMappingService.updateSensorData(
        `DI_${station.id}_TRUCK_PRESENT`,
        station.operationalData.truckPresent,
        'simulation'
      );
    });
  }

  private simulateLoadingStations() {
    this.loadingStations.forEach(station => {
      // Simulate truck arrivals (random events)
      if (!station.operationalData.truckPresent && Math.random() < 0.001) { // 0.1% chance per update
        // New truck arrives
        station.operationalData.truckPresent = true;
        station.truckData.truckId = `TRK-${Math.floor(Math.random() * 9999).toString().padStart(4, '0')}`;
        station.truckData.capacity = 25000 + Math.random() * 10000; // 25-35k liters
        station.truckData.currentVolume = 0;
        station.truckData.targetVolume = station.truckData.capacity * (0.8 + Math.random() * 0.2); // 80-100% fill
        station.truckData.arrivalTime = new Date();

        // Estimate loading time based on target volume and flow rate
        const loadingTimeMinutes = station.truckData.targetVolume / station.specifications.maxFlowRate;
        station.truckData.estimatedDepartureTime = new Date(Date.now() + loadingTimeMinutes * 60 * 1000);

        console.log(`Truck ${station.truckData.truckId} arrived at ${station.name}, target: ${station.truckData.targetVolume}L`);
      }

      // Simulate loading process
      if (station.operationalData.truckPresent && !station.operationalData.loadingInProgress) {
        // Start loading if truck is positioned and safety checks pass
        if (station.controlSystem.safetyInterlocks.truckPositionOk &&
            station.controlSystem.safetyInterlocks.temperatureOk) {
          station.operationalData.loadingInProgress = true;
          station.operationalData.currentFlowRate = station.controlSystem.flowRateSetpoint;
        }
      }

      // Continue loading process
      if (station.operationalData.loadingInProgress) {
        const loadingRate = station.operationalData.currentFlowRate / 60; // L/second
        const volumeToAdd = loadingRate * 2; // 2-second update interval

        station.truckData.currentVolume += volumeToAdd;
        station.operationalData.totalVolumeLoaded += volumeToAdd;
        station.operationalData.loadingTime += 2 / 60; // minutes

        // Check if loading is complete
        if (station.truckData.currentVolume >= station.truckData.targetVolume) {
          // Loading complete
          station.operationalData.loadingInProgress = false;
          station.operationalData.currentFlowRate = 0;
          station.operationalData.dailyLoadings++;

          console.log(`Loading complete for truck ${station.truckData.truckId}: ${station.truckData.currentVolume}L loaded`);

          // Truck departs after a short delay
          setTimeout(() => {
            station.operationalData.truckPresent = false;
            station.truckData = {
              truckId: '',
              capacity: 0,
              currentVolume: 0,
              targetVolume: 0,
              arrivalTime: new Date(),
              estimatedDepartureTime: new Date(),
              driverInfo: { name: '', license: '', company: '' }
            };
          }, 5000); // 5-second departure delay
        }

        // Update connected tanks (reduce levels)
        const volumePerTank = volumeToAdd / station.specifications.numberOfArms;
        station.loadingArms.forEach(arm => {
          if (arm.operationalData.isFlowing) {
            // Find connected tanks and reduce their levels
            const connectedTanks = this.tanks.filter(tank =>
              station.connectedTanks?.includes(tank.id)
            );

            connectedTanks.forEach(tank => {
              tank.currentLevel = Math.max(0, tank.currentLevel - volumePerTank / connectedTanks.length);
            });

            // Update arm operational data
            arm.operationalData.flowRate = station.operationalData.currentFlowRate / station.specifications.numberOfArms;
            arm.operationalData.totalVolumeDelivered += volumePerTank;
            arm.operationalData.pressure = 2.5 + Math.random() * 1.0;
            arm.operationalData.temperature = 145 + Math.random() * 10;
          }
        });
      }

      // Update safety interlocks
      station.controlSystem.safetyInterlocks.truckPositionOk = station.operationalData.truckPresent;
      station.controlSystem.safetyInterlocks.temperatureOk = true; // Simplified
      station.controlSystem.safetyInterlocks.armPositionOk = true; // Simplified
      station.controlSystem.safetyInterlocks.emergencyStopActive = false; // Simplified
    });
  }

  private initializeTanks() {
    // Initialize tanks using the plant layout configuration
    PLANT_LAYOUT_CONFIG.tanks.forEach((tankConfig, index) => {
      const i = tankConfig.id;
      const now = new Date();
      const heatingCoil = this.heatingCoils.get(i);

      this.tanks.push({
        id: i,
        name: tankConfig.name,
        temperature: 140 + Math.random() * 20, // 140-160°C base
        targetTemperature: 150,
        capacity: 80000 + Math.random() * 40000, // 80k-120k liters (larger realistic tanks)
        currentLevel: 30000 + Math.random() * 60000, // 30k-90k liters
        status: 'normal',
        boilerStatus: Math.random() > 0.2 ? 'active' : 'inactive',
        lastUpdated: now,
        position: tankConfig.position,
        sensors: {
          temperatureSensor: {
            value: 140 + Math.random() * 20,
            timestamp: now,
            status: 'online',
            accuracy: 99.0 + Math.random(),
            lastCalibration: new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000)
          },
          levelSensor: {
            value: 20000 + Math.random() * 40000,
            timestamp: now,
            status: 'online',
            accuracy: 98.5 + Math.random() * 1.5,
            lastCalibration: new Date(now.getTime() - Math.random() * 14 * 24 * 60 * 60 * 1000)
          },
          pressureSensor: {
            value: 2.3 + Math.random() * 0.4,
            timestamp: now,
            status: 'online',
            accuracy: 99.2 + Math.random() * 0.6,
            lastCalibration: new Date(now.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000)
          },
          flowSensor: {
            value: 12 + Math.random() * 8,
            timestamp: now,
            status: 'online',
            accuracy: 97.8 + Math.random() * 1.5,
            lastCalibration: new Date(now.getTime() - Math.random() * 21 * 24 * 60 * 60 * 1000)
          }
        },
        prediction: {
          nextBoilerAction: 'no_action',
          actionConfidence: 0.5,
          predictedTemperature: [],
          timeToTarget: -1,
          energyOptimization: 75,
          failureRisk: 0.1,
          maintenanceWindow: null
        },
        efficiency: 85 + Math.random() * 10,
        maintenanceScore: Math.random() * 100,
        energyConsumption: 20 + Math.random() * 15
      });
    });
  }

  public startSimulation(updateInterval: number = 2000) {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }

    this.intervalId = setInterval(() => {
      this.updateTanks();
      this.checkAlerts();
      
      if (this.updateCallback) {
        this.updateCallback([...this.tanks]);
      }
    }, updateInterval);

    console.log('SCADA simulation started');
  }

  public stopSimulation() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    console.log('SCADA simulation stopped');
  }

  private updateTanks() {
    // Simulate enhanced plant systems
    this.simulateHotOilCirculation();
    this.simulateLoadingStations();

    // Update SCADA sensor data
    this.updateSCADASensorData();

    this.tanks.forEach(tank => {
      // Add realistic sensor data simulation
      const enhancedTank = this.mlService.simulateRealtimeSensorData(tank);
      
      // Use ML prediction for temperature control
      const prediction = this.mlService.generatePrediction(enhancedTank);
      
      // Apply ML-driven temperature changes
      const tempDiff = tank.targetTemperature - tank.temperature;
      let tempChange = tempDiff * 0.1 + (Math.random() - 0.5) * 2;
      
      // Apply ML optimization if confidence is high
      if (prediction.actionConfidence > 0.8) {
        if (prediction.nextBoilerAction === 'start' && tank.boilerStatus === 'inactive') {
          tank.boilerStatus = 'active';
          tempChange += 1.5; // Faster heating when ML suggests starting
        } else if (prediction.nextBoilerAction === 'stop' && tank.boilerStatus === 'active') {
          tank.boilerStatus = 'inactive';
          tempChange -= 0.5; // Gradual cooling when ML suggests stopping
        }
      } else {
        // Fallback to traditional control
        if (tank.temperature < tank.targetTemperature - 5 && tank.boilerStatus === 'inactive') {
          tank.boilerStatus = 'active';
        } else if (tank.temperature > tank.targetTemperature + 5 && tank.boilerStatus === 'active') {
          tank.boilerStatus = 'inactive';
        }
      }
      
      tank.temperature = Math.max(100, Math.min(200, tank.temperature + tempChange));

      // Simulate level changes (consumption and refill)
      const levelChange = (Math.random() - 0.6) * 1000; // Slightly decreasing trend
      tank.currentLevel = Math.max(0, Math.min(tank.capacity, tank.currentLevel + levelChange));

      // Randomly set maintenance status
      if (Math.random() < 0.001) { // 0.1% chance per update
        tank.boilerStatus = 'maintenance';
      }

      // Apply enhanced fields from ML service
      tank.sensors = enhancedTank.sensors;
      tank.prediction = prediction;
      tank.efficiency = enhancedTank.efficiency;
      tank.maintenanceScore = enhancedTank.maintenanceScore;
      tank.energyConsumption = enhancedTank.energyConsumption;

      // Update status based on conditions and ML risk assessment
      tank.status = this.calculateTankStatus(tank);
      tank.lastUpdated = new Date();
    });

    // Update ML service with new data
    this.mlService.updateHistoricalData(this.tanks);
  }

  private calculateTankStatus(tank: TankData): 'normal' | 'warning' | 'critical' {
    const tempDiff = Math.abs(tank.temperature - tank.targetTemperature);
    const levelPercentage = (tank.currentLevel / tank.capacity) * 100;

    // Critical conditions
    if (tempDiff > 15 || levelPercentage < 10 || levelPercentage > 95 || tank.boilerStatus === 'maintenance') {
      return 'critical';
    }

    // Warning conditions
    if (tempDiff > 8 || levelPercentage < 20 || levelPercentage > 85) {
      return 'warning';
    }

    return 'normal';
  }

  private checkAlerts() {
    this.tanks.forEach(tank => {
      const tempDiff = Math.abs(tank.temperature - tank.targetTemperature);
      const levelPercentage = (tank.currentLevel / tank.capacity) * 100;

      // Temperature alerts
      if (tempDiff > 20) {
        this.createAlert(tank.id, 'temperature', 'critical', 
          `Critical temperature deviation: ${tempDiff.toFixed(1)}°C from target`);
      } else if (tempDiff > 10) {
        this.createAlert(tank.id, 'temperature', 'high', 
          `High temperature deviation: ${tempDiff.toFixed(1)}°C from target`);
      }

      // Level alerts
      if (levelPercentage < 5) {
        this.createAlert(tank.id, 'level', 'critical', 
          `Critically low level: ${levelPercentage.toFixed(1)}%`);
      } else if (levelPercentage < 15) {
        this.createAlert(tank.id, 'level', 'medium', 
          `Low level warning: ${levelPercentage.toFixed(1)}%`);
      } else if (levelPercentage > 95) {
        this.createAlert(tank.id, 'level', 'high', 
          `Tank nearly full: ${levelPercentage.toFixed(1)}%`);
      }

      // Boiler alerts
      if (tank.boilerStatus === 'maintenance') {
        this.createAlert(tank.id, 'boiler', 'high', 
          'Boiler requires maintenance');
      }
    });
  }

  private createAlert(tankId: number, type: Alert['type'], severity: Alert['severity'], message: string) {
    // Avoid duplicate alerts by checking recent alerts (simplified for demo)
    if (Math.random() < 0.05) { // 5% chance to create alert to avoid spam
      const alert: Alert = {
        id: nanoid(),
        tankId,
        type,
        severity,
        message,
        timestamp: new Date(),
        acknowledged: false
      };

      if (this.alertCallback) {
        this.alertCallback(alert);
      }
    }
  }

  public setAlertCallback(callback: (alert: Alert) => void) {
    this.alertCallback = callback;
  }

  public setUpdateCallback(callback: (tanks: TankData[]) => void) {
    this.updateCallback = callback;
  }

  public getTanks(): TankData[] {
    return [...this.tanks];
  }

  public getHotOilSystem() {
    return {
      boiler: this.hotOilBoiler,
      systemData: this.hotOilSystemData,
      heatingCoils: Array.from(this.heatingCoils.entries()).map(([tankId, coil]) => ({
        tankId,
        ...coil
      }))
    };
  }

  public getLoadingStations() {
    return [...this.loadingStations];
  }

  public getSystemOverview() {
    const pipeSystemStatus = this.pipeRoutingService.getSystemStatus();
    const pipeAlarms = this.pipeRoutingService.getAlarms();

    return {
      tanks: this.tanks.length,
      hotOilBoiler: {
        temperature: this.hotOilBoiler.operationalData.currentTemperature,
        efficiency: this.hotOilBoiler.operationalData.efficiency,
        fuelFlow: this.hotOilBoiler.operationalData.fuelFlow,
        status: this.hotOilBoiler.status.isRunning ? 'running' : 'stopped'
      },
      hotOilSystem: this.hotOilSystemData,
      loadingStations: this.loadingStations.map(station => ({
        id: station.id,
        truckPresent: station.operationalData.truckPresent,
        loadingInProgress: station.operationalData.loadingInProgress,
        dailyLoadings: station.operationalData.dailyLoadings,
        totalVolumeLoaded: station.operationalData.totalVolumeLoaded
      })),
      pipeNetworks: Array.from(pipeSystemStatus.values()),
      pipeAlarms: pipeAlarms.filter(alarm => !alarm.acknowledged),
      systemUptime: Math.floor((Date.now() - this.systemStartTime.getTime()) / 1000 / 60), // minutes
      totalEnergyConsumption: this.hotOilBoiler.operationalData.fuelFlow *
        this.hotOilBoiler.operationalData.operatingHours
    };
  }

  public getPipeNetworks() {
    return this.pipeRoutingService.getNetworks();
  }

  public getPipeFlowCalculations() {
    return Array.from(this.pipeRoutingService.getFlowCalculations().entries()).map(([pipeId, calc]) => ({
      pipeId,
      ...calc
    }));
  }

  public getPipeAlarms() {
    return this.pipeRoutingService.getAlarms();
  }

  public acknowledgePipeAlarm(alarmId: string): boolean {
    return this.pipeRoutingService.acknowledgeAlarm(alarmId);
  }

  public getSCADASensorData(tagName?: string) {
    return this.sensorMappingService.getSensorData(tagName);
  }

  public getSCADAAlarms() {
    return this.sensorMappingService.getActiveAlarms();
  }

  public acknowledgeSCADAAlarm(alarmId: string, acknowledgedBy: string): boolean {
    return this.sensorMappingService.acknowledgeAlarm(alarmId, acknowledgedBy);
  }

  public getSCADASensorMappings() {
    return this.sensorMappingService.getAllSensorMappings();
  }

  public getSCADATagGroups() {
    return this.sensorMappingService.getTagGroups();
  }

  public getSCADASensorReport() {
    return this.sensorMappingService.generateSensorReport();
  }

  public getHotOilPhysicsState() {
    return this.hotOilPhysics.getCurrentState();
  }

  public getSystemEnergyBalance() {
    return this.hotOilPhysics.getEnergyBalance();
  }

  public getSystemEfficiency() {
    return this.hotOilPhysics.getSystemEfficiency();
  }

  public setPumpSpeed(pumpId: string, speed: number): boolean {
    return this.hotOilPhysics.setPumpSpeed(pumpId, speed);
  }

  public setValvePosition(valveId: string, position: number): boolean {
    return this.hotOilPhysics.setValvePosition(valveId, position);
  }

  public getCoilThermalStates() {
    const thermalStates: { [tankId: number]: any } = {};
    this.coilThermalSimulations.forEach((simulation, tankId) => {
      thermalStates[tankId] = simulation.getCurrentState();
    });
    return thermalStates;
  }

  public getCoilThermalState(tankId: number) {
    const simulation = this.coilThermalSimulations.get(tankId);
    return simulation ? simulation.getCurrentState() : null;
  }

  public optimizeCoilFlowRate(tankId: number, targetHeatTransfer: number): number {
    const simulation = this.coilThermalSimulations.get(tankId);
    return simulation ? simulation.calculateOptimalFlowRate(targetHeatTransfer) : 0;
  }

  public updateTankThresholds(tankId: number, thresholds: any) {
    const tank = this.tanks.find(t => t.id === tankId);
    if (tank) {
      tank.targetTemperature = thresholds.targetTemperature;
      console.log(`Updated thresholds for tank ${tankId}:`, thresholds);
    }
  }
}
