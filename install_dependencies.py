"""
Install Dependencies Script
Installs required packages for screen data extraction
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a single package"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install {package}: {e}")
        return False

def main():
    """Install essential packages for screen data extraction"""
    
    print("Installing dependencies for SCADA screen data extraction...")
    print("=" * 60)
    
    # Essential packages in order of priority
    essential_packages = [
        "numpy",
        "pandas", 
        "pillow",
        "opencv-python",
        "mss",
        "pyautogui",
        "fastapi",
        "uvicorn[standard]",
        "pydantic",
        "aiohttp",
        "structlog"
    ]
    
    # OCR packages (may need system dependencies)
    ocr_packages = [
        "pytesseract",
        "easyocr"
    ]
    
    # Optional packages
    optional_packages = [
        "torch",
        "scikit-learn", 
        "matplotlib",
        "plotly",
        "influxdb-client",
        "redis"
    ]
    
    success_count = 0
    total_packages = len(essential_packages)
    
    # Install essential packages
    print("Installing essential packages...")
    for package in essential_packages:
        if install_package(package):
            success_count += 1
    
    # Install OCR packages
    print("\nInstalling OCR packages...")
    print("Note: These may require additional system dependencies (Tesseract OCR)")
    for package in ocr_packages:
        if install_package(package):
            success_count += 1
        total_packages += 1
    
    # Try to install optional packages
    print("\nInstalling optional packages...")
    print("(These may take longer or fail on some systems - that's OK)")
    for package in optional_packages:
        try:
            install_package(package)
            success_count += 1
        except:
            print(f"⚠ Skipping {package} (optional)")
        total_packages += 1
    
    print("\n" + "=" * 60)
    print(f"Installation complete: {success_count}/{total_packages} packages installed")
    
    # Check if we can run basic functionality
    print("\nTesting basic functionality...")
    
    try:
        import numpy
        import pandas  
        import PIL
        print("✓ Core data processing libraries available")
    except ImportError as e:
        print(f"✗ Core libraries missing: {e}")
    
    try:
        import mss
        import cv2
        print("✓ Screen capture libraries available")
    except ImportError as e:
        print(f"✗ Screen capture libraries missing: {e}")
        print("  You may need to install these manually:")
        print("  pip install mss opencv-python pillow")
    
    try:
        import pytesseract
        print("✓ Tesseract OCR available")
        print("  Note: You may need to install Tesseract OCR system package:")
        print("  Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki")
        print("  Linux: sudo apt-get install tesseract-ocr")
        print("  Mac: brew install tesseract")
    except ImportError:
        print("⚠ Tesseract OCR not available (optional)")
    
    try:
        import easyocr
        print("✓ EasyOCR available")
    except ImportError:
        print("⚠ EasyOCR not available (optional)")
    
    try:
        import fastapi
        import uvicorn
        print("✓ FastAPI web framework available")
    except ImportError:
        print("⚠ FastAPI not available")
    
    print("\nNext steps:")
    print("1. Run: python calibrate_screen.py")
    print("   This will help you identify screen regions for data extraction")
    print("2. Configure your SCADA screen layout")
    print("3. Run the screen data extraction system")
    
    if success_count < len(essential_packages):
        print("\n⚠ Some essential packages failed to install.")
        print("You may need to:")
        print("- Update pip: python -m pip install --upgrade pip")
        print("- Install Microsoft Visual C++ Build Tools")
        print("- Try installing packages individually")

if __name__ == "__main__":
    main()