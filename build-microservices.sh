#!/bin/bash

# Build script for Tank Control System Microservices
# This script builds all microservice Docker images

set -e  # Exit on any error

# Configuration
REGISTRY="${DOCKER_REGISTRY:-tank-registry.io}"
TAG="${BUILD_TAG:-v1.0.0}"
BUILD_ARGS="${BUILD_ARGS:-}"

echo "🚀 Starting Tank Control System Microservices Build"
echo "Registry: $REGISTRY"
echo "Tag: $TAG"
echo "=============================================="

# Build base image first
echo "📦 Building base image..."
docker build \
    --target base \
    --tag "$REGISTRY/tank-base:$TAG" \
    $BUILD_ARGS \
    .

# Build all microservices
SERVICES=(
    "tank-management-service"
    "data-ingestion-service"
    "control-service"
    "event-bus-service"
    "api-gateway-service"
    "ml-service"
    "monitoring-service"
)

for service in "${SERVICES[@]}"; do
    echo "📦 Building $service..."
    docker build \
        --target "$service" \
        --tag "$REGISTRY/${service}:$TAG" \
        --tag "$REGISTRY/${service}:latest" \
        $BUILD_ARGS \
        .
    
    echo "✅ Built $REGISTRY/${service}:$TAG"
done

# Build development image
echo "📦 Building development image..."
docker build \
    --target development \
    --tag "$REGISTRY/tank-development:$TAG" \
    --tag "$REGISTRY/tank-development:latest" \
    $BUILD_ARGS \
    .

# Build production image (main tank system)
echo "📦 Building production image..."
docker build \
    --target production \
    --tag "$REGISTRY/tank-system:$TAG" \
    --tag "$REGISTRY/tank-system:latest" \
    $BUILD_ARGS \
    .

echo ""
echo "🎉 All images built successfully!"
echo "=============================================="

# Show built images
echo "📋 Built images:"
for service in "${SERVICES[@]}"; do
    echo "  - $REGISTRY/${service}:$TAG"
done
echo "  - $REGISTRY/tank-development:$TAG"
echo "  - $REGISTRY/tank-system:$TAG"

echo ""
echo "💡 Next steps:"
echo "  1. Push images: ./push-images.sh"
echo "  2. Deploy with Docker Compose: docker-compose -f docker-compose.microservices.yml up"
echo "  3. Deploy with Kubernetes: kubectl apply -f k8s/"
echo "  4. Deploy with Helm: helm install tank-system ./helm/tank-system"