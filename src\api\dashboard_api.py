"""
Dashboard API Server
REST API for accessing dashboard data and metrics
"""

import asyncio
import logging
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import asdict
import traceback

try:
    from fastapi import FastAPI, HTTPException, Query, BackgroundTasks
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.responses import JSONResponse
    from pydantic import BaseModel
    import uvicorn
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    print("Warning: FastAPI not available. REST API will not be available.")

try:
    from flask import Flask, jsonify, request
    from flask_cors import CORS
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    print("Warning: Flask not available. Alternative API framework will be used.")

# Import our dashboard manager
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

try:
    from monitoring.dashboard_manager import DashboardManager, create_dashboard_manager
    from monitoring.dashboard_manager import DataSource, MockDataSource
    DASHBOARD_AVAILABLE = True
except ImportError:
    DASHBOARD_AVAILABLE = False
    print("Warning: Dashboard manager not available.")

logger = logging.getLogger(__name__)


# Pydantic models for API requests/responses
if FASTAPI_AVAILABLE:
    class MetricRequest(BaseModel):
        metric_name: str
        start_time: Optional[datetime] = None
        end_time: Optional[datetime] = None
        window_hours: int = 1
    
    class AlertRuleRequest(BaseModel):
        name: str
        description: str
        metric_name: str
        condition: str
        threshold_value: float
        severity: str = "warning"
    
    class DashboardResponse(BaseModel):
        dashboard_id: str
        name: str
        description: str
        type: str
        widgets: List[Dict[str, Any]]
        alerts: List[Dict[str, Any]]
        last_updated: str


class DashboardAPI:
    """Dashboard API server"""
    
    def __init__(self, dashboard_manager: Optional[DashboardManager] = None):
        self.dashboard_manager = dashboard_manager or create_dashboard_manager()
        self.app = None
        self.is_running = False
        
        if FASTAPI_AVAILABLE:
            self._create_fastapi_app()
        elif FLASK_AVAILABLE:
            self._create_flask_app()
        else:
            logger.error("No web framework available for API")
    
    def _create_fastapi_app(self):
        """Create FastAPI application"""
        self.app = FastAPI(
            title="MLOps Digital Twin Dashboard API",
            description="REST API for accessing dashboard data and metrics",
            version="1.0.0"
        )
        
        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Define routes
        @self.app.get("/")
        async def root():
            return {"message": "MLOps Digital Twin Dashboard API", "version": "1.0.0"}
        
        @self.app.get("/health")
        async def health_check():
            return {"status": "healthy", "timestamp": datetime.now().isoformat()}
        
        @self.app.get("/api/v1/dashboards")
        async def get_dashboards():
            """Get list of available dashboards"""
            try:
                dashboards = self.dashboard_manager.get_available_dashboards()
                return {"dashboards": dashboards}
            except Exception as e:
                logger.error(f"Error getting dashboards: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/v1/dashboards/{dashboard_id}")
        async def get_dashboard(dashboard_id: str):
            """Get dashboard data"""
            try:
                dashboard_data = await self.dashboard_manager.get_dashboard_data(dashboard_id)
                if dashboard_data is None:
                    raise HTTPException(status_code=404, detail="Dashboard not found")
                return dashboard_data
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error getting dashboard {dashboard_id}: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/v1/overview")
        async def get_system_overview():
            """Get system overview"""
            try:
                overview = await self.dashboard_manager.get_system_overview()
                return overview
            except Exception as e:
                logger.error(f"Error getting system overview: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/v1/alerts")
        async def get_alerts():
            """Get alert summary"""
            try:
                alert_summary = await self.dashboard_manager.get_alert_summary()
                return alert_summary
            except Exception as e:
                logger.error(f"Error getting alerts: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/v1/alerts/active")
        async def get_active_alerts():
            """Get active alerts"""
            try:
                active_alerts = self.dashboard_manager.alert_manager.get_active_alerts()
                return {"active_alerts": active_alerts}
            except Exception as e:
                logger.error(f"Error getting active alerts: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/v1/alerts/history")
        async def get_alert_history(hours: int = Query(24, description="Hours to look back")):
            """Get alert history"""
            try:
                alert_history = self.dashboard_manager.alert_manager.get_alert_history(hours)
                return {"alert_history": alert_history, "hours": hours}
            except Exception as e:
                logger.error(f"Error getting alert history: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/v1/metrics/{metric_name}/current")
        async def get_current_metric(metric_name: str):
            """Get current metric value"""
            try:
                value = await self.dashboard_manager.metric_calculator.calculate_metric(metric_name)
                if value is None:
                    raise HTTPException(status_code=404, detail="Metric not found or no data available")
                
                return {
                    "metric_name": metric_name,
                    "value": value,
                    "timestamp": datetime.now().isoformat()
                }
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error getting current metric {metric_name}: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/v1/metrics/{metric_name}/history")
        async def get_metric_history(
            metric_name: str,
            hours: int = Query(1, description="Hours of history to retrieve")
        ):
            """Get metric history"""
            try:
                end_time = datetime.now()
                start_time = end_time - timedelta(hours=hours)
                
                df = await self.dashboard_manager.data_source.get_metric_data(
                    metric_name, start_time, end_time
                )
                
                if df is None or df.empty:
                    raise HTTPException(status_code=404, detail="No data available for metric")
                
                # Convert DataFrame to JSON-serializable format
                data = []
                for _, row in df.iterrows():
                    data.append({
                        "timestamp": row['timestamp'].isoformat(),
                        "value": float(row['value'])
                    })
                
                return {
                    "metric_name": metric_name,
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "data": data
                }
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error getting metric history {metric_name}: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/v1/metrics/{metric_name}/chart")
        async def get_metric_chart(metric_name: str, request: MetricRequest):
            """Get metric chart data"""
            try:
                chart_data = await self.dashboard_manager.visualization_engine.create_time_series_chart(
                    metric_name,
                    f"{metric_name.title()} Chart",
                    request.window_hours
                )
                
                if chart_data is None:
                    raise HTTPException(status_code=404, detail="No chart data available")
                
                return {
                    "metric_name": metric_name,
                    "chart_data": chart_data,
                    "window_hours": request.window_hours
                }
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error getting metric chart {metric_name}: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/v1/metrics")
        async def get_available_metrics():
            """Get list of available metrics"""
            try:
                metrics = []
                for name, metric_def in self.dashboard_manager.metrics.items():
                    metrics.append({
                        "name": name,
                        "title": metric_def.title,
                        "description": metric_def.description,
                        "type": metric_def.metric_type.value,
                        "unit": metric_def.unit
                    })
                
                return {"metrics": metrics}
            except Exception as e:
                logger.error(f"Error getting available metrics: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/v1/alerts/rules")
        async def create_alert_rule(rule: AlertRuleRequest):
            """Create a new alert rule"""
            try:
                from monitoring.dashboard_manager import AlertRule, AlertSeverity
                
                alert_rule = AlertRule(
                    name=rule.name,
                    description=rule.description,
                    metric_name=rule.metric_name,
                    condition=rule.condition,
                    threshold_value=rule.threshold_value,
                    severity=AlertSeverity(rule.severity)
                )
                
                success = self.dashboard_manager.alert_manager.add_alert_rule(alert_rule)
                
                if success:
                    return {"message": "Alert rule created successfully", "rule_name": rule.name}
                else:
                    raise HTTPException(status_code=400, detail="Failed to create alert rule")
            
            except Exception as e:
                logger.error(f"Error creating alert rule: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/v1/system/status")
        async def get_system_status():
            """Get detailed system status"""
            try:
                overview = await self.dashboard_manager.get_system_overview()
                alert_summary = await self.dashboard_manager.get_alert_summary()
                
                return {
                    "overview": overview,
                    "alerts": alert_summary,
                    "monitoring_active": self.dashboard_manager.is_running,
                    "api_status": "running",
                    "timestamp": datetime.now().isoformat()
                }
            except Exception as e:
                logger.error(f"Error getting system status: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/v1/system/start")
        async def start_monitoring(background_tasks: BackgroundTasks):
            """Start monitoring system"""
            try:
                if not self.dashboard_manager.is_running:
                    background_tasks.add_task(self.dashboard_manager.start_monitoring)
                    return {"message": "Monitoring system started"}
                else:
                    return {"message": "Monitoring system already running"}
            except Exception as e:
                logger.error(f"Error starting monitoring: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/v1/system/stop")
        async def stop_monitoring(background_tasks: BackgroundTasks):
            """Stop monitoring system"""
            try:
                if self.dashboard_manager.is_running:
                    background_tasks.add_task(self.dashboard_manager.stop_monitoring)
                    return {"message": "Monitoring system stopped"}
                else:
                    return {"message": "Monitoring system not running"}
            except Exception as e:
                logger.error(f"Error stopping monitoring: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        # Error handlers
        @self.app.exception_handler(Exception)
        async def general_exception_handler(request, exc):
            logger.error(f"Unhandled exception: {exc}\n{traceback.format_exc()}")
            return JSONResponse(
                status_code=500,
                content={"detail": "Internal server error", "error": str(exc)}
            )
    
    def _create_flask_app(self):
        """Create Flask application (fallback)"""
        self.app = Flask(__name__)
        CORS(self.app)
        
        @self.app.route('/')
        def root():
            return jsonify({"message": "MLOps Digital Twin Dashboard API", "version": "1.0.0"})
        
        @self.app.route('/health')
        def health_check():
            return jsonify({"status": "healthy", "timestamp": datetime.now().isoformat()})
        
        @self.app.route('/api/v1/dashboards')
        def get_dashboards():
            try:
                dashboards = self.dashboard_manager.get_available_dashboards()
                return jsonify({"dashboards": dashboards})
            except Exception as e:
                logger.error(f"Error getting dashboards: {e}")
                return jsonify({"error": str(e)}), 500
        
        @self.app.route('/api/v1/overview')
        def get_system_overview():
            try:
                # Note: Flask doesn't support async, so we'd need to run sync version
                # This is a simplified version
                overview = {
                    "timestamp": datetime.now().isoformat(),
                    "system_status": "operational",
                    "message": "Flask implementation - limited async support"
                }
                return jsonify(overview)
            except Exception as e:
                logger.error(f"Error getting system overview: {e}")
                return jsonify({"error": str(e)}), 500
    
    async def start_server(self, host: str = "0.0.0.0", port: int = 8000):
        """Start the API server"""
        try:
            if not FASTAPI_AVAILABLE:
                logger.error("FastAPI not available, cannot start server")
                return False
            
            # Start dashboard monitoring
            await self.dashboard_manager.start_monitoring()
            
            # Start API server
            config = uvicorn.Config(
                app=self.app,
                host=host,
                port=port,
                log_level="info",
                access_log=True
            )
            
            server = uvicorn.Server(config)
            
            logger.info(f"Starting Dashboard API server on {host}:{port}")
            self.is_running = True
            
            await server.serve()
            
        except Exception as e:
            logger.error(f"Error starting API server: {e}")
            return False
    
    async def stop_server(self):
        """Stop the API server"""
        try:
            await self.dashboard_manager.stop_monitoring()
            self.is_running = False
            logger.info("Dashboard API server stopped")
        except Exception as e:
            logger.error(f"Error stopping API server: {e}")
    
    def run_sync_server(self, host: str = "127.0.0.1", port: int = 5000):
        """Run Flask server (synchronous fallback)"""
        if FLASK_AVAILABLE and self.app:
            logger.info(f"Starting Flask API server on {host}:{port}")
            self.app.run(host=host, port=port, debug=False)
        else:
            logger.error("Flask not available")


# Factory function
def create_dashboard_api(dashboard_manager: Optional[DashboardManager] = None) -> DashboardAPI:
    """Create a dashboard API instance"""
    return DashboardAPI(dashboard_manager)

# Create app instance for uvicorn
try:
    dashboard_api = create_dashboard_api()
    app = dashboard_api.app
except Exception as e:
    print(f"Warning: Could not create app instance: {e}")
    # Create a minimal FastAPI app as fallback
    if FASTAPI_AVAILABLE:
        app = FastAPI(title="MLOps Digital Twin API", version="1.0.0")
        
        @app.get("/")
        async def root():
            return {"message": "MLOps Digital Twin Platform API", "status": "running"}
        
        @app.get("/health")
        async def health():
            return {"status": "healthy", "timestamp": datetime.now().isoformat()}
    else:
        app = None


# CLI interface
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Dashboard API Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host address")
    parser.add_argument("--port", type=int, default=8000, help="Port number")
    parser.add_argument("--framework", choices=["fastapi", "flask"], default="fastapi", help="Web framework")
    args = parser.parse_args()
    
    # Create API server
    api_server = create_dashboard_api()
    
    if args.framework == "fastapi" and FASTAPI_AVAILABLE:
        # Run FastAPI server
        asyncio.run(api_server.start_server(args.host, args.port))
    elif args.framework == "flask" and FLASK_AVAILABLE:
        # Run Flask server
        api_server.run_sync_server(args.host, args.port)
    else:
        print(f"Framework {args.framework} not available")
        
        # Try alternative
        if FASTAPI_AVAILABLE:
            print("Falling back to FastAPI")
            asyncio.run(api_server.start_server(args.host, args.port))
        elif FLASK_AVAILABLE:
            print("Falling back to Flask")
            api_server.run_sync_server(args.host, args.port)
        else:
            print("No web framework available")