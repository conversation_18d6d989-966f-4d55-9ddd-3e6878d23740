/**
 * Thermal Dynamics Integration
 * Enhanced tank physics engine with thermal dynamics from heating coils,
 * ambient heat loss, thermal stratification, and weather effects
 */

export interface ThermalLayer {
  id: number;
  height: number; // m from bottom
  thickness: number; // m
  temperature: number; // °C
  volume: number; // m³
  density: number; // kg/m³
  viscosity: number; // Pa·s
  heatCapacity: number; // J/kg·K
  thermalConductivity: number; // W/m·K
}

export interface AmbientConditions {
  airTemperature: number; // °C
  windSpeed: number; // m/s
  windDirection: number; // degrees
  humidity: number; // %
  solarRadiation: number; // W/m²
  cloudCover: number; // % (0-100)
  precipitation: number; // mm/h
  barometricPressure: number; // kPa
}

export interface TankThermalProperties {
  tankId: number;
  geometry: {
    diameter: number; // m
    height: number; // m
    wallThickness: number; // m
    bottomThickness: number; // m
    roofThickness: number; // m
  };
  materials: {
    wall: {
      thermalConductivity: number; // W/m·K
      density: number; // kg/m³
      specificHeat: number; // J/kg·K
    };
    insulation: {
      type: 'mineral_wool' | 'polyurethane' | 'perlite' | 'none';
      thickness: number; // m
      thermalConductivity: number; // W/m·K
      weatherBarrier: boolean;
    };
  };
  surfaceProperties: {
    emissivity: number; // 0-1
    absorptivity: number; // 0-1 (solar)
    roughness: number; // m
    color: 'white' | 'silver' | 'gray' | 'dark';
  };
}

export interface HeatTransferCoefficients {
  convection: {
    external: number; // W/m²·K (air to tank surface)
    internal: number; // W/m²·K (product to tank wall)
  };
  radiation: {
    skyRadiation: number; // W/m²·K
    groundRadiation: number; // W/m²·K
    solarAbsorption: number; // W/m²
  };
  conduction: {
    wallResistance: number; // K/W
    insulationResistance: number; // K/W
    totalResistance: number; // K/W
  };
}

export interface ThermalStratificationState {
  tankId: number;
  timestamp: Date;
  layers: ThermalLayer[];
  averageTemperature: number; // °C
  topTemperature: number; // °C
  bottomTemperature: number; // °C
  temperatureGradient: number; // °C/m
  mixingIntensity: number; // 0-1 (0=fully stratified, 1=fully mixed)
  turnoverTime: number; // hours
  heatLossRate: number; // kW
  heatingRate: number; // kW
  netHeatTransfer: number; // kW
}

export class ThermalDynamicsIntegration {
  private tankThermalProperties: Map<number, TankThermalProperties> = new Map();
  private thermalStates: Map<number, ThermalStratificationState> = new Map();
  private ambientConditions: AmbientConditions;
  private weatherHistory: AmbientConditions[] = [];
  private maxWeatherHistory: number = 24; // hours

  constructor() {
    this.ambientConditions = this.createDefaultAmbientConditions();
    this.initializeTankThermalProperties();
  }

  private createDefaultAmbientConditions(): AmbientConditions {
    return {
      airTemperature: 20, // °C
      windSpeed: 3, // m/s
      windDirection: 180, // degrees (south)
      humidity: 60, // %
      solarRadiation: 500, // W/m²
      cloudCover: 30, // %
      precipitation: 0, // mm/h
      barometricPressure: 101.3 // kPa
    };
  }

  private initializeTankThermalProperties() {
    // Initialize thermal properties for each tank (1-10)
    for (let tankId = 1; tankId <= 10; tankId++) {
      this.tankThermalProperties.set(tankId, {
        tankId,
        geometry: {
          diameter: 12, // m
          height: 6, // m
          wallThickness: 0.012, // 12mm steel
          bottomThickness: 0.015, // 15mm steel
          roofThickness: 0.010 // 10mm steel
        },
        materials: {
          wall: {
            thermalConductivity: 50, // W/m·K (carbon steel)
            density: 7850, // kg/m³
            specificHeat: 460 // J/kg·K
          },
          insulation: {
            type: 'mineral_wool',
            thickness: 0.1, // 100mm
            thermalConductivity: 0.04, // W/m·K
            weatherBarrier: true
          }
        },
        surfaceProperties: {
          emissivity: 0.85, // Painted steel
          absorptivity: 0.6, // Light colored paint
          roughness: 0.0001, // m (smooth painted surface)
          color: 'silver'
        }
      });

      // Initialize thermal state with stratified layers
      this.initializeThermalState(tankId);
    }
  }

  private initializeThermalState(tankId: number) {
    const properties = this.tankThermalProperties.get(tankId);
    if (!properties) return;

    const numLayers = 10; // Divide tank into 10 thermal layers
    const layerHeight = properties.geometry.height / numLayers;
    const layers: ThermalLayer[] = [];

    for (let i = 0; i < numLayers; i++) {
      const height = i * layerHeight + layerHeight / 2; // Center of layer
      const baseTemp = 150; // °C base temperature
      const stratificationEffect = (numLayers - i - 1) * 2; // Warmer at top
      
      layers.push({
        id: i,
        height,
        thickness: layerHeight,
        temperature: baseTemp + stratificationEffect,
        volume: Math.PI * Math.pow(properties.geometry.diameter / 2, 2) * layerHeight,
        density: 1020, // kg/m³ (asphalt)
        viscosity: 0.4, // Pa·s
        heatCapacity: 2100, // J/kg·K
        thermalConductivity: 0.17 // W/m·K
      });
    }

    this.thermalStates.set(tankId, {
      tankId,
      timestamp: new Date(),
      layers,
      averageTemperature: 150,
      topTemperature: 168,
      bottomTemperature: 132,
      temperatureGradient: (168 - 132) / properties.geometry.height,
      mixingIntensity: 0.3, // Partially stratified
      turnoverTime: 4, // hours
      heatLossRate: 0,
      heatingRate: 0,
      netHeatTransfer: 0
    });
  }

  public updateAmbientConditions(conditions: Partial<AmbientConditions>) {
    this.ambientConditions = { ...this.ambientConditions, ...conditions };
    
    // Store weather history
    this.weatherHistory.push({ ...this.ambientConditions });
    if (this.weatherHistory.length > this.maxWeatherHistory) {
      this.weatherHistory.shift();
    }
  }

  public simulateThermalDynamics(
    tankId: number,
    heatingCoilHeatInput: number, // kW
    productOutflow: number, // L/min
    timeStep: number = 1.0 // seconds
  ): ThermalStratificationState {
    
    const properties = this.tankThermalProperties.get(tankId);
    const currentState = this.thermalStates.get(tankId);
    
    if (!properties || !currentState) {
      throw new Error(`Tank ${tankId} not found`);
    }

    // Calculate heat transfer coefficients
    const heatTransferCoeffs = this.calculateHeatTransferCoefficients(properties);
    
    // Calculate ambient heat loss
    const ambientHeatLoss = this.calculateAmbientHeatLoss(properties, currentState, heatTransferCoeffs);
    
    // Update thermal stratification
    this.updateThermalStratification(currentState, heatingCoilHeatInput, ambientHeatLoss, productOutflow, timeStep);
    
    // Calculate mixing effects
    this.calculateMixingEffects(currentState, timeStep);
    
    // Update overall thermal state
    this.updateOverallThermalState(currentState);
    
    currentState.timestamp = new Date();
    this.thermalStates.set(tankId, currentState);
    
    return { ...currentState };
  }

  private calculateHeatTransferCoefficients(properties: TankThermalProperties): HeatTransferCoefficients {
    const windSpeed = this.ambientConditions.windSpeed;
    const airTemp = this.ambientConditions.airTemperature;
    
    // External convection coefficient (wind effect)
    const externalConvection = 5.7 + 3.8 * windSpeed; // W/m²·K
    
    // Internal convection coefficient (natural convection in tank)
    const internalConvection = 8.0; // W/m²·K (typical for viscous fluids)
    
    // Radiation coefficients
    const stefanBoltzmann = 5.67e-8; // W/m²·K⁴
    const emissivity = properties.surfaceProperties.emissivity;
    const skyTemp = airTemp - 10; // °C (typical sky temperature)
    const avgSurfaceTemp = 60; // °C (estimated tank surface temperature)
    
    const skyRadiation = emissivity * stefanBoltzmann * 
      (Math.pow(avgSurfaceTemp + 273.15, 4) - Math.pow(skyTemp + 273.15, 4)) / 
      (avgSurfaceTemp - skyTemp);
    
    // Solar absorption
    const solarAbsorption = properties.surfaceProperties.absorptivity * 
      this.ambientConditions.solarRadiation * (1 - this.ambientConditions.cloudCover / 100);
    
    // Conduction resistances
    const wallResistance = properties.geometry.wallThickness / 
      (properties.materials.wall.thermalConductivity * Math.PI * properties.geometry.diameter * properties.geometry.height);
    
    const insulationResistance = properties.materials.insulation.thickness / 
      (properties.materials.insulation.thermalConductivity * Math.PI * properties.geometry.diameter * properties.geometry.height);
    
    const totalResistance = wallResistance + insulationResistance + 1 / (externalConvection * Math.PI * properties.geometry.diameter * properties.geometry.height);

    return {
      convection: {
        external: externalConvection,
        internal: internalConvection
      },
      radiation: {
        skyRadiation,
        groundRadiation: skyRadiation * 0.3, // Simplified ground radiation
        solarAbsorption
      },
      conduction: {
        wallResistance,
        insulationResistance,
        totalResistance
      }
    };
  }

  private calculateAmbientHeatLoss(
    properties: TankThermalProperties,
    state: ThermalStratificationState,
    coeffs: HeatTransferCoefficients
  ): number {
    
    const surfaceArea = Math.PI * properties.geometry.diameter * properties.geometry.height; // Side area
    const roofArea = Math.PI * Math.pow(properties.geometry.diameter / 2, 2); // Roof area
    const totalArea = surfaceArea + roofArea;
    
    // Convective heat loss
    const avgTankTemp = state.averageTemperature;
    const tempDiff = avgTankTemp - this.ambientConditions.airTemperature;
    const convectiveHeatLoss = coeffs.convection.external * totalArea * tempDiff / 1000; // kW
    
    // Radiative heat loss
    const radiativeHeatLoss = coeffs.radiation.skyRadiation * totalArea * tempDiff / 1000; // kW
    
    // Solar heat gain
    const solarHeatGain = coeffs.radiation.solarAbsorption * totalArea / 1000; // kW
    
    // Wind effect on heat loss
    const windFactor = 1 + 0.1 * this.ambientConditions.windSpeed;
    
    // Precipitation effect (additional cooling)
    const precipitationCooling = this.ambientConditions.precipitation * totalArea * 0.01; // kW
    
    const totalHeatLoss = (convectiveHeatLoss + radiativeHeatLoss + precipitationCooling) * windFactor - solarHeatGain;
    
    return Math.max(0, totalHeatLoss); // Ensure non-negative
  }

  private updateThermalStratification(
    state: ThermalStratificationState,
    heatingInput: number,
    ambientLoss: number,
    outflow: number,
    timeStep: number
  ) {
    
    const layers = state.layers;
    const numLayers = layers.length;
    
    // Distribute heating input to bottom layers (where heating coils are)
    const heatingLayers = 3; // Bottom 3 layers receive direct heating
    const heatingPerLayer = heatingInput / heatingLayers;
    
    // Distribute ambient heat loss across all layers (proportional to surface area)
    const heatLossPerLayer = ambientLoss / numLayers;
    
    layers.forEach((layer, index) => {
      const mass = layer.volume * layer.density; // kg
      
      // Heat input from coils (bottom layers only)
      let heatInput = 0;
      if (index < heatingLayers) {
        heatInput = heatingPerLayer;
      }
      
      // Heat loss to ambient
      const heatLoss = heatLossPerLayer;
      
      // Net heat change
      const netHeat = heatInput - heatLoss; // kW
      
      // Temperature change
      const tempChange = (netHeat * 1000 * timeStep) / (mass * layer.heatCapacity); // °C
      
      layer.temperature += tempChange;
      
      // Ensure reasonable temperature limits
      layer.temperature = Math.max(20, Math.min(300, layer.temperature));
    });
    
    // Handle product outflow (removes hot product from top layers)
    if (outflow > 0) {
      const volumeOutflow = (outflow / 60000) * timeStep; // m³
      const layersAffected = Math.min(3, numLayers); // Top 3 layers
      
      for (let i = numLayers - 1; i >= numLayers - layersAffected; i--) {
        const layer = layers[i];
        const volumeRemoved = Math.min(volumeOutflow / layersAffected, layer.volume * 0.1);
        
        // Cooling effect from product removal
        const coolingEffect = (volumeRemoved / layer.volume) * 5; // °C
        layer.temperature -= coolingEffect;
      }
    }
    
    state.heatingRate = heatingInput;
    state.heatLossRate = ambientLoss;
    state.netHeatTransfer = heatingInput - ambientLoss;
  }

  private calculateMixingEffects(state: ThermalStratificationState, timeStep: number) {
    const layers = state.layers;
    const numLayers = layers.length;
    
    // Calculate buoyancy-driven mixing
    for (let i = 0; i < numLayers - 1; i++) {
      const lowerLayer = layers[i];
      const upperLayer = layers[i + 1];
      
      // Check for density inversion (unstable stratification)
      if (lowerLayer.temperature > upperLayer.temperature + 2) { // 2°C threshold
        // Mix the layers
        const mixingRate = 0.1 * timeStep; // 10% mixing per second
        const avgTemp = (lowerLayer.temperature + upperLayer.temperature) / 2;
        
        lowerLayer.temperature += (avgTemp - lowerLayer.temperature) * mixingRate;
        upperLayer.temperature += (avgTemp - upperLayer.temperature) * mixingRate;
        
        // Increase mixing intensity
        state.mixingIntensity = Math.min(1.0, state.mixingIntensity + 0.01);
      } else {
        // Stable stratification - reduce mixing intensity
        state.mixingIntensity = Math.max(0.1, state.mixingIntensity - 0.005);
      }
    }
    
    // Thermal diffusion between adjacent layers
    for (let i = 0; i < numLayers - 1; i++) {
      const lowerLayer = layers[i];
      const upperLayer = layers[i + 1];
      
      const tempDiff = upperLayer.temperature - lowerLayer.temperature;
      const diffusionRate = 0.001 * timeStep; // Small thermal diffusion
      
      const heatTransfer = tempDiff * diffusionRate;
      lowerLayer.temperature += heatTransfer;
      upperLayer.temperature -= heatTransfer;
    }
  }

  private updateOverallThermalState(state: ThermalStratificationState) {
    const layers = state.layers;
    const numLayers = layers.length;
    
    // Calculate average temperature
    const totalVolume = layers.reduce((sum, layer) => sum + layer.volume, 0);
    state.averageTemperature = layers.reduce((sum, layer) => 
      sum + (layer.temperature * layer.volume), 0) / totalVolume;
    
    // Top and bottom temperatures
    state.topTemperature = layers[numLayers - 1].temperature;
    state.bottomTemperature = layers[0].temperature;
    
    // Temperature gradient
    const tankHeight = layers[numLayers - 1].height + layers[numLayers - 1].thickness / 2;
    state.temperatureGradient = (state.topTemperature - state.bottomTemperature) / tankHeight;
    
    // Turnover time (simplified calculation)
    state.turnoverTime = 4 / (1 + state.mixingIntensity * 2); // hours
  }

  // Public API methods
  public getThermalState(tankId: number): ThermalStratificationState | undefined {
    return this.thermalStates.get(tankId) ? { ...this.thermalStates.get(tankId)! } : undefined;
  }

  public getAllThermalStates(): { [tankId: number]: ThermalStratificationState } {
    const states: { [tankId: number]: ThermalStratificationState } = {};
    this.thermalStates.forEach((state, tankId) => {
      states[tankId] = { ...state };
    });
    return states;
  }

  public getAmbientConditions(): AmbientConditions {
    return { ...this.ambientConditions };
  }

  public getWeatherHistory(): AmbientConditions[] {
    return [...this.weatherHistory];
  }

  public getTankThermalProperties(tankId: number): TankThermalProperties | undefined {
    return this.tankThermalProperties.get(tankId) ? 
      { ...this.tankThermalProperties.get(tankId)! } : undefined;
  }

  public calculateOptimalHeatingStrategy(tankId: number, targetTemperature: number): {
    recommendedHeatingRate: number; // kW
    estimatedTime: number; // hours
    energyRequired: number; // kWh
    efficiency: number; // %
  } {
    const state = this.thermalStates.get(tankId);
    const properties = this.tankThermalProperties.get(tankId);
    
    if (!state || !properties) {
      throw new Error(`Tank ${tankId} not found`);
    }

    const currentTemp = state.averageTemperature;
    const tempRise = targetTemperature - currentTemp;
    
    if (tempRise <= 0) {
      return {
        recommendedHeatingRate: 0,
        estimatedTime: 0,
        energyRequired: 0,
        efficiency: 100
      };
    }

    // Calculate thermal mass
    const totalVolume = state.layers.reduce((sum, layer) => sum + layer.volume, 0);
    const totalMass = totalVolume * 1020; // kg (asphalt density)
    const heatCapacity = 2100; // J/kg·K
    
    // Energy required
    const energyRequired = (totalMass * heatCapacity * tempRise) / 3600000; // kWh
    
    // Account for heat losses
    const heatLossRate = state.heatLossRate; // kW
    const efficiency = 85; // % (typical heating efficiency)
    
    // Recommended heating rate (with safety margin)
    const recommendedHeatingRate = (energyRequired / 2) + heatLossRate; // kW (2-hour target)
    
    // Estimated time
    const estimatedTime = energyRequired / (recommendedHeatingRate * efficiency / 100); // hours
    
    return {
      recommendedHeatingRate,
      estimatedTime,
      energyRequired,
      efficiency
    };
  }

  public simulateWeatherImpact(weatherScenario: Partial<AmbientConditions>, durationHours: number): {
    temperatureChanges: { [tankId: number]: number };
    heatLossIncrease: { [tankId: number]: number };
    energyImpact: { [tankId: number]: number };
  } {
    const originalConditions = { ...this.ambientConditions };
    const results = {
      temperatureChanges: {} as { [tankId: number]: number },
      heatLossIncrease: {} as { [tankId: number]: number },
      energyImpact: {} as { [tankId: number]: number }
    };

    // Temporarily apply weather scenario
    this.updateAmbientConditions(weatherScenario);

    this.thermalStates.forEach((state, tankId) => {
      const properties = this.tankThermalProperties.get(tankId)!;
      const originalTemp = state.averageTemperature;
      
      // Simulate for the duration
      const timeSteps = durationHours * 3600; // seconds
      let totalHeatLoss = 0;
      
      for (let t = 0; t < timeSteps; t += 60) { // 1-minute steps
        const coeffs = this.calculateHeatTransferCoefficients(properties);
        const heatLoss = this.calculateAmbientHeatLoss(properties, state, coeffs);
        totalHeatLoss += heatLoss * (60 / 3600); // kWh
        
        // Apply heat loss to average temperature
        const totalMass = state.layers.reduce((sum, layer) => sum + layer.volume * layer.density, 0);
        const tempDrop = (heatLoss * 1000 * 60) / (totalMass * 2100); // °C
        state.averageTemperature -= tempDrop;
      }
      
      results.temperatureChanges[tankId] = state.averageTemperature - originalTemp;
      results.heatLossIncrease[tankId] = totalHeatLoss;
      results.energyImpact[tankId] = totalHeatLoss * 0.12; // Assuming $0.12/kWh
      
      // Restore original temperature
      state.averageTemperature = originalTemp;
    });

    // Restore original conditions
    this.ambientConditions = originalConditions;

    return results;
  }
}
