"""
Real-time Physics Engine for Digital Twin Simulation
Integrates NVIDIA Modulus with real-time constraints
"""

import asyncio
import logging
import numpy as np
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from abc import ABC, abstractmethod
import threading
import time

# Import tank physics engine
try:
    from .tank_physics_engine import TankPhysicsEngine, SimulationConfiguration as TankSimConfig
    TANK_PHYSICS_AVAILABLE = True
except ImportError:
    TANK_PHYSICS_AVAILABLE = False
    print("Warning: Tank physics engine not available")

logger = logging.getLogger(__name__)


class SimulationState(Enum):
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPED = "stopped"
    ERROR = "error"


class SolverType(Enum):
    FINITE_DIFFERENCE = "finite_difference"
    FINITE_ELEMENT = "finite_element"
    NEURAL_NETWORK = "neural_network"
    HYBRID = "hybrid"


@dataclass
class PhysicsParameters:
    """Physics parameters for heat transfer simulation"""
    thermal_conductivity: float = 0.5  # W/m·K
    specific_heat: float = 920.0  # J/kg·K
    density: float = 2400.0  # kg/m³
    ambient_temperature: float = 20.0  # °C
    heat_transfer_coefficient: float = 25.0  # W/m²·K
    emissivity: float = 0.95
    stefan_boltzmann: float = 5.67e-8  # W/m²·K⁴
    geometry_length: float = 10.0  # m
    geometry_width: float = 5.0  # m
    geometry_height: float = 0.1  # m
    grid_size_x: int = 50
    grid_size_y: int = 25
    time_step: float = 0.1  # seconds


@dataclass
class BoundaryConditions:
    """Boundary conditions for the simulation"""
    left_bc_type: str = "neumann"  # dirichlet, neumann, robin
    left_bc_value: float = 0.0
    right_bc_type: str = "neumann"
    right_bc_value: float = 0.0
    top_bc_type: str = "convection"
    top_bc_value: float = 25.0
    bottom_bc_type: str = "dirichlet"
    bottom_bc_value: float = 20.0


@dataclass
class SimulationConfig:
    """Configuration for physics simulation"""
    solver_type: SolverType = SolverType.FINITE_DIFFERENCE
    physics_params: PhysicsParameters = field(default_factory=PhysicsParameters)
    boundary_conditions: BoundaryConditions = field(default_factory=BoundaryConditions)
    real_time_factor: float = 1.0  # 1.0 = real-time, 0.5 = half speed, 2.0 = double speed
    max_simulation_time: float = 3600.0  # seconds
    output_frequency: int = 10  # steps between outputs
    convergence_tolerance: float = 1e-6
    max_iterations: int = 1000
    enable_checkpointing: bool = True
    checkpoint_frequency: int = 100  # steps between checkpoints


class PhysicsSolver(ABC):
    """Abstract base class for physics solvers"""
    
    @abstractmethod
    async def initialize(self, config: SimulationConfig) -> bool:
        """Initialize the solver"""
        pass
    
    @abstractmethod
    async def step(self, dt: float, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Perform one simulation step"""
        pass
    
    @abstractmethod
    async def get_state(self) -> Dict[str, Any]:
        """Get current simulation state"""
        pass
    
    @abstractmethod
    async def set_state(self, state: Dict[str, Any]) -> bool:
        """Set simulation state"""
        pass
    
    @abstractmethod
    async def cleanup(self):
        """Clean up resources"""
        pass


class FiniteDifferenceSolver(PhysicsSolver):
    """Finite difference solver for heat transfer"""
    
    def __init__(self):
        self.temperature_field = None
        self.previous_temperature = None
        self.config = None
        self.time = 0.0
        self.step_count = 0
        self.is_initialized = False
    
    async def initialize(self, config: SimulationConfig) -> bool:
        """Initialize finite difference solver"""
        try:
            self.config = config
            params = config.physics_params
            
            # Initialize temperature field
            self.temperature_field = np.full(
                (params.grid_size_x, params.grid_size_y),
                params.ambient_temperature,
                dtype=np.float64
            )
            
            self.previous_temperature = self.temperature_field.copy()
            
            # Calculate thermal diffusivity
            self.thermal_diffusivity = (
                params.thermal_conductivity / 
                (params.density * params.specific_heat)
            )
            
            # Grid spacing
            self.dx = params.geometry_length / params.grid_size_x
            self.dy = params.geometry_width / params.grid_size_y
            
            # Stability check
            dt_max = min(self.dx**2, self.dy**2) / (4 * self.thermal_diffusivity)
            if params.time_step > dt_max:
                logger.warning(f"Time step {params.time_step} may be unstable. Max stable: {dt_max}")
            
            self.is_initialized = True
            logger.info("Finite difference solver initialized")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing finite difference solver: {e}")
            return False
    
    async def step(self, dt: float, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Perform one finite difference step"""
        if not self.is_initialized:
            return {}
        
        try:
            # Apply heat sources from inputs
            heat_sources = inputs.get('heat_sources', {})
            self._apply_heat_sources(heat_sources)
            
            # Solve heat equation using explicit finite difference
            self._solve_heat_equation(dt)
            
            # Apply boundary conditions
            self._apply_boundary_conditions()
            
            # Update time and step count
            self.time += dt
            self.step_count += 1
            
            # Prepare output
            output = {
                'temperature_field': self.temperature_field.copy(),
                'time': self.time,
                'step': self.step_count,
                'max_temperature': np.max(self.temperature_field),
                'min_temperature': np.min(self.temperature_field),
                'average_temperature': np.mean(self.temperature_field)
            }
            
            # Calculate heat flux if needed
            if inputs.get('calculate_heat_flux', False):
                output['heat_flux'] = self._calculate_heat_flux()
            
            return output
            
        except Exception as e:
            logger.error(f"Error in finite difference step: {e}")
            return {}
    
    async def get_state(self) -> Dict[str, Any]:
        """Get current solver state"""
        return {
            'temperature_field': self.temperature_field.tolist() if self.temperature_field is not None else None,
            'time': self.time,
            'step_count': self.step_count,
            'is_initialized': self.is_initialized
        }
    
    async def set_state(self, state: Dict[str, Any]) -> bool:
        """Set solver state"""
        try:
            if 'temperature_field' in state and state['temperature_field'] is not None:
                self.temperature_field = np.array(state['temperature_field'])
                self.previous_temperature = self.temperature_field.copy()
            
            self.time = state.get('time', 0.0)
            self.step_count = state.get('step_count', 0)
            self.is_initialized = state.get('is_initialized', False)
            
            return True
            
        except Exception as e:
            logger.error(f"Error setting solver state: {e}")
            return False
    
    async def cleanup(self):
        """Clean up solver resources"""
        self.temperature_field = None
        self.previous_temperature = None
        self.is_initialized = False
    
    def _apply_heat_sources(self, heat_sources: Dict[str, Any]):
        """Apply heat sources to temperature field"""
        for source_name, source_data in heat_sources.items():
            if 'position' in source_data and 'power' in source_data:
                x, y = source_data['position']
                power = source_data['power']
                
                # Convert to grid coordinates
                i = int(x / self.dx)
                j = int(y / self.dy)
                
                # Apply heat source
                if 0 <= i < self.config.physics_params.grid_size_x and 0 <= j < self.config.physics_params.grid_size_y:
                    volume = self.dx * self.dy * self.config.physics_params.geometry_height
                    heat_rate = power / (self.config.physics_params.density * 
                                       self.config.physics_params.specific_heat * volume)
                    self.temperature_field[i, j] += heat_rate * self.config.physics_params.time_step
    
    def _solve_heat_equation(self, dt: float):
        """Solve heat equation using explicit finite difference"""
        params = self.config.physics_params
        
        # Coefficients
        alpha = self.thermal_diffusivity
        r_x = alpha * dt / (self.dx**2)
        r_y = alpha * dt / (self.dy**2)
        
        # Store previous temperature
        self.previous_temperature = self.temperature_field.copy()
        
        # Update interior points
        for i in range(1, params.grid_size_x - 1):
            for j in range(1, params.grid_size_y - 1):
                self.temperature_field[i, j] = (
                    self.previous_temperature[i, j] +
                    r_x * (self.previous_temperature[i+1, j] - 2*self.previous_temperature[i, j] + self.previous_temperature[i-1, j]) +
                    r_y * (self.previous_temperature[i, j+1] - 2*self.previous_temperature[i, j] + self.previous_temperature[i, j-1])
                )
    
    def _apply_boundary_conditions(self):
        """Apply boundary conditions"""
        params = self.config.physics_params
        bc = self.config.boundary_conditions
        
        # Left boundary
        if bc.left_bc_type == "dirichlet":
            self.temperature_field[0, :] = bc.left_bc_value
        elif bc.left_bc_type == "neumann":
            self.temperature_field[0, :] = self.temperature_field[1, :] + bc.left_bc_value * self.dx
        
        # Right boundary
        if bc.right_bc_type == "dirichlet":
            self.temperature_field[-1, :] = bc.right_bc_value
        elif bc.right_bc_type == "neumann":
            self.temperature_field[-1, :] = self.temperature_field[-2, :] + bc.right_bc_value * self.dx
        
        # Top boundary (convection)
        if bc.top_bc_type == "convection":
            h = bc.top_bc_value  # Heat transfer coefficient
            k = params.thermal_conductivity
            T_inf = params.ambient_temperature
            
            for i in range(params.grid_size_x):
                T_surface = self.temperature_field[i, -1]
                q_conv = h * (T_surface - T_inf)
                self.temperature_field[i, -1] = self.temperature_field[i, -2] + q_conv * self.dy / k
        
        # Bottom boundary
        if bc.bottom_bc_type == "dirichlet":
            self.temperature_field[:, 0] = bc.bottom_bc_value
    
    def _calculate_heat_flux(self) -> np.ndarray:
        """Calculate heat flux using temperature gradients"""
        params = self.config.physics_params
        k = params.thermal_conductivity
        
        # Calculate gradients
        grad_x = np.gradient(self.temperature_field, self.dx, axis=0)
        grad_y = np.gradient(self.temperature_field, self.dy, axis=1)
        
        # Heat flux = -k * gradient
        heat_flux_x = -k * grad_x
        heat_flux_y = -k * grad_y
        
        # Magnitude
        heat_flux_magnitude = np.sqrt(heat_flux_x**2 + heat_flux_y**2)
        
        return heat_flux_magnitude


# Legacy tank configuration - use tank_models.TankConfiguration instead
@dataclass
class LegacyTankConfiguration:
    """Legacy configuration for asphalt tank system - use tank_models.TankConfiguration"""
    tank_id: str
    tank_capacity: float = 50000.0  # liters
    tank_diameter: float = 6.0  # meters
    tank_height: float = 8.0  # meters
    insulation_thickness: float = 0.1  # meters
    number_of_heating_zones: int = 4
    heating_element_power: float = 50000.0  # watts per zone
    target_temperature: float = 160.0  # °C
    min_operating_temp: float = 140.0  # °C
    max_operating_temp: float = 180.0  # °C
    safety_temp_limit: float = 200.0  # °C
    material_properties: PhysicsParameters = field(default_factory=PhysicsParameters)

# For backward compatibility
TankConfiguration = LegacyTankConfiguration


class LegacyTankPhysicsSolver(PhysicsSolver):
    """Legacy specialized physics solver for asphalt tank heating system"""
    
    def __init__(self, tank_config: LegacyTankConfiguration):
        self.tank_config = tank_config
        self.config = None
        self.temperature_zones = None  # Temperature in each heating zone
        self.heating_power = None  # Power for each heating zone
        self.heat_losses = None  # Heat loss for each zone
        self.fluid_temperatures = None  # 3D temperature field
        self.time = 0.0
        self.step_count = 0
        self.is_initialized = False
        self.zone_volumes = None
        self.zone_surface_areas = None
        
        # Control parameters
        self.heating_enabled = [True] * tank_config.number_of_heating_zones
        self.pid_controllers = None
        self.ambient_temperature = 20.0
        
        # Safety systems
        self.emergency_shutdown = False
        self.overheat_protection = True
        self.alarms = []
    
    async def initialize(self, config: SimulationConfig) -> bool:
        """Initialize tank physics solver"""
        try:
            self.config = config
            tank = self.tank_config
            
            # Initialize temperature zones (radial zones in cylindrical tank)
            self.temperature_zones = np.full(
                tank.number_of_heating_zones, 
                tank.target_temperature,
                dtype=np.float64
            )
            
            # Initialize heating power for each zone
            self.heating_power = np.zeros(tank.number_of_heating_zones)
            
            # Calculate zone geometry
            zone_height = tank.tank_height / tank.number_of_heating_zones
            tank_radius = tank.tank_diameter / 2
            tank_volume = np.pi * tank_radius**2 * tank.tank_height
            
            self.zone_volumes = np.full(
                tank.number_of_heating_zones,
                tank_volume / tank.number_of_heating_zones
            )
            
            # Surface area for heat loss (cylindrical + top/bottom for edge zones)
            cylinder_area = 2 * np.pi * tank_radius * zone_height
            circle_area = np.pi * tank_radius**2
            
            self.zone_surface_areas = np.full(tank.number_of_heating_zones, cylinder_area)
            self.zone_surface_areas[0] += circle_area  # Bottom zone
            self.zone_surface_areas[-1] += circle_area  # Top zone
            
            # Initialize 3D temperature field for detailed simulation
            grid_r = 20  # Radial grid points
            grid_z = tank.number_of_heating_zones * 5  # Vertical grid points
            self.fluid_temperatures = np.full(
                (grid_r, grid_z),
                tank.target_temperature,
                dtype=np.float64
            )
            
            # Initialize PID controllers for each zone
            self._initialize_pid_controllers()
            
            # Heat loss coefficients
            self.heat_losses = np.zeros(tank.number_of_heating_zones)
            
            self.is_initialized = True
            logger.info(f"Tank physics solver initialized for tank {tank.tank_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing tank physics solver: {e}")
            return False
    
    async def step(self, dt: float, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Perform one tank simulation step"""
        if not self.is_initialized:
            return {}
        
        try:
            # Update ambient conditions
            self.ambient_temperature = inputs.get('ambient_temperature', 20.0)
            
            # Safety checks
            self._perform_safety_checks()
            
            # Update heating control
            target_temps = inputs.get('zone_target_temperatures', 
                                    [self.tank_config.target_temperature] * self.tank_config.number_of_heating_zones)
            self._update_heating_control(target_temps, dt)
            
            # Calculate heat transfer
            self._calculate_heat_transfer(dt)
            
            # Update zone temperatures
            self._update_zone_temperatures(dt)
            
            # Calculate detailed fluid dynamics (simplified)
            self._update_fluid_temperature_field(dt)
            
            # Update time
            self.time += dt
            self.step_count += 1
            
            # Prepare output
            output = {
                'tank_id': self.tank_config.tank_id,
                'zone_temperatures': self.temperature_zones.copy(),
                'heating_powers': self.heating_power.copy(),
                'heat_losses': self.heat_losses.copy(),
                'average_temperature': np.mean(self.temperature_zones),
                'max_temperature': np.max(self.temperature_zones),
                'min_temperature': np.min(self.temperature_zones),
                'total_power_consumption': np.sum(self.heating_power),
                'heating_efficiency': self._calculate_heating_efficiency(),
                'energy_cost': self._calculate_energy_cost(dt),
                'time': self.time,
                'step': self.step_count,
                'safety_status': {
                    'emergency_shutdown': self.emergency_shutdown,
                    'alarms': self.alarms.copy(),
                    'within_operating_range': self._check_operating_range()
                },
                'fluid_temperature_field': self.fluid_temperatures.copy(),
                'thermal_gradients': self._calculate_thermal_gradients()
            }
            
            return output
            
        except Exception as e:
            logger.error(f"Error in tank simulation step: {e}")
            return {}
    
    async def get_state(self) -> Dict[str, Any]:
        """Get current tank state"""
        return {
            'tank_id': self.tank_config.tank_id,
            'zone_temperatures': self.temperature_zones.tolist() if self.temperature_zones is not None else None,
            'heating_power': self.heating_power.tolist() if self.heating_power is not None else None,
            'heating_enabled': self.heating_enabled,
            'time': self.time,
            'step_count': self.step_count,
            'is_initialized': self.is_initialized,
            'emergency_shutdown': self.emergency_shutdown,
            'alarms': self.alarms
        }
    
    async def set_state(self, state: Dict[str, Any]) -> bool:
        """Set tank state"""
        try:
            if 'zone_temperatures' in state and state['zone_temperatures'] is not None:
                self.temperature_zones = np.array(state['zone_temperatures'])
            
            if 'heating_power' in state and state['heating_power'] is not None:
                self.heating_power = np.array(state['heating_power'])
            
            self.heating_enabled = state.get('heating_enabled', self.heating_enabled)
            self.time = state.get('time', 0.0)
            self.step_count = state.get('step_count', 0)
            self.is_initialized = state.get('is_initialized', False)
            self.emergency_shutdown = state.get('emergency_shutdown', False)
            self.alarms = state.get('alarms', [])
            
            return True
            
        except Exception as e:
            logger.error(f"Error setting tank state: {e}")
            return False
    
    async def cleanup(self):
        """Clean up tank solver resources"""
        self.temperature_zones = None
        self.heating_power = None
        self.heat_losses = None
        self.fluid_temperatures = None
        self.is_initialized = False
        self.alarms.clear()
    
    def _initialize_pid_controllers(self):
        """Initialize PID controllers for each heating zone"""
        from collections import namedtuple
        PIDController = namedtuple('PIDController', ['kp', 'ki', 'kd', 'integral', 'previous_error'])
        
        self.pid_controllers = []
        for i in range(self.tank_config.number_of_heating_zones):
            controller = PIDController(
                kp=1000.0,  # Proportional gain
                ki=10.0,    # Integral gain
                kd=50.0,    # Derivative gain
                integral=0.0,
                previous_error=0.0
            )
            self.pid_controllers.append(controller)
    
    def _perform_safety_checks(self):
        """Perform safety checks and trigger alarms if needed"""
        self.alarms.clear()
        
        # Check for overheating
        if self.overheat_protection:
            max_temp = np.max(self.temperature_zones)
            if max_temp > self.tank_config.safety_temp_limit:
                self.emergency_shutdown = True
                self.alarms.append(f"CRITICAL: Temperature {max_temp:.1f}°C exceeds safety limit")
                logger.critical(f"Emergency shutdown triggered: Temperature {max_temp:.1f}°C")
            
            elif max_temp > self.tank_config.max_operating_temp:
                self.alarms.append(f"WARNING: Temperature {max_temp:.1f}°C exceeds operating limit")
                logger.warning(f"Temperature exceeds operating limit: {max_temp:.1f}°C")
        
        # Check for underheating
        min_temp = np.min(self.temperature_zones)
        if min_temp < self.tank_config.min_operating_temp:
            self.alarms.append(f"WARNING: Temperature {min_temp:.1f}°C below operating minimum")
    
    def _update_heating_control(self, target_temps: List[float], dt: float):
        """Update heating control using PID controllers"""
        if self.emergency_shutdown:
            self.heating_power.fill(0.0)
            return
        
        for i in range(self.tank_config.number_of_heating_zones):
            if not self.heating_enabled[i]:
                self.heating_power[i] = 0.0
                continue
            
            # PID control
            target_temp = target_temps[i] if i < len(target_temps) else self.tank_config.target_temperature
            current_temp = self.temperature_zones[i]
            error = target_temp - current_temp
            
            # Update PID controller
            controller = self.pid_controllers[i]
            integral = controller.integral + error * dt
            derivative = (error - controller.previous_error) / dt if dt > 0 else 0.0
            
            # Calculate control output
            output = (controller.kp * error + 
                     controller.ki * integral + 
                     controller.kd * derivative)
            
            # Limit power output
            max_power = self.tank_config.heating_element_power
            self.heating_power[i] = max(0.0, min(output, max_power))
            
            # Update controller state
            self.pid_controllers[i] = controller._replace(
                integral=integral,
                previous_error=error
            )
    
    def _calculate_heat_transfer(self, dt: float):
        """Calculate heat transfer and losses for each zone"""
        tank = self.tank_config
        
        for i in range(tank.number_of_heating_zones):
            # Heat loss through insulation (conduction)
            temp_diff = self.temperature_zones[i] - self.ambient_temperature
            
            # Thermal resistance of insulation
            k_insulation = 0.04  # W/m·K for typical insulation
            thermal_resistance = tank.insulation_thickness / (k_insulation * self.zone_surface_areas[i])
            
            # Heat loss rate (W)
            self.heat_losses[i] = temp_diff / thermal_resistance
            
            # Additional heat loss due to convection and radiation
            h_conv = 10.0  # W/m²·K convective heat transfer coefficient
            emissivity = 0.9
            stefan_boltzmann = 5.67e-8
            
            # Convective loss
            conv_loss = h_conv * self.zone_surface_areas[i] * temp_diff
            
            # Radiative loss
            T_surface_K = self.temperature_zones[i] + 273.15
            T_ambient_K = self.ambient_temperature + 273.15
            rad_loss = (emissivity * stefan_boltzmann * self.zone_surface_areas[i] * 
                       (T_surface_K**4 - T_ambient_K**4))
            
            self.heat_losses[i] += conv_loss + rad_loss
    
    def _update_zone_temperatures(self, dt: float):
        """Update zone temperatures based on energy balance"""
        tank = self.tank_config
        
        # Material properties (asphalt)
        density = 2400.0  # kg/m³
        specific_heat = 920.0  # J/kg·K
        
        for i in range(tank.number_of_heating_zones):
            # Energy balance: dT/dt = (Q_in - Q_out) / (m * cp)
            mass = density * self.zone_volumes[i]
            thermal_capacity = mass * specific_heat
            
            # Net heat input
            net_heat = self.heating_power[i] - self.heat_losses[i]
            
            # Heat exchange with adjacent zones
            if i > 0:  # Heat from zone below
                temp_diff = self.temperature_zones[i-1] - self.temperature_zones[i]
                heat_exchange = 1000.0 * temp_diff  # Simple conduction model
                net_heat += heat_exchange
            
            if i < tank.number_of_heating_zones - 1:  # Heat to zone above
                temp_diff = self.temperature_zones[i+1] - self.temperature_zones[i]
                heat_exchange = 1000.0 * temp_diff
                net_heat += heat_exchange
            
            # Update temperature
            dT_dt = net_heat / thermal_capacity
            self.temperature_zones[i] += dT_dt * dt
            
            # Ensure physical limits
            self.temperature_zones[i] = max(self.ambient_temperature, 
                                          min(self.temperature_zones[i], 300.0))
    
    def _update_fluid_temperature_field(self, dt: float):
        """Update detailed 3D temperature field (simplified)"""
        # Simple approach: interpolate zone temperatures across the field
        grid_r, grid_z = self.fluid_temperatures.shape
        zone_height = grid_z // self.tank_config.number_of_heating_zones
        
        for i, zone_temp in enumerate(self.temperature_zones):
            start_z = i * zone_height
            end_z = min((i + 1) * zone_height, grid_z)
            
            # Apply zone temperature with radial gradient
            for r in range(grid_r):
                # Temperature decreases from center to wall
                radial_factor = 1.0 - 0.1 * (r / grid_r)  # 10% temperature drop at wall
                for z in range(start_z, end_z):
                    target_temp = zone_temp * radial_factor
                    
                    # Exponential approach to target temperature
                    alpha = 0.1  # Thermal diffusion rate
                    self.fluid_temperatures[r, z] += alpha * (target_temp - self.fluid_temperatures[r, z]) * dt
    
    def _calculate_heating_efficiency(self) -> float:
        """Calculate overall heating efficiency"""
        total_heat_input = np.sum(self.heating_power)
        total_heat_loss = np.sum(self.heat_losses)
        
        if total_heat_input > 0:
            return max(0.0, (total_heat_input - total_heat_loss) / total_heat_input)
        return 0.0
    
    def _calculate_energy_cost(self, dt: float) -> float:
        """Calculate energy cost for this time step"""
        # Energy cost calculation (simplified)
        energy_rate = 0.12  # $/kWh
        total_power_kw = np.sum(self.heating_power) / 1000.0  # Convert to kW
        energy_kwh = total_power_kw * (dt / 3600.0)  # Convert to kWh
        return energy_kwh * energy_rate
    
    def _check_operating_range(self) -> bool:
        """Check if all zones are within operating temperature range"""
        return (np.all(self.temperature_zones >= self.tank_config.min_operating_temp) and
                np.all(self.temperature_zones <= self.tank_config.max_operating_temp))
    
    def _calculate_thermal_gradients(self) -> Dict[str, Any]:
        """Calculate thermal gradients in the tank"""
        if self.fluid_temperatures is None:
            return {}
        
        # Calculate gradients
        grad_r = np.gradient(self.fluid_temperatures, axis=0)
        grad_z = np.gradient(self.fluid_temperatures, axis=1)
        
        # Calculate magnitude
        gradient_magnitude = np.sqrt(grad_r**2 + grad_z**2)
        
        return {
            'radial_gradient': grad_r,
            'vertical_gradient': grad_z,
            'gradient_magnitude': gradient_magnitude,
            'max_gradient': np.max(gradient_magnitude),
            'average_gradient': np.mean(gradient_magnitude)
        }


class RealTimePhysicsEngine:
    """Real-time physics engine with timing control"""
    
    def __init__(self, config: SimulationConfig, tank_config: Optional[LegacyTankConfiguration] = None):
        self.config = config
        self.tank_config = tank_config
        self.solver: Optional[PhysicsSolver] = None
        self.state = SimulationState.IDLE
        self.simulation_time = 0.0
        self.real_start_time = None
        self.step_count = 0
        self.last_checkpoint_time = 0.0
        self.checkpoints: List[Dict[str, Any]] = []
        self.output_callbacks: List[Callable] = []
        self.state_callbacks: List[Callable] = []
        self._simulation_task: Optional[asyncio.Task] = None
        self._lock = asyncio.Lock()
        
        # New tank physics engine integration
        self.tank_physics_engine: Optional[TankPhysicsEngine] = None
        self.use_tank_physics = TANK_PHYSICS_AVAILABLE
    
    async def initialize(self) -> bool:
        """Initialize physics engine"""
        try:
            # Create solver based on configuration
            if self.tank_config and self.config.solver_type == SolverType.FINITE_DIFFERENCE:
                # Use tank-specific solver if tank configuration is provided
                self.solver = LegacyTankPhysicsSolver(self.tank_config)
            elif self.config.solver_type == SolverType.FINITE_DIFFERENCE:
                self.solver = FiniteDifferenceSolver()
            else:
                logger.error(f"Unsupported solver type: {self.config.solver_type}")
                return False
            
            # Initialize solver
            success = await self.solver.initialize(self.config)
            if not success:
                return False
            
            self.state = SimulationState.IDLE
            logger.info("Physics engine initialized")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing physics engine: {e}")
            self.state = SimulationState.ERROR
            return False
    
    async def start(self) -> bool:
        """Start real-time simulation"""
        if self.state != SimulationState.IDLE:
            return False
        
        async with self._lock:
            self.state = SimulationState.RUNNING
            self.real_start_time = time.time()
            self.simulation_time = 0.0
            self.step_count = 0
            
            # Start simulation task
            self._simulation_task = asyncio.create_task(self._simulation_loop())
            
            await self._notify_state_callbacks()
            logger.info("Physics simulation started")
            return True
    
    async def pause(self) -> bool:
        """Pause simulation"""
        if self.state != SimulationState.RUNNING:
            return False
        
        async with self._lock:
            self.state = SimulationState.PAUSED
            await self._notify_state_callbacks()
            logger.info("Physics simulation paused")
            return True
    
    async def resume(self) -> bool:
        """Resume simulation"""
        if self.state != SimulationState.PAUSED:
            return False
        
        async with self._lock:
            self.state = SimulationState.RUNNING
            await self._notify_state_callbacks()
            logger.info("Physics simulation resumed")
            return True
    
    async def stop(self) -> bool:
        """Stop simulation"""
        if self.state in [SimulationState.IDLE, SimulationState.STOPPED]:
            return False
        
        async with self._lock:
            self.state = SimulationState.STOPPED
            
            if self._simulation_task:
                self._simulation_task.cancel()
                self._simulation_task = None
            
            await self._notify_state_callbacks()
            logger.info("Physics simulation stopped")
            return True
    
    async def step_simulation(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Perform a single simulation step"""
        if not self.solver:
            return {}
        
        try:
            # Perform physics step
            output = await self.solver.step(self.config.physics_params.time_step, inputs)
            
            # Update counters
            self.simulation_time += self.config.physics_params.time_step
            self.step_count += 1
            
            # Add timing information
            output['simulation_time'] = self.simulation_time
            output['step_count'] = self.step_count
            output['real_time'] = time.time() - self.real_start_time if self.real_start_time else 0
            
            # Checkpointing
            if (self.config.enable_checkpointing and 
                self.step_count % self.config.checkpoint_frequency == 0):
                await self._create_checkpoint()
            
            return output
            
        except Exception as e:
            logger.error(f"Error in simulation step: {e}")
            self.state = SimulationState.ERROR
            return {}
    
    async def get_current_state(self) -> Dict[str, Any]:
        """Get current simulation state"""
        state = {
            'engine_state': self.state.value,
            'simulation_time': self.simulation_time,
            'step_count': self.step_count,
            'real_time': time.time() - self.real_start_time if self.real_start_time else 0
        }
        
        if self.solver:
            solver_state = await self.solver.get_state()
            state['solver_state'] = solver_state
        
        return state
    
    async def set_inputs(self, inputs: Dict[str, Any]) -> bool:
        """Set simulation inputs"""
        # This method can be used to update inputs during simulation
        return True
    
    def register_output_callback(self, callback: Callable):
        """Register callback for simulation outputs"""
        self.output_callbacks.append(callback)
    
    def register_state_callback(self, callback: Callable):
        """Register callback for state changes"""
        self.state_callbacks.append(callback)
    
    async def _simulation_loop(self):
        """Main simulation loop with real-time control"""
        try:
            while self.state == SimulationState.RUNNING:
                loop_start = time.time()
                
                # Check if we should continue
                if self.simulation_time >= self.config.max_simulation_time:
                    await self.stop()
                    break
                
                # Perform simulation step
                inputs = {}  # Default empty inputs
                output = await self.step_simulation(inputs)
                
                # Notify output callbacks
                if self.step_count % self.config.output_frequency == 0:
                    await self._notify_output_callbacks(output)
                
                # Real-time control
                loop_duration = time.time() - loop_start
                target_duration = self.config.physics_params.time_step / self.config.real_time_factor
                
                if loop_duration < target_duration:
                    await asyncio.sleep(target_duration - loop_duration)
                elif loop_duration > target_duration * 2:
                    logger.warning(f"Simulation running slow: {loop_duration:.3f}s > {target_duration:.3f}s")
                
                # Check for pause
                while self.state == SimulationState.PAUSED:
                    await asyncio.sleep(0.1)
                
                # Check for stop
                if self.state == SimulationState.STOPPED:
                    break
                    
        except asyncio.CancelledError:
            logger.info("Simulation loop cancelled")
        except Exception as e:
            logger.error(f"Error in simulation loop: {e}")
            self.state = SimulationState.ERROR
    
    async def _create_checkpoint(self):
        """Create simulation checkpoint"""
        try:
            checkpoint = {
                'time': self.simulation_time,
                'step_count': self.step_count,
                'timestamp': datetime.now().isoformat(),
                'solver_state': await self.solver.get_state() if self.solver else None
            }
            
            self.checkpoints.append(checkpoint)
            
            # Keep only last 10 checkpoints
            if len(self.checkpoints) > 10:
                self.checkpoints.pop(0)
                
            logger.debug(f"Created checkpoint at step {self.step_count}")
            
        except Exception as e:
            logger.error(f"Error creating checkpoint: {e}")
    
    async def _notify_output_callbacks(self, output: Dict[str, Any]):
        """Notify output callbacks"""
        for callback in self.output_callbacks:
            try:
                await callback(output)
            except Exception as e:
                logger.error(f"Error in output callback: {e}")
    
    async def _notify_state_callbacks(self):
        """Notify state change callbacks"""
        for callback in self.state_callbacks:
            try:
                await callback(self.state)
            except Exception as e:
                logger.error(f"Error in state callback: {e}")
    
    async def cleanup(self):
        """Clean up resources"""
        await self.stop()
        if self.solver:
            await self.solver.cleanup()
        self.checkpoints.clear()
        self.output_callbacks.clear()
        self.state_callbacks.clear()
        
        if self.tank_physics_engine:
            await self.tank_physics_engine.stop()


class PhysicsEngine:
    """Enhanced Physics Engine for Digital Twin Integration"""
    
    def __init__(self, physics_parameters: Dict[str, Any]):
        self.physics_parameters = physics_parameters
        self.real_time_engine: Optional[RealTimePhysicsEngine] = None
        self.config: Optional[SimulationConfig] = None
        self.is_initialized = False
        self.last_state = None
        self.prediction_cache = {}
        self.adaptive_time_stepping = True
        self.model_uncertainty = 0.0
        
        # Multi-fidelity model hierarchy
        self.high_fidelity_model = None
        self.medium_fidelity_model = None
        self.low_fidelity_model = None
        self.current_fidelity = "medium"
        
        # Physics-ML hybrid integration
        self.ml_correction_model = None
        self.enable_ml_correction = False
        
    async def initialize(self, config: Optional[SimulationConfig] = None) -> bool:
        """Initialize physics engine with digital twin integration"""
        try:
            # Create default config if not provided
            if config is None:
                config = SimulationConfig(
                    solver_type=SolverType.FINITE_DIFFERENCE,
                    physics_params=PhysicsParameters(**self.physics_parameters)
                )
            
            self.config = config
            
            # Initialize multi-fidelity models
            await self._initialize_multi_fidelity_models()
            
            # Initialize real-time engine
            self.real_time_engine = RealTimePhysicsEngine(config)
            success = await self.real_time_engine.initialize()
            
            if success:
                self.is_initialized = True
                logger.info("Enhanced physics engine initialized")
                
            return success
            
        except Exception as e:
            logger.error(f"Error initializing physics engine: {e}")
            return False
    
    async def _initialize_multi_fidelity_models(self):
        """Initialize multi-fidelity model hierarchy"""
        try:
            # High-fidelity: Detailed FEM/CFD model
            self.high_fidelity_model = {
                'type': 'high_fidelity',
                'solver': 'finite_element',
                'grid_resolution': 'high',
                'computational_cost': 'high',
                'accuracy': 'highest'
            }
            
            # Medium-fidelity: Finite difference with moderate resolution
            self.medium_fidelity_model = {
                'type': 'medium_fidelity',
                'solver': 'finite_difference',
                'grid_resolution': 'medium',
                'computational_cost': 'medium',
                'accuracy': 'high'
            }
            
            # Low-fidelity: Lumped parameter model
            self.low_fidelity_model = {
                'type': 'low_fidelity',
                'solver': 'lumped_parameter',
                'grid_resolution': 'low',
                'computational_cost': 'low',
                'accuracy': 'medium'
            }
            
            logger.info("Multi-fidelity model hierarchy initialized")
            
        except Exception as e:
            logger.error(f"Error initializing multi-fidelity models: {e}")
    
    async def simulate_step(self, current_state, control_inputs: Dict[str, Any], 
                           ambient_conditions: Dict[str, Any]) -> Dict[str, Any]:
        """Enhanced simulation step with adaptive fidelity and ML correction"""
        if not self.is_initialized:
            logger.warning("Physics engine not initialized")
            return {}
        
        try:
            # Adaptive fidelity selection based on computational budget and accuracy needs
            await self._select_optimal_fidelity(current_state, control_inputs)
            
            # Prepare simulation inputs
            inputs = {
                'control_inputs': control_inputs,
                'ambient_conditions': ambient_conditions,
                'current_state': current_state,
                'fidelity_level': self.current_fidelity
            }
            
            # Add heat sources from control inputs
            heat_sources = self._extract_heat_sources(control_inputs)
            if heat_sources:
                inputs['heat_sources'] = heat_sources
            
            # Perform physics simulation
            if self.real_time_engine:
                simulation_results = await self.real_time_engine.step_simulation(inputs)
            else:
                simulation_results = {}
            
            # Apply ML correction if enabled
            if self.enable_ml_correction and self.ml_correction_model:
                simulation_results = await self._apply_ml_correction(simulation_results, inputs)
            
            # Add uncertainty quantification
            simulation_results['model_uncertainty'] = self.model_uncertainty
            simulation_results['confidence'] = max(0.0, 1.0 - self.model_uncertainty)
            
            # Cache results for prediction
            self._cache_prediction_data(simulation_results, inputs)
            
            # Update last state
            self.last_state = simulation_results
            
            return simulation_results
            
        except Exception as e:
            logger.error(f"Error in physics simulation step: {e}")
            return {'error': str(e), 'confidence': 0.0}
    
    async def _select_optimal_fidelity(self, current_state, control_inputs: Dict[str, Any]):
        """Select optimal fidelity level based on current conditions"""
        try:
            # Factors for fidelity selection
            temperature_gradient = self._calculate_temperature_gradient(current_state)
            control_transient = self._assess_control_transient(control_inputs)
            computational_budget = self._get_computational_budget()
            
            # Decision logic
            if (temperature_gradient > 10.0 or control_transient > 0.5) and computational_budget > 0.7:
                self.current_fidelity = "high"
                self.model_uncertainty = 0.02
            elif temperature_gradient > 5.0 or control_transient > 0.2:
                self.current_fidelity = "medium"
                self.model_uncertainty = 0.05
            else:
                self.current_fidelity = "low"
                self.model_uncertainty = 0.10
            
            logger.debug(f"Selected fidelity: {self.current_fidelity}")
            
        except Exception as e:
            logger.error(f"Error selecting fidelity: {e}")
            self.current_fidelity = "medium"
    
    def _calculate_temperature_gradient(self, current_state) -> float:
        """Calculate temperature gradient magnitude"""
        if not hasattr(current_state, 'temperature_profile'):
            return 0.0
        
        try:
            temp_profile = current_state.temperature_profile
            if len(temp_profile) > 1:
                gradient = np.gradient(temp_profile)
                return np.max(np.abs(gradient))
            return 0.0
        except:
            return 0.0
    
    def _assess_control_transient(self, control_inputs: Dict[str, Any]) -> float:
        """Assess how transient the control inputs are"""
        if not control_inputs or not hasattr(self, 'previous_control_inputs'):
            self.previous_control_inputs = control_inputs
            return 0.0
        
        try:
            transient_measure = 0.0
            for key, value in control_inputs.items():
                if isinstance(value, (int, float)):
                    prev_value = self.previous_control_inputs.get(key, value)
                    if prev_value != 0:
                        relative_change = abs((value - prev_value) / prev_value)
                        transient_measure = max(transient_measure, relative_change)
            
            self.previous_control_inputs = control_inputs
            return transient_measure
            
        except:
            return 0.0
    
    def _get_computational_budget(self) -> float:
        """Get available computational budget (0-1 scale)"""
        # In a real implementation, this would check system resources
        # For now, return a moderate budget
        return 0.6
    
    def _extract_heat_sources(self, control_inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Extract heat source information from control inputs"""
        heat_sources = {}
        
        try:
            for key, value in control_inputs.items():
                if 'power' in key.lower() and isinstance(value, (int, float)):
                    # Extract zone information from key
                    zone_id = self._extract_zone_id(key)
                    
                    heat_sources[f'zone_{zone_id}'] = {
                        'position': self._get_zone_position(zone_id),
                        'power': value
                    }
            
        except Exception as e:
            logger.error(f"Error extracting heat sources: {e}")
        
        return heat_sources
    
    def _extract_zone_id(self, key: str) -> int:
        """Extract zone ID from control input key"""
        import re
        match = re.search(r'(\d+)', key)
        return int(match.group(1)) if match else 0
    
    def _get_zone_position(self, zone_id: int) -> tuple:
        """Get physical position of heating zone"""
        # Simplified: assume zones are vertically stacked
        zone_height = 2.0  # meters per zone
        return (2.5, zone_id * zone_height)  # (x, y) position
    
    async def _apply_ml_correction(self, simulation_results: Dict[str, Any], 
                                  inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Apply ML-based correction to physics simulation results"""
        try:
            if not self.ml_correction_model:
                return simulation_results
            
            # Prepare ML input features
            ml_features = self._prepare_ml_features(simulation_results, inputs)
            
            # Get ML correction
            correction = await self.ml_correction_model.predict(ml_features)
            
            # Apply correction to temperature predictions
            if 'temperature_profile' in simulation_results and 'temperature_correction' in correction:
                corrected_temps = simulation_results['temperature_profile'] + correction['temperature_correction']
                simulation_results['temperature_profile'] = corrected_temps
                simulation_results['ml_corrected'] = True
            
            logger.debug("Applied ML correction to physics simulation")
            
        except Exception as e:
            logger.error(f"Error applying ML correction: {e}")
        
        return simulation_results
    
    def _prepare_ml_features(self, simulation_results: Dict[str, Any], 
                           inputs: Dict[str, Any]) -> np.ndarray:
        """Prepare features for ML correction model"""
        features = []
        
        try:
            # Temperature features
            if 'temperature_profile' in simulation_results:
                temp_profile = simulation_results['temperature_profile']
                features.extend([
                    np.mean(temp_profile),
                    np.std(temp_profile),
                    np.max(temp_profile),
                    np.min(temp_profile)
                ])
            
            # Control input features
            control_inputs = inputs.get('control_inputs', {})
            for i in range(4):  # Assume max 4 zones
                power_key = f'power_{i}'
                features.append(control_inputs.get(power_key, 0.0))
            
            # Ambient conditions
            ambient = inputs.get('ambient_conditions', {})
            features.extend([
                ambient.get('temperature', 20.0),
                ambient.get('wind_speed', 0.0)
            ])
            
        except Exception as e:
            logger.error(f"Error preparing ML features: {e}")
            features = [0.0] * 10  # Default features
        
        return np.array(features)
    
    def _cache_prediction_data(self, simulation_results: Dict[str, Any], inputs: Dict[str, Any]):
        """Cache data for future predictions"""
        try:
            cache_key = str(hash(str(inputs)))
            self.prediction_cache[cache_key] = {
                'results': simulation_results,
                'timestamp': datetime.now(),
                'inputs': inputs
            }
            
            # Keep cache size manageable
            if len(self.prediction_cache) > 100:
                oldest_key = min(self.prediction_cache.keys(), 
                               key=lambda k: self.prediction_cache[k]['timestamp'])
                del self.prediction_cache[oldest_key]
                
        except Exception as e:
            logger.error(f"Error caching prediction data: {e}")
    
    async def predict_future_state(self, current_state, control_scenario: Dict[str, Any], 
                                 prediction_horizon: int = 10) -> Dict[str, Any]:
        """Predict future state given control scenario"""
        try:
            predictions = []
            state = current_state
            
            for step in range(prediction_horizon):
                # Extract control inputs for this step
                step_controls = self._get_step_controls(control_scenario, step)
                
                # Get ambient conditions (could be from weather forecast)
                ambient_conditions = control_scenario.get('ambient_conditions', {
                    'temperature': 20.0,
                    'wind_speed': 0.0
                })
                
                # Simulate step
                step_result = await self.simulate_step(state, step_controls, ambient_conditions)
                predictions.append(step_result)
                
                # Update state for next step
                state = self._create_next_state(state, step_result)
            
            return {
                'predictions': predictions,
                'horizon': prediction_horizon,
                'confidence': np.mean([p.get('confidence', 0.0) for p in predictions])
            }
            
        except Exception as e:
            logger.error(f"Error predicting future state: {e}")
            return {'error': str(e)}
    
    def _get_step_controls(self, control_scenario: Dict[str, Any], step: int) -> Dict[str, Any]:
        """Extract control inputs for a specific prediction step"""
        controls = {}
        
        for key, value in control_scenario.get('controls', {}).items():
            if isinstance(value, list) and len(value) > step:
                controls[key] = value[step]
            elif isinstance(value, (int, float)):
                controls[key] = value  # Constant control
            
        return controls
    
    def _create_next_state(self, current_state, simulation_result: Dict[str, Any]):
        """Create next state from simulation results"""
        # This is a simplified state transition
        # In practice, would need to properly map simulation results to state format
        next_state = current_state
        
        if 'temperature_profile' in simulation_result:
            next_state.temperature_profile = simulation_result['temperature_profile']
        
        if 'predicted_outputs' in simulation_result:
            next_state.predicted_outputs = simulation_result['predicted_outputs']
        
        return next_state
    
    async def enable_ml_correction_model(self, model_path: str):
        """Load and enable ML correction model"""
        try:
            # Placeholder for ML model loading
            # In practice, would load trained correction model
            self.ml_correction_model = None  # Load model from path
            self.enable_ml_correction = True
            logger.info(f"Enabled ML correction model from {model_path}")
            
        except Exception as e:
            logger.error(f"Error loading ML correction model: {e}")
            self.enable_ml_correction = False
    
    async def calibrate_from_measurements(self, measurements: Dict[str, Any]):
        """Calibrate physics model from real measurements"""
        try:
            # Extract calibration data
            measured_temps = measurements.get('temperatures', [])
            measured_powers = measurements.get('powers', [])
            
            if not measured_temps:
                return
            
            # Simple calibration: adjust model uncertainty based on prediction error
            if self.last_state and 'temperature_profile' in self.last_state:
                predicted_temps = self.last_state['temperature_profile']
                
                if len(measured_temps) == len(predicted_temps):
                    errors = np.abs(np.array(measured_temps) - np.array(predicted_temps))
                    mean_error = np.mean(errors)
                    
                    # Update model uncertainty
                    self.model_uncertainty = min(0.5, max(0.01, mean_error / 100.0))
                    
                    logger.debug(f"Calibrated model uncertainty to {self.model_uncertainty:.3f}")
            
        except Exception as e:
            logger.error(f"Error calibrating model: {e}")
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get physics engine performance metrics"""
        metrics = {
            'fidelity_level': self.current_fidelity,
            'model_uncertainty': self.model_uncertainty,
            'cache_size': len(self.prediction_cache),
            'ml_correction_enabled': self.enable_ml_correction,
            'initialization_status': self.is_initialized
        }
        
        if self.real_time_engine:
            engine_state = await self.real_time_engine.get_current_state()
            metrics.update({
                'simulation_time': engine_state.get('simulation_time', 0.0),
                'step_count': engine_state.get('step_count', 0),
                'engine_state': engine_state.get('engine_state', 'unknown')
            })
        
        return metrics
    
    async def cleanup(self):
        """Clean up physics engine resources"""
        if self.real_time_engine:
            await self.real_time_engine.cleanup()
        
        self.prediction_cache.clear()
        self.is_initialized = False
        logger.info("Physics engine cleaned up")


# Factory function for creating physics engine instances
def create_physics_engine(physics_parameters: Dict[str, Any], 
                         solver_type: SolverType = SolverType.FINITE_DIFFERENCE) -> PhysicsEngine:
    """Create a physics engine instance"""
    return PhysicsEngine(physics_parameters)