/**
 * Asphalt Flow Simulation
 * Advanced fluid dynamics simulation for asphalt flow through pipes
 * Includes viscosity changes with temperature, pressure requirements, and valve operations
 */

export interface AsphaltProperties {
  grade: 'PG64-22' | 'PG70-28' | 'PG76-22' | 'AC-20' | 'AC-30';
  density: number; // kg/m³
  baseViscosity: number; // Pa·s at reference temperature
  referenceTemperature: number; // °C
  viscosityTemperatureIndex: number; // VTI
  penetration: number; // dmm (0.1mm)
  softeningPoint: number; // °C
  flashPoint: number; // °C
  specificHeat: number; // J/kg·K
  thermalConductivity: number; // W/m·K
}

export interface PipeSegment {
  id: string;
  startPosition: [number, number, number];
  endPosition: [number, number, number];
  diameter: number; // m
  length: number; // m
  roughness: number; // m
  elevation: number; // m
  insulation: {
    type: 'steam_traced' | 'electric_traced' | 'hot_oil_traced' | 'none';
    temperature: number; // °C
    efficiency: number; // %
  };
  material: 'carbon_steel' | 'stainless_steel';
}

export interface FlowControlValve {
  id: string;
  position: [number, number, number];
  type: 'ball' | 'butterfly' | 'gate' | 'globe' | 'needle';
  diameter: number; // m
  cvCoefficient: number; // Flow coefficient
  currentPosition: number; // % open (0-100)
  characteristics: 'linear' | 'equal_percentage' | 'quick_opening';
  actuator: {
    type: 'pneumatic' | 'electric' | 'manual';
    responseTime: number; // seconds
    accuracy: number; // %
  };
}

export interface PumpCharacteristics {
  id: string;
  type: 'gear' | 'screw' | 'progressive_cavity' | 'centrifugal';
  maxFlowRate: number; // L/min
  maxPressure: number; // bar
  maxViscosity: number; // Pa·s
  efficiency: number; // %
  speedRange: [number, number]; // [min, max] RPM
  temperatureRange: [number, number]; // [min, max] °C
}

export interface FlowState {
  timestamp: Date;
  flowRates: Map<string, number>; // L/min
  pressures: Map<string, number>; // bar
  temperatures: Map<string, number>; // °C
  viscosities: Map<string, number>; // Pa·s
  reynoldsNumbers: Map<string, number>;
  pressureDrops: Map<string, number>; // bar
  valvePositions: Map<string, number>; // % open
  pumpSpeeds: Map<string, number>; // RPM
  heatLosses: Map<string, number>; // kW
}

export class AsphaltFlowSimulation {
  private asphaltProperties: AsphaltProperties;
  private pipes: Map<string, PipeSegment> = new Map();
  private valves: Map<string, FlowControlValve> = new Map();
  private pumps: Map<string, PumpCharacteristics> = new Map();
  private currentState: FlowState;
  private ambientTemperature: number = 20; // °C

  constructor(asphaltGrade: AsphaltProperties['grade'] = 'PG64-22') {
    this.asphaltProperties = this.getAsphaltProperties(asphaltGrade);
    this.initializeSystemComponents();
    this.currentState = this.createInitialState();
  }

  private getAsphaltProperties(grade: AsphaltProperties['grade']): AsphaltProperties {
    const properties: { [key in AsphaltProperties['grade']]: AsphaltProperties } = {
      'PG64-22': {
        grade: 'PG64-22',
        density: 1020, // kg/m³
        baseViscosity: 0.4, // Pa·s at 135°C
        referenceTemperature: 135, // °C
        viscosityTemperatureIndex: 2.5,
        penetration: 85, // dmm
        softeningPoint: 48, // °C
        flashPoint: 230, // °C
        specificHeat: 2000, // J/kg·K
        thermalConductivity: 0.17 // W/m·K
      },
      'PG70-28': {
        grade: 'PG70-28',
        density: 1030,
        baseViscosity: 0.5,
        referenceTemperature: 135,
        viscosityTemperatureIndex: 2.3,
        penetration: 65,
        softeningPoint: 52,
        flashPoint: 240,
        specificHeat: 2000,
        thermalConductivity: 0.17
      },
      'PG76-22': {
        grade: 'PG76-22',
        density: 1040,
        baseViscosity: 0.6,
        referenceTemperature: 135,
        viscosityTemperatureIndex: 2.1,
        penetration: 55,
        softeningPoint: 58,
        flashPoint: 250,
        specificHeat: 2000,
        thermalConductivity: 0.17
      },
      'AC-20': {
        grade: 'AC-20',
        density: 1010,
        baseViscosity: 0.35,
        referenceTemperature: 135,
        viscosityTemperatureIndex: 2.7,
        penetration: 95,
        softeningPoint: 45,
        flashPoint: 220,
        specificHeat: 2000,
        thermalConductivity: 0.17
      },
      'AC-30': {
        grade: 'AC-30',
        density: 1015,
        baseViscosity: 0.45,
        referenceTemperature: 135,
        viscosityTemperatureIndex: 2.4,
        penetration: 75,
        softeningPoint: 50,
        flashPoint: 235,
        specificHeat: 2000,
        thermalConductivity: 0.17
      }
    };

    return properties[grade];
  }

  private initializeSystemComponents() {
    // Initialize main distribution pipe
    this.pipes.set('ASP-MAIN-HEADER', {
      id: 'ASP-MAIN-HEADER',
      startPosition: [0, 0.5, -6],
      endPosition: [8, 0.5, -8],
      diameter: 0.2, // 200mm
      length: 15,
      roughness: 0.000045,
      elevation: 0.5,
      insulation: {
        type: 'steam_traced',
        temperature: 160,
        efficiency: 85
      },
      material: 'carbon_steel'
    });

    // Initialize loading station pipes
    this.pipes.set('ASP-LOADING-01', {
      id: 'ASP-LOADING-01',
      startPosition: [8, 0.5, -8],
      endPosition: [8, 0, -10],
      diameter: 0.1, // 100mm
      length: 5,
      roughness: 0.000045,
      elevation: 0,
      insulation: {
        type: 'steam_traced',
        temperature: 160,
        efficiency: 85
      },
      material: 'stainless_steel'
    });

    // Initialize flow control valves
    this.valves.set('AV-ASP-MAIN-01', {
      id: 'AV-ASP-MAIN-01',
      position: [2, 0.5, -6.5],
      type: 'ball',
      diameter: 0.2,
      cvCoefficient: 150,
      currentPosition: 100,
      characteristics: 'linear',
      actuator: {
        type: 'pneumatic',
        responseTime: 5,
        accuracy: 1
      }
    });

    // Initialize asphalt transfer pump
    this.pumps.set('ASP-PUMP-01', {
      id: 'ASP-PUMP-01',
      type: 'gear',
      maxFlowRate: 800,
      maxPressure: 6,
      maxViscosity: 2.0,
      efficiency: 85,
      speedRange: [100, 1200],
      temperatureRange: [120, 180]
    });
  }

  private createInitialState(): FlowState {
    return {
      timestamp: new Date(),
      flowRates: new Map([
        ['ASP-MAIN-HEADER', 600],
        ['ASP-LOADING-01', 300]
      ]),
      pressures: new Map([
        ['ASP-PUMP-DISCHARGE', 4.0],
        ['ASP-MAIN-HEADER', 3.5]
      ]),
      temperatures: new Map([
        ['ASP-MAIN-HEADER', 150],
        ['ASP-LOADING-01', 148]
      ]),
      viscosities: new Map(),
      reynoldsNumbers: new Map(),
      pressureDrops: new Map(),
      valvePositions: new Map([
        ['AV-ASP-MAIN-01', 100]
      ]),
      pumpSpeeds: new Map([
        ['ASP-PUMP-01', 800]
      ]),
      heatLosses: new Map()
    };
  }

  public simulate(timeStep: number = 1.0): FlowState {
    // Update asphalt viscosity based on temperature
    this.updateViscosities();

    // Calculate flow characteristics
    this.calculateFlowCharacteristics();

    // Calculate pressure drops
    this.calculatePressureDrops();

    // Calculate heat losses and temperature changes
    this.calculateHeatTransfer();

    // Update valve flow coefficients
    this.updateValveFlows();

    // Calculate pump performance
    this.calculatePumpPerformance();

    // Update system pressures
    this.updateSystemPressures();

    this.currentState.timestamp = new Date();
    return { ...this.currentState };
  }

  private updateViscosities() {
    this.currentState.temperatures.forEach((temperature, componentId) => {
      const viscosity = this.calculateViscosity(temperature);
      this.currentState.viscosities.set(componentId, viscosity);
    });
  }

  private calculateViscosity(temperature: number): number {
    // Viscosity-temperature relationship for asphalt (Arrhenius-type)
    const tempKelvin = temperature + 273.15;
    const refTempKelvin = this.asphaltProperties.referenceTemperature + 273.15;
    
    // VTI (Viscosity Temperature Index) relationship
    const logViscosity = Math.log10(this.asphaltProperties.baseViscosity) + 
      this.asphaltProperties.viscosityTemperatureIndex * 
      Math.log10(refTempKelvin / tempKelvin);
    
    return Math.pow(10, logViscosity);
  }

  private calculateFlowCharacteristics() {
    this.pipes.forEach((pipe, pipeId) => {
      const flowRate = this.currentState.flowRates.get(pipeId) || 0;
      const temperature = this.currentState.temperatures.get(pipeId) || 150;
      const viscosity = this.currentState.viscosities.get(pipeId) || 0.1;
      
      // Calculate velocity
      const velocity = (flowRate / 60000) / (Math.PI * Math.pow(pipe.diameter / 2, 2)); // m/s
      
      // Calculate Reynolds number for non-Newtonian fluid
      const reynoldsNumber = (this.asphaltProperties.density * velocity * pipe.diameter) / viscosity;
      this.currentState.reynoldsNumbers.set(pipeId, reynoldsNumber);
    });
  }

  private calculatePressureDrops() {
    this.pipes.forEach((pipe, pipeId) => {
      const flowRate = this.currentState.flowRates.get(pipeId) || 0;
      const viscosity = this.currentState.viscosities.get(pipeId) || 0.1;
      const reynoldsNumber = this.currentState.reynoldsNumbers.get(pipeId) || 1000;
      
      const velocity = (flowRate / 60000) / (Math.PI * Math.pow(pipe.diameter / 2, 2)); // m/s
      
      // Friction factor calculation for non-Newtonian fluids
      let frictionFactor: number;
      
      if (reynoldsNumber < 2100) {
        // Laminar flow for non-Newtonian fluids
        frictionFactor = 64 / reynoldsNumber;
      } else {
        // Turbulent flow with viscosity correction
        const relativeRoughness = pipe.roughness / pipe.diameter;
        frictionFactor = 0.25 / Math.pow(
          Math.log10(relativeRoughness / 3.7 + 5.74 / Math.pow(reynoldsNumber, 0.9)), 2
        );
        
        // Correction for high viscosity fluids
        const viscosityCorrection = 1 + 0.1 * Math.log10(viscosity / 0.001);
        frictionFactor *= viscosityCorrection;
      }
      
      // Darcy-Weisbach equation
      const pressureDrop = frictionFactor * (pipe.length / pipe.diameter) * 
        (this.asphaltProperties.density * Math.pow(velocity, 2)) / (2 * 100000); // bar
      
      // Add elevation pressure change
      const elevationPressure = (this.asphaltProperties.density * 9.81 * pipe.elevation) / 100000; // bar
      
      const totalPressureDrop = pressureDrop + elevationPressure;
      this.currentState.pressureDrops.set(pipeId, totalPressureDrop);
    });
  }

  private calculateHeatTransfer() {
    this.pipes.forEach((pipe, pipeId) => {
      const temperature = this.currentState.temperatures.get(pipeId) || 150;
      const flowRate = this.currentState.flowRates.get(pipeId) || 0;
      
      // Heat loss calculation
      let heatLoss = 0; // kW
      
      if (pipe.insulation.type !== 'none') {
        // Heat loss through insulation
        const surfaceArea = Math.PI * pipe.diameter * pipe.length; // m²
        const tempDiff = temperature - this.ambientTemperature;
        
        // Overall heat transfer coefficient (simplified)
        const overallU = 2.0; // W/m²·K (includes insulation, convection, radiation)
        heatLoss = (overallU * surfaceArea * tempDiff) / 1000; // kW
        
        // Heat input from tracing
        if (pipe.insulation.type !== 'none') {
          const tracingHeat = (pipe.insulation.efficiency / 100) * heatLoss * 1.2; // 20% extra for tracing
          heatLoss -= tracingHeat;
        }
      }
      
      this.currentState.heatLosses.set(pipeId, Math.max(0, heatLoss));
      
      // Temperature drop due to heat loss
      if (flowRate > 0) {
        const massFlowRate = (flowRate / 60000) * this.asphaltProperties.density; // kg/s
        const tempDrop = (heatLoss * 1000) / (massFlowRate * this.asphaltProperties.specificHeat); // °C
        
        const newTemperature = temperature - tempDrop;
        this.currentState.temperatures.set(pipeId, Math.max(newTemperature, this.ambientTemperature + 20));
      }
    });
  }

  private updateValveFlows() {
    this.valves.forEach((valve, valveId) => {
      const position = this.currentState.valvePositions.get(valveId) || 100;
      const upstreamPressure = this.currentState.pressures.get('ASP-PUMP-DISCHARGE') || 4.0;
      const downstreamPressure = this.currentState.pressures.get('ASP-MAIN-HEADER') || 3.5;
      
      // Flow coefficient based on valve position and characteristics
      let flowCoefficient: number;
      
      switch (valve.characteristics) {
        case 'linear':
          flowCoefficient = valve.cvCoefficient * (position / 100);
          break;
        case 'equal_percentage':
          flowCoefficient = valve.cvCoefficient * Math.pow(position / 100, 2);
          break;
        case 'quick_opening':
          flowCoefficient = valve.cvCoefficient * Math.sqrt(position / 100);
          break;
        default:
          flowCoefficient = valve.cvCoefficient * (position / 100);
      }
      
      // Flow rate through valve (simplified)
      const pressureDrop = Math.max(0.1, upstreamPressure - downstreamPressure);
      const specificGravity = this.asphaltProperties.density / 1000;
      const flowRate = flowCoefficient * Math.sqrt(pressureDrop / specificGravity); // L/min
      
      // Update flow rate in connected pipe
      const connectedPipe = 'ASP-MAIN-HEADER'; // Simplified connection
      this.currentState.flowRates.set(connectedPipe, flowRate);
    });
  }

  private calculatePumpPerformance() {
    this.pumps.forEach((pump, pumpId) => {
      const speed = this.currentState.pumpSpeeds.get(pumpId) || 800;
      const temperature = this.currentState.temperatures.get('ASP-MAIN-HEADER') || 150;
      const viscosity = this.currentState.viscosities.get('ASP-MAIN-HEADER') || 0.1;
      
      // Check operating limits
      if (temperature < pump.temperatureRange[0] || temperature > pump.temperatureRange[1]) {
        console.warn(`Pump ${pumpId} operating outside temperature range`);
      }
      
      if (viscosity > pump.maxViscosity) {
        console.warn(`Pump ${pumpId} operating above maximum viscosity`);
      }
      
      // Calculate pump flow rate based on speed and viscosity
      const speedRatio = speed / 1000; // Normalized speed
      const viscosityEffect = Math.max(0.5, 1 - (viscosity - 0.1) / pump.maxViscosity);
      
      const flowRate = pump.maxFlowRate * speedRatio * viscosityEffect;
      const pressure = pump.maxPressure * speedRatio * Math.sqrt(viscosityEffect);
      
      this.currentState.flowRates.set('ASP-MAIN-HEADER', flowRate);
      this.currentState.pressures.set('ASP-PUMP-DISCHARGE', pressure);
    });
  }

  private updateSystemPressures() {
    // Calculate system pressures based on pump discharge and pressure drops
    let currentPressure = this.currentState.pressures.get('ASP-PUMP-DISCHARGE') || 4.0;
    
    this.pipes.forEach((pipe, pipeId) => {
      const pressureDrop = this.currentState.pressureDrops.get(pipeId) || 0;
      currentPressure -= pressureDrop;
      this.currentState.pressures.set(pipeId, Math.max(0.5, currentPressure));
    });
  }

  // Public API methods
  public getCurrentState(): FlowState {
    return { ...this.currentState };
  }

  public setValvePosition(valveId: string, position: number): boolean {
    if (this.valves.has(valveId)) {
      this.currentState.valvePositions.set(valveId, Math.max(0, Math.min(100, position)));
      return true;
    }
    return false;
  }

  public setPumpSpeed(pumpId: string, speed: number): boolean {
    const pump = this.pumps.get(pumpId);
    if (pump) {
      const clampedSpeed = Math.max(pump.speedRange[0], Math.min(pump.speedRange[1], speed));
      this.currentState.pumpSpeeds.set(pumpId, clampedSpeed);
      return true;
    }
    return false;
  }

  public getCurrentAsphaltProperties(): AsphaltProperties {
    return { ...this.asphaltProperties };
  }

  public calculateOptimalTemperature(targetViscosity: number): number {
    // Calculate temperature needed for target viscosity
    const logTargetViscosity = Math.log10(targetViscosity);
    const logBaseViscosity = Math.log10(this.asphaltProperties.baseViscosity);
    const refTempKelvin = this.asphaltProperties.referenceTemperature + 273.15;
    
    const tempRatio = Math.pow(10, (logBaseViscosity - logTargetViscosity) / this.asphaltProperties.viscosityTemperatureIndex);
    const targetTempKelvin = refTempKelvin / tempRatio;
    
    return targetTempKelvin - 273.15; // Convert to °C
  }

  public getFlowEfficiency(): number {
    const totalFlowRate = Array.from(this.currentState.flowRates.values()).reduce((sum, rate) => sum + rate, 0);
    const totalPressureDrop = Array.from(this.currentState.pressureDrops.values()).reduce((sum, drop) => sum + drop, 0);
    const totalHeatLoss = Array.from(this.currentState.heatLosses.values()).reduce((sum, loss) => sum + loss, 0);
    
    // Simplified efficiency calculation
    const hydraulicEfficiency = Math.max(0, 100 - totalPressureDrop * 10); // % (simplified)
    const thermalEfficiency = Math.max(0, 100 - totalHeatLoss * 5); // % (simplified)
    
    return (hydraulicEfficiency + thermalEfficiency) / 2;
  }
}
