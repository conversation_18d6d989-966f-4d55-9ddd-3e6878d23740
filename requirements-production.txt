# Production Requirements for MLOps Digital Twin Platform
# Optimized for deployment with minimal dependencies

# Core Dependencies
numpy>=1.21.0,<2.0.0
scipy>=1.8.0,<2.0.0
pandas>=1.3.0,<3.0.0
scikit-learn>=1.0.0,<2.0.0

# Async and Networking
asyncio-mqtt>=0.11.0
aiohttp>=3.8.0,<4.0.0
aiofiles>=0.8.0
websockets>=10.0,<12.0
httpx>=0.23.0

# Machine Learning
torch>=1.12.0,<3.0.0
torchvision>=0.13.0,<1.0.0

# Data Storage and Processing
redis>=4.3.0,<5.0.0
influxdb-client>=1.30.0,<2.0.0
minio>=7.1.0,<8.0.0

# Configuration and Utilities
pyyaml>=6.0,<7.0
pydantic>=1.9.0,<3.0.0
click>=8.0.0,<9.0.0
python-dotenv>=0.19.0,<2.0.0
jsonschema>=4.0.0,<5.0.0

# API and Web Framework
fastapi>=0.85.0,<1.0.0
uvicorn[standard]>=0.18.0,<1.0.0
starlette>=0.20.0,<1.0.0
python-multipart>=0.0.5,<1.0.0

# Database
sqlalchemy>=1.4.0,<3.0.0
alembic>=1.8.0,<2.0.0
psycopg2-binary>=2.9.0,<3.0.0

# Monitoring and Logging
prometheus-client>=0.14.0,<1.0.0
structlog>=22.1.0,<24.0.0

# Visualization (Web Dashboard)
plotly>=5.10.0,<6.0.0
streamlit>=1.25.0,<2.0.0

# Security
cryptography>=37.0.0,<42.0.0
passlib[bcrypt]>=1.7.4,<2.0.0
python-jose[cryptography]>=3.3.0,<4.0.0

# Utilities
joblib>=1.1.0,<2.0.0
requests>=2.28.0,<3.0.0
pillow>=9.0.0,<11.0.0

# Optional Weather API
# openweathermap-api>=0.1.0  # Alternative weather library if needed

# Optional Industrial Protocols (commented out to avoid installation issues)
# asyncua>=1.0.0  # OPC UA client
# pymodbus>=3.0.0  # Modbus client

# MLflow for model management
mlflow>=2.0.0,<3.0.0
boto3>=1.24.0,<2.0.0  # For S3-compatible storage

# Development and Testing (for production debugging)
pytest>=7.0.0,<8.0.0
pytest-asyncio>=0.19.0,<1.0.0

# Image processing for SCADA integration
opencv-python-headless>=4.6.0,<5.0.0  # Headless version for servers
pytesseract>=0.3.10,<1.0.0

# Optional SCADA screen capture (lightweight)
# mss>=6.1.0  # Fast screen capture - only if needed
# pyautogui>=0.9.54  # GUI automation - only if needed