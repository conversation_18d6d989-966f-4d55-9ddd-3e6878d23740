# Digital Twin Integration for Asphalt Heating Control System

This project implements a comprehensive digital twin system with real-time simulation and SCADA measurement integration for asphalt heating control systems using NVIDIA PhysicsML/Modulus.

## 🏗️ System Architecture

The system consists of several integrated components:

- **Digital Twin Core Engine**: Manages twin lifecycle and state synchronization
- **SCADA Integration Layer**: OPC UA and Modbus communication with industrial systems  
- **Real-time Physics Simulation**: NVIDIA Modulus-based heat transfer simulation
- **API Gateway**: REST and WebSocket APIs for system interaction
- **Data Processing Pipeline**: Real-time data fusion and analytics
- **Monitoring & Alerting**: Comprehensive system monitoring

## 🚀 Quick Start

### Prerequisites

- Python 3.9+
- NVIDIA GPU with CUDA 11.8+ (for physics simulation)
- Docker and Docker Compose (for containerized deployment)

### Installation

1. **Clone the repository**:
```bash
git clone <repository-url>
cd simulation-based-deep-learning
```

2. **Set up Python environment**:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

3. **Configure the system**:
```bash
cp config/digital_twin_config.yaml config/local_config.yaml
# Edit config/local_config.yaml as needed
```

4. **Run with Docker Compose** (Recommended):
```bash
docker-compose up -d
```

5. **Or run locally**:
```bash
python src/main.py --config config/local_config.yaml
```

### API Access

- **API Documentation**: http://localhost:8000/docs
- **Grafana Dashboard**: http://localhost:3000 (admin/admin_password)
- **Prometheus Metrics**: http://localhost:9090
- **MLflow Tracking**: http://localhost:5000

## 📋 Phase Implementation Status

### ✅ Phase 0: Digital Twin Foundation (COMPLETED)
- [x] **Digital Twin Core Architecture**
  - [x] Twin state management system
  - [x] Real-time synchronization engine
  - [x] Virtual-physical mapping layer
  - [x] State reconciliation algorithms (Kalman filter)
  - [x] Twin lifecycle management

- [x] **SCADA Integration Layer**
  - [x] OPC UA/DA protocol handlers
  - [x] Modbus TCP/RTU communication
  - [x] Real-time tag management system
  - [x] Alarm and event processing
  - [x] Connection management and reconnection

- [x] **Real-time Simulation Framework**
  - [x] Real-time physics solver (Finite Difference)
  - [x] Simulation state checkpointing
  - [x] Dynamic model switching capabilities
  - [x] Multi-fidelity model support preparation

### 🔧 Next Implementation Phases

#### Phase 1: Enhanced Physics Model Development
- [ ] Define heat transfer PDEs for asphalt heating
- [ ] Set up boundary conditions and material properties
- [ ] Create geometry representation of heating system
- [ ] Implement finite difference/element baseline
- [ ] **Digital Twin Physics Extensions**
  - [ ] Multi-fidelity model hierarchies
  - [ ] Real-time model adaptation
  - [ ] Uncertainty quantification
  - [ ] Model calibration from live data
  - [ ] Physics-constrained state estimation

#### Phase 2: Enhanced NVIDIA Modulus Integration
- [ ] Install Modulus framework
- [ ] Convert PDEs to neural network architecture
- [ ] Define loss functions (physics + data)
- [ ] Set up training infrastructure on GPU
- [ ] **Digital Twin Neural Extensions**
  - [ ] Online learning capabilities
  - [ ] Real-time inference optimization
  - [ ] Model ensemble management

## 🏃‍♂️ Usage Examples

### Creating a Digital Twin

```python
import asyncio
from digital_twin.core import DigitalTwinEngine, TwinConfiguration

async def create_heating_twin():
    engine = DigitalTwinEngine()
    
    config = TwinConfiguration(
        twin_id="asphalt_heater_001",
        name="Asphalt Heating System 1",
        description="Digital twin for asphalt heating control",
        physical_asset_id="heater_001",
        model_version="v1.0",
        update_frequency=5.0,
        synchronization_threshold=0.05,
        physics_parameters={
            "thermal_conductivity": 0.5,
            "specific_heat": 920.0,
            "density": 2400.0
        },
        scada_tags=["temperature_01", "heat_flux_01"]
    )
    
    twin = await engine.create_twin(config)
    await engine.start_twin("asphalt_heater_001")
    
    return twin
```

### SCADA Integration

```python
from digital_twin.scada import OPCUAClient, OPCConfiguration, OPCTag

async def setup_opc_client():
    tags = [
        OPCTag(
            name="temperature_01",
            node_id="ns=2;s=Temperature.Zone1",
            data_type="float",
            description="Temperature sensor zone 1"
        )
    ]
    
    config = OPCConfiguration(
        endpoint_url="opc.tcp://localhost:4840",
        tags=tags
    )
    
    client = OPCUAClient(config)
    await client.connect()
    
    # Read tag value
    value = await client.read_tag("temperature_01")
    print(f"Temperature: {value}°C")
```

### Real-time Physics Simulation

```python
from digital_twin.simulation import RealTimePhysicsEngine, SimulationConfig

async def run_physics_simulation():
    config = SimulationConfig(
        solver_type="finite_difference",
        real_time_factor=1.0,
        max_simulation_time=3600.0
    )
    
    engine = RealTimePhysicsEngine(config)
    await engine.initialize()
    await engine.start()
    
    # Simulation runs in background
    while engine.state == "running":
        state = await engine.get_current_state()
        print(f"Simulation time: {state['simulation_time']:.2f}s")
        await asyncio.sleep(1.0)
```

### API Usage

```bash
# Create a digital twin
curl -X POST "http://localhost:8000/api/v1/twins" \
  -H "Content-Type: application/json" \
  -d '{
    "twin_id": "asphalt_heater_001",
    "name": "Asphalt Heater System 1",
    "description": "Digital twin for asphalt heating system",
    "physical_asset_id": "heater_001",
    "auto_start": true
  }'

# Get twin state
curl "http://localhost:8000/api/v1/twins/asphalt_heater_001/state"

# Update twin with measurements
curl -X POST "http://localhost:8000/api/v1/twins/asphalt_heater_001/update" \
  -H "Content-Type: application/json" \
  -d '{
    "measurements": {
      "temperature_01": 85.5,
      "heat_flux_01": 1250.0
    }
  }'
```

## 🔧 Configuration

The system is configured via `config/digital_twin_config.yaml`. Key sections include:

- **digital_twin**: Twin engine and default settings
- **scada**: OPC UA and Modbus client configurations
- **simulation**: Physics simulation parameters
- **api**: REST API server settings
- **monitoring**: Logging and alerting configuration

Example configuration for a heating system:

```yaml
digital_twin:
  twins:
    asphalt_heating:
      name: "Asphalt Heating System"
      update_frequency: 5.0
      synchronization_threshold: 0.05
      physics_parameters:
        thermal_conductivity: 0.5
        specific_heat: 920.0
        density: 2400.0
        geometry_length: 10.0
        geometry_width: 5.0
        grid_size_x: 50
        grid_size_y: 25
      scada_tags:
        - "temperature_01"
        - "temperature_02" 
        - "heat_flux_01"
        - "control_output_01"

scada:
  opc_ua:
    enabled: true
    endpoint_url: "opc.tcp://localhost:4840"
    tags:
      - name: "temperature_01"
        node_id: "ns=2;s=Temperature.Zone1"
        data_type: "float"
        alarm_high: 200.0
        alarm_low: 0.0
```

## 🛠️ Development

### Project Structure

```
.
├── digital_twin/           # Core digital twin components
│   ├── core/              # Twin engine and state management
│   ├── scada/             # SCADA integration (OPC UA, Modbus)
│   └── simulation/        # Physics simulation framework
├── src/                   # Application source code
│   ├── api/               # REST API endpoints
│   ├── services/          # Business logic services
│   └── utils/             # Utility functions
├── config/                # Configuration files
├── tests/                 # Test suites
├── scripts/               # Deployment and utility scripts
└── docker-compose.yml     # Container orchestration
```

### Running Tests

```bash
# Run all tests
python -m pytest tests/ -v

# Run with coverage
python -m pytest tests/ --cov=src --cov=digital_twin --cov-report=html

# Run specific test category
python -m pytest tests/test_digital_twin/ -v
```

### Code Quality

```bash
# Format code
black src/ digital_twin/ tests/

# Sort imports
isort src/ digital_twin/ tests/

# Lint code
flake8 src/ digital_twin/ tests/

# Type checking
mypy src/ digital_twin/
```

## 🐳 Docker Deployment

The system includes a complete Docker Compose setup with:

- **digital-twin-app**: Main application container
- **redis**: Real-time data caching
- **postgres**: Persistent data storage
- **kafka**: Message streaming
- **influxdb**: Time-series data
- **prometheus**: Metrics collection
- **grafana**: Visualization dashboards
- **nginx**: Load balancer and reverse proxy

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f digital-twin-app

# Scale application
docker-compose up -d --scale digital-twin-app=3

# Stop services
docker-compose down
```

## 📊 Monitoring and Observability

### Metrics

The system exposes Prometheus metrics on port 8001:
- Twin synchronization accuracy
- SCADA connection status
- Simulation performance
- API response times

### Dashboards

Grafana dashboards are available for:
- Digital twin health and performance
- SCADA system monitoring
- Physics simulation metrics
- System resource utilization

### Logging

Structured logging with multiple levels:
- **DEBUG**: Detailed diagnostic information
- **INFO**: General operational information
- **WARNING**: Warning conditions
- **ERROR**: Error conditions
- **CRITICAL**: Critical errors requiring immediate attention

## 🔒 Security Considerations

- Authentication and authorization (configurable)
- TLS/SSL encryption for communications
- SCADA protocol security
- Container security scanning
- Network segmentation in Docker

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the [documentation](docs/)
- Review the API documentation at `/docs` endpoint

## 🗺️ Roadmap

See [CLAUDE.md](CLAUDE.md) for the complete implementation roadmap including:
- NVIDIA Modulus integration
- Advanced MLOps pipeline
- Multi-site federation
- Edge deployment capabilities
- Advanced analytics and AI features