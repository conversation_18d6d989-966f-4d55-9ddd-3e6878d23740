/**
 * Tank Configuration Factory
 * Creates enhanced tank configurations with heating coil specifications
 */

import { 
  TankPosition, 
  HeatingCoilConfiguration, 
  TankThermalProperties, 
  TankGeometry, 
  TankControlParameters 
} from '../config/plant-layout';

export interface TankConfigurationTemplate {
  baseCapacity: number; // L
  baseDiameter: number; // m
  baseHeight: number; // m
  coilDiameter: number; // mm
  pipeDiameter: number; // mm
  heatTransferCoefficient: number; // W/m²K
  maxHeatTransferRate: number; // kW
}

// Standard tank configuration templates
export const TANK_TEMPLATES = {
  LARGE_STORAGE: {
    baseCapacity: 50000,
    baseDiameter: 4.5,
    baseHeight: 6.0,
    coilDiameter: 1800,
    pipeDiameter: 50,
    heatTransferCoefficient: 500,
    maxHeatTransferRate: 75
  } as TankConfigurationTemplate,
  
  MEDIUM_STORAGE: {
    baseCapacity: 35000,
    baseDiameter: 4.0,
    baseHeight: 5.5,
    coilDiameter: 1600,
    pipeDiameter: 40,
    heatTransferCoefficient: 480,
    maxHeatTransferRate: 60
  } as TankConfigurationTemplate,
  
  SMALL_STORAGE: {
    baseCapacity: 25000,
    baseDiameter: 3.5,
    baseHeight: 5.0,
    coilDiameter: 1400,
    pipeDiameter: 32,
    heatTransferCoefficient: 450,
    maxHeatTransferRate: 45
  } as TankConfigurationTemplate
};

export function createHeatingCoilConfiguration(
  tankId: number, 
  template: TankConfigurationTemplate
): HeatingCoilConfiguration {
  const coilLength = Math.PI * (template.coilDiameter / 1000) * 3; // 3 turns
  const heatTransferArea = Math.PI * (template.pipeDiameter / 1000) * coilLength;
  
  return {
    coilId: `HC-ASP-${tankId.toString().padStart(2, '0')}`,
    type: '3-turn',
    specifications: {
      turns: 3,
      coilDiameter: template.coilDiameter,
      pipeDiameter: template.pipeDiameter,
      totalLength: coilLength,
      material: 'stainless_steel',
      wallThickness: Math.max(3, template.pipeDiameter * 0.06), // 6% of diameter, min 3mm
      surfaceFinish: 'smooth'
    },
    positioning: {
      elevationFromBottom: 0.5,
      centerOffset: [0, 0],
      supportType: 'bottom_mounted',
      accessPoints: [
        `AP-HC-${tankId.toString().padStart(2, '0')}-INLET`,
        `AP-HC-${tankId.toString().padStart(2, '0')}-OUTLET`,
        `AP-HC-${tankId.toString().padStart(2, '0')}-DRAIN`
      ]
    },
    thermalCharacteristics: {
      heatTransferArea,
      heatTransferCoefficient: template.heatTransferCoefficient,
      foulingFactor: 0.0002,
      thermalEfficiency: 85,
      maxHeatTransferRate: template.maxHeatTransferRate
    },
    fluidDynamics: {
      maxFlowRate: template.maxHeatTransferRate * 4, // L/min (rough estimate)
      minFlowRate: template.maxHeatTransferRate * 0.4,
      pressureDrop: 0.5,
      reynoldsNumber: 15000,
      prandtlNumber: 7.5
    },
    operationalLimits: {
      maxTemperature: 320,
      maxPressure: 10,
      maxThermalStress: 150,
      fatigueLifeCycles: 100000
    }
  };
}

export function createTankThermalProperties(): TankThermalProperties {
  return {
    fluidProperties: {
      density: 1000, // kg/m³ (asphalt)
      specificHeat: 2100, // J/kg·K
      thermalConductivity: 0.15, // W/m·K
      viscosity: 0.5, // Pa·s at operating temperature
      expansionCoefficient: 0.0007 // 1/K
    },
    tankMaterial: {
      density: 7850, // kg/m³ (steel)
      specificHeat: 460, // J/kg·K
      thermalConductivity: 50, // W/m·K
      thermalDiffusivity: 1.4e-5 // m²/s
    },
    insulation: {
      type: 'mineral_wool',
      thickness: 100, // mm
      thermalConductivity: 0.04, // W/m·K
      temperatureRating: 200, // °C
      weatherResistant: true
    },
    heatLoss: {
      ambientTemperature: 20, // °C
      windSpeed: 3, // m/s
      convectionCoefficient: 15, // W/m²·K
      radiationEmissivity: 0.9,
      totalHeatLossCoefficient: 2.5 // W/m²·K
    }
  };
}

export function createTankGeometry(template: TankConfigurationTemplate): TankGeometry {
  const totalSurfaceArea = 2 * Math.PI * Math.pow(template.baseDiameter / 2, 2) + 
    Math.PI * template.baseDiameter * template.baseHeight;
  
  return {
    shape: 'cylindrical',
    dimensions: {
      diameter: template.baseDiameter,
      height: template.baseHeight,
      wallThickness: Math.max(8, template.baseDiameter * 2) // mm, minimum 8mm
    },
    volumes: {
      totalVolume: template.baseCapacity,
      workingVolume: template.baseCapacity * 0.9,
      deadVolume: template.baseCapacity * 0.04,
      vaporSpace: template.baseCapacity * 0.06
    },
    surfaces: {
      totalSurfaceArea,
      heatedSurfaceArea: totalSurfaceArea * 0.8,
      insulatedSurfaceArea: totalSurfaceArea
    },
    structural: {
      designPressure: 3, // bar
      designTemperature: 200, // °C
      materialGrade: 'A516-70',
      corrosionAllowance: 3, // mm
      windLoadRating: 1.5, // kPa
      seismicRating: 'Zone 2'
    }
  };
}

export function createTankControlParameters(): TankControlParameters {
  return {
    temperature: {
      setpoint: 150, // °C
      deadband: 2, // °C
      rampRate: 1, // °C/min
      maxRampRate: 5, // °C/min
      alarmLimits: {
        highHigh: 180, // °C
        high: 170, // °C
        low: 130, // °C
        lowLow: 120 // °C
      }
    },
    level: {
      setpoint: 75, // %
      deadband: 5, // %
      alarmLimits: {
        highHigh: 95, // %
        high: 90, // %
        low: 20, // %
        lowLow: 10 // %
      }
    },
    pressure: {
      setpoint: 1.5, // bar
      deadband: 0.2, // bar
      alarmLimits: {
        highHigh: 2.8, // bar
        high: 2.5, // bar
        low: 0.5, // bar
        lowLow: 0.2 // bar
      }
    },
    heating: {
      pidParameters: {
        kp: 2.0,
        ki: 0.1,
        kd: 0.05,
        outputLimits: [0, 100]
      },
      heatingStrategy: 'continuous',
      energyOptimization: true,
      loadFollowing: true
    }
  };
}

export function createEnhancedTankConfiguration(
  id: number,
  name: string,
  position: [number, number, number],
  template: TankConfigurationTemplate = TANK_TEMPLATES.LARGE_STORAGE
): TankPosition {
  return {
    id,
    name,
    position,
    capacity: template.baseCapacity,
    type: 'storage',
    hasHeatingCoil: true,
    coilTurns: 3,
    connections: {
      hotOilInlet: `HO-SUPPLY-${id.toString().padStart(2, '0')}`,
      hotOilOutlet: `HO-RETURN-${id.toString().padStart(2, '0')}`,
      asphaltOutlet: `ASP-OUT-${id.toString().padStart(2, '0')}`,
      sensors: [`TT-${id.toString().padStart(2, '0')}`, `LT-${id.toString().padStart(2, '0')}`, `PT-${id.toString().padStart(2, '0')}`]
    },
    heatingCoilConfig: createHeatingCoilConfiguration(id, template),
    thermalProperties: createTankThermalProperties(),
    geometry: createTankGeometry(template),
    controlParameters: createTankControlParameters()
  };
}

// Utility function to calculate thermal performance
export function calculateThermalPerformance(
  tankConfig: TankPosition,
  hotOilTemperature: number,
  tankTemperature: number
): {
  heatTransferRate: number; // kW
  efficiency: number; // %
  requiredFlowRate: number; // L/min
  energyConsumption: number; // kWh/h
} {
  const tempDiff = hotOilTemperature - tankTemperature;
  const heatTransferRate = Math.min(
    tankConfig.heatingCoilConfig.thermalCharacteristics.heatTransferCoefficient *
    tankConfig.heatingCoilConfig.thermalCharacteristics.heatTransferArea *
    tempDiff / 1000, // Convert to kW
    tankConfig.heatingCoilConfig.thermalCharacteristics.maxHeatTransferRate
  );
  
  const efficiency = tankConfig.heatingCoilConfig.thermalCharacteristics.thermalEfficiency;
  const requiredFlowRate = Math.max(
    tankConfig.heatingCoilConfig.fluidDynamics.minFlowRate,
    heatTransferRate * 10 // Rough estimate: 10 L/min per kW
  );
  
  const energyConsumption = heatTransferRate * (100 / efficiency);
  
  return {
    heatTransferRate,
    efficiency,
    requiredFlowRate,
    energyConsumption
  };
}

// Utility function to validate tank configuration
export function validateTankConfiguration(tankConfig: TankPosition): {
  valid: boolean;
  warnings: string[];
  errors: string[];
} {
  const warnings: string[] = [];
  const errors: string[] = [];
  
  // Check capacity vs geometry
  const calculatedVolume = Math.PI * Math.pow(tankConfig.geometry.dimensions.diameter! / 2, 2) * 
    tankConfig.geometry.dimensions.height * 1000; // Convert to liters
  
  if (Math.abs(calculatedVolume - tankConfig.capacity) > tankConfig.capacity * 0.1) {
    warnings.push(`Calculated volume (${calculatedVolume.toFixed(0)}L) differs from specified capacity (${tankConfig.capacity}L)`);
  }
  
  // Check heating coil sizing
  const coilVolume = Math.PI * Math.pow(tankConfig.heatingCoilConfig.specifications.pipeDiameter / 2000, 2) * 
    tankConfig.heatingCoilConfig.specifications.totalLength * 1000; // Convert to liters
  
  if (coilVolume > tankConfig.capacity * 0.05) {
    warnings.push(`Heating coil volume (${coilVolume.toFixed(1)}L) is large relative to tank capacity`);
  }
  
  // Check temperature limits
  if (tankConfig.controlParameters.temperature.setpoint > 
      tankConfig.heatingCoilConfig.operationalLimits.maxTemperature * 0.8) {
    errors.push('Temperature setpoint too close to coil maximum temperature');
  }
  
  return {
    valid: errors.length === 0,
    warnings,
    errors
  };
}
