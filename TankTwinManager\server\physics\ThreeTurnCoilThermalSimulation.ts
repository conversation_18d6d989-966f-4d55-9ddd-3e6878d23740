/**
 * 3-Turn Coil Heat Transfer Simulation
 * Detailed thermal simulation for 3-turn heating coils under tanks
 * Models heat transfer from hot-oil to tank contents without product mixing
 */

export interface CoilGeometry {
  turns: number;
  coilDiameter: number; // m
  pipeDiameter: number; // m
  pipeWallThickness: number; // m
  totalLength: number; // m
  elevationFromBottom: number; // m
  material: {
    thermalConductivity: number; // W/m·K
    density: number; // kg/m³
    specificHeat: number; // J/kg·K
  };
}

export interface FluidProperties {
  hotOil: {
    density: number; // kg/m³
    viscosity: number; // Pa·s
    specificHeat: number; // J/kg·K
    thermalConductivity: number; // W/m·K
    temperature: number; // °C
  };
  tankFluid: {
    density: number; // kg/m³
    viscosity: number; // Pa·s
    specificHeat: number; // J/kg·K
    thermalConductivity: number; // W/m·K
    temperature: number; // °C
  };
}

export interface ThermalBoundaryConditions {
  hotOilInletTemperature: number; // °C
  hotOilFlowRate: number; // L/min
  tankTemperature: number; // °C
  ambientTemperature: number; // °C
  tankLevel: number; // % (affects heat transfer area)
  foulingResistance: number; // m²·K/W
}

export interface CoilThermalState {
  timestamp: Date;
  hotOilTemperatures: number[]; // °C along coil length
  pipeWallTemperatures: number[]; // °C along coil length
  heatTransferRates: number[]; // W/m along coil length
  totalHeatTransfer: number; // kW
  hotOilOutletTemperature: number; // °C
  thermalEfficiency: number; // %
  pressureDrop: number; // bar
  reynoldsNumber: number;
  nusseltNumber: number;
  heatTransferCoefficients: {
    hotOilSide: number; // W/m²·K
    tankSide: number; // W/m²·K
    overall: number; // W/m²·K
  };
}

export class ThreeTurnCoilThermalSimulation {
  private geometry: CoilGeometry;
  private fluidProperties: FluidProperties;
  private boundaryConditions: ThermalBoundaryConditions;
  private currentState: CoilThermalState;
  private segments: number = 30; // Number of segments for numerical simulation

  constructor(geometry: CoilGeometry, fluidProperties: FluidProperties) {
    this.geometry = geometry;
    this.fluidProperties = fluidProperties;
    this.boundaryConditions = this.createDefaultBoundaryConditions();
    this.currentState = this.createInitialState();
  }

  private createDefaultBoundaryConditions(): ThermalBoundaryConditions {
    return {
      hotOilInletTemperature: 280, // °C
      hotOilFlowRate: 100, // L/min
      tankTemperature: 150, // °C
      ambientTemperature: 20, // °C
      tankLevel: 75, // %
      foulingResistance: 0.0002 // m²·K/W
    };
  }

  private createInitialState(): CoilThermalState {
    return {
      timestamp: new Date(),
      hotOilTemperatures: new Array(this.segments).fill(280),
      pipeWallTemperatures: new Array(this.segments).fill(220),
      heatTransferRates: new Array(this.segments).fill(0),
      totalHeatTransfer: 0,
      hotOilOutletTemperature: 260,
      thermalEfficiency: 85,
      pressureDrop: 0.5,
      reynoldsNumber: 15000,
      nusseltNumber: 80,
      heatTransferCoefficients: {
        hotOilSide: 1200,
        tankSide: 800,
        overall: 500
      }
    };
  }

  public simulate(boundaryConditions: ThermalBoundaryConditions, timeStep: number = 1.0): CoilThermalState {
    this.boundaryConditions = boundaryConditions;
    
    // Update fluid properties based on temperature
    this.updateFluidProperties();
    
    // Calculate flow characteristics
    this.calculateFlowCharacteristics();
    
    // Calculate heat transfer coefficients
    this.calculateHeatTransferCoefficients();
    
    // Solve thermal distribution along coil
    this.solveThermalDistribution();
    
    // Calculate overall performance
    this.calculateOverallPerformance();
    
    this.currentState.timestamp = new Date();
    return { ...this.currentState };
  }

  private updateFluidProperties() {
    // Update hot oil properties based on average temperature
    const avgHotOilTemp = (this.boundaryConditions.hotOilInletTemperature + 
      this.currentState.hotOilOutletTemperature) / 2;
    
    this.fluidProperties.hotOil.temperature = avgHotOilTemp;
    this.fluidProperties.hotOil.density = 900 - 0.7 * (avgHotOilTemp - 20);
    this.fluidProperties.hotOil.viscosity = Math.exp(-0.02 * avgHotOilTemp + 3) / 1000;
    
    // Update tank fluid properties
    this.fluidProperties.tankFluid.temperature = this.boundaryConditions.tankTemperature;
  }

  private calculateFlowCharacteristics() {
    const velocity = this.calculateVelocity();
    const diameter = this.geometry.pipeDiameter;
    
    // Reynolds number
    this.currentState.reynoldsNumber = (this.fluidProperties.hotOil.density * velocity * diameter) / 
      this.fluidProperties.hotOil.viscosity;
    
    // Pressure drop calculation
    this.currentState.pressureDrop = this.calculatePressureDrop(velocity);
  }

  private calculateVelocity(): number {
    const volumeFlowRate = this.boundaryConditions.hotOilFlowRate / 60000; // m³/s
    const crossSectionalArea = Math.PI * Math.pow(this.geometry.pipeDiameter / 2, 2);
    return volumeFlowRate / crossSectionalArea; // m/s
  }

  private calculatePressureDrop(velocity: number): number {
    const diameter = this.geometry.pipeDiameter;
    const length = this.geometry.totalLength;
    const roughness = 0.000045; // m (stainless steel)
    
    // Friction factor (Colebrook-White)
    const relativeRoughness = roughness / diameter;
    let frictionFactor: number;
    
    if (this.currentState.reynoldsNumber < 2300) {
      frictionFactor = 64 / this.currentState.reynoldsNumber;
    } else {
      frictionFactor = 0.25 / Math.pow(
        Math.log10(relativeRoughness / 3.7 + 5.74 / Math.pow(this.currentState.reynoldsNumber, 0.9)), 2
      );
    }
    
    // Add coil curvature effect
    const curvatureRatio = diameter / this.geometry.coilDiameter;
    const curvatureFactor = 1 + 0.033 * Math.pow(curvatureRatio, 0.5);
    
    // Darcy-Weisbach equation with curvature correction
    const pressureDrop = frictionFactor * curvatureFactor * (length / diameter) * 
      (this.fluidProperties.hotOil.density * Math.pow(velocity, 2)) / (2 * 100000); // bar
    
    return pressureDrop;
  }

  private calculateHeatTransferCoefficients() {
    // Hot oil side heat transfer coefficient (inside pipe)
    const velocity = this.calculateVelocity();
    const diameter = this.geometry.pipeDiameter;
    
    // Nusselt number for turbulent flow in curved pipes
    let nusselt: number;
    if (this.currentState.reynoldsNumber > 2300) {
      const prandtl = (this.fluidProperties.hotOil.viscosity * this.fluidProperties.hotOil.specificHeat) / 
        this.fluidProperties.hotOil.thermalConductivity;
      
      // Dittus-Boelter equation with curvature correction
      const curvatureRatio = diameter / this.geometry.coilDiameter;
      const curvatureCorrection = 1 + 3.5 * curvatureRatio;
      
      nusselt = 0.023 * Math.pow(this.currentState.reynoldsNumber, 0.8) * 
        Math.pow(prandtl, 0.4) * curvatureCorrection;
    } else {
      nusselt = 3.66; // Laminar flow
    }
    
    this.currentState.nusseltNumber = nusselt;
    this.currentState.heatTransferCoefficients.hotOilSide = 
      (nusselt * this.fluidProperties.hotOil.thermalConductivity) / diameter;
    
    // Tank side heat transfer coefficient (natural convection)
    const tempDiff = Math.abs(this.boundaryConditions.tankTemperature - 
      this.currentState.pipeWallTemperatures[0]);
    const grashof = this.calculateGrashofNumber(tempDiff);
    const prandtlTank = (this.fluidProperties.tankFluid.viscosity * this.fluidProperties.tankFluid.specificHeat) / 
      this.fluidProperties.tankFluid.thermalConductivity;
    
    const rayleigh = grashof * prandtlTank;
    let nusseltTank: number;
    
    if (rayleigh < 1e9) {
      nusseltTank = 0.54 * Math.pow(rayleigh, 0.25);
    } else {
      nusseltTank = 0.15 * Math.pow(rayleigh, 0.33);
    }
    
    this.currentState.heatTransferCoefficients.tankSide = 
      (nusseltTank * this.fluidProperties.tankFluid.thermalConductivity) / this.geometry.pipeDiameter;
    
    // Overall heat transfer coefficient
    const pipeWallResistance = Math.log((this.geometry.pipeDiameter + this.geometry.pipeWallThickness) / 
      this.geometry.pipeDiameter) / (2 * Math.PI * this.geometry.material.thermalConductivity);
    
    const totalResistance = 1 / this.currentState.heatTransferCoefficients.hotOilSide +
      pipeWallResistance +
      this.boundaryConditions.foulingResistance +
      1 / this.currentState.heatTransferCoefficients.tankSide;
    
    this.currentState.heatTransferCoefficients.overall = 1 / totalResistance;
  }

  private calculateGrashofNumber(tempDiff: number): number {
    const beta = 1 / (this.fluidProperties.tankFluid.temperature + 273.15); // 1/K
    const g = 9.81; // m/s²
    const L = this.geometry.pipeDiameter; // characteristic length
    const nu = this.fluidProperties.tankFluid.viscosity / this.fluidProperties.tankFluid.density;
    
    return (g * beta * tempDiff * Math.pow(L, 3)) / Math.pow(nu, 2);
  }

  private solveThermalDistribution() {
    const segmentLength = this.geometry.totalLength / this.segments;
    const massFlowRate = (this.boundaryConditions.hotOilFlowRate / 60000) * 
      this.fluidProperties.hotOil.density; // kg/s
    
    // Initialize temperatures
    this.currentState.hotOilTemperatures[0] = this.boundaryConditions.hotOilInletTemperature;
    
    // Solve along coil length using finite difference method
    for (let i = 1; i < this.segments; i++) {
      const prevTemp = this.currentState.hotOilTemperatures[i - 1];
      const heatTransferArea = Math.PI * this.geometry.pipeDiameter * segmentLength;
      
      // Effective tank temperature considering level
      const effectiveTankTemp = this.getEffectiveTankTemperature(i);
      
      // Heat transfer rate for this segment
      const tempDiff = prevTemp - effectiveTankTemp;
      const heatTransferRate = this.currentState.heatTransferCoefficients.overall * 
        heatTransferArea * tempDiff; // W
      
      this.currentState.heatTransferRates[i] = heatTransferRate / segmentLength; // W/m
      
      // Temperature drop in hot oil
      const tempDrop = heatTransferRate / (massFlowRate * this.fluidProperties.hotOil.specificHeat);
      this.currentState.hotOilTemperatures[i] = prevTemp - tempDrop;
      
      // Pipe wall temperature (simplified)
      this.currentState.pipeWallTemperatures[i] = (prevTemp + effectiveTankTemp) / 2;
    }
    
    this.currentState.hotOilOutletTemperature = 
      this.currentState.hotOilTemperatures[this.segments - 1];
  }

  private getEffectiveTankTemperature(segmentIndex: number): number {
    // Consider tank level - segments above liquid level have reduced heat transfer
    const segmentHeight = (segmentIndex / this.segments) * this.geometry.coilDiameter;
    const liquidHeight = (this.boundaryConditions.tankLevel / 100) * 6; // Assume 6m tank height
    
    if (segmentHeight > liquidHeight) {
      // Above liquid level - heat transfer to vapor space
      return this.boundaryConditions.ambientTemperature + 
        (this.boundaryConditions.tankTemperature - this.boundaryConditions.ambientTemperature) * 0.3;
    } else {
      return this.boundaryConditions.tankTemperature;
    }
  }

  private calculateOverallPerformance() {
    // Total heat transfer
    this.currentState.totalHeatTransfer = this.currentState.heatTransferRates
      .reduce((sum, rate) => sum + rate, 0) * this.geometry.totalLength / this.segments / 1000; // kW
    
    // Thermal efficiency
    const maxPossibleHeatTransfer = (this.boundaryConditions.hotOilFlowRate / 60000) * 
      this.fluidProperties.hotOil.density * this.fluidProperties.hotOil.specificHeat * 
      (this.boundaryConditions.hotOilInletTemperature - this.boundaryConditions.tankTemperature) / 1000; // kW
    
    this.currentState.thermalEfficiency = 
      (this.currentState.totalHeatTransfer / maxPossibleHeatTransfer) * 100;
  }

  // Public API methods
  public getCurrentState(): CoilThermalState {
    return { ...this.currentState };
  }

  public setBoundaryConditions(conditions: Partial<ThermalBoundaryConditions>) {
    this.boundaryConditions = { ...this.boundaryConditions, ...conditions };
  }

  public getCoilGeometry(): CoilGeometry {
    return { ...this.geometry };
  }

  public getFluidProperties(): FluidProperties {
    return { ...this.fluidProperties };
  }

  public calculateOptimalFlowRate(targetHeatTransfer: number): number {
    // Iterative calculation to find optimal flow rate for target heat transfer
    let flowRate = 50; // Start with 50 L/min
    let bestFlowRate = flowRate;
    let minError = Infinity;
    
    for (let i = 0; i < 20; i++) {
      const testConditions = { ...this.boundaryConditions, hotOilFlowRate: flowRate };
      const testState = this.simulate(testConditions);
      const error = Math.abs(testState.totalHeatTransfer - targetHeatTransfer);
      
      if (error < minError) {
        minError = error;
        bestFlowRate = flowRate;
      }
      
      if (testState.totalHeatTransfer < targetHeatTransfer) {
        flowRate += 10;
      } else {
        flowRate -= 5;
      }
      
      if (flowRate < 10) flowRate = 10;
      if (flowRate > 300) flowRate = 300;
    }
    
    return bestFlowRate;
  }
}
