# Default values for tank-system
# This is a YAML-formatted file.

global:
  imageRegistry: "tank-registry.io"
  imageTag: "v1.0.0"
  imagePullPolicy: IfNotPresent
  storageClass: "standard"
  namespace: "tank-system"

# Tank Management Service
tankManagement:
  enabled: true
  replicaCount: 3
  image:
    repository: tank-management
    tag: ""
    pullPolicy: ""
  service:
    type: ClusterIP
    port: 8080
  resources:
    requests:
      memory: "512Mi"
      cpu: "250m"
    limits:
      memory: "1Gi"
      cpu: "500m"
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  probes:
    liveness:
      enabled: true
      initialDelaySeconds: 30
      periodSeconds: 10
    readiness:
      enabled: true
      initialDelaySeconds: 5
      periodSeconds: 5

# API Gateway
apiGateway:
  enabled: true
  replicaCount: 3
  image:
    repository: api-gateway
    tag: ""
    pullPolicy: ""
  service:
    type: LoadBalancer
    port: 80
    targetPort: 8000
  ingress:
    enabled: true
    className: "nginx"
    annotations:
      nginx.ingress.kubernetes.io/rewrite-target: /
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      cert-manager.io/cluster-issuer: "letsencrypt-prod"
    hosts:
      - host: tank-api.company.com
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: tank-api-tls
        hosts:
          - tank-api.company.com
  resources:
    requests:
      memory: "256Mi"
      cpu: "200m"
    limits:
      memory: "512Mi"
      cpu: "400m"
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 15
    targetCPUUtilizationPercentage: 70

# Control Service
controlService:
  enabled: true
  replicaCount: 2
  image:
    repository: control-service
    tag: ""
    pullPolicy: ""
  service:
    type: ClusterIP
    port: 8082
  resources:
    requests:
      memory: "512Mi"
      cpu: "300m"
    limits:
      memory: "1Gi"
      cpu: "600m"
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 8
    targetCPUUtilizationPercentage: 70

# Data Ingestion Service
dataIngestion:
  enabled: true
  replicaCount: 2
  image:
    repository: data-ingestion-service
    tag: ""
    pullPolicy: ""
  service:
    type: ClusterIP
    port: 8081
  resources:
    requests:
      memory: "512Mi"
      cpu: "250m"
    limits:
      memory: "1Gi"
      cpu: "500m"
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70

# Event Bus Service
eventBus:
  enabled: true
  replicaCount: 2
  image:
    repository: event-bus-service
    tag: ""
    pullPolicy: ""
  service:
    type: ClusterIP
    port: 8083
  resources:
    requests:
      memory: "256Mi"
      cpu: "200m"
    limits:
      memory: "512Mi"
      cpu: "400m"

# ML Service
mlService:
  enabled: true
  replicaCount: 1
  image:
    repository: ml-service
    tag: ""
    pullPolicy: ""
  service:
    type: ClusterIP
    port: 8084
  resources:
    requests:
      memory: "1Gi"
      cpu: "500m"
      nvidia.com/gpu: 0
    limits:
      memory: "2Gi"
      cpu: "1000m"
      nvidia.com/gpu: 1
  persistence:
    enabled: true
    storageClass: ""
    size: 50Gi
    mountPath: /app/models

# Monitoring Service
monitoringService:
  enabled: true
  replicaCount: 1
  image:
    repository: monitoring-service
    tag: ""
    pullPolicy: ""
  service:
    type: ClusterIP
    port: 8085
  resources:
    requests:
      memory: "256Mi"
      cpu: "200m"
    limits:
      memory: "512Mi"
      cpu: "400m"

# External Dependencies Configuration

# Redis
redis:
  enabled: true
  auth:
    enabled: false
  master:
    persistence:
      enabled: true
      size: 10Gi
      storageClass: ""
    resources:
      requests:
        memory: 256Mi
        cpu: 100m
      limits:
        memory: 512Mi
        cpu: 200m

# PostgreSQL
postgresql:
  enabled: true
  auth:
    postgresPassword: "postgres"
    username: "postgres"
    password: "postgres"
    database: "tank_config"
  primary:
    persistence:
      enabled: true
      size: 50Gi
      storageClass: ""
    resources:
      requests:
        memory: 512Mi
        cpu: 250m
      limits:
        memory: 1Gi
        cpu: 500m

# Kafka
kafka:
  enabled: true
  persistence:
    enabled: true
    size: 20Gi
    storageClass: ""
  resources:
    requests:
      memory: 1Gi
      cpu: 500m
    limits:
      memory: 2Gi
      cpu: 1000m
  zookeeper:
    enabled: true
    persistence:
      enabled: true
      size: 10Gi

# InfluxDB
influxdb:
  enabled: true
  image:
    repository: influxdb
    tag: "2.7-alpine"
  persistence:
    enabled: true
    size: 100Gi
    storageClass: ""
  env:
    - name: DOCKER_INFLUXDB_INIT_MODE
      value: "setup"
    - name: DOCKER_INFLUXDB_INIT_USERNAME
      value: "admin"
    - name: DOCKER_INFLUXDB_INIT_PASSWORD
      value: "admin123"
    - name: DOCKER_INFLUXDB_INIT_ORG
      value: "tank-org"
    - name: DOCKER_INFLUXDB_INIT_BUCKET
      value: "tank-data"
    - name: DOCKER_INFLUXDB_INIT_ADMIN_TOKEN
      value: "tank-admin-token"
  resources:
    requests:
      memory: 1Gi
      cpu: 500m
    limits:
      memory: 2Gi
      cpu: 1000m

# MinIO Object Storage
minio:
  enabled: true
  image:
    repository: minio/minio
    tag: "RELEASE.2023-09-04T19-57-37Z"
  persistence:
    enabled: true
    size: 200Gi
    storageClass: ""
  accessKey: "minioadmin"
  secretKey: "minioadmin"
  defaultBucket:
    enabled: true
    name: "tank-data"
  resources:
    requests:
      memory: 512Mi
      cpu: 250m
    limits:
      memory: 1Gi
      cpu: 500m

# Monitoring Stack
monitoring:
  prometheus:
    enabled: true
    server:
      persistentVolume:
        enabled: true
        size: 50Gi
        storageClass: ""
      resources:
        requests:
          memory: 1Gi
          cpu: 500m
        limits:
          memory: 2Gi
          cpu: 1000m
      retention: "15d"
  
  grafana:
    enabled: true
    persistence:
      enabled: true
      size: 10Gi
      storageClass: ""
    adminPassword: "admin123"
    resources:
      requests:
        memory: 256Mi
        cpu: 200m
      limits:
        memory: 512Mi
        cpu: 400m
    datasources:
      datasources.yaml:
        apiVersion: 1
        datasources:
        - name: Prometheus
          type: prometheus
          url: http://prometheus-server
          access: proxy
          isDefault: true
        - name: InfluxDB
          type: influxdb
          url: http://influxdb:8086
          access: proxy
          database: tank-data

# Security Configuration
security:
  serviceAccount:
    create: true
    name: "tank-service-account"
  
  rbac:
    create: true
  
  podSecurityContext:
    runAsNonRoot: true
    runAsUser: 1000
    fsGroup: 1000
  
  securityContext:
    allowPrivilegeEscalation: false
    readOnlyRootFilesystem: false
    capabilities:
      drop:
      - ALL

# Configuration
config:
  # SCADA Configuration
  scada:
    opcua:
      enabled: true
      endpoint: "opc.tcp://scada-server.company.com:4840"
      security_policy: "Basic256Sha256"
      security_mode: "SignAndEncrypt"
    
    historian:
      enabled: true
      type: "pi"
      connection_string: "Data Source=historian.company.com;Initial Catalog=PIArchive"
  
  # Control Configuration
  control:
    scan_rate: 1.0
    enable_auto_control: true
    enable_data_logging: true
  
  # Simulation Configuration
  simulation:
    enabled: true
    real_time_factor: 1.0
    advanced_physics: true
  
  # API Configuration
  api:
    cors:
      allow_origins: ["*"]
    authentication:
      enabled: true
      type: "bearer"
  
  # Features
  features:
    machine_learning: true
    predictive_maintenance: true
    energy_optimization: true
    digital_twin: true
    real_time_optimization: false

# Secrets (these should be overridden in production)
secrets:
  postgresql:
    username: "postgres"
    password: "postgres"
  
  influxdb:
    username: "admin"
    password: "admin123"
    admin_token: "tank-admin-token"
  
  jwt:
    secret_key: "tank-system-jwt-secret-key-2024"
  
  opcua:
    username: "operator"
    password: "password123"
  
  smtp:
    username: "<EMAIL>"
    password: "email_password"

# Environment-specific overrides
environment: "production"  # development, staging, production

# Node selectors and tolerations
nodeSelector: {}
tolerations: []
affinity: {}

# Pod disruption budgets
podDisruptionBudget:
  enabled: true
  minAvailable: 1

# Network policies
networkPolicy:
  enabled: false