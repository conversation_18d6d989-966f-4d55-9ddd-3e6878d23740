"""
Streaming Data Pipeline for Real-time Analytics
Advanced data pipeline with streaming analytics, feature store, and real-time processing
"""

import asyncio
import logging
import json
import pickle
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import threading
import time
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from pathlib import Path
import hashlib
import warnings
warnings.filterwarnings('ignore')

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    print("Warning: Redis not available. In-memory streaming will be used.")

try:
    from kafka import KafkaProducer, KafkaConsumer
    KAFKA_AVAILABLE = True
except ImportError:
    KAFKA_AVAILABLE = False
    print("Warning: Kafka not available. Alternative streaming will be used.")

try:
    import influxdb_client
    from influxdb_client import InfluxDBClient, Point
    from influxdb_client.client.write_api import SYNCHRONOUS
    INFLUXDB_AVAILABLE = True
except ImportError:
    INFLUXDB_AVAILABLE = False
    print("Warning: InfluxDB not available. Alternative time-series storage will be used.")

try:
    import minio
    from minio import Minio
    MINIO_AVAILABLE = True
except ImportError:
    MINIO_AVAILABLE = False
    print("Warning: MinIO not available. Local storage will be used.")

logger = logging.getLogger(__name__)


class StreamingMode(Enum):
    BATCH = "batch"
    REAL_TIME = "real_time"
    MICRO_BATCH = "micro_batch"


class DataQuality(Enum):
    VALID = "valid"
    INVALID = "invalid"
    ANOMALOUS = "anomalous"
    MISSING = "missing"


@dataclass
class StreamingMessage:
    """Message structure for streaming data"""
    message_id: str
    timestamp: datetime
    source: str
    data_type: str
    payload: Dict[str, Any]
    quality: DataQuality = DataQuality.VALID
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'message_id': self.message_id,
            'timestamp': self.timestamp.isoformat(),
            'source': self.source,
            'data_type': self.data_type,
            'payload': self.payload,
            'quality': self.quality.value,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StreamingMessage':
        return cls(
            message_id=data['message_id'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            source=data['source'],
            data_type=data['data_type'],
            payload=data['payload'],
            quality=DataQuality(data.get('quality', 'valid')),
            metadata=data.get('metadata', {})
        )


@dataclass
class FeatureDefinition:
    """Feature definition for feature store"""
    name: str
    data_type: str
    description: str
    source_system: str
    transformation_logic: Optional[str] = None
    validation_rules: List[str] = field(default_factory=list)
    refresh_frequency: timedelta = timedelta(hours=1)
    tags: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    owner: str = "system"


@dataclass
class FeatureValue:
    """Feature value in the feature store"""
    feature_name: str
    value: Any
    timestamp: datetime
    entity_id: str
    quality_score: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)


class StreamProcessor(ABC):
    """Abstract base class for stream processors"""
    
    @abstractmethod
    async def process_message(self, message: StreamingMessage) -> List[StreamingMessage]:
        """Process a streaming message"""
        pass
    
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the processor"""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup resources"""
        pass


class DataQualityProcessor(StreamProcessor):
    """Data quality validation processor"""
    
    def __init__(self, validation_rules: Dict[str, List[Callable]]):
        self.validation_rules = validation_rules
        self.quality_metrics = {
            'total_messages': 0,
            'valid_messages': 0,
            'invalid_messages': 0,
            'anomalous_messages': 0,
            'missing_data': 0
        }
    
    async def initialize(self) -> bool:
        logger.info("Data quality processor initialized")
        return True
    
    async def process_message(self, message: StreamingMessage) -> List[StreamingMessage]:
        """Validate message data quality"""
        try:
            self.quality_metrics['total_messages'] += 1
            
            # Apply validation rules
            rules = self.validation_rules.get(message.data_type, [])
            quality_issues = []
            
            for rule in rules:
                try:
                    is_valid = rule(message.payload)
                    if not is_valid:
                        quality_issues.append(f"Rule failed: {rule.__name__}")
                except Exception as e:
                    quality_issues.append(f"Rule error: {str(e)}")
            
            # Determine quality
            if quality_issues:
                message.quality = DataQuality.INVALID
                message.metadata['quality_issues'] = quality_issues
                self.quality_metrics['invalid_messages'] += 1
            else:
                message.quality = DataQuality.VALID
                self.quality_metrics['valid_messages'] += 1
            
            # Check for anomalies
            if await self._check_anomalies(message):
                message.quality = DataQuality.ANOMALOUS
                self.quality_metrics['anomalous_messages'] += 1
            
            return [message]
        
        except Exception as e:
            logger.error(f"Error in data quality processing: {e}")
            message.quality = DataQuality.INVALID
            message.metadata['processing_error'] = str(e)
            return [message]
    
    async def _check_anomalies(self, message: StreamingMessage) -> bool:
        """Check for data anomalies"""
        try:
            # Simple anomaly detection for temperature data
            if message.data_type == 'temperature':
                temp_value = message.payload.get('temperature', 0)
                if temp_value < -50 or temp_value > 200:
                    message.metadata['anomaly_reason'] = f"Temperature out of range: {temp_value}"
                    return True
            
            # Check for missing critical fields
            critical_fields = ['timestamp', 'value']
            for field in critical_fields:
                if field not in message.payload:
                    message.metadata['anomaly_reason'] = f"Missing critical field: {field}"
                    return True
            
            return False
        
        except Exception as e:
            logger.error(f"Error checking anomalies: {e}")
            return True
    
    async def cleanup(self) -> None:
        logger.info(f"Data quality metrics: {self.quality_metrics}")


class FeatureEngineeringProcessor(StreamProcessor):
    """Feature engineering processor"""
    
    def __init__(self, feature_definitions: Dict[str, Callable]):
        self.feature_definitions = feature_definitions
        self.feature_cache = {}
        self.processing_stats = {
            'features_computed': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }
    
    async def initialize(self) -> bool:
        logger.info("Feature engineering processor initialized")
        return True
    
    async def process_message(self, message: StreamingMessage) -> List[StreamingMessage]:
        """Extract features from streaming message"""
        try:
            feature_messages = []
            
            # Apply feature engineering functions
            for feature_name, feature_func in self.feature_definitions.items():
                try:
                    feature_value = feature_func(message.payload)
                    
                    if feature_value is not None:
                        feature_message = StreamingMessage(
                            message_id=f"{message.message_id}_{feature_name}",
                            timestamp=message.timestamp,
                            source=f"{message.source}_feature_eng",
                            data_type=f"feature_{feature_name}",
                            payload={
                                'feature_name': feature_name,
                                'feature_value': feature_value,
                                'entity_id': message.payload.get('entity_id', 'unknown')
                            },
                            quality=message.quality,
                            metadata={
                                'original_message_id': message.message_id,
                                'feature_type': 'engineered',
                                'processing_time': datetime.now().isoformat()
                            }
                        )
                        
                        feature_messages.append(feature_message)
                        self.processing_stats['features_computed'] += 1
                
                except Exception as e:
                    logger.error(f"Error computing feature {feature_name}: {e}")
            
            # Return original message plus feature messages
            return [message] + feature_messages
        
        except Exception as e:
            logger.error(f"Error in feature engineering: {e}")
            return [message]
    
    async def cleanup(self) -> None:
        logger.info(f"Feature engineering stats: {self.processing_stats}")


class AggregationProcessor(StreamProcessor):
    """Data aggregation processor"""
    
    def __init__(self, aggregation_windows: Dict[str, timedelta]):
        self.aggregation_windows = aggregation_windows
        self.aggregation_buffer = {}
        self.aggregation_stats = {
            'total_aggregations': 0,
            'buffer_size': 0
        }
    
    async def initialize(self) -> bool:
        logger.info("Aggregation processor initialized")
        return True
    
    async def process_message(self, message: StreamingMessage) -> List[StreamingMessage]:
        """Aggregate data over time windows"""
        try:
            aggregated_messages = []
            
            # Add to buffer
            buffer_key = f"{message.source}_{message.data_type}"
            if buffer_key not in self.aggregation_buffer:
                self.aggregation_buffer[buffer_key] = []
            
            self.aggregation_buffer[buffer_key].append(message)
            
            # Clean old messages
            current_time = datetime.now()
            for window_name, window_size in self.aggregation_windows.items():
                cutoff_time = current_time - window_size
                self.aggregation_buffer[buffer_key] = [
                    msg for msg in self.aggregation_buffer[buffer_key]
                    if msg.timestamp >= cutoff_time
                ]
            
            # Create aggregations
            for window_name, window_size in self.aggregation_windows.items():
                window_messages = [
                    msg for msg in self.aggregation_buffer[buffer_key]
                    if msg.timestamp >= current_time - window_size
                ]
                
                if len(window_messages) > 1:
                    aggregated_data = await self._aggregate_messages(window_messages, window_name)
                    
                    if aggregated_data:
                        agg_message = StreamingMessage(
                            message_id=f"{message.message_id}_{window_name}_agg",
                            timestamp=current_time,
                            source=f"{message.source}_aggregated",
                            data_type=f"{message.data_type}_{window_name}",
                            payload=aggregated_data,
                            quality=DataQuality.VALID,
                            metadata={
                                'aggregation_window': window_name,
                                'window_size_seconds': window_size.total_seconds(),
                                'message_count': len(window_messages),
                                'aggregation_type': 'streaming'
                            }
                        )
                        
                        aggregated_messages.append(agg_message)
                        self.aggregation_stats['total_aggregations'] += 1
            
            self.aggregation_stats['buffer_size'] = len(self.aggregation_buffer.get(buffer_key, []))
            
            return [message] + aggregated_messages
        
        except Exception as e:
            logger.error(f"Error in aggregation processing: {e}")
            return [message]
    
    async def _aggregate_messages(self, messages: List[StreamingMessage], window_name: str) -> Dict[str, Any]:
        """Aggregate messages within a time window"""
        try:
            if not messages:
                return {}
            
            # Extract numeric values
            numeric_values = []
            for msg in messages:
                payload = msg.payload
                if 'value' in payload:
                    try:
                        numeric_values.append(float(payload['value']))
                    except (ValueError, TypeError):
                        pass
                elif 'temperature' in payload:
                    try:
                        numeric_values.append(float(payload['temperature']))
                    except (ValueError, TypeError):
                        pass
            
            if not numeric_values:
                return {}
            
            # Calculate statistics
            return {
                'count': len(numeric_values),
                'mean': np.mean(numeric_values),
                'std': np.std(numeric_values),
                'min': np.min(numeric_values),
                'max': np.max(numeric_values),
                'median': np.median(numeric_values),
                'percentile_25': np.percentile(numeric_values, 25),
                'percentile_75': np.percentile(numeric_values, 75),
                'window_start': min(msg.timestamp for msg in messages).isoformat(),
                'window_end': max(msg.timestamp for msg in messages).isoformat()
            }
        
        except Exception as e:
            logger.error(f"Error aggregating messages: {e}")
            return {}
    
    async def cleanup(self) -> None:
        logger.info(f"Aggregation stats: {self.aggregation_stats}")


class FeatureStore:
    """Feature store for ML model features"""
    
    def __init__(self, backend: str = "memory"):
        self.backend = backend
        self.feature_definitions = {}
        self.feature_values = {}
        self.feature_stats = {
            'features_stored': 0,
            'features_retrieved': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        
        # Initialize backend
        if backend == "redis" and REDIS_AVAILABLE:
            self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
        else:
            self.redis_client = None
    
    async def register_feature(self, feature_def: FeatureDefinition) -> bool:
        """Register a new feature definition"""
        try:
            self.feature_definitions[feature_def.name] = feature_def
            logger.info(f"Feature registered: {feature_def.name}")
            return True
        except Exception as e:
            logger.error(f"Error registering feature: {e}")
            return False
    
    async def store_feature(self, feature_value: FeatureValue) -> bool:
        """Store a feature value"""
        try:
            # Create storage key
            storage_key = f"{feature_value.entity_id}_{feature_value.feature_name}"
            
            # Store in backend
            if self.redis_client:
                # Store in Redis
                value_data = {
                    'value': feature_value.value,
                    'timestamp': feature_value.timestamp.isoformat(),
                    'quality_score': feature_value.quality_score,
                    'metadata': feature_value.metadata
                }
                self.redis_client.set(storage_key, json.dumps(value_data))
                
                # Set TTL based on feature definition
                if feature_value.feature_name in self.feature_definitions:
                    ttl = self.feature_definitions[feature_value.feature_name].refresh_frequency
                    self.redis_client.expire(storage_key, int(ttl.total_seconds()))
            else:
                # Store in memory
                if storage_key not in self.feature_values:
                    self.feature_values[storage_key] = []
                
                self.feature_values[storage_key].append(feature_value)
                
                # Keep only recent values
                cutoff_time = datetime.now() - timedelta(hours=24)
                self.feature_values[storage_key] = [
                    fv for fv in self.feature_values[storage_key]
                    if fv.timestamp >= cutoff_time
                ]
            
            self.feature_stats['features_stored'] += 1
            return True
        
        except Exception as e:
            logger.error(f"Error storing feature: {e}")
            return False
    
    async def get_feature(self, feature_name: str, entity_id: str, 
                         max_age: Optional[timedelta] = None) -> Optional[FeatureValue]:
        """Get the latest feature value"""
        try:
            storage_key = f"{entity_id}_{feature_name}"
            
            if self.redis_client:
                # Get from Redis
                value_data = self.redis_client.get(storage_key)
                if value_data:
                    data = json.loads(value_data)
                    feature_value = FeatureValue(
                        feature_name=feature_name,
                        value=data['value'],
                        timestamp=datetime.fromisoformat(data['timestamp']),
                        entity_id=entity_id,
                        quality_score=data.get('quality_score', 1.0),
                        metadata=data.get('metadata', {})
                    )
                    
                    # Check age
                    if max_age and datetime.now() - feature_value.timestamp > max_age:
                        self.feature_stats['cache_misses'] += 1
                        return None
                    
                    self.feature_stats['cache_hits'] += 1
                    self.feature_stats['features_retrieved'] += 1
                    return feature_value
            else:
                # Get from memory
                if storage_key in self.feature_values:
                    recent_values = self.feature_values[storage_key]
                    if recent_values:
                        latest_value = max(recent_values, key=lambda fv: fv.timestamp)
                        
                        # Check age
                        if max_age and datetime.now() - latest_value.timestamp > max_age:
                            self.feature_stats['cache_misses'] += 1
                            return None
                        
                        self.feature_stats['cache_hits'] += 1
                        self.feature_stats['features_retrieved'] += 1
                        return latest_value
            
            self.feature_stats['cache_misses'] += 1
            return None
        
        except Exception as e:
            logger.error(f"Error getting feature: {e}")
            return None
    
    async def get_features_for_entity(self, entity_id: str, 
                                     feature_names: List[str]) -> Dict[str, Any]:
        """Get multiple features for an entity"""
        try:
            features = {}
            
            for feature_name in feature_names:
                feature_value = await self.get_feature(feature_name, entity_id)
                if feature_value:
                    features[feature_name] = feature_value.value
                else:
                    features[feature_name] = None
            
            return features
        
        except Exception as e:
            logger.error(f"Error getting features for entity: {e}")
            return {}
    
    async def get_feature_statistics(self) -> Dict[str, Any]:
        """Get feature store statistics"""
        return {
            'registered_features': len(self.feature_definitions),
            'stored_feature_values': len(self.feature_values) if not self.redis_client else 'unknown',
            'stats': self.feature_stats
        }


class StreamingPipeline:
    """Main streaming data pipeline"""
    
    def __init__(self, pipeline_config: Dict[str, Any]):
        self.config = pipeline_config
        self.processors = []
        self.feature_store = FeatureStore(backend=pipeline_config.get('feature_store_backend', 'memory'))
        self.message_queue = asyncio.Queue()
        self.processing_tasks = []
        self.is_running = False
        self.pipeline_stats = {
            'messages_processed': 0,
            'messages_failed': 0,
            'processing_time_total': 0.0,
            'average_processing_time': 0.0
        }
    
    async def add_processor(self, processor: StreamProcessor) -> bool:
        """Add a processor to the pipeline"""
        try:
            await processor.initialize()
            self.processors.append(processor)
            logger.info(f"Added processor: {processor.__class__.__name__}")
            return True
        except Exception as e:
            logger.error(f"Error adding processor: {e}")
            return False
    
    async def ingest_message(self, message: StreamingMessage) -> bool:
        """Ingest a message into the pipeline"""
        try:
            await self.message_queue.put(message)
            return True
        except Exception as e:
            logger.error(f"Error ingesting message: {e}")
            return False
    
    async def start_pipeline(self) -> bool:
        """Start the streaming pipeline"""
        try:
            if self.is_running:
                logger.warning("Pipeline already running")
                return False
            
            self.is_running = True
            
            # Start processing tasks
            num_workers = self.config.get('num_workers', 4)
            for i in range(num_workers):
                task = asyncio.create_task(self._process_messages(f"worker_{i}"))
                self.processing_tasks.append(task)
            
            logger.info(f"Streaming pipeline started with {num_workers} workers")
            return True
        
        except Exception as e:
            logger.error(f"Error starting pipeline: {e}")
            return False
    
    async def stop_pipeline(self) -> bool:
        """Stop the streaming pipeline"""
        try:
            self.is_running = False
            
            # Cancel processing tasks
            for task in self.processing_tasks:
                task.cancel()
            
            # Wait for tasks to complete
            await asyncio.gather(*self.processing_tasks, return_exceptions=True)
            
            # Cleanup processors
            for processor in self.processors:
                await processor.cleanup()
            
            logger.info("Streaming pipeline stopped")
            return True
        
        except Exception as e:
            logger.error(f"Error stopping pipeline: {e}")
            return False
    
    async def _process_messages(self, worker_id: str) -> None:
        """Process messages from the queue"""
        logger.info(f"Worker {worker_id} started")
        
        while self.is_running:
            try:
                # Get message from queue
                message = await asyncio.wait_for(self.message_queue.get(), timeout=1.0)
                
                start_time = time.time()
                
                # Process through all processors
                current_messages = [message]
                
                for processor in self.processors:
                    new_messages = []
                    for msg in current_messages:
                        processed = await processor.process_message(msg)
                        new_messages.extend(processed)
                    current_messages = new_messages
                
                # Store features in feature store
                for processed_message in current_messages:
                    if processed_message.data_type.startswith('feature_'):
                        await self._store_feature_from_message(processed_message)
                
                # Update stats
                processing_time = time.time() - start_time
                self.pipeline_stats['messages_processed'] += 1
                self.pipeline_stats['processing_time_total'] += processing_time
                self.pipeline_stats['average_processing_time'] = (
                    self.pipeline_stats['processing_time_total'] / 
                    self.pipeline_stats['messages_processed']
                )
                
                # Mark task as done
                self.message_queue.task_done()
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Error in worker {worker_id}: {e}")
                self.pipeline_stats['messages_failed'] += 1
                await asyncio.sleep(1)
    
    async def _store_feature_from_message(self, message: StreamingMessage) -> None:
        """Store feature from message in feature store"""
        try:
            if 'feature_name' in message.payload and 'feature_value' in message.payload:
                feature_value = FeatureValue(
                    feature_name=message.payload['feature_name'],
                    value=message.payload['feature_value'],
                    timestamp=message.timestamp,
                    entity_id=message.payload.get('entity_id', 'unknown'),
                    quality_score=1.0 if message.quality == DataQuality.VALID else 0.5,
                    metadata=message.metadata
                )
                
                await self.feature_store.store_feature(feature_value)
        
        except Exception as e:
            logger.error(f"Error storing feature from message: {e}")
    
    async def get_pipeline_statistics(self) -> Dict[str, Any]:
        """Get pipeline statistics"""
        feature_stats = await self.feature_store.get_feature_statistics()
        
        return {
            'pipeline_stats': self.pipeline_stats,
            'feature_store_stats': feature_stats,
            'processors_count': len(self.processors),
            'queue_size': self.message_queue.qsize(),
            'is_running': self.is_running
        }


# Factory functions and utility functions
def create_validation_rules() -> Dict[str, List[Callable]]:
    """Create default validation rules"""
    
    def temperature_range_check(payload: Dict[str, Any]) -> bool:
        """Check if temperature is in valid range"""
        if 'temperature' in payload:
            temp = payload['temperature']
            return -100 <= temp <= 300
        return True
    
    def timestamp_check(payload: Dict[str, Any]) -> bool:
        """Check if timestamp is valid"""
        if 'timestamp' in payload:
            try:
                timestamp = datetime.fromisoformat(payload['timestamp'])
                # Check if timestamp is not too far in the future
                return timestamp <= datetime.now() + timedelta(hours=1)
            except:
                return False
        return True
    
    def required_fields_check(payload: Dict[str, Any]) -> bool:
        """Check if required fields are present"""
        required = ['timestamp', 'value']
        return all(field in payload for field in required)
    
    return {
        'temperature': [temperature_range_check, timestamp_check, required_fields_check],
        'sensor_data': [timestamp_check, required_fields_check],
        'default': [timestamp_check]
    }


def create_feature_engineering_functions() -> Dict[str, Callable]:
    """Create feature engineering functions"""
    
    def temperature_moving_average(payload: Dict[str, Any]) -> Optional[float]:
        """Calculate temperature moving average"""
        if 'temperature' in payload:
            # This is a simplified version - in practice, you'd maintain a window
            return payload['temperature']
        return None
    
    def temperature_rate_of_change(payload: Dict[str, Any]) -> Optional[float]:
        """Calculate temperature rate of change"""
        if 'temperature' in payload and 'previous_temperature' in payload:
            current = payload['temperature']
            previous = payload['previous_temperature']
            return current - previous
        return None
    
    def energy_efficiency_ratio(payload: Dict[str, Any]) -> Optional[float]:
        """Calculate energy efficiency ratio"""
        if 'energy_consumption' in payload and 'heat_output' in payload:
            energy = payload['energy_consumption']
            heat = payload['heat_output']
            if energy > 0:
                return heat / energy
        return None
    
    def operating_status_flag(payload: Dict[str, Any]) -> Optional[int]:
        """Create operating status flag"""
        if 'power_consumption' in payload:
            power = payload['power_consumption']
            return 1 if power > 50 else 0  # Threshold for "operating"
        return None
    
    return {
        'temperature_ma': temperature_moving_average,
        'temperature_roc': temperature_rate_of_change,
        'energy_efficiency': energy_efficiency_ratio,
        'operating_status': operating_status_flag
    }


async def create_streaming_pipeline(config: Optional[Dict[str, Any]] = None) -> StreamingPipeline:
    """Create and configure a streaming pipeline"""
    
    if config is None:
        config = {
            'num_workers': 4,
            'feature_store_backend': 'memory',
            'enable_data_quality': True,
            'enable_feature_engineering': True,
            'enable_aggregation': True
        }
    
    # Create pipeline
    pipeline = StreamingPipeline(config)
    
    # Add processors
    if config.get('enable_data_quality', True):
        validation_rules = create_validation_rules()
        quality_processor = DataQualityProcessor(validation_rules)
        await pipeline.add_processor(quality_processor)
    
    if config.get('enable_feature_engineering', True):
        feature_functions = create_feature_engineering_functions()
        feature_processor = FeatureEngineeringProcessor(feature_functions)
        await pipeline.add_processor(feature_processor)
    
    if config.get('enable_aggregation', True):
        aggregation_windows = {
            '1min': timedelta(minutes=1),
            '5min': timedelta(minutes=5),
            '15min': timedelta(minutes=15),
            '1hour': timedelta(hours=1)
        }
        aggregation_processor = AggregationProcessor(aggregation_windows)
        await pipeline.add_processor(aggregation_processor)
    
    # Register common features
    common_features = [
        FeatureDefinition(
            name="temperature_ma",
            data_type="float",
            description="Temperature moving average",
            source_system="sensor",
            refresh_frequency=timedelta(minutes=5)
        ),
        FeatureDefinition(
            name="energy_efficiency",
            data_type="float",
            description="Energy efficiency ratio",
            source_system="energy_monitor",
            refresh_frequency=timedelta(minutes=15)
        ),
        FeatureDefinition(
            name="operating_status",
            data_type="int",
            description="Operating status flag",
            source_system="control_system",
            refresh_frequency=timedelta(minutes=1)
        )
    ]
    
    for feature_def in common_features:
        await pipeline.feature_store.register_feature(feature_def)
    
    return pipeline


# Example usage
if __name__ == "__main__":
    async def test_streaming_pipeline():
        # Create pipeline
        pipeline = await create_streaming_pipeline()
        
        # Start pipeline
        await pipeline.start_pipeline()
        
        # Simulate some messages
        test_messages = [
            StreamingMessage(
                message_id="msg_001",
                timestamp=datetime.now(),
                source="temperature_sensor_1",
                data_type="temperature",
                payload={
                    "temperature": 150.5,
                    "timestamp": datetime.now().isoformat(),
                    "entity_id": "tank_1",
                    "sensor_id": "temp_001"
                }
            ),
            StreamingMessage(
                message_id="msg_002",
                timestamp=datetime.now(),
                source="energy_monitor",
                data_type="energy",
                payload={
                    "energy_consumption": 1200.0,
                    "heat_output": 1000.0,
                    "timestamp": datetime.now().isoformat(),
                    "entity_id": "tank_1"
                }
            )
        ]
        
        # Ingest messages
        for message in test_messages:
            await pipeline.ingest_message(message)
        
        # Wait for processing
        await asyncio.sleep(5)
        
        # Get statistics
        stats = await pipeline.get_pipeline_statistics()
        print(f"Pipeline statistics: {json.dumps(stats, indent=2)}")
        
        # Test feature retrieval
        features = await pipeline.feature_store.get_features_for_entity(
            "tank_1", ["temperature_ma", "energy_efficiency"]
        )
        print(f"Features for tank_1: {features}")
        
        # Stop pipeline
        await pipeline.stop_pipeline()
    
    # Run test
    asyncio.run(test_streaming_pipeline())