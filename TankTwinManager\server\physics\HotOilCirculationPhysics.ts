/**
 * Hot-Oil Circulation Physics Engine
 * Advanced physics simulation for hot-oil circulation system
 * Includes pump dynamics, pressure drops, flow rates, and thermal energy transfer
 */

export interface FluidProperties {
  density: number; // kg/m³
  viscosity: number; // Pa·s
  specificHeat: number; // J/kg·K
  thermalConductivity: number; // W/m·K
  temperature: number; // °C
}

export interface PumpCharacteristics {
  id: string;
  type: 'centrifugal' | 'positive_displacement';
  nominalFlowRate: number; // L/min
  nominalHead: number; // m
  nominalSpeed: number; // RPM
  efficiency: number; // %
  powerRating: number; // kW
  impellerDiameter: number; // mm
  curveCoefficients: {
    a: number; // Head coefficient
    b: number; // Flow coefficient
    c: number; // Efficiency coefficient
  };
}

export interface PipeSegmentPhysics {
  id: string;
  length: number; // m
  diameter: number; // m
  roughness: number; // m
  elevation: number; // m
  insulation: {
    thickness: number; // m
    thermalConductivity: number; // W/m·K
  };
}

export interface HeatExchangerPhysics {
  id: string;
  type: '3-turn-coil' | 'shell-tube' | 'plate';
  area: number; // m²
  overallHeatTransferCoefficient: number; // W/m²·K
  foulingFactor: number; // m²·K/W
  effectiveness: number; // %
  hotSideFlowRate: number; // L/min
  coldSideFlowRate: number; // L/min
}

export interface SystemState {
  timestamp: Date;
  flowRates: Map<string, number>; // L/min
  pressures: Map<string, number>; // bar
  temperatures: Map<string, number>; // °C
  pumpSpeeds: Map<string, number>; // RPM
  valvePositions: Map<string, number>; // % open
  heatTransferRates: Map<string, number>; // kW
}

export class HotOilCirculationPhysics {
  private fluidProperties: FluidProperties;
  private pumps: Map<string, PumpCharacteristics> = new Map();
  private pipes: Map<string, PipeSegmentPhysics> = new Map();
  private heatExchangers: Map<string, HeatExchangerPhysics> = new Map();
  private currentState: SystemState;
  private timeStep: number = 1.0; // seconds

  constructor() {
    this.initializeFluidProperties();
    this.initializeSystemComponents();
    this.currentState = this.createInitialState();
  }

  private initializeFluidProperties() {
    // Hot oil properties at 280°C
    this.fluidProperties = {
      density: 850, // kg/m³
      viscosity: 0.001, // Pa·s (very low viscosity at high temp)
      specificHeat: 2200, // J/kg·K
      thermalConductivity: 0.12, // W/m·K
      temperature: 280 // °C
    };
  }

  private initializeSystemComponents() {
    // Initialize main circulation pump
    this.pumps.set('HO-PUMP-01', {
      id: 'HO-PUMP-01',
      type: 'centrifugal',
      nominalFlowRate: 1200, // L/min
      nominalHead: 50, // m
      nominalSpeed: 1450, // RPM
      efficiency: 85, // %
      powerRating: 15, // kW
      impellerDiameter: 250, // mm
      curveCoefficients: {
        a: 55, // Head at zero flow
        b: -0.0003, // Head vs flow slope
        c: 0.85 // Peak efficiency
      }
    });

    // Initialize main supply pipe
    this.pipes.set('HO-MAIN-SUPPLY', {
      id: 'HO-MAIN-SUPPLY',
      length: 25, // m
      diameter: 0.15, // m (150mm)
      roughness: 0.000045, // m (steel pipe)
      elevation: 1, // m
      insulation: {
        thickness: 0.1, // m
        thermalConductivity: 0.04 // W/m·K
      }
    });

    // Initialize tank heating coils
    for (let i = 1; i <= 10; i++) {
      this.heatExchangers.set(`HC-ASP-${i.toString().padStart(2, '0')}`, {
        id: `HC-ASP-${i.toString().padStart(2, '0')}`,
        type: '3-turn-coil',
        area: 2.67, // m²
        overallHeatTransferCoefficient: 500, // W/m²·K
        foulingFactor: 0.0002, // m²·K/W
        effectiveness: 85, // %
        hotSideFlowRate: 100, // L/min
        coldSideFlowRate: 0 // No flow on cold side (tank contents)
      });
    }
  }

  private createInitialState(): SystemState {
    return {
      timestamp: new Date(),
      flowRates: new Map([
        ['HO-MAIN-SUPPLY', 1000],
        ['HO-MAIN-RETURN', 1000]
      ]),
      pressures: new Map([
        ['HO-PUMP-DISCHARGE', 5.0],
        ['HO-PUMP-SUCTION', 1.0]
      ]),
      temperatures: new Map([
        ['HO-SUPPLY', 280],
        ['HO-RETURN', 260]
      ]),
      pumpSpeeds: new Map([
        ['HO-PUMP-01', 1450]
      ]),
      valvePositions: new Map([
        ['HV-HO-01', 100]
      ]),
      heatTransferRates: new Map()
    };
  }

  public simulateTimeStep(deltaTime: number = this.timeStep): SystemState {
    // Update fluid properties based on temperature
    this.updateFluidProperties();

    // Calculate pump performance
    this.calculatePumpPerformance();

    // Calculate pressure drops through pipes
    this.calculatePressureDrops();

    // Calculate heat transfer in heat exchangers
    this.calculateHeatTransfer();

    // Update system temperatures
    this.updateTemperatures();

    // Update flow distribution
    this.updateFlowDistribution();

    // Update timestamp
    this.currentState.timestamp = new Date();

    return { ...this.currentState };
  }

  private updateFluidProperties() {
    const avgTemp = this.getAverageSystemTemperature();
    
    // Temperature-dependent properties
    this.fluidProperties.temperature = avgTemp;
    this.fluidProperties.density = 900 - 0.7 * (avgTemp - 20); // kg/m³
    this.fluidProperties.viscosity = Math.exp(-0.02 * avgTemp + 3); // Pa·s
  }

  private calculatePumpPerformance() {
    this.pumps.forEach((pump, pumpId) => {
      const speed = this.currentState.pumpSpeeds.get(pumpId) || pump.nominalSpeed;
      const flowRate = this.currentState.flowRates.get('HO-MAIN-SUPPLY') || 0;
      
      // Affinity laws for centrifugal pumps
      const speedRatio = speed / pump.nominalSpeed;
      const flowRatio = flowRate / pump.nominalFlowRate;
      
      // Calculate head using pump curve
      const head = pump.curveCoefficients.a + 
        pump.curveCoefficients.b * Math.pow(flowRate, 2) * Math.pow(speedRatio, 2);
      
      // Calculate efficiency
      const efficiency = pump.curveCoefficients.c * 
        (1 - Math.pow(flowRatio - 1, 2) * 0.3);
      
      // Calculate power consumption
      const power = (this.fluidProperties.density * 9.81 * head * (flowRate / 60000)) / 
        (efficiency / 100) / 1000; // kW
      
      // Convert head to pressure
      const pressure = (this.fluidProperties.density * 9.81 * head) / 100000; // bar
      
      this.currentState.pressures.set(`${pumpId}-DISCHARGE`, pressure);
    });
  }

  private calculatePressureDrops() {
    this.pipes.forEach((pipe, pipeId) => {
      const flowRate = this.currentState.flowRates.get(pipeId) || 0;
      const velocity = (flowRate / 60000) / (Math.PI * Math.pow(pipe.diameter / 2, 2)); // m/s
      
      // Reynolds number
      const reynolds = (this.fluidProperties.density * velocity * pipe.diameter) / 
        this.fluidProperties.viscosity;
      
      // Friction factor (Colebrook-White equation)
      const relativeRoughness = pipe.roughness / pipe.diameter;
      let frictionFactor: number;
      
      if (reynolds < 2300) {
        // Laminar flow
        frictionFactor = 64 / reynolds;
      } else {
        // Turbulent flow (simplified)
        frictionFactor = 0.25 / Math.pow(
          Math.log10(relativeRoughness / 3.7 + 5.74 / Math.pow(reynolds, 0.9)), 2
        );
      }
      
      // Darcy-Weisbach equation
      const pressureDrop = frictionFactor * (pipe.length / pipe.diameter) * 
        (this.fluidProperties.density * Math.pow(velocity, 2)) / (2 * 100000); // bar
      
      // Add elevation pressure change
      const elevationPressure = (this.fluidProperties.density * 9.81 * pipe.elevation) / 100000; // bar
      
      const totalPressureDrop = pressureDrop + elevationPressure;
      this.currentState.pressures.set(`${pipeId}-DROP`, totalPressureDrop);
    });
  }

  private calculateHeatTransfer() {
    this.heatExchangers.forEach((hx, hxId) => {
      const hotInletTemp = this.currentState.temperatures.get('HO-SUPPLY') || 280;
      const coldInletTemp = this.getTankTemperature(hxId) || 150;
      const flowRate = hx.hotSideFlowRate / 60000; // m³/s
      
      // Heat capacity rate
      const heatCapacityRate = this.fluidProperties.density * flowRate * 
        this.fluidProperties.specificHeat; // W/K
      
      // Temperature difference
      const tempDiff = hotInletTemp - coldInletTemp;
      
      // Heat transfer rate using effectiveness-NTU method
      const maxHeatTransfer = heatCapacityRate * tempDiff; // W
      const actualHeatTransfer = (hx.effectiveness / 100) * maxHeatTransfer; // W
      
      // Account for fouling
      const foulingEffect = 1 / (1 + hx.foulingFactor * hx.overallHeatTransferCoefficient);
      const adjustedHeatTransfer = actualHeatTransfer * foulingEffect;
      
      this.currentState.heatTransferRates.set(hxId, adjustedHeatTransfer / 1000); // kW
      
      // Calculate outlet temperature
      const hotOutletTemp = hotInletTemp - (adjustedHeatTransfer / heatCapacityRate);
      this.currentState.temperatures.set(`${hxId}-OUTLET`, hotOutletTemp);
    });
  }

  private updateTemperatures() {
    // Calculate return temperature as weighted average of all heat exchanger outlets
    let totalFlow = 0;
    let weightedTempSum = 0;
    
    this.heatExchangers.forEach((hx, hxId) => {
      const outletTemp = this.currentState.temperatures.get(`${hxId}-OUTLET`) || 260;
      const flow = hx.hotSideFlowRate;
      
      totalFlow += flow;
      weightedTempSum += outletTemp * flow;
    });
    
    const returnTemp = totalFlow > 0 ? weightedTempSum / totalFlow : 260;
    this.currentState.temperatures.set('HO-RETURN', returnTemp);
    
    // Supply temperature is maintained by boiler (simplified)
    const supplyTemp = this.currentState.temperatures.get('HO-SUPPLY') || 280;
    this.currentState.temperatures.set('HO-SUPPLY', supplyTemp);
  }

  private updateFlowDistribution() {
    // Simplified flow distribution - equal flow to all heat exchangers
    const mainFlow = this.currentState.flowRates.get('HO-MAIN-SUPPLY') || 1000;
    const numberOfHeatExchangers = this.heatExchangers.size;
    const flowPerHeatExchanger = mainFlow / numberOfHeatExchangers;
    
    this.heatExchangers.forEach((hx, hxId) => {
      this.currentState.flowRates.set(hxId, flowPerHeatExchanger);
    });
    
    // Return flow equals supply flow (conservation of mass)
    this.currentState.flowRates.set('HO-MAIN-RETURN', mainFlow);
  }

  private getAverageSystemTemperature(): number {
    const supplyTemp = this.currentState.temperatures.get('HO-SUPPLY') || 280;
    const returnTemp = this.currentState.temperatures.get('HO-RETURN') || 260;
    return (supplyTemp + returnTemp) / 2;
  }

  private getTankTemperature(heatExchangerId: string): number {
    // Extract tank ID from heat exchanger ID
    const tankId = heatExchangerId.replace('HC-ASP-', '');
    // This would normally come from the tank simulation
    return 150; // Simplified - assume 150°C tank temperature
  }

  // Public API methods
  public getCurrentState(): SystemState {
    return { ...this.currentState };
  }

  public setPumpSpeed(pumpId: string, speed: number): boolean {
    if (this.pumps.has(pumpId)) {
      this.currentState.pumpSpeeds.set(pumpId, speed);
      return true;
    }
    return false;
  }

  public setValvePosition(valveId: string, position: number): boolean {
    this.currentState.valvePositions.set(valveId, Math.max(0, Math.min(100, position)));
    return true;
  }

  public getSystemEfficiency(): number {
    const totalHeatTransfer = Array.from(this.currentState.heatTransferRates.values())
      .reduce((sum, rate) => sum + rate, 0);
    
    const pumpPower = Array.from(this.pumps.values())
      .reduce((sum, pump) => sum + pump.powerRating, 0);
    
    return totalHeatTransfer / pumpPower; // kW heat transfer per kW pump power
  }

  public getEnergyBalance(): {
    heatInput: number; // kW
    heatOutput: number; // kW
    heatLoss: number; // kW
    efficiency: number; // %
  } {
    const heatOutput = Array.from(this.currentState.heatTransferRates.values())
      .reduce((sum, rate) => sum + rate, 0);
    
    // Simplified heat loss calculation
    const heatLoss = this.pipes.size * 5; // 5 kW per pipe segment
    const heatInput = heatOutput + heatLoss;
    const efficiency = (heatOutput / heatInput) * 100;
    
    return {
      heatInput,
      heatOutput,
      heatLoss,
      efficiency
    };
  }
}
