# Core Dependencies
numpy>=1.21.0
scipy>=1.8.0
pandas>=1.3.0
matplotlib>=3.5.0
scikit-learn>=1.0.0

# Async and Networking
asyncio-mqtt>=0.11.0
aiohttp>=3.8.0
aiofiles>=0.8.0
websockets>=10.0

# Digital Twin and Simulation
torch>=1.12.0
torchvision>=0.13.0
# modulus @ git+https://github.com/NVIDIA/modulus.git  # Uncomment for NVIDIA Modulus

# SCADA Integration (optional)
# asyncua>=1.0.0  # OPC UA client - commented out due to installation issues
# pymodbus>=3.0.0  # Modbus client - optional
# opcua-asyncio>=0.9.0  # Alternative OPC UA - not available

# Screen capture and image processing
pillow>=9.0.0
opencv-python>=4.6.0
pyautogui>=0.9.54
pytesseract>=0.3.10
easyocr>=1.6.0
mss>=6.1.0  # Fast screen capture

# Data Processing and Streaming
# kafka-python>=2.0.2  # Optional
redis>=4.3.0
influxdb-client>=1.30.0
# apache-airflow>=2.3.0  # Optional for workflow management - heavy dependency

# Configuration and Utilities
pyyaml>=6.0
pydantic>=1.9.0
click>=8.0.0
python-dotenv>=0.19.0
jsonschema>=4.0.0

# API and Web Framework
fastapi>=0.85.0
uvicorn[standard]>=0.18.0
starlette>=0.20.0
python-multipart>=0.0.5

# Database
sqlalchemy>=1.4.0
alembic>=1.8.0
psycopg2-binary>=2.9.0  # PostgreSQL
# sqlite3 is built-in to Python, no need to install

# Monitoring and Logging
prometheus-client>=0.14.0
structlog>=22.1.0
sentry-sdk[fastapi]>=1.9.0

# Testing
pytest>=7.0.0
pytest-asyncio>=0.19.0
pytest-cov>=3.0.0
httpx>=0.23.0  # For testing FastAPI

# Development Tools
black>=22.0.0
isort>=5.10.0
flake8>=5.0.0
mypy>=0.971
pre-commit>=2.20.0

# Jupyter and Data Science
jupyter>=1.0.0
jupyterlab>=3.4.0
notebook>=6.4.0
ipywidgets>=7.7.0

# Visualization
plotly>=5.10.0
dash>=2.6.0
bokeh>=2.4.0
seaborn>=0.11.0

# Machine Learning and Physics
# tensorflow>=2.9.0  # Optional, for TensorFlow models - large dependency
sympy>=1.11.0  # Symbolic mathematics
# fenics>=2019.1.0  # FEM solver (optional) - complex installation
# pyfmi>=2.5.0  # FMI/FMU support (optional)

# Cloud and Deployment (optional)
# docker>=6.0.0  # Optional
# kubernetes>=24.2.0  # Optional
# boto3>=1.24.0  # AWS SDK - optional
# azure-storage-blob>=12.12.0  # Azure SDK - optional
# google-cloud-storage>=2.5.0  # GCP SDK - optional

# Security
cryptography>=37.0.0
passlib[bcrypt]>=1.7.4
python-jose[cryptography]>=3.3.0

# Performance and Optimization
# numba>=0.56.0  # Optional - can have installation issues
# cython>=0.29.0  # Optional
joblib>=1.1.0
# dask[complete]>=2022.8.0  # Optional - large dependency

# Optional: GPU Computing
# cupy>=11.0.0  # CUDA arrays
# rapids-cudf>=22.08.0  # GPU DataFrames
# nvidia-ml-py>=11.0.0  # NVIDIA GPU monitoring