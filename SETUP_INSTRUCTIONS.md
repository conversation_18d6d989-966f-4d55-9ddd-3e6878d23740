# Tank System Setup Instructions

## ✅ Issues Fixed Successfully

### 1. OPC UA Import Errors Resolved
- **Fixed**: `NameError: name '<PERSON>Hand<PERSON>' is not defined` 
- **Solution**: Added conditional import handling with dummy classes when asyncua is not available
- **Files Modified**: 
  - `digital_twin/scada/opc_client.py`
  - `digital_twin/scada/__init__.py`

### 2. Class Name Mismatches Fixed
- **Fixed**: Import statements throughout the codebase
- **Solution**: Updated all references from `TankControllerEngine` to `TankControlSystem`
- **Files Modified**:
  - `tank_system_main.py`
  - `src/api/tank_endpoints.py`

### 3. Python Package Structure Fixed
- **Fixed**: Module import errors for `src.api.tank_endpoints`
- **Solution**: Created proper `__init__.py` files
- **Files Created**:
  - `src/__init__.py`
  - `src/api/__init__.py`

## 🚀 Current Application Status

✅ **Tank Control System Running Successfully**
- API Server: http://0.0.0.0:8000
- API Documentation: http://0.0.0.0:8000/docs
- Physics simulation initialized
- Tank control managers operational
- Two tanks configured (tank_001, tank_002)

## 🐳 Docker Issue Resolution

### Error Encountered
```
Error: rpc error: code = Internal desc = service jupyter has build context service digital-twin-app has build context
```

### Root Cause
This error indicates that multiple Docker services are trying to use the same build context directory, causing a conflict.

### Solution Options

#### Option 1: Check docker-compose.yml
1. Look for multiple services with the same `build:` context:
```yaml
services:
  jupyter:
    build: .  # ← Same context
  digital-twin-app:
    build: .  # ← Same context (CONFLICT)
```

2. Fix by using different contexts or images:
```yaml
services:
  jupyter:
    build: 
      context: .
      dockerfile: Dockerfile.jupyter
  digital-twin-app:
    build:
      context: .
      dockerfile: Dockerfile.app
```

#### Option 2: Clean Docker State
```bash
# Stop all containers
docker-compose down

# Remove orphaned containers
docker-compose down --remove-orphans

# Clean build cache
docker system prune -f

# Rebuild from scratch
docker-compose build --no-cache
docker-compose up
```

#### Option 3: Use Different Build Contexts
Create separate directories:
```
project/
├── jupyter/
│   └── Dockerfile
├── app/
│   └── Dockerfile
└── docker-compose.yml
```

## 📋 Running the Application

### Local Development (Recommended)
```bash
# Activate virtual environment
.venv\Scripts\activate  # Windows
source .venv/bin/activate  # Linux/Mac

# Install dependencies
pip install -r requirements.txt

# Run the application
python tank_system_main.py
```

### Access Points
- **Main API**: http://localhost:8000
- **Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **Tank List**: http://localhost:8000/api/v1/tanks/
- **System Status**: http://localhost:8000/api/v1/tanks/status

## 🔧 Development Notes

### Missing Dependencies (Handled Gracefully)
- `asyncua` - OPC UA client (shows warning, continues without SCADA)
- `pymodbus` - Modbus client (shows warning, continues without Modbus)

### Key Features Working
- Tank configuration loading from YAML
- PID controllers for temperature/pressure/level control
- Physics simulation with heat transfer models
- REST API endpoints for tank management
- Real-time monitoring capabilities
- Graceful degradation when SCADA dependencies missing

### API Endpoints Available

#### Tank Control APIs
- `GET /api/v1/tanks/` - List all tanks
- `POST /api/v1/tanks/` - Create new tank
- `GET /api/v1/tanks/{tank_id}/state` - Get tank state
- `POST /api/v1/tanks/{tank_id}/control` - Control tank
- `GET /api/v1/tanks/{tank_id}/metrics` - Get performance metrics
- `GET /api/v1/tanks/status` - System status

#### MLOps APIs
- `POST /api/v1/mlops/models/train` - Train new ML model
- `GET /api/v1/mlops/models` - List all models
- `GET /api/v1/mlops/models/{model_name}` - Get model details
- `POST /api/v1/mlops/models/{model_name}/deploy` - Deploy model
- `POST /api/v1/mlops/models/{model_name}/predict` - Make predictions
- `POST /api/v1/mlops/models/{model_name}/batch-predict` - Batch predictions
- `POST /api/v1/mlops/models/{model_name}/retrain` - Retrain model
- `DELETE /api/v1/mlops/models/{model_name}` - Delete model
- `GET /api/v1/mlops/models/{model_name}/metrics` - Model performance metrics
- `POST /api/v1/mlops/models/upload` - Upload pre-trained model
- `GET /api/v1/mlops/experiments` - List ML experiments
- `GET /api/v1/mlops/data-drift/{model_name}` - Check data drift

## ⚠️ Known Issues
1. Some API endpoints still reference old variable names (being fixed)
2. SCADA integration requires additional configuration for full functionality
3. Docker build context conflicts need resolution for containerized deployment

## 🎯 Next Steps
1. Resolve Docker configuration conflicts
2. Complete remaining API endpoint fixes
3. Test full SCADA integration with real OPC UA server
4. Add comprehensive logging and monitoring
5. Implement database persistence for historical data
