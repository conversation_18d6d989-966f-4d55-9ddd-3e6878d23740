/**
 * Hot-Oil Boiler Simulation
 * Detailed simulation of boiler operations including fuel consumption, heat generation,
 * temperature control, and safety systems with realistic operational parameters
 */

export interface BoilerSpecifications {
  id: string;
  name: string;
  fuelType: 'natural_gas' | 'diesel' | 'heavy_fuel_oil' | 'biomass';
  ratedCapacity: number; // kW
  maxTemperature: number; // °C
  minTemperature: number; // °C
  efficiency: {
    rated: number; // % at rated conditions
    minimum: number; // % at minimum load
    curve: number[]; // Efficiency curve points [load%, efficiency%]
  };
  turndownRatio: number; // Minimum load / Maximum load
  responseTime: number; // seconds to reach setpoint
  fuelConsumption: {
    ratedFlow: number; // m³/h for gas, L/h for liquid fuels
    heatingValue: number; // kJ/m³ or kJ/L
    density: number; // kg/m³ or kg/L
  };
  emissions: {
    nox: number; // mg/m³
    co: number; // mg/m³
    co2Factor: number; // kg CO2/kWh
  };
}

export interface BoilerSafetySystem {
  flameFailureDetection: boolean;
  lowWaterCutoff: boolean;
  highTemperatureShutdown: boolean;
  highPressureShutdown: boolean;
  emergencyShutdown: boolean;
  gasLeakDetection: boolean;
  ventilationMonitoring: boolean;
  safetyValves: {
    count: number;
    setpressure: number; // bar
    capacity: number; // kg/h
  };
}

export interface BoilerControlSystem {
  temperatureControl: {
    mode: 'manual' | 'automatic' | 'cascade';
    setpoint: number; // °C
    deadband: number; // °C
    pidParameters: {
      kp: number;
      ki: number;
      kd: number;
      outputLimits: [number, number]; // [min%, max%]
    };
  };
  fuelControl: {
    mode: 'single_stage' | 'two_stage' | 'modulating';
    modulationRange: [number, number]; // [min%, max%]
    airFuelRatio: number;
    excessAir: number; // %
  };
  sequenceControl: {
    startupSequence: string[];
    shutdownSequence: string[];
    purgeTime: number; // seconds
    ignitionTime: number; // seconds
  };
}

export interface BoilerOperationalState {
  timestamp: Date;
  isRunning: boolean;
  currentLoad: number; // % of rated capacity
  outputTemperature: number; // °C
  returnTemperature: number; // °C
  fuelFlow: number; // m³/h or L/h
  airFlow: number; // m³/h
  stackTemperature: number; // °C
  efficiency: number; // %
  heatOutput: number; // kW
  fuelConsumption: number; // m³/h or L/h
  operatingHours: number;
  cycleCount: number;
  lastStartup: Date;
  lastShutdown: Date;
  maintenanceHours: number;
}

export interface BoilerAlarms {
  flameFailure: boolean;
  highTemperature: boolean;
  lowWaterLevel: boolean;
  highPressure: boolean;
  gasLeak: boolean;
  lowFuelPressure: boolean;
  highStackTemperature: boolean;
  lowEfficiency: boolean;
  maintenanceRequired: boolean;
}

export class HotOilBoilerSimulation {
  private specifications: BoilerSpecifications;
  private safetySystem: BoilerSafetySystem;
  private controlSystem: BoilerControlSystem;
  private operationalState: BoilerOperationalState;
  private alarms: BoilerAlarms;
  private ambientTemperature: number = 20; // °C
  private simulationStartTime: Date = new Date();

  constructor(specifications: BoilerSpecifications) {
    this.specifications = specifications;
    this.initializeSafetySystem();
    this.initializeControlSystem();
    this.operationalState = this.createInitialState();
    this.alarms = this.createInitialAlarms();
  }

  private initializeSafetySystem() {
    this.safetySystem = {
      flameFailureDetection: true,
      lowWaterCutoff: true,
      highTemperatureShutdown: true,
      highPressureShutdown: true,
      emergencyShutdown: false,
      gasLeakDetection: true,
      ventilationMonitoring: true,
      safetyValves: {
        count: 2,
        setpressure: 12, // bar
        capacity: 5000 // kg/h
      }
    };
  }

  private initializeControlSystem() {
    this.controlSystem = {
      temperatureControl: {
        mode: 'automatic',
        setpoint: 280, // °C
        deadband: 2, // °C
        pidParameters: {
          kp: 2.0,
          ki: 0.1,
          kd: 0.05,
          outputLimits: [10, 100] // 10-100% output
        }
      },
      fuelControl: {
        mode: 'modulating',
        modulationRange: [20, 100], // 20-100% capacity
        airFuelRatio: 10.5, // Stoichiometric ratio
        excessAir: 15 // % excess air
      },
      sequenceControl: {
        startupSequence: [
          'pre_purge',
          'ignition_preparation',
          'pilot_ignition',
          'main_flame_ignition',
          'normal_operation'
        ],
        shutdownSequence: [
          'fuel_cutoff',
          'post_purge',
          'fan_shutdown',
          'system_isolation'
        ],
        purgeTime: 30, // seconds
        ignitionTime: 10 // seconds
      }
    };
  }

  private createInitialState(): BoilerOperationalState {
    return {
      timestamp: new Date(),
      isRunning: false,
      currentLoad: 0,
      outputTemperature: this.ambientTemperature,
      returnTemperature: this.ambientTemperature,
      fuelFlow: 0,
      airFlow: 0,
      stackTemperature: this.ambientTemperature,
      efficiency: 0,
      heatOutput: 0,
      fuelConsumption: 0,
      operatingHours: 0,
      cycleCount: 0,
      lastStartup: new Date(),
      lastShutdown: new Date(),
      maintenanceHours: 0
    };
  }

  private createInitialAlarms(): BoilerAlarms {
    return {
      flameFailure: false,
      highTemperature: false,
      lowWaterLevel: false,
      highPressure: false,
      gasLeak: false,
      lowFuelPressure: false,
      highStackTemperature: false,
      lowEfficiency: false,
      maintenanceRequired: false
    };
  }

  public simulate(
    heatDemand: number, // kW
    returnTemperature: number, // °C
    timeStep: number = 1.0 // seconds
  ): BoilerOperationalState {
    
    // Update operational hours
    if (this.operationalState.isRunning) {
      this.operationalState.operatingHours += timeStep / 3600;
    }

    // Calculate required load based on heat demand
    const requiredLoad = Math.min(100, (heatDemand / this.specifications.ratedCapacity) * 100);
    
    // Check if boiler should be running
    const shouldRun = requiredLoad > (this.specifications.turndownRatio * 100);
    
    if (shouldRun && !this.operationalState.isRunning) {
      this.startBoiler();
    } else if (!shouldRun && this.operationalState.isRunning) {
      this.shutdownBoiler();
    }

    if (this.operationalState.isRunning) {
      // Update load based on demand and control system
      this.updateLoad(requiredLoad);
      
      // Calculate combustion parameters
      this.calculateCombustion();
      
      // Update temperatures
      this.updateTemperatures(returnTemperature, timeStep);
      
      // Calculate efficiency
      this.calculateEfficiency();
      
      // Update fuel consumption
      this.updateFuelConsumption();
      
      // Check safety systems
      this.checkSafetyAlarms();
    } else {
      // Boiler is off - cool down
      this.coolDown(timeStep);
    }

    this.operationalState.timestamp = new Date();
    return { ...this.operationalState };
  }

  private startBoiler() {
    if (this.checkStartupConditions()) {
      this.operationalState.isRunning = true;
      this.operationalState.lastStartup = new Date();
      this.operationalState.cycleCount++;
      console.log(`Boiler ${this.specifications.id} started`);
    }
  }

  private shutdownBoiler() {
    this.operationalState.isRunning = false;
    this.operationalState.currentLoad = 0;
    this.operationalState.fuelFlow = 0;
    this.operationalState.lastShutdown = new Date();
    console.log(`Boiler ${this.specifications.id} shutdown`);
  }

  private checkStartupConditions(): boolean {
    // Check safety interlocks
    if (this.alarms.gasLeak || this.alarms.lowWaterLevel || this.safetySystem.emergencyShutdown) {
      return false;
    }
    return true;
  }

  private updateLoad(requiredLoad: number) {
    const currentLoad = this.operationalState.currentLoad;
    const maxChangeRate = 5; // % per second maximum load change rate
    
    // Apply ramp rate limiting
    const loadDifference = requiredLoad - currentLoad;
    const maxChange = maxChangeRate * 1.0; // 1 second time step
    
    if (Math.abs(loadDifference) <= maxChange) {
      this.operationalState.currentLoad = requiredLoad;
    } else {
      this.operationalState.currentLoad += Math.sign(loadDifference) * maxChange;
    }
    
    // Apply turndown ratio limits
    const minLoad = this.specifications.turndownRatio * 100;
    this.operationalState.currentLoad = Math.max(minLoad, 
      Math.min(100, this.operationalState.currentLoad));
  }

  private calculateCombustion() {
    const load = this.operationalState.currentLoad / 100;
    
    // Calculate fuel flow based on load
    this.operationalState.fuelFlow = this.specifications.fuelConsumption.ratedFlow * load;
    
    // Calculate air flow (includes excess air)
    const stoichiometricAir = this.operationalState.fuelFlow * this.controlSystem.fuelControl.airFuelRatio;
    this.operationalState.airFlow = stoichiometricAir * (1 + this.controlSystem.fuelControl.excessAir / 100);
    
    // Calculate stack temperature
    const baseStackTemp = 150; // °C base stack temperature
    const loadEffect = load * 50; // Higher load = higher stack temp
    this.operationalState.stackTemperature = baseStackTemp + loadEffect;
  }

  private updateTemperatures(returnTemp: number, timeStep: number) {
    this.operationalState.returnTemperature = returnTemp;
    
    // Temperature control using PID
    const setpoint = this.controlSystem.temperatureControl.setpoint;
    const currentTemp = this.operationalState.outputTemperature;
    const error = setpoint - currentTemp;
    
    // Simplified PID control
    const pidOutput = this.controlSystem.temperatureControl.pidParameters.kp * error;
    
    // Apply temperature change based on load and PID output
    const load = this.operationalState.currentLoad / 100;
    const heatInput = this.specifications.ratedCapacity * load; // kW
    
    // Simplified thermal mass calculation
    const thermalMass = 5000; // kg equivalent thermal mass
    const specificHeat = 0.5; // kJ/kg·K for steel/refractory
    
    const tempRise = (heatInput * timeStep) / (thermalMass * specificHeat);
    const tempLoss = (currentTemp - this.ambientTemperature) * 0.001 * timeStep; // Heat loss
    
    this.operationalState.outputTemperature += tempRise - tempLoss + (pidOutput * 0.1);
    this.operationalState.outputTemperature = Math.max(this.ambientTemperature, 
      Math.min(this.specifications.maxTemperature, this.operationalState.outputTemperature));
  }

  private calculateEfficiency() {
    const load = this.operationalState.currentLoad;
    
    // Efficiency curve interpolation
    let efficiency = this.specifications.efficiency.rated;
    
    if (load < 50) {
      // Lower efficiency at low loads
      efficiency = this.specifications.efficiency.minimum + 
        (this.specifications.efficiency.rated - this.specifications.efficiency.minimum) * (load / 50);
    }
    
    // Stack loss effect
    const stackLoss = (this.operationalState.stackTemperature - this.ambientTemperature) * 0.1;
    efficiency -= stackLoss;
    
    this.operationalState.efficiency = Math.max(50, Math.min(95, efficiency));
    
    // Calculate actual heat output
    const fuelEnergyInput = this.operationalState.fuelFlow * 
      this.specifications.fuelConsumption.heatingValue / 3600; // kW
    
    this.operationalState.heatOutput = fuelEnergyInput * (this.operationalState.efficiency / 100);
  }

  private updateFuelConsumption() {
    this.operationalState.fuelConsumption = this.operationalState.fuelFlow;
  }

  private checkSafetyAlarms() {
    // High temperature alarm
    this.alarms.highTemperature = this.operationalState.outputTemperature > 
      (this.specifications.maxTemperature * 0.95);
    
    // High stack temperature alarm
    this.alarms.highStackTemperature = this.operationalState.stackTemperature > 300;
    
    // Low efficiency alarm
    this.alarms.lowEfficiency = this.operationalState.efficiency < 70;
    
    // Maintenance required (based on operating hours)
    this.alarms.maintenanceRequired = this.operationalState.operatingHours > 8760; // Annual maintenance
    
    // Emergency shutdown if critical alarms
    if (this.alarms.highTemperature || this.alarms.gasLeak || this.alarms.lowWaterLevel) {
      this.safetySystem.emergencyShutdown = true;
      this.shutdownBoiler();
    }
  }

  private coolDown(timeStep: number) {
    // Natural cooling when boiler is off
    const coolingRate = 0.5; // °C per second
    const tempDiff = this.operationalState.outputTemperature - this.ambientTemperature;
    
    if (tempDiff > 0) {
      this.operationalState.outputTemperature -= Math.min(coolingRate * timeStep, tempDiff);
    }
    
    this.operationalState.stackTemperature = this.operationalState.outputTemperature + 20;
    this.operationalState.heatOutput = 0;
    this.operationalState.efficiency = 0;
  }

  // Public API methods
  public getCurrentState(): BoilerOperationalState {
    return { ...this.operationalState };
  }

  public getAlarms(): BoilerAlarms {
    return { ...this.alarms };
  }

  public getSpecifications(): BoilerSpecifications {
    return { ...this.specifications };
  }

  public setTemperatureSetpoint(setpoint: number): boolean {
    if (setpoint >= this.specifications.minTemperature && 
        setpoint <= this.specifications.maxTemperature) {
      this.controlSystem.temperatureControl.setpoint = setpoint;
      return true;
    }
    return false;
  }

  public acknowledgeAlarm(alarmType: keyof BoilerAlarms): boolean {
    if (alarmType in this.alarms) {
      this.alarms[alarmType] = false;
      return true;
    }
    return false;
  }

  public emergencyShutdown(): void {
    this.safetySystem.emergencyShutdown = true;
    this.shutdownBoiler();
    console.log(`Emergency shutdown activated for boiler ${this.specifications.id}`);
  }

  public resetEmergencyShutdown(): boolean {
    if (this.checkStartupConditions()) {
      this.safetySystem.emergencyShutdown = false;
      return true;
    }
    return false;
  }

  public getPerformanceMetrics(): {
    availability: number; // %
    efficiency: number; // %
    fuelConsumptionRate: number; // m³/kWh or L/kWh
    emissionsRate: number; // kg CO2/kWh
    maintenanceInterval: number; // hours
  } {
    const totalTime = (Date.now() - this.simulationStartTime.getTime()) / 1000 / 3600; // hours
    const availability = totalTime > 0 ? (this.operationalState.operatingHours / totalTime) * 100 : 0;
    
    const fuelConsumptionRate = this.operationalState.heatOutput > 0 ? 
      this.operationalState.fuelConsumption / this.operationalState.heatOutput : 0;
    
    const emissionsRate = fuelConsumptionRate * this.specifications.emissions.co2Factor;
    
    return {
      availability,
      efficiency: this.operationalState.efficiency,
      fuelConsumptionRate,
      emissionsRate,
      maintenanceInterval: 8760 - this.operationalState.operatingHours // Hours until next maintenance
    };
  }
}
