"""
FastAPI Application for Digital Twin System
Provides REST API endpoints for digital twin management
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime
import asyncio

from digital_twin.core import DigitalTwinEngine, TwinConfiguration, TwinStateData
from digital_twin.scada import OPCUAClient, ModbusClient
from src.api.models import (
    TwinCreateRequest, TwinUpdateRequest, TwinResponse,
    SCADADataRequest, SCADADataResponse,
    SimulationControlRequest, SimulationStatusResponse,
    HealthResponse
)

logger = logging.getLogger(__name__)
security = HTTPBearer(auto_error=False)


def create_app(config: Dict[str, Any], 
               twin_engine: DigitalTwinEngine,
               scada_clients: Dict[str, Any]) -> FastAPI:
    """Create and configure FastAPI application"""
    
    # Create FastAPI app
    app = FastAPI(
        title="Digital Twin System API",
        description="REST API for digital twin management and SCADA integration",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # CORS middleware
    api_config = config.get('api', {})
    if api_config.get('cors_enabled', True):
        app.add_middleware(
            CORSMiddleware,
            allow_origins=api_config.get('cors_origins', ["*"]),
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    # Store dependencies
    app.state.config = config
    app.state.twin_engine = twin_engine
    app.state.scada_clients = scada_clients
    
    # Health check endpoint
    @app.get("/health", response_model=HealthResponse)
    async def health_check():
        """Health check endpoint"""
        return HealthResponse(
            status="healthy",
            timestamp=datetime.now(),
            version="1.0.0",
            components={
                "twin_engine": "healthy" if twin_engine else "unavailable",
                "scada_opc_ua": "healthy" if "opc_ua" in scada_clients else "unavailable",
                "scada_modbus": "healthy" if "modbus" in scada_clients else "unavailable"
            }
        )
    
    # Digital Twin Management Endpoints
    @app.post("/api/v1/twins", response_model=TwinResponse)
    async def create_twin(request: TwinCreateRequest):
        """Create a new digital twin"""
        try:
            # Create twin configuration
            config = TwinConfiguration(
                twin_id=request.twin_id,
                name=request.name,
                description=request.description,
                physical_asset_id=request.physical_asset_id,
                model_version=request.model_version,
                update_frequency=request.update_frequency,
                synchronization_threshold=request.synchronization_threshold,
                physics_parameters=request.physics_parameters or {},
                scada_tags=request.scada_tags or []
            )
            
            # Create twin
            twin = await twin_engine.create_twin(config)
            
            # Start twin if requested
            if request.auto_start:
                await twin_engine.start_twin(request.twin_id)
            
            return TwinResponse(
                twin_id=request.twin_id,
                status="created",
                message="Digital twin created successfully"
            )
            
        except Exception as e:
            logger.error(f"Error creating twin: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/v1/twins", response_model=List[Dict[str, Any]])
    async def list_twins():
        """List all digital twins"""
        try:
            twins = []
            for twin_id, twin in twin_engine.twins.items():
                twins.append(twin.to_dict())
            return twins
        except Exception as e:
            logger.error(f"Error listing twins: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/v1/twins/{twin_id}", response_model=Dict[str, Any])
    async def get_twin(twin_id: str):
        """Get digital twin by ID"""
        try:
            if twin_id not in twin_engine.twins:
                raise HTTPException(status_code=404, detail="Twin not found")
            
            twin = twin_engine.twins[twin_id]
            return twin.to_dict()
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting twin: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/v1/twins/{twin_id}/start", response_model=TwinResponse)
    async def start_twin(twin_id: str):
        """Start a digital twin"""
        try:
            success = await twin_engine.start_twin(twin_id)
            if not success:
                raise HTTPException(status_code=404, detail="Twin not found")
            
            return TwinResponse(
                twin_id=twin_id,
                status="started",
                message="Digital twin started successfully"
            )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error starting twin: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/v1/twins/{twin_id}/stop", response_model=TwinResponse)
    async def stop_twin(twin_id: str):
        """Stop a digital twin"""
        try:
            success = await twin_engine.stop_twin(twin_id)
            if not success:
                raise HTTPException(status_code=404, detail="Twin not found")
            
            return TwinResponse(
                twin_id=twin_id,
                status="stopped",
                message="Digital twin stopped successfully"
            )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error stopping twin: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/v1/twins/{twin_id}/state", response_model=Dict[str, Any])
    async def get_twin_state(twin_id: str):
        """Get current state of a digital twin"""
        try:
            state = await twin_engine.get_twin_state(twin_id)
            if state is None:
                raise HTTPException(status_code=404, detail="Twin not found")
            
            return {
                "twin_id": twin_id,
                "timestamp": state.timestamp.isoformat(),
                "temperature_profile": state.temperature_profile.tolist(),
                "heat_flux": state.heat_flux.tolist(),
                "control_inputs": state.control_inputs,
                "measured_outputs": state.measured_outputs,
                "predicted_outputs": state.predicted_outputs,
                "deviation_metrics": state.deviation_metrics,
                "model_confidence": state.model_confidence,
                "simulation_step": state.simulation_step
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting twin state: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/v1/twins/{twin_id}/update", response_model=TwinResponse)
    async def update_twin_state(twin_id: str, request: TwinUpdateRequest):
        """Update twin state with measurements"""
        try:
            success = await twin_engine.update_twin_state(twin_id, request.measurements)
            if not success:
                raise HTTPException(status_code=404, detail="Twin not found")
            
            return TwinResponse(
                twin_id=twin_id,
                status="updated",
                message="Twin state updated successfully"
            )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating twin state: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    # SCADA Integration Endpoints
    @app.get("/api/v1/scada/status", response_model=Dict[str, Any])
    async def get_scada_status():
        """Get SCADA connection status"""
        try:
            status = {}
            for name, client in scada_clients.items():
                if hasattr(client, 'is_connected'):
                    status[name] = {
                        "connected": client.is_connected,
                        "type": name
                    }
                elif hasattr(client, 'get_connection_state'):
                    state = client.get_connection_state()
                    status[name] = {
                        "connected": state.value == "connected",
                        "state": state.value,
                        "type": name
                    }
            return status
        except Exception as e:
            logger.error(f"Error getting SCADA status: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/v1/scada/{client_name}/tags", response_model=Dict[str, Any])
    async def get_scada_tags(client_name: str):
        """Get SCADA tag values"""
        try:
            if client_name not in scada_clients:
                raise HTTPException(status_code=404, detail="SCADA client not found")
            
            client = scada_clients[client_name]
            
            if hasattr(client, 'read_all_tags'):
                data = await client.read_all_tags()
                return data
            elif hasattr(client, 'get_tag_status'):
                status = client.get_tag_status()
                return status
            else:
                raise HTTPException(status_code=501, detail="Tag reading not implemented")
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error reading SCADA tags: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/v1/scada/{client_name}/write", response_model=SCADADataResponse)
    async def write_scada_tag(client_name: str, request: SCADADataRequest):
        """Write value to SCADA tag"""
        try:
            if client_name not in scada_clients:
                raise HTTPException(status_code=404, detail="SCADA client not found")
            
            client = scada_clients[client_name]
            
            if hasattr(client, 'write_tag'):
                success = await client.write_tag(request.tag_name, request.value)
                return SCADADataResponse(
                    success=success,
                    message="Tag written successfully" if success else "Failed to write tag"
                )
            else:
                raise HTTPException(status_code=501, detail="Tag writing not implemented")
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error writing SCADA tag: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    # Simulation Control Endpoints
    @app.get("/api/v1/simulation/status", response_model=Dict[str, Any])
    async def get_simulation_status():
        """Get simulation status"""
        try:
            # This would interface with physics engines
            # Placeholder implementation
            return {
                "status": "running",
                "engines": ["main"],
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error getting simulation status: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/v1/simulation/control", response_model=SimulationStatusResponse)
    async def control_simulation(request: SimulationControlRequest):
        """Control simulation (start/stop/pause/resume)"""
        try:
            # This would interface with physics engines
            # Placeholder implementation
            return SimulationStatusResponse(
                status=request.action,
                message=f"Simulation {request.action} command executed"
            )
        except Exception as e:
            logger.error(f"Error controlling simulation: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    # Data Analytics Endpoints
    @app.get("/api/v1/analytics/twins/{twin_id}/metrics", response_model=Dict[str, Any])
    async def get_twin_metrics(twin_id: str, 
                              start_time: Optional[str] = None,
                              end_time: Optional[str] = None):
        """Get analytics metrics for a digital twin"""
        try:
            if twin_id not in twin_engine.twins:
                raise HTTPException(status_code=404, detail="Twin not found")
            
            # Placeholder for analytics
            return {
                "twin_id": twin_id,
                "metrics": {
                    "avg_temperature": 75.5,
                    "max_temperature": 120.0,
                    "min_temperature": 20.0,
                    "sync_accuracy": 0.95,
                    "prediction_error": 0.12
                },
                "time_range": {
                    "start": start_time or datetime.now().isoformat(),
                    "end": end_time or datetime.now().isoformat()
                }
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting twin metrics: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    # WebSocket endpoint for real-time data
    @app.websocket("/api/v1/ws/twins/{twin_id}")
    async def websocket_twin_data(websocket, twin_id: str):
        """WebSocket endpoint for real-time twin data"""
        await websocket.accept()
        try:
            while True:
                # Get current twin state
                state = await twin_engine.get_twin_state(twin_id)
                if state:
                    data = {
                        "timestamp": state.timestamp.isoformat(),
                        "temperature_profile": state.temperature_profile.tolist(),
                        "deviation_metrics": state.deviation_metrics,
                        "model_confidence": state.model_confidence
                    }
                    await websocket.send_json(data)
                
                await asyncio.sleep(1)  # Send data every second
                
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            await websocket.close()
    
    return app


# Authentication dependency (placeholder)
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Authentication dependency"""
    # This would implement actual authentication
    return {"user_id": "admin", "permissions": ["all"]}