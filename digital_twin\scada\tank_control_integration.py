"""
Tank Control System Integration
Integrates SCADA OPC UA with digital twin for real-time tank control
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum

from .opc_client import OPCUAClient, OPCConfiguration, OPCTag
from .modbus_client import ModbusClient, ModbusConfiguration, ModbusTag, ModbusFunction, ModbusDataType
from ..core.twin_engine import DigitalTwinEngine, TwinConfiguration
from ..simulation.physics_engine import TankConfiguration, RealTimePhysicsEngine, SimulationConfig

logger = logging.getLogger(__name__)


class ControlMode(Enum):
    MANUAL = "manual"
    AUTOMATIC = "automatic"
    EMERGENCY = "emergency"
    MAINTENANCE = "maintenance"


@dataclass
class TankControlMapping:
    """Mapping between SCADA tags and tank control parameters"""
    tank_id: str
    
    # Temperature sensors (OPC UA tags)
    zone_temperature_tags: List[str] = field(default_factory=list)
    ambient_temperature_tag: str = ""
    outlet_temperature_tag: str = ""
    
    # Heating control outputs (OPC UA/Modbus)
    heating_power_tags: List[str] = field(default_factory=list)
    heating_enable_tags: List[str] = field(default_factory=list)
    
    # Status and alarms
    tank_level_tag: str = ""
    pump_status_tags: List[str] = field(default_factory=list)
    emergency_stop_tag: str = ""
    alarm_tags: List[str] = field(default_factory=list)
    
    # Setpoints
    target_temperature_tags: List[str] = field(default_factory=list)
    flow_rate_setpoint_tag: str = ""
    
    # Energy management
    power_consumption_tag: str = ""
    energy_cost_tag: str = ""


@dataclass
class TankControlConfiguration:
    """Configuration for tank control system"""
    tank_config: TankConfiguration
    control_mapping: TankControlMapping
    opc_config: Optional[OPCConfiguration] = None
    modbus_config: Optional[ModbusConfiguration] = None
    
    # Control parameters
    control_mode: ControlMode = ControlMode.AUTOMATIC
    update_frequency: float = 1.0  # Hz
    pid_gains: Dict[str, float] = field(default_factory=lambda: {
        'kp': 1000.0, 'ki': 10.0, 'kd': 50.0
    })
    
    # Safety parameters
    emergency_temperature_limit: float = 200.0
    min_safe_temperature: float = 100.0
    max_heating_power: float = 100000.0  # watts
    
    # Optimization parameters
    energy_optimization_enabled: bool = True
    demand_response_enabled: bool = False
    peak_shaving_enabled: bool = True


class TankControlSystem:
    """Integrated tank control system with SCADA and digital twin"""
    
    def __init__(self, config: TankControlConfiguration):
        self.config = config
        self.opc_client: Optional[OPCUAClient] = None
        self.modbus_client: Optional[ModbusClient] = None
        self.twin_engine: Optional[DigitalTwinEngine] = None
        self.physics_engine: Optional[RealTimePhysicsEngine] = None
        
        # Current state
        self.current_temperatures: Dict[str, float] = {}
        self.current_heating_powers: Dict[str, float] = {}
        self.current_setpoints: Dict[str, float] = {}
        self.system_alarms: List[str] = []
        self.control_mode = config.control_mode
        
        # Control callbacks
        self.data_callbacks: List[Callable] = []
        self.alarm_callbacks: List[Callable] = []
        self.control_callbacks: List[Callable] = []
        
        # Tasks
        self._control_task: Optional[asyncio.Task] = None
        self._monitoring_task: Optional[asyncio.Task] = None
        self.is_running = False
    
    async def initialize(self) -> bool:
        """Initialize the tank control system"""
        try:
            logger.info(f"Initializing tank control system for tank {self.config.tank_config.tank_id}")
            
            # Initialize OPC UA client if configured
            if self.config.opc_config:
                self.opc_client = OPCUAClient(self.config.opc_config)
                self.opc_client.register_subscription_handler(self._handle_opc_data)
                self.opc_client.register_connection_callback(self._handle_opc_connection)
                
                if not await self.opc_client.connect():
                    logger.error("Failed to connect to OPC UA server")
                    return False
            
            # Initialize Modbus client if configured
            if self.config.modbus_config:
                self.modbus_client = ModbusClient(self.config.modbus_config)
                self.modbus_client.register_data_callback(self._handle_modbus_data)
                self.modbus_client.register_connection_callback(self._handle_modbus_connection)
                
                if not await self.modbus_client.connect():
                    logger.error("Failed to connect to Modbus device")
                    return False
            
            # Initialize digital twin engine
            await self._initialize_digital_twin()
            
            # Initialize physics engine
            await self._initialize_physics_engine()
            
            logger.info("Tank control system initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing tank control system: {e}")
            return False
    
    async def start(self) -> bool:
        """Start the tank control system"""
        if self.is_running:
            return True
        
        try:
            # Start SCADA polling
            if self.modbus_client:
                await self.modbus_client.start_polling(1.0 / self.config.update_frequency)
            
            # Start physics simulation
            if self.physics_engine:
                await self.physics_engine.start()
            
            # Start control and monitoring tasks
            self._control_task = asyncio.create_task(self._control_loop())
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())
            
            self.is_running = True
            logger.info("Tank control system started")
            return True
            
        except Exception as e:
            logger.error(f"Error starting tank control system: {e}")
            return False
    
    async def stop(self):
        """Stop the tank control system"""
        try:
            self.is_running = False
            
            # Cancel tasks
            if self._control_task:
                self._control_task.cancel()
            if self._monitoring_task:
                self._monitoring_task.cancel()
            
            # Stop SCADA connections
            if self.opc_client:
                await self.opc_client.disconnect()
            if self.modbus_client:
                await self.modbus_client.stop_polling()
                await self.modbus_client.disconnect()
            
            # Stop physics simulation
            if self.physics_engine:
                await self.physics_engine.stop()
            
            logger.info("Tank control system stopped")
            
        except Exception as e:
            logger.error(f"Error stopping tank control system: {e}")
    
    async def set_control_mode(self, mode: ControlMode) -> bool:
        """Set control mode"""
        try:
            old_mode = self.control_mode
            self.control_mode = mode
            
            # Handle mode changes
            if mode == ControlMode.EMERGENCY:
                await self._execute_emergency_shutdown()
            elif mode == ControlMode.MANUAL:
                await self._switch_to_manual_control()
            elif mode == ControlMode.AUTOMATIC:
                await self._switch_to_automatic_control()
            
            logger.info(f"Control mode changed from {old_mode.value} to {mode.value}")
            return True
            
        except Exception as e:
            logger.error(f"Error setting control mode: {e}")
            return False
    
    async def set_zone_setpoint(self, zone: int, temperature: float) -> bool:
        """Set temperature setpoint for a specific zone"""
        try:
            if 0 <= zone < self.config.tank_config.number_of_heating_zones:
                zone_key = f"zone_{zone}"
                self.current_setpoints[zone_key] = temperature
                
                # Write to SCADA if available
                if self.config.control_mapping.target_temperature_tags and zone < len(self.config.control_mapping.target_temperature_tags):
                    tag_name = self.config.control_mapping.target_temperature_tags[zone]
                    
                    if self.opc_client:
                        await self.opc_client.write_tag(tag_name, temperature)
                    elif self.modbus_client:
                        await self.modbus_client.write_tag(tag_name, temperature)
                
                logger.info(f"Set zone {zone} temperature setpoint to {temperature}°C")
                return True
            else:
                logger.error(f"Invalid zone number: {zone}")
                return False
                
        except Exception as e:
            logger.error(f"Error setting zone setpoint: {e}")
            return False
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        try:
            status = {
                'tank_id': self.config.tank_config.tank_id,
                'control_mode': self.control_mode.value,
                'is_running': self.is_running,
                'temperatures': self.current_temperatures.copy(),
                'heating_powers': self.current_heating_powers.copy(),
                'setpoints': self.current_setpoints.copy(),
                'alarms': self.system_alarms.copy(),
                'timestamp': datetime.now().isoformat()
            }
            
            # Add SCADA connection status
            if self.opc_client:
                status['opc_connected'] = self.opc_client.is_connected()
                status['opc_tags'] = self.opc_client.get_tag_status()
            
            if self.modbus_client:
                status['modbus_connected'] = self.modbus_client.is_connected
                status['modbus_tags'] = self.modbus_client.get_tag_status()
            
            # Add digital twin status
            if self.physics_engine:
                status['physics_state'] = await self.physics_engine.get_current_state()
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {}
    
    async def _initialize_digital_twin(self):
        """Initialize digital twin engine"""
        try:
            # Create twin configuration
            twin_config = TwinConfiguration(
                twin_id=f"tank_{self.config.tank_config.tank_id}",
                name=f"Tank {self.config.tank_config.tank_id} Digital Twin",
                description=f"Digital twin for asphalt tank {self.config.tank_config.tank_id}",
                physical_asset_id=self.config.tank_config.tank_id,
                model_version="1.0",
                update_frequency=self.config.update_frequency,
                scada_tags=self._get_all_tag_names()
            )
            
            # Initialize twin engine
            self.twin_engine = DigitalTwinEngine()
            twin = await self.twin_engine.create_twin(twin_config)
            await self.twin_engine.start_twin(twin_config.twin_id)
            
            logger.info("Digital twin initialized")
            
        except Exception as e:
            logger.error(f"Error initializing digital twin: {e}")
    
    async def _initialize_physics_engine(self):
        """Initialize physics engine"""
        try:
            # Create simulation configuration
            sim_config = SimulationConfig(
                real_time_factor=1.0,
                max_simulation_time=86400.0,  # 24 hours
                output_frequency=1
            )
            
            # Initialize physics engine with tank configuration
            self.physics_engine = RealTimePhysicsEngine(sim_config, self.config.tank_config)
            await self.physics_engine.initialize()
            
            # Register callbacks
            self.physics_engine.register_output_callback(self._handle_physics_output)
            
            logger.info("Physics engine initialized")
            
        except Exception as e:
            logger.error(f"Error initializing physics engine: {e}")
    
    async def _control_loop(self):
        """Main control loop"""
        while self.is_running:
            try:
                if self.control_mode == ControlMode.AUTOMATIC:
                    await self._execute_automatic_control()
                elif self.control_mode == ControlMode.MANUAL:
                    await self._execute_manual_control()
                
                # Update digital twin with current measurements
                if self.twin_engine:
                    measurements = self._prepare_twin_measurements()
                    await self.twin_engine.update_twin_state(
                        f"tank_{self.config.tank_config.tank_id}",
                        measurements
                    )
                
                await asyncio.sleep(1.0 / self.config.update_frequency)
                
            except Exception as e:
                logger.error(f"Error in control loop: {e}")
                await asyncio.sleep(1.0)
    
    async def _monitoring_loop(self):
        """System monitoring loop"""
        while self.is_running:
            try:
                # Check safety conditions
                await self._check_safety_conditions()
                
                # Update system metrics
                await self._update_system_metrics()
                
                # Check alarms
                await self._check_alarms()
                
                await asyncio.sleep(5.0)  # Monitor every 5 seconds
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(5.0)
    
    async def _execute_automatic_control(self):
        """Execute automatic control logic"""
        try:
            # PID control for each heating zone
            for zone in range(self.config.tank_config.number_of_heating_zones):
                zone_key = f"zone_{zone}"
                
                # Get current temperature and setpoint
                current_temp = self.current_temperatures.get(zone_key, 0.0)
                setpoint = self.current_setpoints.get(zone_key, self.config.tank_config.target_temperature)
                
                # Calculate PID output
                heating_power = await self._calculate_pid_output(zone, current_temp, setpoint)
                
                # Apply power limits
                heating_power = max(0.0, min(heating_power, self.config.max_heating_power))
                
                # Update heating power
                self.current_heating_powers[zone_key] = heating_power
                
                # Write to SCADA
                await self._write_heating_power(zone, heating_power)
            
        except Exception as e:
            logger.error(f"Error in automatic control: {e}")
    
    async def _execute_manual_control(self):
        """Execute manual control logic"""
        # In manual mode, heating powers are set externally
        # Just read current values and update internal state
        pass
    
    async def _execute_emergency_shutdown(self):
        """Execute emergency shutdown"""
        try:
            logger.critical("Executing emergency shutdown")
            
            # Turn off all heating
            for zone in range(self.config.tank_config.number_of_heating_zones):
                await self._write_heating_power(zone, 0.0)
                self.current_heating_powers[f"zone_{zone}"] = 0.0
            
            # Set emergency stop flag
            if self.config.control_mapping.emergency_stop_tag:
                if self.opc_client:
                    await self.opc_client.write_tag(self.config.control_mapping.emergency_stop_tag, True)
                elif self.modbus_client:
                    await self.modbus_client.write_tag(self.config.control_mapping.emergency_stop_tag, True)
            
            # Add alarm
            self.system_alarms.append("EMERGENCY: System shutdown activated")
            
        except Exception as e:
            logger.error(f"Error in emergency shutdown: {e}")
    
    async def _calculate_pid_output(self, zone: int, current_temp: float, setpoint: float) -> float:
        """Calculate PID controller output for heating power"""
        # Simplified PID implementation
        error = setpoint - current_temp
        
        gains = self.config.pid_gains
        kp = gains.get('kp', 1000.0)
        
        # Simple proportional control for now
        output = kp * error
        
        return max(0.0, output)
    
    async def _write_heating_power(self, zone: int, power: float):
        """Write heating power to SCADA system"""
        try:
            if (self.config.control_mapping.heating_power_tags and 
                zone < len(self.config.control_mapping.heating_power_tags)):
                
                tag_name = self.config.control_mapping.heating_power_tags[zone]
                
                if self.opc_client:
                    await self.opc_client.write_tag(tag_name, power)
                elif self.modbus_client:
                    # Convert power to percentage (0-100%)
                    power_percent = (power / self.config.max_heating_power) * 100.0
                    await self.modbus_client.write_tag(tag_name, power_percent)
        
        except Exception as e:
            logger.error(f"Error writing heating power for zone {zone}: {e}")
    
    async def _check_safety_conditions(self):
        """Check safety conditions and trigger alarms"""
        try:
            # Check temperature limits
            for zone_key, temp in self.current_temperatures.items():
                if temp > self.config.emergency_temperature_limit:
                    await self.set_control_mode(ControlMode.EMERGENCY)
                    self.system_alarms.append(f"CRITICAL: {zone_key} temperature {temp}°C exceeds emergency limit")
                    break
                elif temp < self.config.min_safe_temperature:
                    self.system_alarms.append(f"WARNING: {zone_key} temperature {temp}°C below safe minimum")
        
        except Exception as e:
            logger.error(f"Error checking safety conditions: {e}")
    
    async def _check_alarms(self):
        """Check for system alarms"""
        # Clear old alarms (keep only recent ones)
        if len(self.system_alarms) > 100:
            self.system_alarms = self.system_alarms[-50:]
        
        # Notify alarm callbacks
        if self.system_alarms:
            for callback in self.alarm_callbacks:
                try:
                    await callback(self.system_alarms)
                except Exception as e:
                    logger.error(f"Error in alarm callback: {e}")
    
    async def _update_system_metrics(self):
        """Update system performance metrics"""
        try:
            # Calculate total power consumption
            total_power = sum(self.current_heating_powers.values())
            
            # Calculate average temperature
            if self.current_temperatures:
                avg_temp = sum(self.current_temperatures.values()) / len(self.current_temperatures)
            else:
                avg_temp = 0.0
            
            # Update metrics in SCADA if configured
            if self.config.control_mapping.power_consumption_tag:
                if self.opc_client:
                    await self.opc_client.write_tag(self.config.control_mapping.power_consumption_tag, total_power)
                elif self.modbus_client:
                    await self.modbus_client.write_tag(self.config.control_mapping.power_consumption_tag, total_power)
        
        except Exception as e:
            logger.error(f"Error updating system metrics: {e}")
    
    async def _handle_opc_data(self, tag_name: str, value: Any, timestamp: datetime):
        """Handle OPC UA data changes"""
        try:
            # Update temperature readings
            mapping = self.config.control_mapping
            
            if tag_name in mapping.zone_temperature_tags:
                zone = mapping.zone_temperature_tags.index(tag_name)
                self.current_temperatures[f"zone_{zone}"] = float(value)
            
            elif tag_name == mapping.ambient_temperature_tag:
                self.current_temperatures["ambient"] = float(value)
            
            elif tag_name == mapping.outlet_temperature_tag:
                self.current_temperatures["outlet"] = float(value)
            
            # Update setpoints if changed externally
            if tag_name in mapping.target_temperature_tags:
                zone = mapping.target_temperature_tags.index(tag_name)
                self.current_setpoints[f"zone_{zone}"] = float(value)
            
            # Notify data callbacks
            for callback in self.data_callbacks:
                await callback(tag_name, value, timestamp)
        
        except Exception as e:
            logger.error(f"Error handling OPC data: {e}")
    
    async def _handle_modbus_data(self, data: Dict[str, Any]):
        """Handle Modbus data updates"""
        try:
            # Similar to OPC data handling but for Modbus tags
            mapping = self.config.control_mapping
            
            for tag_name, value in data.items():
                if tag_name in mapping.zone_temperature_tags:
                    zone = mapping.zone_temperature_tags.index(tag_name)
                    self.current_temperatures[f"zone_{zone}"] = float(value)
        
        except Exception as e:
            logger.error(f"Error handling Modbus data: {e}")
    
    async def _handle_opc_connection(self, connected: bool):
        """Handle OPC UA connection state changes"""
        if connected:
            logger.info("OPC UA connection established")
        else:
            logger.warning("OPC UA connection lost")
            self.system_alarms.append("WARNING: OPC UA connection lost")
    
    async def _handle_modbus_connection(self, connected: bool):
        """Handle Modbus connection state changes"""
        if connected:
            logger.info("Modbus connection established")
        else:
            logger.warning("Modbus connection lost")
            self.system_alarms.append("WARNING: Modbus connection lost")
    
    async def _handle_physics_output(self, output: Dict[str, Any]):
        """Handle physics simulation output"""
        try:
            # Use physics simulation results to enhance control
            if 'zone_temperatures' in output:
                predicted_temps = output['zone_temperatures']
                # Could use for predictive control
            
            if 'energy_cost' in output:
                energy_cost = output['energy_cost']
                # Could use for optimization
        
        except Exception as e:
            logger.error(f"Error handling physics output: {e}")
    
    def _prepare_twin_measurements(self) -> Dict[str, Any]:
        """Prepare measurements for digital twin update"""
        return {
            'temperatures': self.current_temperatures.copy(),
            'heating_powers': self.current_heating_powers.copy(),
            'setpoints': self.current_setpoints.copy(),
            'timestamp': datetime.now().isoformat()
        }
    
    def _get_all_tag_names(self) -> List[str]:
        """Get all SCADA tag names"""
        mapping = self.config.control_mapping
        tags = []
        
        tags.extend(mapping.zone_temperature_tags)
        tags.extend(mapping.heating_power_tags)
        tags.extend(mapping.heating_enable_tags)
        tags.extend(mapping.target_temperature_tags)
        tags.extend(mapping.pump_status_tags)
        tags.extend(mapping.alarm_tags)
        
        if mapping.ambient_temperature_tag:
            tags.append(mapping.ambient_temperature_tag)
        if mapping.outlet_temperature_tag:
            tags.append(mapping.outlet_temperature_tag)
        if mapping.tank_level_tag:
            tags.append(mapping.tank_level_tag)
        if mapping.emergency_stop_tag:
            tags.append(mapping.emergency_stop_tag)
        if mapping.flow_rate_setpoint_tag:
            tags.append(mapping.flow_rate_setpoint_tag)
        if mapping.power_consumption_tag:
            tags.append(mapping.power_consumption_tag)
        if mapping.energy_cost_tag:
            tags.append(mapping.energy_cost_tag)
        
        return tags
    
    async def _switch_to_manual_control(self):
        """Switch to manual control mode"""
        logger.info("Switching to manual control mode")
        # In manual mode, operators control heating directly through SCADA
    
    async def _switch_to_automatic_control(self):
        """Switch to automatic control mode"""
        logger.info("Switching to automatic control mode")
        # Resume automatic PID control
    
    def register_data_callback(self, callback: Callable):
        """Register callback for data updates"""
        self.data_callbacks.append(callback)
    
    def register_alarm_callback(self, callback: Callable):
        """Register callback for alarm notifications"""
        self.alarm_callbacks.append(callback)
    
    def register_control_callback(self, callback: Callable):
        """Register callback for control actions"""
        self.control_callbacks.append(callback)


class TankControlFactory:
    """Factory for creating tank control configurations based on SCADA interface"""
    
    @staticmethod
    def create_asphalt_tank_config(tank_id: str, scada_server_url: str) -> TankControlConfiguration:
        """Create configuration for asphalt tank based on standard SCADA layout"""
        
        # Tank configuration based on typical asphalt plant setup
        tank_config = TankConfiguration(
            tank_id=tank_id,
            tank_capacity=50000.0,  # 50,000 liters
            tank_diameter=6.0,
            tank_height=8.0,
            number_of_heating_zones=4,
            heating_element_power=50000.0,  # 50kW per zone
            target_temperature=160.0,
            min_operating_temp=140.0,
            max_operating_temp=180.0,
            safety_temp_limit=200.0
        )
        
        # Control mapping for typical SCADA tags
        control_mapping = TankControlMapping(
            tank_id=tank_id,
            zone_temperature_tags=[
                f"Tank{tank_id}.Zone1.Temperature",
                f"Tank{tank_id}.Zone2.Temperature", 
                f"Tank{tank_id}.Zone3.Temperature",
                f"Tank{tank_id}.Zone4.Temperature"
            ],
            ambient_temperature_tag=f"Plant.Ambient.Temperature",
            outlet_temperature_tag=f"Tank{tank_id}.Outlet.Temperature",
            heating_power_tags=[
                f"Tank{tank_id}.Zone1.HeatingPower",
                f"Tank{tank_id}.Zone2.HeatingPower",
                f"Tank{tank_id}.Zone3.HeatingPower", 
                f"Tank{tank_id}.Zone4.HeatingPower"
            ],
            heating_enable_tags=[
                f"Tank{tank_id}.Zone1.HeatingEnable",
                f"Tank{tank_id}.Zone2.HeatingEnable",
                f"Tank{tank_id}.Zone3.HeatingEnable",
                f"Tank{tank_id}.Zone4.HeatingEnable"
            ],
            target_temperature_tags=[
                f"Tank{tank_id}.Zone1.Setpoint",
                f"Tank{tank_id}.Zone2.Setpoint",
                f"Tank{tank_id}.Zone3.Setpoint",
                f"Tank{tank_id}.Zone4.Setpoint"
            ],
            tank_level_tag=f"Tank{tank_id}.Level",
            emergency_stop_tag=f"Tank{tank_id}.EmergencyStop",
            power_consumption_tag=f"Tank{tank_id}.PowerConsumption",
            energy_cost_tag=f"Tank{tank_id}.EnergyCost"
        )
        
        # OPC UA configuration
        opc_config = OPCConfiguration(
            endpoint_url=scada_server_url,
            session_timeout=60000,
            tags=[
                OPCTag(name=tag, node_id=f"ns=2;s={tag}", data_type="float")
                for tag in control_mapping.zone_temperature_tags +
                         control_mapping.heating_power_tags +
                         control_mapping.target_temperature_tags +
                         [control_mapping.ambient_temperature_tag,
                          control_mapping.outlet_temperature_tag,
                          control_mapping.tank_level_tag,
                          control_mapping.power_consumption_tag,
                          control_mapping.energy_cost_tag]
            ] + [
                OPCTag(name=tag, node_id=f"ns=2;s={tag}", data_type="bool")
                for tag in control_mapping.heating_enable_tags +
                         [control_mapping.emergency_stop_tag]
            ]
        )
        
        return TankControlConfiguration(
            tank_config=tank_config,
            control_mapping=control_mapping,
            opc_config=opc_config,
            control_mode=ControlMode.AUTOMATIC,
            update_frequency=1.0,
            emergency_temperature_limit=200.0,
            min_safe_temperature=100.0,
            max_heating_power=200000.0,  # Total 200kW for 4 zones
            energy_optimization_enabled=True
        )