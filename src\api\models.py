"""
Pydantic Models for API Request/Response
"""

from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
from datetime import datetime
from enum import Enum


class TwinCreateRequest(BaseModel):
    """Request model for creating a digital twin"""
    twin_id: str = Field(..., description="Unique identifier for the twin")
    name: str = Field(..., description="Human-readable name for the twin")
    description: str = Field("", description="Description of the twin")
    physical_asset_id: str = Field(..., description="ID of the physical asset")
    model_version: str = Field("v1.0", description="Model version")
    update_frequency: float = Field(1.0, description="Update frequency in Hz")
    synchronization_threshold: float = Field(0.1, description="Synchronization threshold")
    physics_parameters: Optional[Dict[str, Any]] = Field(None, description="Physics parameters")
    scada_tags: Optional[List[str]] = Field(None, description="SCADA tag names")
    auto_start: bool = Field(False, description="Auto-start the twin after creation")

    class Config:
        schema_extra = {
            "example": {
                "twin_id": "asphalt_heater_001",
                "name": "Asphalt Heater System 1",
                "description": "Digital twin for asphalt heating system",
                "physical_asset_id": "heater_001",
                "model_version": "v1.0",
                "update_frequency": 5.0,
                "synchronization_threshold": 0.05,
                "physics_parameters": {
                    "thermal_conductivity": 0.5,
                    "specific_heat": 920.0
                },
                "scada_tags": ["temperature_01", "heat_flux_01"],
                "auto_start": True
            }
        }


class TwinUpdateRequest(BaseModel):
    """Request model for updating twin state"""
    measurements: Dict[str, Any] = Field(..., description="Measurement data")

    class Config:
        schema_extra = {
            "example": {
                "measurements": {
                    "temperature_01": 85.5,
                    "temperature_02": 82.3,
                    "heat_flux_01": 1250.0,
                    "control_output_01": 75.0
                }
            }
        }


class TwinResponse(BaseModel):
    """Response model for twin operations"""
    twin_id: str = Field(..., description="Twin identifier")
    status: str = Field(..., description="Operation status")
    message: str = Field(..., description="Status message")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")

    class Config:
        schema_extra = {
            "example": {
                "twin_id": "asphalt_heater_001",
                "status": "created",
                "message": "Digital twin created successfully",
                "timestamp": "2023-10-01T12:00:00Z"
            }
        }


class SCADADataRequest(BaseModel):
    """Request model for SCADA data operations"""
    tag_name: str = Field(..., description="SCADA tag name")
    value: Any = Field(..., description="Tag value")

    class Config:
        schema_extra = {
            "example": {
                "tag_name": "control_output_01",
                "value": 75.0
            }
        }


class SCADADataResponse(BaseModel):
    """Response model for SCADA operations"""
    success: bool = Field(..., description="Operation success")
    message: str = Field(..., description="Response message")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "Tag written successfully",
                "timestamp": "2023-10-01T12:00:00Z"
            }
        }


class SimulationAction(str, Enum):
    """Simulation control actions"""
    START = "start"
    STOP = "stop"
    PAUSE = "pause"
    RESUME = "resume"
    RESET = "reset"


class SimulationControlRequest(BaseModel):
    """Request model for simulation control"""
    action: SimulationAction = Field(..., description="Control action")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Action parameters")

    class Config:
        schema_extra = {
            "example": {
                "action": "start",
                "parameters": {
                    "real_time_factor": 1.0,
                    "max_simulation_time": 3600.0
                }
            }
        }


class SimulationStatusResponse(BaseModel):
    """Response model for simulation status"""
    status: str = Field(..., description="Simulation status")
    message: str = Field(..., description="Status message")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")

    class Config:
        schema_extra = {
            "example": {
                "status": "running",
                "message": "Simulation started successfully",
                "timestamp": "2023-10-01T12:00:00Z"
            }
        }


class HealthStatus(str, Enum):
    """Health status values"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNAVAILABLE = "unavailable"


class HealthResponse(BaseModel):
    """Response model for health check"""
    status: HealthStatus = Field(..., description="Overall health status")
    timestamp: datetime = Field(default_factory=datetime.now, description="Health check timestamp")
    version: str = Field(..., description="Application version")
    components: Dict[str, str] = Field(..., description="Component health status")

    class Config:
        schema_extra = {
            "example": {
                "status": "healthy",
                "timestamp": "2023-10-01T12:00:00Z",
                "version": "1.0.0",
                "components": {
                    "twin_engine": "healthy",
                    "scada_opc_ua": "healthy",
                    "scada_modbus": "unavailable",
                    "database": "healthy"
                }
            }
        }


class TwinStateResponse(BaseModel):
    """Response model for twin state"""
    twin_id: str = Field(..., description="Twin identifier")
    timestamp: datetime = Field(..., description="State timestamp")
    temperature_profile: List[float] = Field(..., description="Temperature profile")
    heat_flux: List[float] = Field(..., description="Heat flux values")
    control_inputs: Dict[str, float] = Field(..., description="Control inputs")
    measured_outputs: Dict[str, float] = Field(..., description="Measured outputs")
    predicted_outputs: Dict[str, float] = Field(..., description="Predicted outputs")
    deviation_metrics: Dict[str, float] = Field(..., description="Deviation metrics")
    model_confidence: float = Field(..., description="Model confidence")
    simulation_step: int = Field(..., description="Simulation step number")

    class Config:
        schema_extra = {
            "example": {
                "twin_id": "asphalt_heater_001",
                "timestamp": "2023-10-01T12:00:00Z",
                "temperature_profile": [20.0, 25.0, 30.0, 35.0, 40.0],
                "heat_flux": [100.0, 150.0, 200.0],
                "control_inputs": {"heater_1": 75.0, "heater_2": 80.0},
                "measured_outputs": {"temp_sensor_1": 85.5, "temp_sensor_2": 82.3},
                "predicted_outputs": {"temp_sensor_1": 85.2, "temp_sensor_2": 82.1},
                "deviation_metrics": {"temp_sensor_1": 0.3, "temp_sensor_2": 0.2},
                "model_confidence": 0.95,
                "simulation_step": 1250
            }
        }


class AlertSeverity(str, Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AlertRequest(BaseModel):
    """Request model for creating alerts"""
    title: str = Field(..., description="Alert title")
    message: str = Field(..., description="Alert message")
    severity: AlertSeverity = Field(..., description="Alert severity")
    source: str = Field(..., description="Alert source")
    tags: Optional[Dict[str, str]] = Field(None, description="Alert tags")

    class Config:
        schema_extra = {
            "example": {
                "title": "High Temperature Alert",
                "message": "Temperature sensor reading exceeds threshold",
                "severity": "warning",
                "source": "twin_asphalt_heater_001",
                "tags": {
                    "sensor": "temperature_01",
                    "threshold": "180.0",
                    "value": "185.5"
                }
            }
        }


class AlertResponse(BaseModel):
    """Response model for alerts"""
    alert_id: str = Field(..., description="Alert identifier")
    status: str = Field(..., description="Alert status")
    created_at: datetime = Field(default_factory=datetime.now, description="Alert creation time")

    class Config:
        schema_extra = {
            "example": {
                "alert_id": "alert_12345",
                "status": "created",
                "created_at": "2023-10-01T12:00:00Z"
            }
        }


class MetricsRequest(BaseModel):
    """Request model for metrics queries"""
    metric_names: List[str] = Field(..., description="Metric names to query")
    start_time: Optional[datetime] = Field(None, description="Query start time")
    end_time: Optional[datetime] = Field(None, description="Query end time")
    aggregation: Optional[str] = Field("avg", description="Aggregation method")
    interval: Optional[str] = Field("1m", description="Time interval")

    class Config:
        schema_extra = {
            "example": {
                "metric_names": ["temperature", "heat_flux", "deviation"],
                "start_time": "2023-10-01T10:00:00Z",
                "end_time": "2023-10-01T12:00:00Z",
                "aggregation": "avg",
                "interval": "5m"
            }
        }


class MetricsResponse(BaseModel):
    """Response model for metrics data"""
    metrics: Dict[str, List[Dict[str, Any]]] = Field(..., description="Metrics data")
    query_info: Dict[str, Any] = Field(..., description="Query information")

    class Config:
        schema_extra = {
            "example": {
                "metrics": {
                    "temperature": [
                        {"timestamp": "2023-10-01T10:00:00Z", "value": 85.5},
                        {"timestamp": "2023-10-01T10:05:00Z", "value": 87.2}
                    ],
                    "heat_flux": [
                        {"timestamp": "2023-10-01T10:00:00Z", "value": 1250.0},
                        {"timestamp": "2023-10-01T10:05:00Z", "value": 1275.0}
                    ]
                },
                "query_info": {
                    "total_points": 24,
                    "query_time_ms": 45
                }
            }
        }


class ConfigUpdateRequest(BaseModel):
    """Request model for configuration updates"""
    section: str = Field(..., description="Configuration section")
    updates: Dict[str, Any] = Field(..., description="Configuration updates")

    class Config:
        schema_extra = {
            "example": {
                "section": "simulation.physics_parameters",
                "updates": {
                    "thermal_conductivity": 0.6,
                    "specific_heat": 950.0
                }
            }
        }


class ConfigUpdateResponse(BaseModel):
    """Response model for configuration updates"""
    success: bool = Field(..., description="Update success")
    message: str = Field(..., description="Update message")
    restart_required: bool = Field(False, description="Whether restart is required")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "Configuration updated successfully",
                "restart_required": False
            }
        }