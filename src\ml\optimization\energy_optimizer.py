"""
Energy Optimization Models
Cost optimization algorithms for asphalt tank heating control
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from abc import ABC, abstractmethod
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class EnergyRate:
    """Energy rate structure"""
    peak_rate: float  # $/kWh during peak hours
    off_peak_rate: float  # $/kWh during off-peak hours
    demand_charge: float  # $/kW for peak demand
    peak_hours: List[Tuple[int, int]]  # List of (start_hour, end_hour) for peak periods
    weekday_only: bool = True  # Whether peak rates apply only to weekdays


@dataclass
class HeatingConstraints:
    """Constraints for heating optimization"""
    min_temperature: float = 140.0  # Minimum temperature to maintain liquid state
    max_temperature: float = 180.0  # Maximum safe temperature
    max_heating_rate: float = 5.0  # Maximum temperature change per hour (°C/hr)
    max_power: float = 1000.0  # Maximum power consumption (kW)
    min_power: float = 0.0  # Minimum power consumption (kW)
    tank_capacity: float = 10000.0  # Tank capacity (gallons)
    heat_loss_coefficient: float = 0.02  # Heat loss coefficient (per hour)


@dataclass
class OptimizationResult:
    """Result of energy optimization"""
    schedule: pd.DataFrame  # Optimal heating schedule
    total_cost: float  # Total energy cost
    peak_demand: float  # Peak power demand
    energy_consumption: float  # Total energy consumption
    temperature_profile: np.ndarray  # Predicted temperature profile
    cost_breakdown: Dict[str, float]  # Breakdown of costs
    feasible: bool = True  # Whether solution is feasible
    optimization_time: float = 0.0  # Time taken for optimization


class EnergyOptimizer(ABC):
    """Abstract base class for energy optimization algorithms"""
    
    @abstractmethod
    def optimize(self, 
                initial_temperature: float,
                forecast_horizon: int,
                weather_forecast: Optional[pd.DataFrame] = None,
                demand_forecast: Optional[np.ndarray] = None) -> OptimizationResult:
        """Optimize heating schedule"""
        pass


class MIPEnergyOptimizer(EnergyOptimizer):
    """Mixed-Integer Programming optimizer for energy scheduling"""
    
    def __init__(self, 
                 energy_rates: EnergyRate,
                 constraints: HeatingConstraints,
                 solver: str = 'PULP_CBC_CMD'):
        """
        Args:
            energy_rates: Energy rate structure
            constraints: Heating constraints
            solver: Optimization solver to use
        """
        self.energy_rates = energy_rates
        self.constraints = constraints
        self.solver = solver
        
        # Try to import optimization library
        try:
            import pulp
            self.pulp = pulp
            self.solver_available = True
        except ImportError:
            logger.warning("PuLP not available. Using heuristic optimization.")
            self.pulp = None
            self.solver_available = False
    
    def optimize(self, 
                initial_temperature: float,
                forecast_horizon: int = 24,
                weather_forecast: Optional[pd.DataFrame] = None,
                demand_forecast: Optional[np.ndarray] = None,
                time_resolution: float = 1.0) -> OptimizationResult:
        """
        Optimize heating schedule using Mixed-Integer Programming
        
        Args:
            initial_temperature: Current tank temperature
            forecast_horizon: Optimization horizon in hours
            weather_forecast: Weather forecast data
            demand_forecast: Heating demand forecast
            time_resolution: Time resolution in hours
        
        Returns:
            Optimization result with optimal schedule
        """
        import time
        start_time = time.time()
        
        logger.info(f"Starting MIP optimization for {forecast_horizon} hour horizon")
        
        if not self.solver_available:
            return self._heuristic_optimization(
                initial_temperature, forecast_horizon, weather_forecast, demand_forecast
            )
        
        # Create time periods
        periods = int(forecast_horizon / time_resolution)
        time_periods = range(periods)
        
        # Create optimization problem
        prob = self.pulp.LpProblem("Energy_Optimization", self.pulp.LpMinimize)
        
        # Decision variables
        # Power consumption at each time period (kW)
        power = {t: self.pulp.LpVariable(f"power_{t}", 
                                        lowBound=self.constraints.min_power,
                                        upBound=self.constraints.max_power,
                                        cat='Continuous') for t in time_periods}
        
        # Binary variables for on/off decisions
        heating_on = {t: self.pulp.LpVariable(f"heating_{t}", cat='Binary') for t in time_periods}
        
        # Peak demand variable
        peak_demand = self.pulp.LpVariable("peak_demand", lowBound=0, cat='Continuous')
        
        # Temperature variables
        temperature = {t: self.pulp.LpVariable(f"temp_{t}", 
                                              lowBound=self.constraints.min_temperature,
                                              upBound=self.constraints.max_temperature,
                                              cat='Continuous') for t in time_periods}
        
        # Objective function: minimize total cost
        energy_cost = 0
        demand_cost = self.energy_rates.demand_charge * peak_demand
        
        for t in time_periods:
            hour = t % 24
            rate = self._get_hourly_rate(hour)
            energy_cost += power[t] * time_resolution * rate
        
        total_cost = energy_cost + demand_cost
        prob += total_cost
        
        # Constraints
        
        # 1. Temperature dynamics
        for t in time_periods:
            if t == 0:
                # Initial temperature constraint
                heat_input = power[t] * time_resolution * 3.412  # Convert kW to BTU/hr
                heat_loss = self._calculate_heat_loss(initial_temperature, weather_forecast, t)
                temp_change = (heat_input - heat_loss) / self._get_thermal_mass()
                prob += temperature[t] == initial_temperature + temp_change
            else:
                # Temperature propagation
                heat_input = power[t] * time_resolution * 3.412
                heat_loss = self._calculate_heat_loss(temperature[t-1], weather_forecast, t)
                temp_change = (heat_input - heat_loss) / self._get_thermal_mass()
                prob += temperature[t] == temperature[t-1] + temp_change
        
        # 2. Power constraints
        for t in time_periods:
            # Power on/off constraint
            prob += power[t] <= self.constraints.max_power * heating_on[t]
            prob += power[t] >= self.constraints.min_power * heating_on[t]
            
            # Peak demand constraint
            prob += peak_demand >= power[t]
        
        # 3. Temperature rate constraints
        for t in time_periods[1:]:
            max_temp_change = self.constraints.max_heating_rate * time_resolution
            prob += temperature[t] - temperature[t-1] <= max_temp_change
            prob += temperature[t-1] - temperature[t] <= max_temp_change
        
        # 4. Demand constraints (if provided)
        if demand_forecast is not None:
            for t in time_periods:
                if t < len(demand_forecast):
                    # Ensure sufficient heating for demand
                    min_temp_for_demand = self.constraints.min_temperature + demand_forecast[t] * 0.1
                    prob += temperature[t] >= min_temp_for_demand
        
        # Solve the problem
        if self.solver == 'PULP_CBC_CMD':
            prob.solve(self.pulp.PULP_CBC_CMD(msg=0))
        else:
            prob.solve()
        
        # Extract results
        if prob.status == self.pulp.LpStatusOptimal:
            feasible = True
            
            # Extract optimal schedule
            schedule_data = []
            temperature_profile = []
            
            for t in time_periods:
                schedule_data.append({
                    'hour': t,
                    'power_kw': power[t].varValue,
                    'heating_on': bool(heating_on[t].varValue),
                    'temperature': temperature[t].varValue,
                    'hourly_cost': power[t].varValue * time_resolution * self._get_hourly_rate(t % 24)
                })
                temperature_profile.append(temperature[t].varValue)
            
            schedule_df = pd.DataFrame(schedule_data)
            
            # Calculate costs
            total_energy_cost = sum(row['hourly_cost'] for row in schedule_data)
            total_demand_cost = self.energy_rates.demand_charge * peak_demand.varValue
            
            cost_breakdown = {
                'energy_cost': total_energy_cost,
                'demand_cost': total_demand_cost,
                'total_cost': total_energy_cost + total_demand_cost
            }
            
            optimization_time = time.time() - start_time
            
            logger.info(f"MIP optimization completed successfully in {optimization_time:.2f}s")
            logger.info(f"Total cost: ${cost_breakdown['total_cost']:.2f}")
            
            return OptimizationResult(
                schedule=schedule_df,
                total_cost=cost_breakdown['total_cost'],
                peak_demand=peak_demand.varValue,
                energy_consumption=schedule_df['power_kw'].sum() * time_resolution,
                temperature_profile=np.array(temperature_profile),
                cost_breakdown=cost_breakdown,
                feasible=feasible,
                optimization_time=optimization_time
            )
        
        else:
            logger.error(f"Optimization failed with status: {prob.status}")
            return self._create_fallback_result(initial_temperature, forecast_horizon)
    
    def _get_hourly_rate(self, hour: int) -> float:
        """Get energy rate for given hour"""
        for peak_start, peak_end in self.energy_rates.peak_hours:
            if peak_start <= hour < peak_end:
                return self.energy_rates.peak_rate
        return self.energy_rates.off_peak_rate
    
    def _calculate_heat_loss(self, temperature: float, weather_forecast: Optional[pd.DataFrame], 
                           time_period: int) -> float:
        """Calculate heat loss for given temperature and weather conditions"""
        # Base heat loss
        ambient_temp = 20.0  # Default ambient temperature
        
        if weather_forecast is not None and time_period < len(weather_forecast):
            ambient_temp = weather_forecast.iloc[time_period].get('temperature', 20.0)
        
        # Heat loss proportional to temperature difference
        temp_diff = max(0, temperature - ambient_temp)
        heat_loss = self.constraints.heat_loss_coefficient * temp_diff * self._get_thermal_mass()
        
        return heat_loss
    
    def _get_thermal_mass(self) -> float:
        """Calculate thermal mass of the tank"""
        # Simplified thermal mass calculation
        # Assumes asphalt density ~2000 kg/m³, specific heat ~0.9 kJ/kg·K
        volume_m3 = self.constraints.tank_capacity * 0.00378541  # gallons to m³
        mass_kg = volume_m3 * 2000  # kg
        thermal_mass = mass_kg * 0.9  # kJ/K
        return thermal_mass * 947.817  # Convert to BTU/°F
    
    def _heuristic_optimization(self, initial_temperature: float, forecast_horizon: int,
                               weather_forecast: Optional[pd.DataFrame],
                               demand_forecast: Optional[np.ndarray]) -> OptimizationResult:
        """Fallback heuristic optimization when MIP solver not available"""
        logger.info("Using heuristic optimization (MIP solver not available)")
        
        # Simple heuristic: heat during off-peak hours, maintain during peak hours
        schedule_data = []
        current_temp = initial_temperature
        total_cost = 0
        peak_demand = 0
        
        for hour in range(forecast_horizon):
            hour_of_day = hour % 24
            rate = self._get_hourly_rate(hour_of_day)
            
            # Determine heating strategy
            if rate == self.energy_rates.off_peak_rate:
                # Off-peak: heat aggressively if below target
                target_temp = self.constraints.max_temperature * 0.9
                if current_temp < target_temp:
                    power = self.constraints.max_power * 0.8
                    heating_on = True
                else:
                    power = self.constraints.max_power * 0.2
                    heating_on = True
            else:
                # Peak: minimal heating to maintain temperature
                if current_temp < self.constraints.min_temperature + 5:
                    power = self.constraints.max_power * 0.3
                    heating_on = True
                else:
                    power = 0
                    heating_on = False
            
            # Update temperature
            heat_input = power * 3.412  # kW to BTU/hr
            heat_loss = self._calculate_heat_loss(current_temp, weather_forecast, hour)
            temp_change = (heat_input - heat_loss) / self._get_thermal_mass()
            current_temp += temp_change
            
            # Ensure constraints
            current_temp = max(self.constraints.min_temperature, 
                             min(self.constraints.max_temperature, current_temp))
            
            # Calculate cost
            hourly_cost = power * rate
            total_cost += hourly_cost
            peak_demand = max(peak_demand, power)
            
            schedule_data.append({
                'hour': hour,
                'power_kw': power,
                'heating_on': heating_on,
                'temperature': current_temp,
                'hourly_cost': hourly_cost
            })
        
        schedule_df = pd.DataFrame(schedule_data)
        
        # Add demand cost
        demand_cost = self.energy_rates.demand_charge * peak_demand
        total_cost += demand_cost
        
        cost_breakdown = {
            'energy_cost': total_cost - demand_cost,
            'demand_cost': demand_cost,
            'total_cost': total_cost
        }
        
        return OptimizationResult(
            schedule=schedule_df,
            total_cost=total_cost,
            peak_demand=peak_demand,
            energy_consumption=schedule_df['power_kw'].sum(),
            temperature_profile=schedule_df['temperature'].values,
            cost_breakdown=cost_breakdown,
            feasible=True,
            optimization_time=0.1
        )
    
    def _create_fallback_result(self, initial_temperature: float, 
                               forecast_horizon: int) -> OptimizationResult:
        """Create fallback result when optimization fails"""
        schedule_data = []
        for hour in range(forecast_horizon):
            schedule_data.append({
                'hour': hour,
                'power_kw': self.constraints.max_power * 0.5,
                'heating_on': True,
                'temperature': initial_temperature,
                'hourly_cost': 0
            })
        
        schedule_df = pd.DataFrame(schedule_data)
        
        return OptimizationResult(
            schedule=schedule_df,
            total_cost=0,
            peak_demand=0,
            energy_consumption=0,
            temperature_profile=np.full(forecast_horizon, initial_temperature),
            cost_breakdown={'energy_cost': 0, 'demand_cost': 0, 'total_cost': 0},
            feasible=False,
            optimization_time=0
        )


class RLEnergyOptimizer(EnergyOptimizer):
    """Reinforcement Learning-based energy optimizer"""
    
    def __init__(self, 
                 energy_rates: EnergyRate,
                 constraints: HeatingConstraints):
        """
        Args:
            energy_rates: Energy rate structure
            constraints: Heating constraints
        """
        self.energy_rates = energy_rates
        self.constraints = constraints
        self.q_table = {}  # Simple Q-learning table
        self.learning_rate = 0.1
        self.discount_factor = 0.95
        self.epsilon = 0.1  # Exploration rate
        
        logger.info("RL Energy Optimizer initialized (simplified Q-learning)")
    
    def optimize(self, 
                initial_temperature: float,
                forecast_horizon: int = 24,
                weather_forecast: Optional[pd.DataFrame] = None,
                demand_forecast: Optional[np.ndarray] = None) -> OptimizationResult:
        """
        Optimize using reinforcement learning (simplified implementation)
        """
        logger.info("Using RL-based optimization (simplified)")
        
        # For now, use a simple rule-based approach
        # In a full implementation, this would use trained RL agents
        schedule_data = []
        current_temp = initial_temperature
        total_cost = 0
        peak_demand = 0
        
        for hour in range(forecast_horizon):
            # State: (temperature_range, hour_of_day, rate_type)
            temp_range = self._discretize_temperature(current_temp)
            hour_of_day = hour % 24
            rate_type = 'peak' if self._get_hourly_rate(hour_of_day) == self.energy_rates.peak_rate else 'off_peak'
            
            state = (temp_range, hour_of_day, rate_type)
            
            # Select action (power level)
            action = self._select_action(state)
            power = action * self.constraints.max_power / 10  # Convert to actual power
            
            # Execute action and observe reward
            heat_input = power * 3.412
            heat_loss = self._calculate_heat_loss(current_temp, weather_forecast, hour)
            temp_change = (heat_input - heat_loss) / self._get_thermal_mass()
            new_temp = current_temp + temp_change
            
            # Ensure constraints
            new_temp = max(self.constraints.min_temperature, 
                          min(self.constraints.max_temperature, new_temp))
            
            # Calculate reward (negative cost)
            hourly_cost = power * self._get_hourly_rate(hour_of_day)
            penalty = 0
            
            if new_temp < self.constraints.min_temperature:
                penalty += 1000  # High penalty for going below minimum
            if new_temp > self.constraints.max_temperature:
                penalty += 500   # Penalty for overheating
            
            reward = -(hourly_cost + penalty)
            
            # Update Q-table (simplified)
            self._update_q_table(state, action, reward)
            
            # Store results
            total_cost += hourly_cost
            peak_demand = max(peak_demand, power)
            current_temp = new_temp
            
            schedule_data.append({
                'hour': hour,
                'power_kw': power,
                'heating_on': power > 0,
                'temperature': current_temp,
                'hourly_cost': hourly_cost
            })
        
        schedule_df = pd.DataFrame(schedule_data)
        
        # Add demand cost
        demand_cost = self.energy_rates.demand_charge * peak_demand
        total_cost += demand_cost
        
        cost_breakdown = {
            'energy_cost': total_cost - demand_cost,
            'demand_cost': demand_cost,
            'total_cost': total_cost
        }
        
        return OptimizationResult(
            schedule=schedule_df,
            total_cost=total_cost,
            peak_demand=peak_demand,
            energy_consumption=schedule_df['power_kw'].sum(),
            temperature_profile=schedule_df['temperature'].values,
            cost_breakdown=cost_breakdown,
            feasible=True,
            optimization_time=0.05
        )
    
    def _discretize_temperature(self, temperature: float) -> int:
        """Discretize temperature for state representation"""
        temp_ranges = [0, 130, 140, 150, 160, 170, 180, 200]
        for i, threshold in enumerate(temp_ranges[1:]):
            if temperature < threshold:
                return i
        return len(temp_ranges) - 2
    
    def _select_action(self, state) -> int:
        """Select action using epsilon-greedy policy"""
        if np.random.random() < self.epsilon:
            # Explore: random action
            return np.random.randint(0, 11)  # 0-10 power levels
        else:
            # Exploit: best known action
            if state in self.q_table:
                return max(self.q_table[state], key=self.q_table[state].get)
            else:
                return 5  # Default medium power
    
    def _update_q_table(self, state, action: int, reward: float):
        """Update Q-table with new experience"""
        if state not in self.q_table:
            self.q_table[state] = {a: 0.0 for a in range(11)}
        
        # Simplified Q-learning update
        old_value = self.q_table[state][action]
        self.q_table[state][action] = old_value + self.learning_rate * (reward - old_value)
    
    def _get_hourly_rate(self, hour: int) -> float:
        """Get energy rate for given hour"""
        for peak_start, peak_end in self.energy_rates.peak_hours:
            if peak_start <= hour < peak_end:
                return self.energy_rates.peak_rate
        return self.energy_rates.off_peak_rate
    
    def _calculate_heat_loss(self, temperature: float, weather_forecast: Optional[pd.DataFrame], 
                           time_period: int) -> float:
        """Calculate heat loss for given temperature and weather conditions"""
        ambient_temp = 20.0
        if weather_forecast is not None and time_period < len(weather_forecast):
            ambient_temp = weather_forecast.iloc[time_period].get('temperature', 20.0)
        
        temp_diff = max(0, temperature - ambient_temp)
        thermal_mass = self.constraints.tank_capacity * 2000 * 0.9 * 947.817  # Simplified
        heat_loss = self.constraints.heat_loss_coefficient * temp_diff * thermal_mass
        return heat_loss
    
    def _get_thermal_mass(self) -> float:
        """Calculate thermal mass of the tank"""
        volume_m3 = self.constraints.tank_capacity * 0.00378541
        mass_kg = volume_m3 * 2000
        thermal_mass = mass_kg * 0.9
        return thermal_mass * 947.817


# This will be replaced by enhanced version above


class DemandResponseOptimizer(EnergyOptimizer):
    """Demand response optimizer for grid interaction"""
    
    def __init__(self, energy_rates: EnergyRate, constraints: HeatingConstraints):
        self.energy_rates = energy_rates
        self.constraints = constraints
        self.dr_events = []  # Demand response events
        
    def add_demand_response_event(self, start_hour: int, end_hour: int, 
                                 reduction_percentage: float, incentive_rate: float):
        """Add demand response event"""
        self.dr_events.append({
            'start_hour': start_hour,
            'end_hour': end_hour,
            'reduction_percentage': reduction_percentage,
            'incentive_rate': incentive_rate  # $/kWh reduced
        })
    
    def optimize(self, initial_temperature: float, forecast_horizon: int = 24,
                weather_forecast: Optional[pd.DataFrame] = None,
                demand_forecast: Optional[np.ndarray] = None) -> OptimizationResult:
        """Optimize with demand response considerations"""
        
        schedule_data = []
        current_temp = initial_temperature
        total_cost = 0
        total_incentives = 0
        peak_demand = 0
        
        for hour in range(forecast_horizon):
            hour_of_day = hour % 24
            base_rate = self._get_hourly_rate(hour_of_day)
            
            # Check for demand response events
            dr_event = self._get_active_dr_event(hour_of_day)
            
            if dr_event:
                # During DR event: reduce power consumption
                baseline_power = self.constraints.max_power * 0.5
                reduced_power = baseline_power * (1 - dr_event['reduction_percentage'])
                power = max(0, reduced_power)
                
                # Calculate incentive
                power_reduction = baseline_power - power
                incentive = power_reduction * dr_event['incentive_rate']
                total_incentives += incentive
                
                heating_on = power > 0
            else:
                # Normal operation: optimize based on rate
                if base_rate == self.energy_rates.off_peak_rate:
                    # Off-peak: heat to higher temperature
                    target_temp = self.constraints.max_temperature * 0.95
                    if current_temp < target_temp:
                        power = self.constraints.max_power * 0.8
                        heating_on = True
                    else:
                        power = self.constraints.max_power * 0.3
                        heating_on = True
                else:
                    # Peak: maintain minimum
                    if current_temp < self.constraints.min_temperature + 10:
                        power = self.constraints.max_power * 0.4
                        heating_on = True
                    else:
                        power = 0
                        heating_on = False
            
            # Update temperature
            heat_input = power * 3.412
            heat_loss = self._calculate_heat_loss(current_temp, weather_forecast, hour)
            temp_change = (heat_input - heat_loss) / self._get_thermal_mass()
            current_temp += temp_change
            
            # Ensure constraints
            current_temp = max(self.constraints.min_temperature,
                             min(self.constraints.max_temperature, current_temp))
            
            # Calculate cost
            hourly_cost = power * base_rate
            total_cost += hourly_cost
            peak_demand = max(peak_demand, power)
            
            schedule_data.append({
                'hour': hour,
                'power_kw': power,
                'heating_on': heating_on,
                'temperature': current_temp,
                'hourly_cost': hourly_cost,
                'dr_event': dr_event is not None,
                'incentive': incentive if dr_event else 0
            })
        
        schedule_df = pd.DataFrame(schedule_data)
        
        # Add demand cost and subtract incentives
        demand_cost = self.energy_rates.demand_charge * peak_demand
        net_cost = total_cost + demand_cost - total_incentives
        
        cost_breakdown = {
            'energy_cost': total_cost,
            'demand_cost': demand_cost,
            'dr_incentives': -total_incentives,
            'total_cost': net_cost
        }
        
        return OptimizationResult(
            schedule=schedule_df,
            total_cost=net_cost,
            peak_demand=peak_demand,
            energy_consumption=schedule_df['power_kw'].sum(),
            temperature_profile=schedule_df['temperature'].values,
            cost_breakdown=cost_breakdown,
            feasible=True,
            optimization_time=0.05
        )
    
    def _get_active_dr_event(self, hour: int) -> Optional[Dict[str, Any]]:
        """Check if there's an active DR event"""
        for event in self.dr_events:
            if event['start_hour'] <= hour < event['end_hour']:
                return event
        return None
    
    def _get_hourly_rate(self, hour: int) -> float:
        """Get energy rate for given hour"""
        for peak_start, peak_end in self.energy_rates.peak_hours:
            if peak_start <= hour < peak_end:
                return self.energy_rates.peak_rate
        return self.energy_rates.off_peak_rate
    
    def _calculate_heat_loss(self, temperature: float, weather_forecast: Optional[pd.DataFrame],
                           time_period: int) -> float:
        """Calculate heat loss"""
        ambient_temp = 20.0
        if weather_forecast is not None and time_period < len(weather_forecast):
            ambient_temp = weather_forecast.iloc[time_period].get('temperature', 20.0)
        
        temp_diff = max(0, temperature - ambient_temp)
        thermal_mass = self._get_thermal_mass()
        heat_loss = self.constraints.heat_loss_coefficient * temp_diff * thermal_mass
        return heat_loss
    
    def _get_thermal_mass(self) -> float:
        """Calculate thermal mass"""
        volume_m3 = self.constraints.tank_capacity * 0.00378541
        mass_kg = volume_m3 * 2000
        thermal_mass = mass_kg * 0.9
        return thermal_mass * 947.817


class PredictiveEnergyOptimizer(EnergyOptimizer):
    """Predictive optimizer using ML temperature forecasts"""
    
    def __init__(self, energy_rates: EnergyRate, constraints: HeatingConstraints,
                 temperature_model=None):
        self.energy_rates = energy_rates
        self.constraints = constraints
        self.temperature_model = temperature_model
        
    def optimize(self, initial_temperature: float, forecast_horizon: int = 24,
                weather_forecast: Optional[pd.DataFrame] = None,
                demand_forecast: Optional[np.ndarray] = None) -> OptimizationResult:
        """Optimize using predictive temperature forecasting"""
        
        # If we have a temperature model, use it for predictions
        if self.temperature_model and weather_forecast is not None:
            # Prepare features for temperature prediction
            features = self._prepare_features(initial_temperature, weather_forecast)
            predicted_temps = self.temperature_model.predict(features, forecast_horizon)
        else:
            # Fallback to simple temperature evolution
            predicted_temps = self._simple_temperature_forecast(
                initial_temperature, forecast_horizon, weather_forecast
            )
        
        # Optimize based on predicted temperatures
        schedule_data = []
        total_cost = 0
        peak_demand = 0
        
        for hour in range(forecast_horizon):
            hour_of_day = hour % 24
            rate = self._get_hourly_rate(hour_of_day)
            
            # Predicted temperature without heating
            predicted_temp = predicted_temps[hour] if hour < len(predicted_temps) else initial_temperature
            
            # Determine heating need
            temp_deficit = max(0, self.constraints.min_temperature + 5 - predicted_temp)
            
            # Calculate required power to meet temperature target
            if temp_deficit > 0:
                # Need heating
                required_heat = temp_deficit * self._get_thermal_mass() / 3.412
                power = min(required_heat, self.constraints.max_power)
                heating_on = True
            else:
                # No heating needed, but consider pre-heating for peak periods
                if (rate == self.energy_rates.off_peak_rate and 
                    hour + 1 < forecast_horizon and
                    self._get_hourly_rate((hour + 1) % 24) == self.energy_rates.peak_rate):
                    # Pre-heat before peak period
                    power = self.constraints.max_power * 0.6
                    heating_on = True
                else:
                    power = 0
                    heating_on = False
            
            # Calculate cost
            hourly_cost = power * rate
            total_cost += hourly_cost
            peak_demand = max(peak_demand, power)
            
            # Update temperature with heating
            heat_input = power * 3.412
            heat_loss = self._calculate_heat_loss(predicted_temp, weather_forecast, hour)
            temp_change = (heat_input - heat_loss) / self._get_thermal_mass()
            final_temp = predicted_temp + temp_change
            
            schedule_data.append({
                'hour': hour,
                'power_kw': power,
                'heating_on': heating_on,
                'temperature': final_temp,
                'predicted_temp_no_heat': predicted_temp,
                'hourly_cost': hourly_cost
            })
        
        schedule_df = pd.DataFrame(schedule_data)
        
        # Add demand cost
        demand_cost = self.energy_rates.demand_charge * peak_demand
        total_cost += demand_cost
        
        cost_breakdown = {
            'energy_cost': total_cost - demand_cost,
            'demand_cost': demand_cost,
            'total_cost': total_cost
        }
        
        return OptimizationResult(
            schedule=schedule_df,
            total_cost=total_cost,
            peak_demand=peak_demand,
            energy_consumption=schedule_df['power_kw'].sum(),
            temperature_profile=schedule_df['temperature'].values,
            cost_breakdown=cost_breakdown,
            feasible=True,
            optimization_time=0.02
        )
    
    def _prepare_features(self, initial_temp: float, weather_forecast: pd.DataFrame) -> np.ndarray:
        """Prepare features for temperature prediction model"""
        # Create feature matrix (simplified)
        features = []
        for _, row in weather_forecast.iterrows():
            features.append([
                initial_temp,
                row.get('temperature', 20.0),
                row.get('humidity', 50.0),
                row.get('wind_speed', 5.0),
                0,  # energy_consumption (placeholder)
                0   # control_output (placeholder)
            ])
        
        return np.array(features)
    
    def _simple_temperature_forecast(self, initial_temp: float, horizon: int,
                                   weather_forecast: Optional[pd.DataFrame]) -> np.ndarray:
        """Simple temperature forecast without heating"""
        temps = [initial_temp]
        current_temp = initial_temp
        
        for hour in range(1, horizon):
            # Simple exponential decay towards ambient
            ambient_temp = 20.0
            if weather_forecast is not None and hour < len(weather_forecast):
                ambient_temp = weather_forecast.iloc[hour].get('temperature', 20.0)
            
            # Temperature decay
            decay_rate = 0.02  # 2% per hour
            temp_change = (ambient_temp - current_temp) * decay_rate
            current_temp += temp_change
            temps.append(current_temp)
        
        return np.array(temps)
    
    def _get_hourly_rate(self, hour: int) -> float:
        """Get energy rate for given hour"""
        for peak_start, peak_end in self.energy_rates.peak_hours:
            if peak_start <= hour < peak_end:
                return self.energy_rates.peak_rate
        return self.energy_rates.off_peak_rate
    
    def _calculate_heat_loss(self, temperature: float, weather_forecast: Optional[pd.DataFrame],
                           time_period: int) -> float:
        """Calculate heat loss"""
        ambient_temp = 20.0
        if weather_forecast is not None and time_period < len(weather_forecast):
            ambient_temp = weather_forecast.iloc[time_period].get('temperature', 20.0)
        
        temp_diff = max(0, temperature - ambient_temp)
        thermal_mass = self._get_thermal_mass()
        heat_loss = self.constraints.heat_loss_coefficient * temp_diff * thermal_mass
        return heat_loss
    
    def _get_thermal_mass(self) -> float:
        """Calculate thermal mass"""
        volume_m3 = self.constraints.tank_capacity * 0.00378541
        mass_kg = volume_m3 * 2000
        thermal_mass = mass_kg * 0.9
        return thermal_mass * 947.817


class EnergyOptimizationPipeline:
    """Complete energy optimization pipeline"""
    
    def __init__(self, optimizer: EnergyOptimizer):
        self.optimizer = optimizer
        self.optimization_history = []
        
    def run_optimization(self, tank_data: Dict[str, Any], 
                        weather_forecast: Optional[pd.DataFrame] = None,
                        demand_forecast: Optional[np.ndarray] = None) -> OptimizationResult:
        """Run optimization with data validation and post-processing"""
        
        # Validate inputs
        initial_temp = tank_data.get('current_temperature', 150.0)
        horizon = tank_data.get('forecast_horizon', 24)
        
        # Run optimization
        result = self.optimizer.optimize(
            initial_temperature=initial_temp,
            forecast_horizon=horizon,
            weather_forecast=weather_forecast,
            demand_forecast=demand_forecast
        )
        
        # Post-process results
        result = self._post_process_result(result, tank_data)
        
        # Store in history
        self.optimization_history.append({
            'timestamp': datetime.now(),
            'result': result,
            'tank_data': tank_data
        })
        
        # Keep only recent history
        if len(self.optimization_history) > 100:
            self.optimization_history = self.optimization_history[-50:]
        
        return result
    
    def _post_process_result(self, result: OptimizationResult, 
                           tank_data: Dict[str, Any]) -> OptimizationResult:
        """Post-process optimization result"""
        
        # Add metadata
        result.schedule['tank_id'] = tank_data.get('tank_id', 'unknown')
        result.schedule['optimization_timestamp'] = datetime.now()
        
        # Calculate additional metrics
        result.schedule['efficiency'] = self._calculate_efficiency(
            result.schedule['power_kw'], result.schedule['temperature']
        )
        
        # Add rolling averages
        result.schedule['power_ma_3h'] = result.schedule['power_kw'].rolling(3).mean()
        result.schedule['temp_ma_3h'] = result.schedule['temperature'].rolling(3).mean()
        
        return result
    
    def _calculate_efficiency(self, power: pd.Series, temperature: pd.Series) -> pd.Series:
        """Calculate heating efficiency"""
        # Simplified efficiency calculation
        efficiency = np.ones(len(power))
        
        for i in range(1, len(power)):
            if power.iloc[i] > 0:
                temp_rise = temperature.iloc[i] - temperature.iloc[i-1]
                efficiency[i] = max(0.5, min(1.0, temp_rise / (power.iloc[i] * 0.01)))
            else:
                efficiency[i] = 1.0
        
        return pd.Series(efficiency, index=power.index)
    
    def get_optimization_statistics(self) -> Dict[str, Any]:
        """Get optimization performance statistics"""
        if not self.optimization_history:
            return {}
        
        recent_results = [h['result'] for h in self.optimization_history[-10:]]
        
        return {
            'total_optimizations': len(self.optimization_history),
            'avg_cost': np.mean([r.total_cost for r in recent_results]),
            'avg_energy': np.mean([r.energy_consumption for r in recent_results]),
            'avg_peak_demand': np.mean([r.peak_demand for r in recent_results]),
            'feasibility_rate': np.mean([r.feasible for r in recent_results]),
            'avg_optimization_time': np.mean([r.optimization_time for r in recent_results])
        }


def create_energy_optimizer(optimizer_type: str = 'mip', 
                           energy_rates: Optional[EnergyRate] = None,
                           constraints: Optional[HeatingConstraints] = None,
                           **kwargs) -> EnergyOptimizer:
    """
    Factory function to create energy optimizers
    
    Args:
        optimizer_type: Type of optimizer ('mip', 'rl', 'demand_response', 'predictive')
        energy_rates: Energy rate structure
        constraints: Heating constraints
        **kwargs: Optimizer-specific parameters
    
    Returns:
        Energy optimizer instance
    """
    # Default energy rates
    if energy_rates is None:
        energy_rates = EnergyRate(
            peak_rate=0.15,    # $0.15/kWh during peak
            off_peak_rate=0.08, # $0.08/kWh during off-peak
            demand_charge=15.0,  # $15/kW demand charge
            peak_hours=[(8, 12), (17, 21)],  # 8-12 and 5-9 PM
            weekday_only=True
        )
    
    # Default constraints
    if constraints is None:
        constraints = HeatingConstraints(
            min_temperature=140.0,
            max_temperature=180.0,
            max_heating_rate=5.0,
            max_power=200.0,  # 200kW
            tank_capacity=50000.0,  # 50,000 gallons
            heat_loss_coefficient=0.02
        )
    
    if optimizer_type == 'mip':
        return MIPEnergyOptimizer(energy_rates, constraints, **kwargs)
    elif optimizer_type == 'rl':
        return RLEnergyOptimizer(energy_rates, constraints, **kwargs)
    elif optimizer_type == 'demand_response':
        return DemandResponseOptimizer(energy_rates, constraints, **kwargs)
    elif optimizer_type == 'predictive':
        temperature_model = kwargs.get('temperature_model', None)
        return PredictiveEnergyOptimizer(energy_rates, constraints, temperature_model)
    else:
        raise ValueError(f"Unknown optimizer type: {optimizer_type}")


# Example usage and testing
if __name__ == "__main__":
    print("Testing Enhanced Energy Optimization Models...")
    
    # Create sample weather forecast
    weather_data = pd.DataFrame({
        'hour': range(24),
        'temperature': 20 + 10 * np.sin(2 * np.pi * np.arange(24) / 24),
        'humidity': 50 + 20 * np.random.random(24),
        'wind_speed': 5 + 5 * np.random.random(24)
    })
    
    # Test MIP optimizer
    mip_optimizer = create_energy_optimizer('mip')
    mip_result = mip_optimizer.optimize(
        initial_temperature=150.0,
        forecast_horizon=24,
        weather_forecast=weather_data
    )
    
    print(f"MIP Optimization Results:")
    print(f"  Total Cost: ${mip_result.total_cost:.2f}")
    print(f"  Peak Demand: {mip_result.peak_demand:.1f} kW")
    print(f"  Energy Consumption: {mip_result.energy_consumption:.1f} kWh")
    print(f"  Temperature Range: {mip_result.temperature_profile.min():.1f}°F - {mip_result.temperature_profile.max():.1f}°F")
    print(f"  Feasible: {mip_result.feasible}")
    
    # Test Demand Response optimizer
    dr_optimizer = create_energy_optimizer('demand_response')
    dr_optimizer.add_demand_response_event(14, 18, 0.2, 0.10)  # 20% reduction, $0.10/kWh incentive
    dr_result = dr_optimizer.optimize(
        initial_temperature=150.0,
        forecast_horizon=24,
        weather_forecast=weather_data
    )
    
    print(f"\nDemand Response Optimization Results:")
    print(f"  Total Cost: ${dr_result.total_cost:.2f}")
    print(f"  Cost Breakdown: {dr_result.cost_breakdown}")
    print(f"  Peak Demand: {dr_result.peak_demand:.1f} kW")
    
    # Test Predictive optimizer
    pred_optimizer = create_energy_optimizer('predictive')
    pred_result = pred_optimizer.optimize(
        initial_temperature=150.0,
        forecast_horizon=24,
        weather_forecast=weather_data
    )
    
    print(f"\nPredictive Optimization Results:")
    print(f"  Total Cost: ${pred_result.total_cost:.2f}")
    print(f"  Peak Demand: {pred_result.peak_demand:.1f} kW")
    print(f"  Energy Consumption: {pred_result.energy_consumption:.1f} kWh")
    
    # Test optimization pipeline
    pipeline = EnergyOptimizationPipeline(mip_optimizer)
    tank_data = {
        'tank_id': 'tank_001',
        'current_temperature': 155.0,
        'forecast_horizon': 24
    }
    
    pipeline_result = pipeline.run_optimization(tank_data, weather_data)
    print(f"\nPipeline Result: ${pipeline_result.total_cost:.2f}")
    
    stats = pipeline.get_optimization_statistics()
    print(f"Pipeline Statistics: {stats}")
    
    print("\nEnhanced energy optimization testing completed!")


class WeatherIntegratedEnergyOptimizer(EnergyOptimizer):
    """Energy optimizer with advanced weather integration"""
    
    def __init__(self, 
                 energy_rates: EnergyRate,
                 constraints: HeatingConstraints,
                 weather_integration = None):
        """
        Args:
            energy_rates: Energy rate structure
            constraints: Heating constraints
            weather_integration: WeatherMLIntegration instance
        """
        self.energy_rates = energy_rates
        self.constraints = constraints
        self.weather_integration = weather_integration
        self.wind_factor_enabled = True
        self.solar_factor_enabled = True
        self.humidity_factor_enabled = True
        
        # Weather impact coefficients
        self.wind_heat_loss_factor = 0.15  # 15% increase per 10 m/s wind
        self.solar_heat_gain_factor = 0.05  # 5% reduction per 100 W/m² solar
        self.humidity_factor = 0.02  # 2% change per 10% humidity change
        
        logger.info("Weather-integrated energy optimizer initialized")
    
    async def optimize_with_weather(self, 
                                   initial_temperature: float,
                                   forecast_horizon: int = 24,
                                   demand_forecast: Optional[np.ndarray] = None) -> OptimizationResult:
        """
        Optimize with real-time weather integration
        
        Args:
            initial_temperature: Current tank temperature
            forecast_horizon: Optimization horizon in hours
            demand_forecast: Heating demand forecast
            
        Returns:
            Optimization result with weather-enhanced scheduling
        """
        try:
            # Get weather features if integration is available
            weather_features = {}
            weather_forecast = None
            
            if self.weather_integration:
                weather_features = await self.weather_integration.get_energy_optimization_features()
                
                # Get detailed weather forecast
                if hasattr(self.weather_integration, 'current_forecast') and self.weather_integration.current_forecast:
                    weather_forecast = self._convert_weather_forecast_to_dataframe(
                        self.weather_integration.current_forecast
                    )
            
            # Enhanced optimization with weather factors
            result = await self._optimize_with_weather_factors(
                initial_temperature, forecast_horizon, weather_features, 
                weather_forecast, demand_forecast
            )
            
            # Add weather impact analysis
            if weather_features:
                result = self._add_weather_impact_analysis(result, weather_features)
            
            return result
            
        except Exception as e:
            logger.error(f"Error in weather-integrated optimization: {e}")
            # Fallback to basic optimization
            return self._basic_optimization_fallback(initial_temperature, forecast_horizon)
    
    async def _optimize_with_weather_factors(self, 
                                           initial_temperature: float,
                                           forecast_horizon: int,
                                           weather_features: Dict[str, Any],
                                           weather_forecast: Optional[pd.DataFrame],
                                           demand_forecast: Optional[np.ndarray]) -> OptimizationResult:
        """Optimize considering weather factors"""
        
        schedule_data = []
        current_temp = initial_temperature
        total_cost = 0
        peak_demand = 0
        
        # Weather-adjusted constraints
        adjusted_constraints = self._adjust_constraints_for_weather(weather_features)
        
        for hour in range(forecast_horizon):
            hour_of_day = hour % 24
            base_rate = self._get_hourly_rate(hour_of_day)
            
            # Get weather conditions for this hour
            weather_conditions = self._get_weather_conditions_for_hour(
                weather_forecast, hour, weather_features
            )
            
            # Calculate weather-adjusted heat loss
            weather_heat_loss = self._calculate_weather_adjusted_heat_loss(
                current_temp, weather_conditions
            )
            
            # Determine optimal power based on weather and costs
            optimal_power = self._calculate_optimal_power_with_weather(
                current_temp, weather_conditions, base_rate, adjusted_constraints,
                demand_forecast, hour
            )
            
            # Update temperature with weather effects
            heat_input = optimal_power * 3.412  # kW to BTU/hr
            net_heat_change = (heat_input - weather_heat_loss) / self._get_thermal_mass()
            current_temp += net_heat_change
            
            # Apply safety constraints
            current_temp = max(adjusted_constraints['min_temp'], 
                             min(adjusted_constraints['max_temp'], current_temp))
            
            # Calculate cost with weather-dependent factors
            hourly_cost = optimal_power * base_rate
            total_cost += hourly_cost
            peak_demand = max(peak_demand, optimal_power)
            
            # Store schedule data
            schedule_data.append({
                'hour': hour,
                'power_kw': optimal_power,
                'heating_on': optimal_power > 0,
                'temperature': current_temp,
                'hourly_cost': hourly_cost,
                'weather_heat_loss': weather_heat_loss,
                'ambient_temp': weather_conditions.get('temperature', 20.0),
                'wind_speed': weather_conditions.get('wind_speed', 0.0),
                'solar_radiation': weather_conditions.get('solar_radiation', 0.0),
                'heating_efficiency': self._calculate_weather_heating_efficiency(weather_conditions)
            })
        
        schedule_df = pd.DataFrame(schedule_data)
        
        # Calculate total costs
        demand_cost = self.energy_rates.demand_charge * peak_demand
        total_cost += demand_cost
        
        cost_breakdown = {
            'energy_cost': total_cost - demand_cost,
            'demand_cost': demand_cost,
            'weather_adjusted_cost': self._calculate_weather_cost_adjustment(schedule_df),
            'total_cost': total_cost
        }
        
        return OptimizationResult(
            schedule=schedule_df,
            total_cost=total_cost,
            peak_demand=peak_demand,
            energy_consumption=schedule_df['power_kw'].sum(),
            temperature_profile=schedule_df['temperature'].values,
            cost_breakdown=cost_breakdown,
            feasible=True,
            optimization_time=0.1
        )
    
    def _convert_weather_forecast_to_dataframe(self, weather_forecast) -> pd.DataFrame:
        """Convert weather forecast to DataFrame format"""
        data = []
        for wp in weather_forecast.data_points:
            data.append({
                'timestamp': wp.timestamp,
                'temperature': wp.temperature,
                'humidity': wp.humidity,
                'wind_speed': wp.wind_speed,
                'wind_direction': wp.wind_direction,
                'pressure': wp.pressure,
                'cloud_cover': wp.cloud_cover,
                'precipitation': wp.precipitation,
                'solar_radiation': wp.solar_radiation
            })
        
        return pd.DataFrame(data)
    
    def _adjust_constraints_for_weather(self, weather_features: Dict[str, Any]) -> Dict[str, Any]:
        """Adjust heating constraints based on weather conditions"""
        base_constraints = {
            'min_temp': self.constraints.min_temperature,
            'max_temp': self.constraints.max_temperature,
            'max_power': self.constraints.max_power
        }
        
        # Adjust for cold weather
        if 'current_temp' in weather_features:
            ambient_temp = weather_features['current_temp']
            if ambient_temp < 0:  # Very cold
                base_constraints['min_temp'] += 5  # Keep tank warmer
            elif ambient_temp < 10:  # Cold
                base_constraints['min_temp'] += 2
        
        # Adjust for high wind
        if 'current_wind_speed' in weather_features:
            wind_speed = weather_features['current_wind_speed']
            if wind_speed > 15:  # High wind
                base_constraints['min_temp'] += 3  # Compensate for heat loss
                base_constraints['max_power'] *= 1.1  # Allow more power
        
        return base_constraints
    
    def _get_weather_conditions_for_hour(self, weather_forecast: Optional[pd.DataFrame], 
                                       hour: int, weather_features: Dict[str, Any]) -> Dict[str, Any]:
        """Get weather conditions for specific hour"""
        default_conditions = {
            'temperature': weather_features.get('current_temp', 20.0),
            'humidity': weather_features.get('current_humidity', 50.0),
            'wind_speed': weather_features.get('current_wind_speed', 5.0),
            'solar_radiation': weather_features.get('current_solar_radiation', 500.0)
        }
        
        if weather_forecast is not None and hour < len(weather_forecast):
            row = weather_forecast.iloc[hour]
            return {
                'temperature': row.get('temperature', default_conditions['temperature']),
                'humidity': row.get('humidity', default_conditions['humidity']),
                'wind_speed': row.get('wind_speed', default_conditions['wind_speed']),
                'solar_radiation': row.get('solar_radiation', default_conditions['solar_radiation'])
            }
        
        return default_conditions
    
    def _calculate_weather_adjusted_heat_loss(self, tank_temp: float, 
                                            weather_conditions: Dict[str, Any]) -> float:
        """Calculate heat loss adjusted for weather conditions"""
        ambient_temp = weather_conditions.get('temperature', 20.0)
        wind_speed = weather_conditions.get('wind_speed', 5.0)
        humidity = weather_conditions.get('humidity', 50.0)
        
        # Base heat loss
        temp_diff = max(0, tank_temp - ambient_temp)
        base_heat_loss = self.constraints.heat_loss_coefficient * temp_diff * self._get_thermal_mass()
        
        # Wind factor
        wind_factor = 1.0
        if self.wind_factor_enabled:
            wind_factor = 1.0 + (wind_speed / 10.0) * self.wind_heat_loss_factor
        
        # Humidity factor (high humidity can reduce heat loss slightly)
        humidity_factor = 1.0
        if self.humidity_factor_enabled:
            humidity_factor = 1.0 - ((humidity - 50.0) / 100.0) * self.humidity_factor
        
        # Solar gain (reduces effective heat loss during day)
        solar_gain = 0.0
        if self.solar_factor_enabled:
            solar_radiation = weather_conditions.get('solar_radiation', 0.0)
            solar_gain = solar_radiation * self.solar_heat_gain_factor * 1000  # Convert to BTU/hr
        
        adjusted_heat_loss = base_heat_loss * wind_factor * humidity_factor - solar_gain
        return max(0, adjusted_heat_loss)
    
    def _calculate_optimal_power_with_weather(self, current_temp: float,
                                            weather_conditions: Dict[str, Any],
                                            energy_rate: float,
                                            constraints: Dict[str, Any],
                                            demand_forecast: Optional[np.ndarray],
                                            hour: int) -> float:
        """Calculate optimal power considering weather and energy costs"""
        
        # Temperature deficit
        target_temp = constraints['min_temp'] + 10  # Target above minimum
        temp_deficit = max(0, target_temp - current_temp)
        
        # Weather heating demand
        weather_heat_loss = self._calculate_weather_adjusted_heat_loss(current_temp, weather_conditions)
        
        # Required power to maintain temperature
        maintenance_power = weather_heat_loss / 3.412  # BTU/hr to kW
        
        # Additional power for heating up
        if temp_deficit > 0:
            heating_power = temp_deficit * self._get_thermal_mass() / (3.412 * 1.0)  # 1 hour
            required_power = maintenance_power + heating_power
        else:
            required_power = maintenance_power
        
        # Cost-based optimization
        if energy_rate > self.energy_rates.off_peak_rate:
            # During peak hours: minimize power while meeting constraints
            optimal_power = min(required_power, constraints['max_power'] * 0.7)
        else:
            # During off-peak: can use more power efficiently
            if temp_deficit > 5:  # Significant heating needed
                optimal_power = min(required_power * 1.2, constraints['max_power'])
            else:
                optimal_power = required_power
        
        # Demand forecast consideration
        if demand_forecast is not None and hour < len(demand_forecast):
            demand_multiplier = 1.0 + demand_forecast[hour] * 0.5
            optimal_power *= demand_multiplier
        
        # Weather opportunity optimization
        if weather_conditions.get('solar_radiation', 0) > 800:  # High solar gain
            optimal_power *= 0.9  # Reduce power due to solar assistance
        
        if weather_conditions.get('wind_speed', 0) > 20:  # Very high wind
            optimal_power *= 1.1  # Increase power to compensate
        
        return max(0, min(optimal_power, constraints['max_power']))
    
    def _calculate_weather_heating_efficiency(self, weather_conditions: Dict[str, Any]) -> float:
        """Calculate heating efficiency based on weather conditions"""
        base_efficiency = 0.85
        
        # Temperature efficiency (better efficiency at moderate ambient temps)
        ambient_temp = weather_conditions.get('temperature', 20.0)
        temp_efficiency = 1.0
        if ambient_temp < -10:
            temp_efficiency = 0.9  # Lower efficiency in extreme cold
        elif ambient_temp > 35:
            temp_efficiency = 0.95  # Slightly lower in extreme heat
        
        # Wind efficiency (wind can affect heat transfer)
        wind_speed = weather_conditions.get('wind_speed', 5.0)
        wind_efficiency = 1.0 - min(0.1, wind_speed / 100.0)  # Small reduction for high wind
        
        return base_efficiency * temp_efficiency * wind_efficiency
    
    def _calculate_weather_cost_adjustment(self, schedule_df: pd.DataFrame) -> float:
        """Calculate cost adjustment due to weather factors"""
        if 'weather_heat_loss' not in schedule_df.columns:
            return 0.0
        
        # Calculate additional costs due to weather
        base_heat_loss = schedule_df['power_kw'].mean() * 3.412 * 0.8  # Estimate base
        actual_heat_loss = schedule_df['weather_heat_loss'].mean()
        
        weather_cost_factor = max(0, (actual_heat_loss - base_heat_loss) / base_heat_loss)
        return weather_cost_factor * schedule_df['hourly_cost'].sum() * 0.1
    
    def _add_weather_impact_analysis(self, result: OptimizationResult, 
                                   weather_features: Dict[str, Any]) -> OptimizationResult:
        """Add weather impact analysis to optimization result"""
        
        # Add weather metadata to cost breakdown
        result.cost_breakdown['weather_impact'] = {
            'current_conditions': {
                'temperature': weather_features.get('current_temp', 'N/A'),
                'wind_speed': weather_features.get('current_wind_speed', 'N/A'),
                'humidity': weather_features.get('current_humidity', 'N/A'),
                'solar_radiation': weather_features.get('current_solar_radiation', 'N/A')
            },
            'heating_demand_factor': weather_features.get('heating_demand_level', 'N/A'),
            'preheating_opportunity': weather_features.get('preheating_opportunity', 'N/A'),
            'weather_trend': weather_features.get('temp_trend_6h', 'N/A')
        }
        
        # Calculate weather-related metrics
        if 'weather_heat_loss' in result.schedule.columns:
            total_weather_loss = result.schedule['weather_heat_loss'].sum()
            avg_efficiency = result.schedule['heating_efficiency'].mean()
            
            result.cost_breakdown['weather_metrics'] = {
                'total_weather_heat_loss': total_weather_loss,
                'avg_heating_efficiency': avg_efficiency,
                'weather_cost_penalty': result.cost_breakdown.get('weather_adjusted_cost', 0)
            }
        
        return result
    
    def _basic_optimization_fallback(self, initial_temperature: float, 
                                   forecast_horizon: int) -> OptimizationResult:
        """Fallback optimization when weather integration fails"""
        logger.warning("Using basic optimization fallback")
        
        # Simple schedule
        schedule_data = []
        current_temp = initial_temperature
        total_cost = 0
        
        for hour in range(forecast_horizon):
            power = self.constraints.max_power * 0.5  # Fixed 50% power
            hourly_cost = power * self._get_hourly_rate(hour % 24)
            total_cost += hourly_cost
            
            schedule_data.append({
                'hour': hour,
                'power_kw': power,
                'heating_on': True,
                'temperature': current_temp,
                'hourly_cost': hourly_cost
            })
        
        schedule_df = pd.DataFrame(schedule_data)
        
        return OptimizationResult(
            schedule=schedule_df,
            total_cost=total_cost,
            peak_demand=self.constraints.max_power * 0.5,
            energy_consumption=self.constraints.max_power * 0.5 * forecast_horizon,
            temperature_profile=np.full(forecast_horizon, current_temp),
            cost_breakdown={'total_cost': total_cost, 'fallback': True},
            feasible=True,
            optimization_time=0.01
        )
    
    def optimize(self, initial_temperature: float, forecast_horizon: int = 24,
                weather_forecast: Optional[pd.DataFrame] = None,
                demand_forecast: Optional[np.ndarray] = None) -> OptimizationResult:
        """Standard optimize interface (synchronous wrapper)"""
        import asyncio
        return asyncio.run(self.optimize_with_weather(
            initial_temperature, forecast_horizon, demand_forecast
        ))
    
    def _get_hourly_rate(self, hour: int) -> float:
        """Get energy rate for given hour"""
        for peak_start, peak_end in self.energy_rates.peak_hours:
            if peak_start <= hour < peak_end:
                return self.energy_rates.peak_rate
        return self.energy_rates.off_peak_rate
    
    def _get_thermal_mass(self) -> float:
        """Calculate thermal mass of the tank"""
        volume_m3 = self.constraints.tank_capacity * 0.00378541
        mass_kg = volume_m3 * 2000
        thermal_mass = mass_kg * 0.9
        return thermal_mass * 947.817


class MLEnhancedEnergyOptimizer(WeatherIntegratedEnergyOptimizer):
    """Energy optimizer enhanced with ML models"""
    
    def __init__(self,
                 energy_rates: EnergyRate,
                 constraints: HeatingConstraints,
                 weather_integration = None,
                 temperature_model = None,
                 demand_model = None):
        """
        Args:
            energy_rates: Energy rate structure
            constraints: Heating constraints  
            weather_integration: WeatherMLIntegration instance
            temperature_model: ML model for temperature prediction
            demand_model: ML model for demand prediction
        """
        super().__init__(energy_rates, constraints, weather_integration)
        self.temperature_model = temperature_model
        self.demand_model = demand_model
        self.learning_enabled = True
        self.optimization_history = []
        
        logger.info("ML-enhanced energy optimizer initialized")
    
    async def optimize_with_ml_prediction(self,
                                        initial_temperature: float,
                                        forecast_horizon: int = 24) -> OptimizationResult:
        """Optimize using ML models for prediction"""
        
        try:
            # Get weather features
            weather_features = {}
            if self.weather_integration:
                weather_features = await self.weather_integration.get_weather_features_for_ml()
            
            # Predict temperatures using ML model
            predicted_temps = await self._predict_temperatures_ml(
                initial_temperature, forecast_horizon, weather_features
            )
            
            # Predict heating demand using ML model  
            predicted_demand = await self._predict_heating_demand_ml(
                forecast_horizon, weather_features
            )
            
            # Optimize based on predictions
            result = await self._optimize_with_ml_predictions(
                initial_temperature, forecast_horizon, 
                predicted_temps, predicted_demand, weather_features
            )
            
            # Store for learning
            if self.learning_enabled:
                self._store_optimization_for_learning(result, weather_features)
            
            return result
            
        except Exception as e:
            logger.error(f"Error in ML-enhanced optimization: {e}")
            return await self.optimize_with_weather(initial_temperature, forecast_horizon)
    
    async def _predict_temperatures_ml(self, initial_temp: float, horizon: int,
                                     weather_features: Dict[str, Any]) -> np.ndarray:
        """Predict temperatures using ML model"""
        
        if not self.temperature_model:
            # Fallback to simple prediction
            return self._simple_temperature_prediction(initial_temp, horizon, weather_features)
        
        try:
            # Prepare features for ML model
            ml_features = self._prepare_temperature_features(initial_temp, weather_features, horizon)
            
            # Get ML predictions
            predictions = self.temperature_model.predict(ml_features, horizon)
            
            return np.array(predictions) if isinstance(predictions, list) else predictions
            
        except Exception as e:
            logger.error(f"Error in ML temperature prediction: {e}")
            return self._simple_temperature_prediction(initial_temp, horizon, weather_features)
    
    async def _predict_heating_demand_ml(self, horizon: int,
                                       weather_features: Dict[str, Any]) -> np.ndarray:
        """Predict heating demand using ML model"""
        
        if not self.demand_model:
            return self._simple_demand_prediction(horizon, weather_features)
        
        try:
            # Prepare features for demand prediction
            demand_features = self._prepare_demand_features(weather_features, horizon)
            
            # Get ML predictions
            demand_predictions = self.demand_model.predict(demand_features)
            
            return np.array(demand_predictions) if isinstance(demand_predictions, list) else demand_predictions
            
        except Exception as e:
            logger.error(f"Error in ML demand prediction: {e}")
            return self._simple_demand_prediction(horizon, weather_features)
    
    def _prepare_temperature_features(self, initial_temp: float, 
                                    weather_features: Dict[str, Any], horizon: int) -> np.ndarray:
        """Prepare features for temperature ML model"""
        
        # Base features
        features = [
            initial_temp,
            weather_features.get('current_temp', 20.0),
            weather_features.get('current_humidity', 50.0), 
            weather_features.get('current_wind_speed', 5.0),
            weather_features.get('current_solar_radiation', 500.0)
        ]
        
        # Add time features
        features.extend([
            weather_features.get('hour_of_day', 12),
            weather_features.get('day_of_week', 1),
            weather_features.get('month', 6),
            weather_features.get('is_weekend', 0)
        ])
        
        # Add forecast features if available
        for i in range(min(6, horizon)):  # Next 6 hours
            features.extend([
                weather_features.get(f'forecast_temp_mean_{i+1}h', weather_features.get('current_temp', 20.0)),
                weather_features.get(f'forecast_humidity_mean_{i+1}h', weather_features.get('current_humidity', 50.0))
            ])
        
        # Pad if needed
        while len(features) < 30:  # Standard feature size
            features.append(0.0)
        
        return np.array(features[:30]).reshape(1, -1)
    
    def _prepare_demand_features(self, weather_features: Dict[str, Any], horizon: int) -> np.ndarray:
        """Prepare features for demand ML model"""
        
        features = [
            weather_features.get('current_temp', 20.0),
            weather_features.get('heating_demand_level', 0.5),
            weather_features.get('hour_of_day', 12),
            weather_features.get('day_of_week', 1),
            weather_features.get('is_weekend', 0),
            weather_features.get('is_business_hours', 1),
            weather_features.get('peak_hours', 0),
            weather_features.get('temp_trend_6h', 0.0)
        ]
        
        # Add seasonal features
        features.extend([
            weather_features.get('month_sin', 0.0),
            weather_features.get('month_cos', 1.0),
            weather_features.get('season', 1)
        ])
        
        return np.array(features).reshape(1, -1)
    
    def _simple_temperature_prediction(self, initial_temp: float, horizon: int,
                                     weather_features: Dict[str, Any]) -> np.ndarray:
        """Simple temperature prediction fallback"""
        
        predictions = [initial_temp]
        current_temp = initial_temp
        ambient_temp = weather_features.get('current_temp', 20.0)
        
        for hour in range(1, horizon):
            # Simple exponential decay toward ambient
            decay_rate = 0.05  # 5% per hour without heating
            temp_change = (ambient_temp - current_temp) * decay_rate
            current_temp += temp_change
            predictions.append(current_temp)
        
        return np.array(predictions)
    
    def _simple_demand_prediction(self, horizon: int, 
                                weather_features: Dict[str, Any]) -> np.ndarray:
        """Simple demand prediction fallback"""
        
        base_demand = 0.5  # Base demand level
        temp_factor = max(0, (25.0 - weather_features.get('current_temp', 20.0)) / 25.0)
        
        # Higher demand in cold weather and business hours
        demand_predictions = []
        for hour in range(horizon):
            hour_of_day = (weather_features.get('hour_of_day', 12) + hour) % 24
            
            if 8 <= hour_of_day <= 17:  # Business hours
                demand = base_demand + temp_factor * 0.3
            else:
                demand = base_demand + temp_factor * 0.1
            
            demand_predictions.append(demand)
        
        return np.array(demand_predictions)
    
    async def _optimize_with_ml_predictions(self, initial_temp: float, horizon: int,
                                          predicted_temps: np.ndarray,
                                          predicted_demand: np.ndarray,
                                          weather_features: Dict[str, Any]) -> OptimizationResult:
        """Optimize using ML predictions"""
        
        schedule_data = []
        current_temp = initial_temp
        total_cost = 0
        peak_demand = 0
        
        for hour in range(horizon):
            hour_of_day = hour % 24
            energy_rate = self._get_hourly_rate(hour_of_day)
            
            # Get predictions for this hour
            predicted_temp = predicted_temps[hour] if hour < len(predicted_temps) else current_temp
            demand_level = predicted_demand[hour] if hour < len(predicted_demand) else 0.5
            
            # Calculate optimal power using predictions
            optimal_power = self._calculate_ml_optimal_power(
                current_temp, predicted_temp, demand_level, energy_rate, hour_of_day
            )
            
            # Update temperature
            heat_input = optimal_power * 3.412
            heat_loss = self._estimate_heat_loss(current_temp, weather_features)
            temp_change = (heat_input - heat_loss) / self._get_thermal_mass()
            current_temp += temp_change
            
            # Apply constraints
            current_temp = max(self.constraints.min_temperature,
                             min(self.constraints.max_temperature, current_temp))
            
            # Calculate costs
            hourly_cost = optimal_power * energy_rate
            total_cost += hourly_cost
            peak_demand = max(peak_demand, optimal_power)
            
            schedule_data.append({
                'hour': hour,
                'power_kw': optimal_power,
                'heating_on': optimal_power > 0,
                'temperature': current_temp,
                'predicted_temp': predicted_temp,
                'demand_level': demand_level,
                'hourly_cost': hourly_cost,
                'ml_optimized': True
            })
        
        schedule_df = pd.DataFrame(schedule_data)
        
        # Calculate total costs
        demand_cost = self.energy_rates.demand_charge * peak_demand
        total_cost += demand_cost
        
        cost_breakdown = {
            'energy_cost': total_cost - demand_cost,
            'demand_cost': demand_cost,
            'ml_optimization_enabled': True,
            'total_cost': total_cost
        }
        
        return OptimizationResult(
            schedule=schedule_df,
            total_cost=total_cost,
            peak_demand=peak_demand,
            energy_consumption=schedule_df['power_kw'].sum(),
            temperature_profile=schedule_df['temperature'].values,
            cost_breakdown=cost_breakdown,
            feasible=True,
            optimization_time=0.15
        )
    
    def _calculate_ml_optimal_power(self, current_temp: float, predicted_temp: float,
                                  demand_level: float, energy_rate: float, hour_of_day: int) -> float:
        """Calculate optimal power using ML predictions"""
        
        # Temperature-based power need
        temp_deficit = max(0, self.constraints.min_temperature + 5 - predicted_temp)
        temp_power_need = temp_deficit * self._get_thermal_mass() / (3.412 * 1.0)
        
        # Demand-based adjustment
        demand_adjustment = demand_level * self.constraints.max_power * 0.3
        
        # Time-based optimization
        if energy_rate > self.energy_rates.off_peak_rate:
            # Peak hours: minimize power
            power = min(temp_power_need + demand_adjustment * 0.5, self.constraints.max_power * 0.6)
        else:
            # Off-peak: can use more power
            power = min(temp_power_need + demand_adjustment, self.constraints.max_power * 0.9)
        
        # Predictive adjustment
        if predicted_temp < current_temp:  # Cooling predicted
            power *= 1.2  # Increase power to compensate
        elif predicted_temp > current_temp + 2:  # Warming predicted
            power *= 0.8  # Reduce power, let ambient help
        
        return max(0, power)
    
    def _estimate_heat_loss(self, temp: float, weather_features: Dict[str, Any]) -> float:
        """Estimate heat loss using weather features"""
        ambient_temp = weather_features.get('current_temp', 20.0)
        wind_speed = weather_features.get('current_wind_speed', 5.0)
        
        temp_diff = max(0, temp - ambient_temp)
        wind_factor = 1.0 + (wind_speed / 10.0) * 0.15
        
        base_loss = self.constraints.heat_loss_coefficient * temp_diff * self._get_thermal_mass()
        return base_loss * wind_factor
    
    def _store_optimization_for_learning(self, result: OptimizationResult, 
                                       weather_features: Dict[str, Any]):
        """Store optimization results for learning"""
        
        learning_data = {
            'timestamp': datetime.now(),
            'weather_features': weather_features,
            'optimization_result': {
                'total_cost': result.total_cost,
                'peak_demand': result.peak_demand,
                'energy_consumption': result.energy_consumption,
                'feasible': result.feasible
            },
            'schedule_summary': {
                'avg_power': result.schedule['power_kw'].mean(),
                'max_power': result.schedule['power_kw'].max(),
                'avg_temp': result.schedule['temperature'].mean(),
                'temp_stability': result.schedule['temperature'].std()
            }
        }
        
        self.optimization_history.append(learning_data)
        
        # Keep only recent history
        if len(self.optimization_history) > 200:
            self.optimization_history = self.optimization_history[-100:]
    
    def get_learning_insights(self) -> Dict[str, Any]:
        """Get insights from optimization learning"""
        
        if len(self.optimization_history) < 10:
            return {'error': 'Insufficient data for insights'}
        
        recent_data = self.optimization_history[-50:]
        
        # Calculate performance metrics
        costs = [d['optimization_result']['total_cost'] for d in recent_data]
        consumptions = [d['optimization_result']['energy_consumption'] for d in recent_data]
        
        insights = {
            'optimization_count': len(self.optimization_history),
            'recent_performance': {
                'avg_cost': np.mean(costs),
                'cost_trend': np.polyfit(range(len(costs)), costs, 1)[0],
                'avg_consumption': np.mean(consumptions),
                'consumption_trend': np.polyfit(range(len(consumptions)), consumptions, 1)[0]
            },
            'weather_correlations': self._analyze_weather_correlations(recent_data)
        }
        
        return insights
    
    def _analyze_weather_correlations(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze correlations between weather and optimization performance"""
        
        try:
            weather_temps = [d['weather_features'].get('current_temp', 20) for d in data]
            costs = [d['optimization_result']['total_cost'] for d in data]
            consumptions = [d['optimization_result']['energy_consumption'] for d in data]
            
            temp_cost_corr = np.corrcoef(weather_temps, costs)[0, 1]
            temp_consumption_corr = np.corrcoef(weather_temps, consumptions)[0, 1]
            
            return {
                'temperature_cost_correlation': temp_cost_corr,
                'temperature_consumption_correlation': temp_consumption_corr,
                'insights': {
                    'cold_weather_impact': 'High cost correlation' if temp_cost_corr < -0.5 else 'Moderate impact',
                    'energy_weather_sensitivity': 'High' if abs(temp_consumption_corr) > 0.7 else 'Moderate'
                }
            }
        
        except Exception as e:
            logger.error(f"Error analyzing weather correlations: {e}")
            return {'error': 'Analysis failed'}


# Enhanced factory function
def create_enhanced_energy_optimizer(optimizer_type: str = 'weather_integrated',
                                   energy_rates: Optional[EnergyRate] = None,
                                   constraints: Optional[HeatingConstraints] = None,
                                   weather_integration = None,
                                   temperature_model = None,
                                   demand_model = None,
                                   **kwargs) -> EnergyOptimizer:
    """
    Enhanced factory function for creating energy optimizers
    
    Args:
        optimizer_type: Type of optimizer ('weather_integrated', 'ml_enhanced', 'mip', etc.)
        energy_rates: Energy rate structure
        constraints: Heating constraints
        weather_integration: WeatherMLIntegration instance
        temperature_model: ML model for temperature prediction
        demand_model: ML model for demand prediction
        **kwargs: Additional optimizer-specific parameters
        
    Returns:
        Energy optimizer instance
    """
    # Use defaults if not provided
    if energy_rates is None:
        energy_rates = EnergyRate(
            peak_rate=0.15,
            off_peak_rate=0.08,
            demand_charge=15.0,
            peak_hours=[(8, 12), (17, 21)],
            weekday_only=True
        )
    
    if constraints is None:
        constraints = HeatingConstraints(
            min_temperature=140.0,
            max_temperature=180.0,
            max_heating_rate=5.0,
            max_power=200.0,
            tank_capacity=50000.0,
            heat_loss_coefficient=0.02
        )
    
    if optimizer_type == 'weather_integrated':
        return WeatherIntegratedEnergyOptimizer(energy_rates, constraints, weather_integration)
    elif optimizer_type == 'ml_enhanced':
        return MLEnhancedEnergyOptimizer(
            energy_rates, constraints, weather_integration, temperature_model, demand_model
        )
    else:
        # Fall back to original factory function
        return create_energy_optimizer(optimizer_type, energy_rates, constraints, **kwargs)


print("\nEnhanced energy optimization with weather and ML integration completed!")