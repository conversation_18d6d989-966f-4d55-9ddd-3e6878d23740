"""
MLOps API Endpoints for Tank System
Provides machine learning operations for predictive maintenance, anomaly detection, and optimization
"""

from fastapi import APIRouter, HTTPException, UploadFile, File, BackgroundTasks
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import json
import asyncio
import logging
import pandas as pd
import numpy as np
from pydantic import BaseModel, Field
from enum import Enum
import pickle
import joblib
from pathlib import Path

logger = logging.getLogger(__name__)

# Create MLOps API router
router = APIRouter(prefix="/api/v1/mlops", tags=["MLOps"])

# Model storage directory
MODEL_STORAGE_PATH = Path("models")
MODEL_STORAGE_PATH.mkdir(exist_ok=True)

class ModelType(str, Enum):
    REGRESSION = "regression"
    CLASSIFICATION = "classification"
    TIME_SERIES = "time_series"
    ANOMALY_DETECTION = "anomaly_detection"
    CLUSTERING = "clustering"

class ModelStatus(str, Enum):
    TRAINING = "training"
    TRAINED = "trained"
    DEPLOYED = "deployed"
    FAILED = "failed"
    DEPRECATED = "deprecated"

class TrainingRequest(BaseModel):
    """Request model for training a new ML model"""
    model_name: str = Field(..., description="Unique name for the model")
    model_type: ModelType = Field(..., description="Type of ML model")
    algorithm: str = Field(..., description="ML algorithm to use")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Model hyperparameters")
    features: List[str] = Field(..., description="List of feature columns")
    target: str = Field(..., description="Target variable column")
    tank_ids: Optional[List[str]] = Field(None, description="Tank IDs for training data")
    training_period: Optional[Dict[str, str]] = Field(None, description="Training data time range")
    
    class Config:
        schema_extra = {
            "example": {
                "model_name": "tank_temperature_predictor",
                "model_type": "regression",
                "algorithm": "random_forest",
                "parameters": {
                    "n_estimators": 100,
                    "max_depth": 10,
                    "random_state": 42
                },
                "features": ["ambient_temp", "heating_power", "flow_rate", "pressure"],
                "target": "temperature",
                "tank_ids": ["tank_001", "tank_002"],
                "training_period": {
                    "start": "2024-01-01T00:00:00Z",
                    "end": "2024-12-31T23:59:59Z"
                }
            }
        }

class PredictionRequest(BaseModel):
    """Request model for making predictions"""
    model_name: str = Field(..., description="Name of the model to use")
    input_data: Dict[str, Any] = Field(..., description="Input features for prediction")
    
    class Config:
        schema_extra = {
            "example": {
                "model_name": "tank_temperature_predictor",
                "input_data": {
                    "ambient_temp": 25.5,
                    "heating_power": 75.0,
                    "flow_rate": 100.0,
                    "pressure": 2.1
                }
            }
        }

class BatchPredictionRequest(BaseModel):
    """Request model for batch predictions"""
    model_name: str = Field(..., description="Name of the model to use")
    tank_ids: List[str] = Field(..., description="Tank IDs for batch prediction")
    prediction_period: Optional[Dict[str, str]] = Field(None, description="Time range for prediction")

class ModelResponse(BaseModel):
    """Response model for model operations"""
    success: bool = Field(..., description="Operation success")
    message: str = Field(..., description="Response message")
    model_name: str = Field(..., description="Model name")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")
    timestamp: datetime = Field(default_factory=datetime.now)

class ModelInfo(BaseModel):
    """Model information response"""
    model_name: str
    model_type: ModelType
    algorithm: str
    status: ModelStatus
    created_at: datetime
    updated_at: datetime
    accuracy: Optional[float] = None
    features: List[str]
    target: str
    version: str
    file_size: Optional[int] = None

# Global model registry
model_registry: Dict[str, Dict[str, Any]] = {}

@router.post("/models/train", response_model=ModelResponse)
async def train_model(request: TrainingRequest, background_tasks: BackgroundTasks):
    """Train a new machine learning model"""
    try:
        if request.model_name in model_registry:
            raise HTTPException(status_code=400, detail=f"Model {request.model_name} already exists")
        
        # Register model with initial status
        model_registry[request.model_name] = {
            "model_type": request.model_type,
            "algorithm": request.algorithm,
            "status": ModelStatus.TRAINING,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "features": request.features,
            "target": request.target,
            "parameters": request.parameters,
            "version": "1.0.0",
            "accuracy": None
        }
        
        # Start training in background
        background_tasks.add_task(
            _train_model_background,
            request.model_name,
            request.model_type,
            request.algorithm,
            request.parameters,
            request.features,
            request.target,
            request.tank_ids,
            request.training_period
        )
        
        return ModelResponse(
            success=True,
            message=f"Training started for model {request.model_name}",
            model_name=request.model_name,
            data={"status": "training", "estimated_completion": "15-30 minutes"}
        )
        
    except Exception as e:
        logger.error(f"Error starting model training: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/models", response_model=List[ModelInfo])
async def list_models():
    """Get list of all models in the registry"""
    try:
        models = []
        for name, info in model_registry.items():
            model_path = MODEL_STORAGE_PATH / f"{name}.pkl"
            file_size = model_path.stat().st_size if model_path.exists() else None
            
            model_info = ModelInfo(
                model_name=name,
                model_type=info["model_type"],
                algorithm=info["algorithm"],
                status=info["status"],
                created_at=info["created_at"],
                updated_at=info["updated_at"],
                accuracy=info.get("accuracy"),
                features=info["features"],
                target=info["target"],
                version=info.get("version", "1.0.0"),
                file_size=file_size
            )
            models.append(model_info)
        
        return models
        
    except Exception as e:
        logger.error(f"Error listing models: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/models/{model_name}", response_model=ModelInfo)
async def get_model_info(model_name: str):
    """Get detailed information about a specific model"""
    try:
        if model_name not in model_registry:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")
        
        info = model_registry[model_name]
        model_path = MODEL_STORAGE_PATH / f"{model_name}.pkl"
        file_size = model_path.stat().st_size if model_path.exists() else None
        
        return ModelInfo(
            model_name=model_name,
            model_type=info["model_type"],
            algorithm=info["algorithm"],
            status=info["status"],
            created_at=info["created_at"],
            updated_at=info["updated_at"],
            accuracy=info.get("accuracy"),
            features=info["features"],
            target=info["target"],
            version=info.get("version", "1.0.0"),
            file_size=file_size
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting model info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/models/{model_name}/deploy", response_model=ModelResponse)
async def deploy_model(model_name: str):
    """Deploy a trained model for inference"""
    try:
        if model_name not in model_registry:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")
        
        model_info = model_registry[model_name]
        if model_info["status"] != ModelStatus.TRAINED:
            raise HTTPException(
                status_code=400, 
                detail=f"Model {model_name} is not in trained status. Current status: {model_info['status']}"
            )
        
        # Update model status to deployed
        model_registry[model_name]["status"] = ModelStatus.DEPLOYED
        model_registry[model_name]["updated_at"] = datetime.now()
        
        return ModelResponse(
            success=True,
            message=f"Model {model_name} deployed successfully",
            model_name=model_name,
            data={"status": "deployed", "endpoint": f"/api/v1/mlops/models/{model_name}/predict"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deploying model: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/models/{model_name}/predict")
async def predict(model_name: str, request: PredictionRequest):
    """Make a prediction using a deployed model"""
    try:
        if model_name not in model_registry:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")
        
        model_info = model_registry[model_name]
        if model_info["status"] != ModelStatus.DEPLOYED:
            raise HTTPException(
                status_code=400,
                detail=f"Model {model_name} is not deployed. Current status: {model_info['status']}"
            )
        
        # Load the model
        model_path = MODEL_STORAGE_PATH / f"{model_name}.pkl"
        if not model_path.exists():
            raise HTTPException(status_code=500, detail=f"Model file not found for {model_name}")
        
        model = joblib.load(model_path)
        
        # Prepare input data
        features = model_info["features"]
        input_array = np.array([[request.input_data.get(feature, 0.0) for feature in features]])
        
        # Make prediction
        prediction = model.predict(input_array)
        
        # Handle different prediction types
        if model_info["model_type"] == ModelType.CLASSIFICATION:
            prediction_proba = model.predict_proba(input_array) if hasattr(model, 'predict_proba') else None
            result = {
                "prediction": int(prediction[0]),
                "probability": prediction_proba[0].tolist() if prediction_proba is not None else None
            }
        else:
            result = {
                "prediction": float(prediction[0])
            }
        
        return {
            "model_name": model_name,
            "input_features": request.input_data,
            "prediction": result,
            "timestamp": datetime.now().isoformat(),
            "model_version": model_info.get("version", "1.0.0")
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error making prediction: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/models/{model_name}/batch-predict")
async def batch_predict(model_name: str, request: BatchPredictionRequest):
    """Make batch predictions for multiple tanks"""
    try:
        if model_name not in model_registry:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")
        
        model_info = model_registry[model_name]
        if model_info["status"] != ModelStatus.DEPLOYED:
            raise HTTPException(
                status_code=400,
                detail=f"Model {model_name} is not deployed"
            )
        
        # Load the model
        model_path = MODEL_STORAGE_PATH / f"{model_name}.pkl"
        model = joblib.load(model_path)
        
        # Get data for each tank (mock implementation)
        predictions = []
        for tank_id in request.tank_ids:
            # In real implementation, fetch actual tank data
            mock_data = _generate_mock_tank_data(tank_id, model_info["features"])
            
            input_array = np.array([[mock_data[feature] for feature in model_info["features"]]])
            prediction = model.predict(input_array)
            
            predictions.append({
                "tank_id": tank_id,
                "prediction": float(prediction[0]),
                "input_features": mock_data,
                "timestamp": datetime.now().isoformat()
            })
        
        return {
            "model_name": model_name,
            "predictions": predictions,
            "total_predictions": len(predictions),
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error making batch predictions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/models/{model_name}/retrain", response_model=ModelResponse)
async def retrain_model(model_name: str, background_tasks: BackgroundTasks):
    """Retrain an existing model with new data"""
    try:
        if model_name not in model_registry:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")
        
        model_info = model_registry[model_name]
        
        # Update model status
        model_registry[model_name]["status"] = ModelStatus.TRAINING
        model_registry[model_name]["updated_at"] = datetime.now()
        
        # Increment version
        current_version = model_info.get("version", "1.0.0")
        major, minor, patch = map(int, current_version.split("."))
        new_version = f"{major}.{minor + 1}.{patch}"
        model_registry[model_name]["version"] = new_version
        
        # Start retraining in background
        background_tasks.add_task(
            _train_model_background,
            model_name,
            model_info["model_type"],
            model_info["algorithm"],
            model_info["parameters"],
            model_info["features"],
            model_info["target"],
            None,  # Use default tank IDs
            None   # Use default time period
        )
        
        return ModelResponse(
            success=True,
            message=f"Retraining started for model {model_name}",
            model_name=model_name,
            data={"status": "training", "new_version": new_version}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retraining model: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/models/{model_name}", response_model=ModelResponse)
async def delete_model(model_name: str):
    """Delete a model from the registry"""
    try:
        if model_name not in model_registry:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")
        
        # Remove model file
        model_path = MODEL_STORAGE_PATH / f"{model_name}.pkl"
        if model_path.exists():
            model_path.unlink()
        
        # Remove from registry
        del model_registry[model_name]
        
        return ModelResponse(
            success=True,
            message=f"Model {model_name} deleted successfully",
            model_name=model_name
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting model: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/models/{model_name}/metrics")
async def get_model_metrics(model_name: str):
    """Get performance metrics for a model"""
    try:
        if model_name not in model_registry:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")
        
        model_info = model_registry[model_name]
        
        # Mock metrics (in real implementation, calculate from validation data)
        metrics = {
            "model_name": model_name,
            "accuracy": model_info.get("accuracy", 0.85),
            "precision": 0.82,
            "recall": 0.88,
            "f1_score": 0.85,
            "mean_absolute_error": 2.3,
            "root_mean_square_error": 3.1,
            "training_samples": 10000,
            "validation_samples": 2000,
            "feature_importance": {
                feature: np.random.random() 
                for feature in model_info["features"]
            },
            "last_evaluated": datetime.now().isoformat()
        }
        
        return metrics
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting model metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/models/upload")
async def upload_model(
    file: UploadFile = File(...),
    model_name: str = None,
    model_type: ModelType = ModelType.REGRESSION,
    features: str = None,
    target: str = None
):
    """Upload a pre-trained model"""
    try:
        if not model_name:
            model_name = file.filename.split('.')[0]
        
        if model_name in model_registry:
            raise HTTPException(status_code=400, detail=f"Model {model_name} already exists")
        
        # Save uploaded file
        model_path = MODEL_STORAGE_PATH / f"{model_name}.pkl"
        with open(model_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Validate the model file
        try:
            joblib.load(model_path)
        except Exception as e:
            model_path.unlink()  # Remove invalid file
            raise HTTPException(status_code=400, detail=f"Invalid model file: {e}")
        
        # Register model
        feature_list = features.split(",") if features else ["feature1", "feature2"]
        target_name = target if target else "target"
        
        model_registry[model_name] = {
            "model_type": model_type,
            "algorithm": "uploaded",
            "status": ModelStatus.TRAINED,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "features": feature_list,
            "target": target_name,
            "parameters": {},
            "version": "1.0.0",
            "accuracy": None
        }
        
        return {
            "success": True,
            "message": f"Model {model_name} uploaded successfully",
            "model_name": model_name,
            "file_size": len(content)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading model: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/experiments")
async def list_experiments():
    """Get list of ML experiments and their results"""
    try:
        # Mock experiment data
        experiments = [
            {
                "experiment_id": "exp_001",
                "name": "Temperature Prediction Optimization",
                "description": "Comparing different algorithms for temperature prediction",
                "status": "completed",
                "created_at": "2024-01-15T10:00:00Z",
                "models_tested": ["random_forest", "xgboost", "linear_regression"],
                "best_model": "xgboost",
                "best_accuracy": 0.92,
                "duration_minutes": 45
            },
            {
                "experiment_id": "exp_002", 
                "name": "Anomaly Detection Tuning",
                "description": "Hyperparameter tuning for anomaly detection",
                "status": "running",
                "created_at": "2024-01-20T14:30:00Z",
                "models_tested": ["isolation_forest"],
                "best_model": None,
                "best_accuracy": None,
                "duration_minutes": None
            }
        ]
        
        return experiments
        
    except Exception as e:
        logger.error(f"Error listing experiments: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/data-drift/{model_name}")
async def check_data_drift(model_name: str):
    """Check for data drift in model inputs"""
    try:
        if model_name not in model_registry:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")
        
        # Mock data drift analysis
        drift_analysis = {
            "model_name": model_name,
            "drift_detected": False,
            "drift_score": 0.12,
            "threshold": 0.3,
            "feature_drift": {
                feature: {
                    "drift_score": np.random.random() * 0.5,
                    "p_value": np.random.random(),
                    "status": "stable" if np.random.random() > 0.3 else "drift_detected"
                }
                for feature in model_registry[model_name]["features"]
            },
            "recommendation": "Model is stable, no action required",
            "last_checked": datetime.now().isoformat()
        }
        
        return drift_analysis
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking data drift: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Background training function
async def _train_model_background(
    model_name: str,
    model_type: ModelType,
    algorithm: str,
    parameters: Dict[str, Any],
    features: List[str],
    target: str,
    tank_ids: Optional[List[str]],
    training_period: Optional[Dict[str, str]]
):
    """Background task for model training"""
    try:
        # Simulate training delay
        await asyncio.sleep(10)  # Simulate 10 seconds of training
        
        # Generate mock training data
        X_train, y_train = _generate_mock_training_data(features, target, 1000)
        
        # Create and train model based on algorithm
        model = _create_model(algorithm, parameters)
        model.fit(X_train, y_train)
        
        # Calculate mock accuracy
        accuracy = 0.80 + np.random.random() * 0.15  # Random accuracy between 0.80-0.95
        
        # Save model
        model_path = MODEL_STORAGE_PATH / f"{model_name}.pkl"
        joblib.dump(model, model_path)
        
        # Update registry
        model_registry[model_name]["status"] = ModelStatus.TRAINED
        model_registry[model_name]["updated_at"] = datetime.now()
        model_registry[model_name]["accuracy"] = accuracy
        
        logger.info(f"Model {model_name} training completed with accuracy: {accuracy:.3f}")
        
    except Exception as e:
        logger.error(f"Error training model {model_name}: {e}")
        model_registry[model_name]["status"] = ModelStatus.FAILED
        model_registry[model_name]["updated_at"] = datetime.now()

def _create_model(algorithm: str, parameters: Dict[str, Any]):
    """Create ML model based on algorithm"""
    try:
        from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
        from sklearn.linear_model import LinearRegression, LogisticRegression
        from sklearn.svm import SVR, SVC
        
        if algorithm == "random_forest":
            return RandomForestRegressor(**parameters)
        elif algorithm == "linear_regression":
            return LinearRegression(**parameters)
        elif algorithm == "svr":
            return SVR(**parameters)
        else:
            # Default to random forest
            return RandomForestRegressor(n_estimators=100)
    except ImportError:
        # Fallback mock model if sklearn not available
        class MockModel:
            def fit(self, X, y): pass
            def predict(self, X): return np.random.random(X.shape[0])
            def predict_proba(self, X): return np.random.random((X.shape[0], 2))
        return MockModel()

def _generate_mock_training_data(features: List[str], target: str, n_samples: int):
    """Generate mock training data"""
    X = np.random.random((n_samples, len(features)))
    y = np.random.random(n_samples) * 100  # Random target values
    return X, y

def _generate_mock_tank_data(tank_id: str, features: List[str]) -> Dict[str, float]:
    """Generate mock tank data for predictions"""
    np.random.seed(hash(tank_id) % 2**32)  # Consistent mock data per tank
    return {feature: np.random.random() * 100 for feature in features}

# Include this router in the main FastAPI app
def get_mlops_router() -> APIRouter:
    """Get the MLOps API router"""
    return router
