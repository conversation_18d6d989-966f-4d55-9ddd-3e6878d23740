# Ingress configuration for MLOps Digital Twin Platform
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: digital-twin-ingress
  namespace: digital-twin
  labels:
    app: digital-twin
    component: ingress
  annotations:
    # Nginx ingress controller annotations
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    
    # Rate limiting
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    
    # CORS
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "Content-Type, Authorization"
    
    # Security headers
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header X-Frame-Options "SAMEORIGIN" always;
      add_header X-XSS-Protection "1; mode=block" always;
      add_header X-Content-Type-Options "nosniff" always;
      add_header Referrer-Policy "no-referrer-when-downgrade" always;
      add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Load balancing
    nginx.ingress.kubernetes.io/upstream-hash-by: "$remote_addr"
    
    # Timeouts
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "30"
    
    # Body size
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    
    # Certificate management (if using cert-manager)
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    cert-manager.io/acme-challenge-type: "http01"
    
spec:
  tls:
  - hosts:
    - digital-twin.local
    - api.digital-twin.local
    - dashboard.digital-twin.local
    secretName: digital-twin-tls
  
  rules:
  # Main application API
  - host: digital-twin.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: digital-twin-app
            port:
              number: 8000
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: digital-twin-app
            port:
              number: 8000
      - path: /health
        pathType: Prefix
        backend:
          service:
            name: digital-twin-app
            port:
              number: 8000
      - path: /metrics
        pathType: Prefix
        backend:
          service:
            name: digital-twin-app
            port:
              number: 8001
  
  # API subdomain
  - host: api.digital-twin.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: digital-twin-app
            port:
              number: 8000
  
  # Dashboard subdomain
  - host: dashboard.digital-twin.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: digital-twin-dashboard
            port:
              number: 8501

---
# Dashboard deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: digital-twin-dashboard
  namespace: digital-twin
  labels:
    app: digital-twin-dashboard
    component: frontend
    version: "1.0.0"
spec:
  replicas: 2
  selector:
    matchLabels:
      app: digital-twin-dashboard
  template:
    metadata:
      labels:
        app: digital-twin-dashboard
        component: frontend
        version: "1.0.0"
    spec:
      serviceAccountName: digital-twin-service-account
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: dashboard
        image: digital-twin:latest
        command: ["streamlit", "run", "src/monitoring/web_dashboard.py"]
        args:
        - "--server.address"
        - "0.0.0.0"
        - "--server.port"
        - "8501"
        - "--server.enableCORS"
        - "true"
        - "--server.enableXsrfProtection"
        - "false"
        ports:
        - containerPort: 8501
          name: streamlit
          protocol: TCP
        env:
        - name: API_URL
          value: "http://digital-twin-app:8000"
        - name: STREAMLIT_SERVER_PORT
          value: "8501"
        - name: STREAMLIT_SERVER_ADDRESS
          value: "0.0.0.0"
        resources:
          requests:
            cpu: "250m"
            memory: "512Mi"
          limits:
            cpu: "1"
            memory: "2Gi"
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8501
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /healthz
            port: 8501
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3

---
# Dashboard service
apiVersion: v1
kind: Service
metadata:
  name: digital-twin-dashboard
  namespace: digital-twin
  labels:
    app: digital-twin-dashboard
    component: frontend
spec:
  type: ClusterIP
  ports:
  - port: 8501
    targetPort: 8501
    protocol: TCP
    name: streamlit
  selector:
    app: digital-twin-dashboard

---
# Monitoring ingress (Grafana, Prometheus)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: digital-twin-monitoring-ingress
  namespace: digital-twin
  labels:
    app: digital-twin
    component: monitoring-ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: basic-auth
    nginx.ingress.kubernetes.io/auth-realm: "Authentication Required - Digital Twin Monitoring"
spec:
  tls:
  - hosts:
    - grafana.digital-twin.local
    - prometheus.digital-twin.local
    secretName: digital-twin-tls
  
  rules:
  # Grafana
  - host: grafana.digital-twin.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: digital-twin-grafana
            port:
              number: 3000
  
  # Prometheus
  - host: prometheus.digital-twin.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: digital-twin-prometheus
            port:
              number: 9090