import React, { useRef, useMemo } from 'react';
import { useFrame } from '@react-three/fiber';
import { Mesh, Color } from 'three';
import { Text } from '@react-three/drei';

interface LoadingStation3DProps {
  stationId: number;
  position: [number, number, number];
  isActive: boolean;
  loadingInProgress: boolean;
  currentFlowRate: number;
  onSelect: () => void;
}

export function LoadingStation3D({ 
  stationId, 
  position, 
  isActive, 
  loadingInProgress, 
  currentFlowRate,
  onSelect 
}: LoadingStation3DProps) {
  const stationRef = useRef<Mesh>(null);
  const arm1Ref = useRef<Mesh>(null);
  const arm2Ref = useRef<Mesh>(null);

  // Calculate colors based on status
  const stationColor = useMemo(() => {
    if (loadingInProgress) return '#00ff00'; // Green when loading
    if (isActive) return '#ffaa00'; // Orange when active
    return '#666666'; // Gray when inactive
  }, [isActive, loadingInProgress]);

  const flowColor = useMemo(() => {
    const intensity = Math.min(currentFlowRate / 800, 1); // Assuming max 800 L/min
    return new Color().lerpColors(
      new Color(0x333333), // Dark when no flow
      new Color(0xff6600), // Orange when flowing
      intensity
    );
  }, [currentFlowRate]);

  // Animate loading arms when active
  useFrame((state) => {
    if (arm1Ref.current && arm2Ref.current) {
      if (loadingInProgress) {
        // Gentle swaying motion when loading
        arm1Ref.current.rotation.z = Math.sin(state.clock.elapsedTime * 2) * 0.05;
        arm2Ref.current.rotation.z = Math.sin(state.clock.elapsedTime * 2 + Math.PI) * 0.05;
      } else {
        // Return to neutral position
        arm1Ref.current.rotation.z *= 0.95;
        arm2Ref.current.rotation.z *= 0.95;
      }
    }
  });

  return (
    <group position={position}>
      {/* Loading station base */}
      <mesh
        ref={stationRef}
        onClick={onSelect}
        onPointerOver={(e) => {
          e.stopPropagation();
          document.body.style.cursor = 'pointer';
        }}
        onPointerOut={() => {
          document.body.style.cursor = 'auto';
        }}
      >
        <boxGeometry args={[2, 0.5, 1]} />
        <meshStandardMaterial 
          color={stationColor}
          emissive={stationColor}
          emissiveIntensity={loadingInProgress ? 0.3 : 0.1}
          roughness={0.3}
          metalness={0.7}
        />
      </mesh>

      {/* Loading arm 1 */}
      <group position={[-0.5, 0.5, 0]}>
        <mesh ref={arm1Ref}>
          <cylinderGeometry args={[0.08, 0.08, 2, 8]} />
          <meshStandardMaterial 
            color="#444444"
            roughness={0.2}
            metalness={0.8}
          />
        </mesh>
        
        {/* Arm 1 nozzle */}
        <mesh position={[0, -1.2, 0]}>
          <cylinderGeometry args={[0.12, 0.08, 0.3, 8]} />
          <meshStandardMaterial 
            color={flowColor}
            emissive={flowColor}
            emissiveIntensity={loadingInProgress ? 0.5 : 0}
          />
        </mesh>
      </group>

      {/* Loading arm 2 */}
      <group position={[0.5, 0.5, 0]}>
        <mesh ref={arm2Ref}>
          <cylinderGeometry args={[0.08, 0.08, 2, 8]} />
          <meshStandardMaterial 
            color="#444444"
            roughness={0.2}
            metalness={0.8}
          />
        </mesh>
        
        {/* Arm 2 nozzle */}
        <mesh position={[0, -1.2, 0]}>
          <cylinderGeometry args={[0.12, 0.08, 0.3, 8]} />
          <meshStandardMaterial 
            color={flowColor}
            emissive={flowColor}
            emissiveIntensity={loadingInProgress ? 0.5 : 0}
          />
        </mesh>
      </group>

      {/* Control panel */}
      <mesh position={[1.2, 0.3, 0]}>
        <boxGeometry args={[0.3, 0.6, 0.1]} />
        <meshStandardMaterial 
          color="#2a2a2a"
          roughness={0.1}
          metalness={0.9}
        />
      </mesh>

      {/* Status lights */}
      <mesh position={[1.35, 0.5, 0.05]}>
        <sphereGeometry args={[0.03, 8, 8]} />
        <meshStandardMaterial 
          color={isActive ? '#00ff00' : '#ff0000'}
          emissive={isActive ? '#00ff00' : '#ff0000'}
          emissiveIntensity={0.8}
        />
      </mesh>

      <mesh position={[1.35, 0.3, 0.05]}>
        <sphereGeometry args={[0.03, 8, 8]} />
        <meshStandardMaterial 
          color={loadingInProgress ? '#ffaa00' : '#333333'}
          emissive={loadingInProgress ? '#ffaa00' : '#333333'}
          emissiveIntensity={loadingInProgress ? 0.8 : 0}
        />
      </mesh>

      {/* Station label */}
      <Text
        position={[0, 1.5, 0]}
        fontSize={0.2}
        color="white"
        anchorX="center"
        anchorY="middle"
      >
        Loading Station {stationId}
      </Text>

      {/* Flow rate display */}
      {loadingInProgress && (
        <Text
          position={[0, -0.8, 0]}
          fontSize={0.15}
          color="#00ff00"
          anchorX="center"
          anchorY="middle"
        >
          {currentFlowRate.toFixed(0)} L/min
        </Text>
      )}

      {/* Safety zone indicator */}
      <mesh position={[0, -1.9, 0]} rotation={[-Math.PI / 2, 0, 0]}>
        <ringGeometry args={[3, 3.2, 16]} />
        <meshStandardMaterial 
          color={loadingInProgress ? '#ffff00' : '#666666'}
          transparent
          opacity={0.3}
        />
      </mesh>

      {/* Truck positioning guides */}
      <mesh position={[0, -1.95, -2]} rotation={[-Math.PI / 2, 0, 0]}>
        <planeGeometry args={[1, 4]} />
        <meshStandardMaterial 
          color="#ffff00"
          transparent
          opacity={0.5}
        />
      </mesh>

      {/* Flow visualization when loading */}
      {loadingInProgress && (
        <group>
          {/* Flow particles effect */}
          <mesh position={[-0.5, -0.5, 0]}>
            <sphereGeometry args={[0.02, 4, 4]} />
            <meshStandardMaterial 
              color="#ff6600"
              emissive="#ff6600"
              emissiveIntensity={0.8}
              transparent
              opacity={0.8}
            />
          </mesh>
          <mesh position={[0.5, -0.5, 0]}>
            <sphereGeometry args={[0.02, 4, 4]} />
            <meshStandardMaterial 
              color="#ff6600"
              emissive="#ff6600"
              emissiveIntensity={0.8}
              transparent
              opacity={0.8}
            />
          </mesh>
        </group>
      )}

      {/* Emergency stop button */}
      <mesh position={[1.35, 0.1, 0.05]}>
        <cylinderGeometry args={[0.05, 0.05, 0.02, 8]} />
        <meshStandardMaterial 
          color="#ff0000"
          emissive="#ff0000"
          emissiveIntensity={0.3}
        />
      </mesh>
    </group>
  );
}
