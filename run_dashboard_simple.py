#!/usr/bin/env python3
"""
Simple Dashboard for MLOps Digital Twin Platform
Minimal Streamlit dashboard for quick deployment testing
"""

import streamlit as st
import requests
import json
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time

# Page configuration
st.set_page_config(
    page_title="MLOps Digital Twin Dashboard",
    page_icon="🏭",
    layout="wide",
    initial_sidebar_state="expanded"
)

# API Configuration
API_URL = "http://localhost:8000"

def get_api_data(endpoint):
    """Get data from API endpoint"""
    try:
        response = requests.get(f"{API_URL}{endpoint}", timeout=5)
        if response.status_code == 200:
            return response.json()
        else:
            return None
    except Exception as e:
        st.error(f"Error connecting to API: {e}")
        return None

def create_mock_time_series(value, variation=10, points=50):
    """Create mock time series data"""
    timestamps = pd.date_range(
        start=datetime.now() - timedelta(hours=2),
        end=datetime.now(),
        periods=points
    )
    values = [value + variation * np.sin(i/10) + np.random.normal(0, variation/4) 
              for i in range(points)]
    return timestamps, values

def main():
    # Title and header
    st.title("🏭 MLOps Digital Twin Platform")
    st.markdown("**Real-time Monitoring Dashboard for Predictive Asphalt Tank Control**")
    
    # Sidebar
    st.sidebar.title("🔧 Navigation")
    page = st.sidebar.radio(
        "Select Dashboard",
        ["System Overview", "Operational", "Performance", "Energy", "API Status"]
    )
    
    # Auto-refresh
    auto_refresh = st.sidebar.checkbox("Auto Refresh (30s)", value=True)
    if auto_refresh:
        time.sleep(30)
        st.rerun()
    
    # Manual refresh
    if st.sidebar.button("🔄 Refresh Now"):
        st.rerun()
    
    # Get system overview data
    overview_data = get_api_data("/api/v1/overview")
    
    if page == "System Overview":
        render_overview_page(overview_data)
    elif page == "Operational":
        render_operational_page(overview_data)
    elif page == "Performance":
        render_performance_page(overview_data)
    elif page == "Energy":
        render_energy_page(overview_data)
    elif page == "API Status":
        render_api_status_page()

def render_overview_page(overview_data):
    """Render system overview page"""
    st.header("📊 System Overview")
    
    if overview_data:
        # Key metrics row
        col1, col2, col3, col4 = st.columns(4)
        
        key_metrics = overview_data.get('key_metrics', {})
        
        with col1:
            temp_data = key_metrics.get('temperature', {})
            st.metric(
                label="🌡️ Temperature",
                value=f"{temp_data.get('value', 0):.1f}°C",
                delta=f"+{np.random.uniform(-2, 2):.1f}°C"
            )
        
        with col2:
            energy_data = key_metrics.get('energy_consumption', {})
            st.metric(
                label="⚡ Energy",
                value=f"{energy_data.get('value', 0):.0f}kW",
                delta=f"{np.random.uniform(-50, 50):.0f}kW"
            )
        
        with col3:
            efficiency_data = key_metrics.get('efficiency', {})
            st.metric(
                label="📊 Efficiency",
                value=f"{efficiency_data.get('value', 0):.1f}%",
                delta=f"{np.random.uniform(-1, 1):.1f}%"
            )
        
        with col4:
            availability_data = key_metrics.get('system_availability', {})
            st.metric(
                label="🔧 Availability",
                value=f"{availability_data.get('value', 0):.1f}%",
                delta=f"{np.random.uniform(-0.1, 0.1):.2f}%"
            )
        
        st.markdown("---")
        
        # System status
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("System Status")
            
            status_data = {
                'System Status': overview_data.get('system_status', 'Unknown'),
                'Active Alerts': overview_data.get('active_alerts', 0),
                'Total Dashboards': overview_data.get('total_dashboards', 0),
                'Total Metrics': overview_data.get('total_metrics', 0),
                'Last Updated': overview_data.get('timestamp', 'Unknown')
            }
            
            for key, value in status_data.items():
                if key == 'System Status':
                    if value == 'operational':
                        st.success(f"**{key}:** {value.title()}")
                    else:
                        st.warning(f"**{key}:** {value.title()}")
                elif key == 'Active Alerts':
                    if value == 0:
                        st.success(f"**{key}:** {value}")
                    else:
                        st.error(f"**{key}:** {value}")
                else:
                    st.info(f"**{key}:** {value}")
        
        with col2:
            st.subheader("Recent Activity")
            st.info("📈 Energy optimization algorithm updated")
            st.info("🔧 Predictive maintenance scheduled")
            st.success("✅ All systems operational")
            st.info("📊 Daily report generated")
    
    else:
        st.error("Unable to connect to API. Please check if the service is running on http://localhost:8000")

def render_operational_page(overview_data):
    """Render operational dashboard"""
    st.header("⚙️ Operational Dashboard")
    
    # Temperature and Energy charts
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("🌡️ Temperature Monitoring")
        
        # Create mock temperature data
        timestamps, temps = create_mock_time_series(150.5, 5)
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=timestamps,
            y=temps,
            mode='lines+markers',
            name='Temperature',
            line=dict(color='red', width=2)
        ))
        
        fig.update_layout(
            title="Tank Temperature (°C)",
            xaxis_title="Time",
            yaxis_title="Temperature (°C)",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.subheader("⚡ Energy Consumption")
        
        # Create mock energy data
        timestamps, energy = create_mock_time_series(1150, 100)
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=timestamps,
            y=energy,
            mode='lines+markers',
            name='Energy',
            line=dict(color='blue', width=2)
        ))
        
        fig.update_layout(
            title="Energy Consumption (kW)",
            xaxis_title="Time",
            yaxis_title="Power (kW)",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    # Real-time metrics
    st.subheader("📊 Real-time Metrics")
    
    col1, col2, col3 = st.columns(3)
    
    if overview_data:
        key_metrics = overview_data.get('key_metrics', {})
        
        with col1:
            temp = key_metrics.get('temperature', {}).get('value', 150.5)
            st.metric("Current Temperature", f"{temp:.1f}°C", f"{temp - 150:.1f}")
        
        with col2:
            energy = key_metrics.get('energy_consumption', {}).get('value', 1150)
            st.metric("Power Consumption", f"{energy:.0f}kW", f"{energy - 1000:.0f}")
        
        with col3:
            efficiency = key_metrics.get('efficiency', {}).get('value', 89.2)
            st.metric("System Efficiency", f"{efficiency:.1f}%", f"{efficiency - 85:.1f}")

def render_performance_page(overview_data):
    """Render performance dashboard"""
    st.header("📈 Performance Dashboard")
    
    # Performance trends
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("System Performance Trends")
        
        # Create mock performance data
        days = pd.date_range(start=datetime.now() - timedelta(days=7), 
                           end=datetime.now(), freq='D')
        performance = [85 + 10 * np.sin(i/7) + np.random.normal(0, 2) for i in range(len(days))]
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=days,
            y=performance,
            mode='lines+markers',
            name='Performance',
            line=dict(color='green', width=3)
        ))
        
        fig.update_layout(
            title="System Performance (7 days)",
            xaxis_title="Date",
            yaxis_title="Performance (%)",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.subheader("Efficiency Analysis")
        
        # Create efficiency breakdown
        categories = ['Heating', 'Thermal Loss', 'Control', 'Overall']
        efficiency_values = [92.5, 87.3, 95.1, 89.2]
        
        fig = go.Figure(data=[
            go.Bar(x=categories, y=efficiency_values, 
                  marker_color=['red', 'orange', 'blue', 'green'])
        ])
        
        fig.update_layout(
            title="System Efficiency by Component",
            xaxis_title="Component",
            yaxis_title="Efficiency (%)",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    # Performance table
    st.subheader("Performance Summary")
    
    performance_data = {
        'Metric': ['Temperature Stability', 'Energy Efficiency', 'System Uptime', 
                  'Prediction Accuracy', 'Response Time'],
        'Value': ['94.2%', '89.2%', '99.8%', '92.1%', '45ms'],
        'Target': ['95%', '90%', '99.5%', '90%', '< 100ms'],
        'Status': ['🟡', '🟡', '🟢', '🟢', '🟢']
    }
    
    df = pd.DataFrame(performance_data)
    st.dataframe(df, use_container_width=True)

def render_energy_page(overview_data):
    """Render energy dashboard"""
    st.header("⚡ Energy Management")
    
    # Energy metrics
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Daily Energy Pattern")
        
        # Create hourly energy data
        hours = list(range(24))
        consumption = [800 + 400 * np.sin(h/4) + np.random.normal(0, 50) for h in hours]
        
        fig = go.Figure()
        fig.add_trace(go.Bar(
            x=hours,
            y=consumption,
            name='Energy Consumption',
            marker_color='blue'
        ))
        
        fig.update_layout(
            title="Daily Energy Consumption Pattern",
            xaxis_title="Hour of Day",
            yaxis_title="Energy (kWh)",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.subheader("Cost Analysis")
        
        # Create cost comparison
        categories = ['Baseline', 'Current', 'Optimized']
        costs = [1200, 950, 800]
        colors = ['gray', 'blue', 'green']
        
        fig = go.Figure(data=[
            go.Bar(x=categories, y=costs, marker_color=colors)
        ])
        
        fig.update_layout(
            title="Cost Comparison ($)",
            xaxis_title="Scenario",
            yaxis_title="Daily Cost ($)",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    # Energy KPIs
    st.subheader("Energy Efficiency Metrics")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if overview_data:
            efficiency = overview_data.get('key_metrics', {}).get('efficiency', {}).get('value', 89.2)
            st.metric("Current Efficiency", f"{efficiency:.1f}%")
        else:
            st.metric("Current Efficiency", "89.2%")
    
    with col2:
        st.metric("Energy Saved Today", "15.2%", "↑ 2.1%")
    
    with col3:
        st.metric("Cost Savings", "$342", "↑ $45")

def render_api_status_page():
    """Render API status page"""
    st.header("🔌 API Status")
    
    # API Health Check
    health_data = get_api_data("/health")
    
    if health_data:
        st.success("✅ API is healthy and responsive")
        
        # API Information
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("API Information")
            st.info(f"**Status:** {health_data.get('status', 'Unknown')}")
            st.info(f"**Last Check:** {health_data.get('timestamp', 'Unknown')}")
            st.info(f"**URL:** {API_URL}")
        
        with col2:
            st.subheader("Service Status")
            services = health_data.get('services', {})
            for service, status in services.items():
                if status == 'running':
                    st.success(f"**{service.title()}:** {status}")
                else:
                    st.warning(f"**{service.title()}:** {status}")
    
    else:
        st.error("❌ Unable to connect to API")
        st.warning(f"Please check if the API is running at {API_URL}")
    
    # Available Endpoints
    st.subheader("Available Endpoints")
    
    endpoints = [
        {"endpoint": "/", "description": "API root"},
        {"endpoint": "/health", "description": "Health check"},
        {"endpoint": "/docs", "description": "API documentation"},
        {"endpoint": "/api/v1/overview", "description": "System overview"},
        {"endpoint": "/api/v1/dashboards", "description": "Available dashboards"},
        {"endpoint": "/api/v1/metrics/{metric}/current", "description": "Current metric value"},
        {"endpoint": "/api/v1/alerts", "description": "Alert summary"}
    ]
    
    for ep in endpoints:
        with st.expander(f"**{ep['endpoint']}** - {ep['description']}"):
            if st.button(f"Test {ep['endpoint']}", key=ep['endpoint']):
                if '{metric}' in ep['endpoint']:
                    test_url = ep['endpoint'].replace('{metric}', 'temperature')
                else:
                    test_url = ep['endpoint']
                
                result = get_api_data(test_url)
                if result:
                    st.json(result)
                else:
                    st.error("Failed to get response")

if __name__ == "__main__":
    main()