"""
SCADA Integration Module
Handles communication with industrial control systems
"""

try:
    from .opc_client import (
        OPCUAClient,
        OPCConfiguration,
        OPCTag,
        ConnectionState,
        OPCSubscriptionHandler
    )
    OPC_CLIENT_AVAILABLE = True
except ImportError as e:
    # Handle case where OPC client dependencies are not available
    OPCUAClient = None
    OPCConfiguration = None
    OPCTag = None
    ConnectionState = None
    OPCSubscriptionHandler = None
    OPC_CLIENT_AVAILABLE = False
    print(f"Warning: OPC client functionality not available: {e}")

from .modbus_client import (
    ModbusClient,
    ModbusConfiguration,
    ModbusTag,
    ModbusProtocol,
    ModbusDataType,
    ModbusFunction,
    ModbusTagBuilder
)

__all__ = [
    'OPCUAClient',
    'OPCConfiguration', 
    'OPCTag',
    'ConnectionState',
    'OPCSubscriptionHandler',
    'ModbusClient',
    'ModbusConfiguration',
    'ModbusTag',
    'ModbusProtocol',
    'ModbusDataType',
    'ModbusFunction',
    'ModbusTagBuilder'
]
