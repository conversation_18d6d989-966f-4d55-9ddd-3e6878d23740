"""
SCADA Screen Integration Demo
Demonstrates how to extract data from SCADA screens and integrate with digital twin
"""

import asyncio
import logging
import json
import sys
import os
from datetime import datetime
from typing import Dict, Any

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.data_lake.ingestion.screen_data_extractor import (
        ScreenDataExtractor, TankScreenLayout, ScreenRegion, create_asphalt_tank_layout
    )
    from src.data_lake.storage.influxdb_client import (
        InfluxDBTimeSeriesClient, TankDataPoint, InfluxDBConfig
    )
    IMPORTS_AVAILABLE = True
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all dependencies are installed.")
    IMPORTS_AVAILABLE = False

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SCADAScreenIntegration:
    """Integration system for SCADA screen data extraction"""
    
    def __init__(self, tank_id: str = "tank_001"):
        self.tank_id = tank_id
        self.screen_extractor = None
        self.influx_client = None
        self.is_running = False
        
        # Data storage
        self.latest_data = {}
        self.data_history = []
        self.extraction_count = 0
        
    async def initialize(self, layout_file: str = None) -> bool:
        """Initialize the integration system"""
        try:
            logger.info("Initializing SCADA screen integration...")
            
            # Load or create screen layout
            if layout_file and os.path.exists(layout_file):
                layout = self._load_layout_from_file(layout_file)
            else:
                # Use default layout (coordinates need to be calibrated)
                layout = create_asphalt_tank_layout(self.tank_id)
                logger.warning("Using default layout - please calibrate coordinates!")
            
            # Initialize screen extractor
            self.screen_extractor = ScreenDataExtractor(layout)
            self.screen_extractor.register_data_callback(self._handle_extracted_data)
            self.screen_extractor.register_error_callback(self._handle_extraction_error)
            
            # Initialize InfluxDB client (optional)
            try:
                influx_config = InfluxDBConfig(
                    url="http://localhost:8086",
                    token="your-token-here",  # Replace with actual token
                    org="asphalt_plant",
                    bucket="tank_data"
                )
                self.influx_client = InfluxDBTimeSeriesClient(influx_config)
                influx_connected = await self.influx_client.connect()
                if influx_connected:
                    logger.info("Connected to InfluxDB")
                else:
                    logger.warning("Failed to connect to InfluxDB - data will only be logged")
                    self.influx_client = None
            except Exception as e:
                logger.warning(f"InfluxDB not available: {e}")
                self.influx_client = None
            
            logger.info("SCADA screen integration initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize integration: {e}")
            return False
    
    async def start(self) -> bool:
        """Start the integration system"""
        try:
            if not self.screen_extractor:
                logger.error("Screen extractor not initialized")
                return False
            
            logger.info("Starting SCADA screen monitoring...")
            
            # Start screen monitoring
            success = await self.screen_extractor.start_monitoring()
            if not success:
                logger.error("Failed to start screen monitoring")
                return False
            
            self.is_running = True
            
            # Start monitoring task
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())
            
            logger.info("SCADA screen integration started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start integration: {e}")
            return False
    
    async def stop(self):
        """Stop the integration system"""
        try:
            logger.info("Stopping SCADA screen integration...")
            
            self.is_running = False
            
            # Stop screen monitoring
            if self.screen_extractor:
                await self.screen_extractor.stop_monitoring()
            
            # Cancel monitoring task
            if hasattr(self, '_monitoring_task'):
                self._monitoring_task.cancel()
            
            # Disconnect from InfluxDB
            if self.influx_client:
                await self.influx_client.disconnect()
            
            logger.info("SCADA screen integration stopped")
            
        except Exception as e:
            logger.error(f"Error stopping integration: {e}")
    
    async def _handle_extracted_data(self, data: Dict[str, Any]):
        """Handle data extracted from screen"""
        try:
            self.extraction_count += 1
            self.latest_data = data
            
            # Add to history (keep last 100 entries)
            self.data_history.append(data)
            if len(self.data_history) > 100:
                self.data_history.pop(0)
            
            # Log the data
            logger.info(f"Extracted data from tank {data['tank_id']}:")
            
            # Zone temperatures
            zone_temps = data.get('zone_temperatures', {})
            if zone_temps:
                temp_str = ", ".join([f"Zone {z}: {t:.1f}°C" for z, t in zone_temps.items()])
                logger.info(f"  Temperatures: {temp_str}")
            
            # Heating powers
            heating_powers = data.get('heating_powers', {})
            if heating_powers:
                power_str = ", ".join([f"Zone {z}: {p:.0f}kW" for z, p in heating_powers.items()])
                logger.info(f"  Heating Powers: {power_str}")
            
            # System data
            system_data = data.get('system_data', {})
            if system_data:
                logger.info(f"  System: {system_data}")
            
            # Alarms
            alarms = data.get('alarms', [])
            if alarms:
                logger.warning(f"  Alarms: {alarms}")
            
            # Store to InfluxDB if available
            if self.influx_client:
                await self._store_to_influxdb(data)
            
        except Exception as e:
            logger.error(f"Error handling extracted data: {e}")
    
    async def _handle_extraction_error(self, error: Exception):
        """Handle extraction errors"""
        logger.error(f"Screen extraction error: {error}")
    
    async def _store_to_influxdb(self, data: Dict[str, Any]):
        """Store extracted data to InfluxDB"""
        try:
            # Convert to TankDataPoint
            data_point = TankDataPoint(
                tank_id=data['tank_id'],
                timestamp=data['timestamp'],
                zone_temperatures=data.get('zone_temperatures', {}),
                heating_powers=data.get('heating_powers', {}),
                setpoints=data.get('setpoints', {}),
                ambient_temperature=data.get('system_data', {}).get('ambient_temperature'),
                total_power=data.get('system_data', {}).get('power_consumption'),
                alarms=data.get('alarms', []),
                control_mode=data.get('system_data', {}).get('control_mode')
            )
            
            # Write to InfluxDB
            success = await self.influx_client.write_tank_data(data_point)
            if success:
                logger.debug("Data stored to InfluxDB")
            else:
                logger.warning("Failed to store data to InfluxDB")
                
        except Exception as e:
            logger.error(f"Error storing to InfluxDB: {e}")
    
    async def _monitoring_loop(self):
        """Monitoring loop for system health"""
        while self.is_running:
            try:
                # Log statistics every 60 seconds
                await asyncio.sleep(60)
                
                if self.screen_extractor:
                    stats = self.screen_extractor.get_extraction_statistics()
                    logger.info(f"Extraction stats: {stats}")
                
                logger.info(f"Total extractions: {self.extraction_count}")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)
    
    def _load_layout_from_file(self, filename: str) -> TankScreenLayout:
        """Load screen layout from JSON file"""
        try:
            with open(filename, 'r') as f:
                layout_data = json.load(f)
            
            # Create layout from data
            layout = TankScreenLayout(
                tank_id=layout_data.get('tank_id', 'tank_001'),
                window_title=layout_data.get('window_title', '')
            )
            
            # Parse regions
            regions = layout_data.get('regions', [])
            
            for region_data in regions:
                region = ScreenRegion(
                    name=region_data['name'],
                    x=region_data['x'],
                    y=region_data['y'],
                    width=region_data['width'],
                    height=region_data['height'],
                    data_type=region_data.get('type', 'text'),
                    preprocessing=region_data.get('preprocessing', 'default'),
                    expected_format=region_data.get('expected_format'),
                    description=region_data.get('description', '')
                )
                
                # Categorize regions based on name
                name_lower = region_data['name'].lower()
                
                if 'temp' in name_lower and 'zone' in name_lower:
                    layout.zone_temp_regions.append(region)
                elif 'power' in name_lower or 'heating' in name_lower:
                    layout.heating_power_regions.append(region)
                elif 'setpoint' in name_lower:
                    layout.setpoint_regions.append(region)
                elif 'alarm' in name_lower:
                    layout.alarm_regions.append(region)
                elif 'outlet' in name_lower and 'temp' in name_lower:
                    layout.outlet_temp_region = region
                elif 'ambient' in name_lower and 'temp' in name_lower:
                    layout.ambient_temp_region = region
                elif 'power' in name_lower and 'total' in name_lower:
                    layout.power_consumption_region = region
                elif 'mode' in name_lower:
                    layout.mode_region = region
                elif 'level' in name_lower:
                    layout.level_region = region
            
            logger.info(f"Loaded layout with {len(regions)} regions")
            return layout
            
        except Exception as e:
            logger.error(f"Error loading layout from file: {e}")
            raise
    
    def get_latest_data(self) -> Dict[str, Any]:
        """Get the latest extracted data"""
        return self.latest_data.copy() if self.latest_data else {}
    
    def get_data_history(self) -> list:
        """Get data extraction history"""
        return self.data_history.copy()
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get integration statistics"""
        stats = {
            'is_running': self.is_running,
            'extraction_count': self.extraction_count,
            'latest_extraction': self.latest_data.get('timestamp', '').isoformat() if self.latest_data.get('timestamp') else None,
            'influxdb_connected': self.influx_client is not None
        }
        
        if self.screen_extractor:
            extractor_stats = self.screen_extractor.get_extraction_statistics()
            stats.update(extractor_stats)
        
        return stats


async def main():
    """Main function to run the integration"""
    
    if not IMPORTS_AVAILABLE:
        print("\nRequired modules not available.")
        print("Please run: python install_dependencies.py")
        return
    
    print("SCADA Screen Data Integration Demo")
    print("=" * 40)
    print("This demo extracts data from SCADA screens and integrates with digital twin.")
    print("\nIMPORTANT:")
    print("1. Make sure your SCADA screen is visible")
    print("2. Run 'python calibrate_screen.py' first to define screen regions")
    print("3. Save the layout and specify the path below")
    print()
    
    # Get layout file from user
    layout_file = input("Enter layout file path (or press Enter for default): ").strip()
    if not layout_file:
        layout_file = None
        print("Using default layout - coordinates may need calibration!")
    
    # Initialize integration
    integration = SCADAScreenIntegration()
    
    try:
        # Initialize
        success = await integration.initialize(layout_file)
        if not success:
            print("Failed to initialize integration")
            return
        
        # Start integration
        success = await integration.start()
        if not success:
            print("Failed to start integration")
            return
        
        print("\nIntegration started successfully!")
        print("Monitoring SCADA screen... Press Ctrl+C to stop")
        print("Watch the console for extracted data...")
        
        # Run for demo
        try:
            while True:
                await asyncio.sleep(10)
                
                # Print statistics every 10 seconds
                stats = integration.get_statistics()
                print(f"\n--- Statistics ---")
                print(f"Extractions: {stats['extraction_count']}")
                print(f"Success rate: {stats.get('success_rate', 0):.1%}")
                print(f"Running: {stats['is_running']}")
                
                # Print latest data
                latest = integration.get_latest_data()
                if latest:
                    zone_temps = latest.get('zone_temperatures', {})
                    if zone_temps:
                        temp_avg = sum(zone_temps.values()) / len(zone_temps)
                        print(f"Average zone temp: {temp_avg:.1f}°C")
        
        except KeyboardInterrupt:
            print("\nStopping integration...")
            await integration.stop()
            print("Integration stopped.")
            
    except Exception as e:
        logger.error(f"Error in main: {e}")
        await integration.stop()


if __name__ == "__main__":
    if IMPORTS_AVAILABLE:
        asyncio.run(main())
    else:
        print("Please install dependencies first: python install_dependencies.py")