import { useEffect, useRef } from 'react';
export function useLeaveDetection(onLeave) {
    const onLeaveRef = useRef(onLeave);
    useEffect(() => {
        onLeaveRef.current = onLeave;
    }, [onLeave]);
    useEffect(() => {
        const handler = function (ev) {
            onLeaveRef.current.call(this, ev);
        };
        document.documentElement.addEventListener('mouseleave', handler);
        return () => document.documentElement.removeEventListener('mouseleave', handler);
    }, []);
}
