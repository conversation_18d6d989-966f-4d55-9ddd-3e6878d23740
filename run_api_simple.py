#!/usr/bin/env python3
"""
Simple API runner for MLOps Digital Twin Platform
Minimal implementation for quick deployment testing
"""

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime
import json

# Create FastAPI app
app = FastAPI(
    title="MLOps Digital Twin Platform API",
    description="Simple API for the MLOps Digital Twin Platform",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "MLOps Digital Twin Platform API",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "api": "running",
            "database": "mock",
            "redis": "mock",
            "ml_engine": "mock"
        }
    }

@app.get("/api/v1/overview")
async def get_system_overview():
    """Get system overview"""
    return {
        "timestamp": datetime.now().isoformat(),
        "system_status": "operational",
        "active_alerts": 0,
        "total_dashboards": 3,
        "total_metrics": 12,
        "key_metrics": {
            "temperature": {
                "value": 150.5,
                "unit": "°C",
                "title": "Tank Temperature"
            },
            "energy_consumption": {
                "value": 1150.0,
                "unit": "kW",
                "title": "Energy Consumption"
            },
            "efficiency": {
                "value": 89.2,
                "unit": "%",
                "title": "System Efficiency"
            },
            "system_availability": {
                "value": 99.8,
                "unit": "%",
                "title": "System Availability"
            }
        }
    }

@app.get("/api/v1/dashboards")
async def get_dashboards():
    """Get available dashboards"""
    return {
        "dashboards": [
            {
                "dashboard_id": "operational",
                "name": "Operational Dashboard",
                "description": "Real-time operational monitoring",
                "type": "operational"
            },
            {
                "dashboard_id": "performance",
                "name": "Performance Dashboard",
                "description": "System performance monitoring",
                "type": "performance"
            },
            {
                "dashboard_id": "energy",
                "name": "Energy Dashboard",
                "description": "Energy consumption and optimization",
                "type": "energy"
            }
        ]
    }

@app.get("/api/v1/metrics/{metric_name}/current")
async def get_current_metric(metric_name: str):
    """Get current metric value"""
    # Mock data for demonstration
    mock_values = {
        "temperature": 150.5,
        "energy_consumption": 1150.0,
        "efficiency": 89.2,
        "pressure": 2.1,
        "flow_rate": 25.3
    }
    
    if metric_name in mock_values:
        return {
            "metric_name": metric_name,
            "value": mock_values[metric_name],
            "timestamp": datetime.now().isoformat()
        }
    else:
        return {
            "error": f"Metric '{metric_name}' not found",
            "available_metrics": list(mock_values.keys())
        }

@app.get("/api/v1/alerts")
async def get_alerts():
    """Get alert summary"""
    return {
        "active_alerts_count": 0,
        "alerts_last_24h": 2,
        "severity_breakdown": {
            "info": 0,
            "warning": 0,
            "error": 0,
            "critical": 0
        },
        "recent_alerts": [],
        "alert_trends": {
            "total_today": 0,
            "resolved_today": 2
        }
    }

if __name__ == "__main__":
    print("🚀 Starting MLOps Digital Twin Platform API...")
    print("📊 Dashboard API will be available at: http://localhost:8000")
    print("📋 API Documentation: http://localhost:8000/docs")
    print("❤️  Health Check: http://localhost:8000/health")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )