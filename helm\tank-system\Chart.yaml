apiVersion: v2
name: tank-system
description: Industrial Tank Control System - Comprehensive MLOps platform for predictive tank control
type: application
version: 1.0.0
appVersion: "1.0.0"
keywords:
  - industrial
  - tank
  - control
  - iot
  - mlops
  - digital-twin
  - scada
home: https://github.com/company/tank-control-system
sources:
  - https://github.com/company/tank-control-system
maintainers:
  - name: Tank Control Team
    email: <EMAIL>
dependencies:
  - name: redis
    version: "17.11.3"
    repository: "https://charts.bitnami.com/bitnami"
    condition: redis.enabled
  - name: postgresql
    version: "12.6.6"
    repository: "https://charts.bitnami.com/bitnami"
    condition: postgresql.enabled
  - name: kafka
    version: "22.1.5"
    repository: "https://charts.bitnami.com/bitnami"
    condition: kafka.enabled
  - name: prometheus
    version: "23.1.0"
    repository: "https://prometheus-community.github.io/helm-charts"
    condition: monitoring.prometheus.enabled
  - name: grafana
    version: "6.57.4"
    repository: "https://grafana.github.io/helm-charts"
    condition: monitoring.grafana.enabled