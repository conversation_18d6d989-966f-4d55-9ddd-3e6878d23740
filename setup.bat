@echo off
echo ====================================
echo SCADA Screen Data Extraction Setup
echo ====================================
echo.

REM Activate virtual environment
echo Activating virtual environment...
call .venv\Scripts\activate.bat

echo.
echo Installing essential dependencies...
python -m pip install --upgrade pip

REM Install core dependencies one by one to handle failures gracefully
echo Installing numpy...
pip install numpy
echo Installing pandas...
pip install pandas
echo Installing pillow...
pip install pillow
echo Installing opencv-python...
pip install opencv-python
echo Installing mss...
pip install mss
echo Installing pyautogui...
pip install pyautogui

echo.
echo Installing OCR libraries...
pip install pytesseract
pip install easyocr

echo.
echo Installing web framework...
pip install fastapi
pip install uvicorn[standard]
pip install pydantic

echo.
echo Installing additional utilities...
pip install aiohttp structlog

echo.
echo Installation complete!
echo.
echo Next steps:
echo 1. Run: python calibrate_screen.py
echo    to calibrate your SCADA screen regions
echo 2. Run: python run_screen_integration.py
echo    to start data extraction
echo.
echo Note: You may need to install Tesseract OCR separately:
echo Windows: https://github.com/UB-Mannheim/tesseract/wiki
echo.
pause