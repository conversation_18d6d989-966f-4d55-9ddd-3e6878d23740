2025-07-04 11:27:50,809 - __main__ - INFO - Starting Tank Control System...
2025-07-04 11:27:50,871 - __main__ - INFO - Configuration loaded from config/tank_system_config.yaml
2025-07-04 11:27:50,872 - __main__ - INFO - Initializing tank system components...
2025-07-04 11:27:50,873 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
2025-07-04 11:27:50,873 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
2025-07-04 11:27:50,873 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
2025-07-04 11:27:50,874 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
2025-07-04 11:27:50,874 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 1.5, 'ki': 0.05, 'kd': 0.02}
2025-07-04 11:27:50,874 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 1.0, 'ki': 0.1, 'kd': 0.01}
2025-07-04 11:27:50,874 - digital_twin.core.tank_control_algorithms - INFO - Added tank tank_001 to control manager
2025-07-04 11:27:50,874 - __main__ - INFO - Loaded tank configuration: tank_001
2025-07-04 11:27:50,874 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
2025-07-04 11:27:50,874 - digital_twin.core.tank_control_algorithms - INFO - Added tank tank_002 to control manager
2025-07-04 11:27:50,874 - __main__ - INFO - Loaded tank configuration: tank_002
2025-07-04 11:27:50,874 - __main__ - ERROR - Error initializing SCADA integration: name 'TankControllerConfiguration' is not defined
2025-07-04 11:27:50,874 - digital_twin.simulation.tank_physics_engine - INFO - Initializing tank physics engine
2025-07-04 11:27:50,876 - digital_twin.simulation.tank_physics_engine - INFO - Setup 4 heating zones: [(0, 11), (12, 23), (24, 35), (36, 47)]
2025-07-04 11:27:50,876 - digital_twin.simulation.tank_physics_engine - INFO - Heat transfer model initialized with 50 nodes
2025-07-04 11:27:50,876 - digital_twin.simulation.tank_physics_engine - INFO - Setup 1 heating zones: [(0, 49)]
2025-07-04 11:27:50,876 - digital_twin.simulation.tank_physics_engine - INFO - Heat transfer model initialized with 50 nodes
2025-07-04 11:27:50,877 - digital_twin.simulation.tank_physics_engine - INFO - Fluid dynamics model initialized
2025-07-04 11:27:50,877 - digital_twin.simulation.tank_physics_engine - INFO - Fluid dynamics model initialized
2025-07-04 11:27:50,877 - digital_twin.simulation.tank_physics_engine - INFO - Tank physics engine initialized successfully
2025-07-04 11:27:50,877 - digital_twin.simulation.tank_physics_engine - INFO - Starting physics simulation
2025-07-04 11:27:50,878 - __main__ - INFO - Physics simulation initialized successfully
2025-07-04 11:27:50,893 - __main__ - INFO - Web API initialized successfully
2025-07-04 11:27:50,894 - __main__ - INFO - All components initialized successfully
2025-07-04 11:27:50,909 - __main__ - INFO - Tank Control System started successfully
2025-07-04 11:27:50,911 - __main__ - INFO - API server running on http://0.0.0.0:8000
2025-07-04 11:27:50,911 - __main__ - INFO - API documentation: http://0.0.0.0:8000/docs
2025-07-04 11:27:51,325 - src.api.tank_endpoints - INFO - Tank system initialized successfully
2025-07-04 11:31:40,662 - __main__ - INFO - Starting Tank Control System...
2025-07-04 11:31:40,698 - __main__ - INFO - Configuration loaded from config/tank_system_config.yaml
2025-07-04 11:31:40,698 - __main__ - INFO - Initializing tank system components...
2025-07-04 11:31:40,698 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
2025-07-04 11:31:40,698 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
2025-07-04 11:31:40,699 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
2025-07-04 11:31:40,699 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
2025-07-04 11:31:40,699 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 1.5, 'ki': 0.05, 'kd': 0.02}
2025-07-04 11:31:40,699 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 1.0, 'ki': 0.1, 'kd': 0.01}
2025-07-04 11:31:40,699 - digital_twin.core.tank_control_algorithms - INFO - Added tank tank_001 to control manager
2025-07-04 11:31:40,699 - __main__ - INFO - Loaded tank configuration: tank_001
2025-07-04 11:31:40,699 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
2025-07-04 11:31:40,699 - digital_twin.core.tank_control_algorithms - INFO - Added tank tank_002 to control manager
2025-07-04 11:31:40,700 - __main__ - INFO - Loaded tank configuration: tank_002
2025-07-04 11:31:40,709 - __main__ - ERROR - Error initializing SCADA integration: TankControlConfiguration.__init__() got an unexpected keyword argument 'controller_id'
2025-07-04 11:31:40,709 - digital_twin.simulation.tank_physics_engine - INFO - Initializing tank physics engine
2025-07-04 11:31:40,710 - digital_twin.simulation.tank_physics_engine - INFO - Setup 4 heating zones: [(0, 11), (12, 23), (24, 35), (36, 47)]
2025-07-04 11:31:40,711 - digital_twin.simulation.tank_physics_engine - INFO - Heat transfer model initialized with 50 nodes
2025-07-04 11:31:40,712 - digital_twin.simulation.tank_physics_engine - INFO - Setup 1 heating zones: [(0, 49)]
2025-07-04 11:31:40,712 - digital_twin.simulation.tank_physics_engine - INFO - Heat transfer model initialized with 50 nodes
2025-07-04 11:31:40,713 - digital_twin.simulation.tank_physics_engine - INFO - Fluid dynamics model initialized
2025-07-04 11:31:40,713 - digital_twin.simulation.tank_physics_engine - INFO - Fluid dynamics model initialized
2025-07-04 11:31:40,714 - digital_twin.simulation.tank_physics_engine - INFO - Tank physics engine initialized successfully
2025-07-04 11:31:40,714 - digital_twin.simulation.tank_physics_engine - INFO - Starting physics simulation
2025-07-04 11:31:40,714 - __main__ - INFO - Physics simulation initialized successfully
2025-07-04 11:31:40,728 - __main__ - INFO - Web API initialized successfully
2025-07-04 11:31:40,729 - __main__ - INFO - All components initialized successfully
2025-07-04 11:31:40,730 - __main__ - INFO - Tank Control System started successfully
2025-07-04 11:31:40,731 - __main__ - INFO - API server running on http://0.0.0.0:8000
2025-07-04 11:31:40,732 - __main__ - INFO - API documentation: http://0.0.0.0:8000/docs
2025-07-04 11:31:40,797 - src.api.tank_endpoints - INFO - Tank system initialized successfully
2025-07-04 11:33:24,966 - src.api.tank_endpoints - ERROR - Error getting system status: name 'tank_controller_engine' is not defined
2025-07-04 12:54:50,146 - src.api.tank_endpoints - ERROR - Error getting system status: name 'tank_controller_engine' is not defined
2025-07-04 16:41:52,568 - __main__ - INFO - Starting Tank Control System...
2025-07-04 16:41:52,627 - __main__ - INFO - Configuration loaded from config/tank_system_config.yaml
2025-07-04 16:41:52,627 - __main__ - INFO - Initializing tank system components...
2025-07-04 16:41:52,628 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
2025-07-04 16:41:52,629 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
2025-07-04 16:41:52,629 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
2025-07-04 16:41:52,630 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
2025-07-04 16:41:52,630 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 1.5, 'ki': 0.05, 'kd': 0.02}
2025-07-04 16:41:52,631 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 1.0, 'ki': 0.1, 'kd': 0.01}
2025-07-04 16:41:52,631 - digital_twin.core.tank_control_algorithms - INFO - Added tank tank_001 to control manager
2025-07-04 16:41:52,631 - __main__ - INFO - Loaded tank configuration: tank_001
2025-07-04 16:41:52,632 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
2025-07-04 16:41:52,632 - digital_twin.core.tank_control_algorithms - INFO - Added tank tank_002 to control manager
2025-07-04 16:41:52,632 - __main__ - INFO - Loaded tank configuration: tank_002
2025-07-04 16:41:52,633 - __main__ - ERROR - Error initializing SCADA integration: TankControlConfiguration.__init__() got an unexpected keyword argument 'controller_id'
2025-07-04 16:41:52,633 - digital_twin.simulation.tank_physics_engine - INFO - Initializing tank physics engine
2025-07-04 16:41:52,634 - digital_twin.simulation.tank_physics_engine - INFO - Setup 4 heating zones: [(0, 11), (12, 23), (24, 35), (36, 47)]
2025-07-04 16:41:52,634 - digital_twin.simulation.tank_physics_engine - INFO - Heat transfer model initialized with 50 nodes
2025-07-04 16:41:52,634 - digital_twin.simulation.tank_physics_engine - INFO - Setup 1 heating zones: [(0, 49)]
2025-07-04 16:41:52,635 - digital_twin.simulation.tank_physics_engine - INFO - Heat transfer model initialized with 50 nodes
2025-07-04 16:41:52,635 - digital_twin.simulation.tank_physics_engine - INFO - Fluid dynamics model initialized
2025-07-04 16:41:52,635 - digital_twin.simulation.tank_physics_engine - INFO - Fluid dynamics model initialized
2025-07-04 16:41:52,636 - digital_twin.simulation.tank_physics_engine - INFO - Tank physics engine initialized successfully
2025-07-04 16:41:52,636 - digital_twin.simulation.tank_physics_engine - INFO - Starting physics simulation
2025-07-04 16:41:52,636 - __main__ - INFO - Physics simulation initialized successfully
2025-07-04 16:41:52,666 - __main__ - INFO - Web API initialized successfully
2025-07-04 16:41:52,667 - __main__ - INFO - All components initialized successfully
2025-07-04 16:41:52,669 - __main__ - INFO - Tank Control System started successfully
2025-07-04 16:41:52,669 - __main__ - INFO - API server running on http://0.0.0.0:8000
2025-07-04 16:41:52,670 - __main__ - INFO - API documentation: http://0.0.0.0:8000/docs
2025-07-04 16:41:54,219 - src.api.tank_endpoints - INFO - Tank system initialized successfully
2025-07-04 16:41:54,220 - digital_twin.simulation.tank_physics_engine - INFO - Stopping physics simulation
2025-07-04 16:46:49,286 - __main__ - INFO - Starting Tank Control System...
2025-07-04 16:46:49,318 - __main__ - INFO - Configuration loaded from config/tank_system_config.yaml
2025-07-04 16:46:49,318 - __main__ - INFO - Initializing tank system components...
2025-07-04 16:46:49,319 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
2025-07-04 16:46:49,319 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
2025-07-04 16:46:49,319 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
2025-07-04 16:46:49,319 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
2025-07-04 16:46:49,319 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 1.5, 'ki': 0.05, 'kd': 0.02}
2025-07-04 16:46:49,320 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 1.0, 'ki': 0.1, 'kd': 0.01}
2025-07-04 16:46:49,320 - digital_twin.core.tank_control_algorithms - INFO - Added tank tank_001 to control manager
2025-07-04 16:46:49,320 - __main__ - INFO - Loaded tank configuration: tank_001
2025-07-04 16:46:49,320 - digital_twin.core.tank_control_algorithms - INFO - PID controller initialized with gains: {'kp': 2.0, 'ki': 0.1, 'kd': 0.05}
2025-07-04 16:46:49,320 - digital_twin.core.tank_control_algorithms - INFO - Added tank tank_002 to control manager
2025-07-04 16:46:49,320 - __main__ - INFO - Loaded tank configuration: tank_002
2025-07-04 16:46:49,321 - __main__ - ERROR - Error initializing SCADA integration: TankControlConfiguration.__init__() got an unexpected keyword argument 'name'
2025-07-04 16:46:49,321 - digital_twin.simulation.tank_physics_engine - INFO - Initializing tank physics engine
2025-07-04 16:46:49,335 - digital_twin.simulation.tank_physics_engine - INFO - Setup 4 heating zones: [(0, 11), (12, 23), (24, 35), (36, 47)]
2025-07-04 16:46:49,336 - digital_twin.simulation.tank_physics_engine - INFO - Heat transfer model initialized with 50 nodes
2025-07-04 16:46:49,337 - digital_twin.simulation.tank_physics_engine - INFO - Setup 1 heating zones: [(0, 49)]
2025-07-04 16:46:49,338 - digital_twin.simulation.tank_physics_engine - INFO - Heat transfer model initialized with 50 nodes
2025-07-04 16:46:49,338 - digital_twin.simulation.tank_physics_engine - INFO - Fluid dynamics model initialized
2025-07-04 16:46:49,338 - digital_twin.simulation.tank_physics_engine - INFO - Fluid dynamics model initialized
2025-07-04 16:46:49,338 - digital_twin.simulation.tank_physics_engine - INFO - Tank physics engine initialized successfully
2025-07-04 16:46:49,339 - digital_twin.simulation.tank_physics_engine - INFO - Starting physics simulation
2025-07-04 16:46:49,339 - __main__ - INFO - Physics simulation initialized successfully
2025-07-04 16:46:49,368 - __main__ - INFO - Web API initialized successfully
2025-07-04 16:46:49,369 - __main__ - INFO - All components initialized successfully
2025-07-04 16:46:49,380 - __main__ - INFO - Tank Control System started successfully
2025-07-04 16:46:49,380 - __main__ - INFO - API server running on http://0.0.0.0:8000
2025-07-04 16:46:49,381 - __main__ - INFO - API documentation: http://0.0.0.0:8000/docs
2025-07-04 16:46:49,522 - src.api.tank_endpoints - INFO - Tank system initialized successfully
2025-07-04 16:46:49,524 - digital_twin.simulation.tank_physics_engine - INFO - Stopping physics simulation
