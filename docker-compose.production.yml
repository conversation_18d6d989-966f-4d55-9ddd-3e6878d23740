# Production Docker Compose for MLOps Digital Twin Platform
version: '3.8'

services:
  # Main Application Service
  digital-twin-app:
    build:
      context: .
      dockerfile: Dockerfile.production
      target: production
    container_name: digital-twin-app
    ports:
      - "8000:8000"
      - "8001:8001"
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - DATABASE_URL=********************************************/digital_twin
      - REDIS_URL=redis://redis:6379/0
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=${INFLUXDB_TOKEN:-admin-token}
      - INFLUXDB_ORG=digital-twin
      - INFLUXDB_BUCKET=metrics
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - OPENWEATHER_API_KEY=${OPENWEATHER_API_KEY:-your-api-key}
      - LOG_LEVEL=INFO
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./models:/app/models
      - ./config:/app/config
    depends_on:
      - postgres
      - redis
      - influxdb
      - minio
    networks:
      - digital-twin-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Streamlit Dashboard
  dashboard:
    build:
      context: .
      dockerfile: Dockerfile.production
      target: production
    container_name: digital-twin-dashboard
    ports:
      - "8501:8501"
    environment:
      - API_URL=http://digital-twin-app:8000
      - STREAMLIT_SERVER_PORT=8501
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
    command: ["streamlit", "run", "src/monitoring/web_dashboard.py", "--server.address", "0.0.0.0", "--server.port", "8501"]
    depends_on:
      - digital-twin-app
    networks:
      - digital-twin-network
    restart: unless-stopped

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: digital-twin-postgres
    environment:
      - POSTGRES_DB=digital_twin
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_HOST_AUTH_METHOD=scram-sha-256
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    ports:
      - "5432:5432"
    networks:
      - digital-twin-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: digital-twin-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - digital-twin-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # InfluxDB Time-Series Database
  influxdb:
    image: influxdb:2.7-alpine
    container_name: digital-twin-influxdb
    ports:
      - "8086:8086"
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=admin
      - DOCKER_INFLUXDB_INIT_PASSWORD=adminpassword
      - DOCKER_INFLUXDB_INIT_ORG=digital-twin
      - DOCKER_INFLUXDB_INIT_BUCKET=metrics
      - DOCKER_INFLUXDB_INIT_ADMIN_TOKEN=admin-token
    volumes:
      - influxdb_data:/var/lib/influxdb2
      - influxdb_config:/etc/influxdb2
    networks:
      - digital-twin-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8086/ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    container_name: digital-twin-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
      - MINIO_DEFAULT_BUCKETS=datalake,models,artifacts
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - digital-twin-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Grafana Monitoring
  grafana:
    image: grafana/grafana:latest
    container_name: digital-twin-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - influxdb
    networks:
      - digital-twin-network
    restart: unless-stopped

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: digital-twin-prometheus
    ports:
      - "9090:9090"
    volumes:
      - prometheus_data:/prometheus
      - ./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - digital-twin-network
    restart: unless-stopped

  # MLflow Model Registry
  mlflow:
    image: python:3.11-slim
    container_name: digital-twin-mlflow
    ports:
      - "5000:5000"
    environment:
      - MLFLOW_BACKEND_STORE_URI=********************************************/mlflow
      - MLFLOW_DEFAULT_ARTIFACT_ROOT=s3://mlflow/artifacts
      - AWS_ACCESS_KEY_ID=minioadmin
      - AWS_SECRET_ACCESS_KEY=minioadmin
      - MLFLOW_S3_ENDPOINT_URL=http://minio:9000
    command: >
      bash -c "
        pip install mlflow[extras] psycopg2-binary boto3 &&
        mlflow server 
        --backend-store-uri ********************************************/mlflow 
        --default-artifact-root s3://mlflow/artifacts 
        --host 0.0.0.0 
        --port 5000
      "
    depends_on:
      - postgres
      - minio
    networks:
      - digital-twin-network
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: digital-twin-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./config/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - digital-twin-app
      - dashboard
      - grafana
    networks:
      - digital-twin-network
    restart: unless-stopped

# Volumes
volumes:
  postgres_data:
  redis_data:
  influxdb_data:
  influxdb_config:
  minio_data:
  grafana_data:
  prometheus_data:

# Networks
networks:
  digital-twin-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16