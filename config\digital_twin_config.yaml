# Digital Twin Configuration
# Main configuration file for digital twin system

# Digital Twin Engine Configuration
digital_twin:
  engine:
    reconciler_type: "kalman"
    update_frequency: 10.0  # Hz
    max_twins: 100
    enable_logging: true
    log_level: "INFO"
    
  twins:
    # Default twin configuration
    default:
      update_frequency: 1.0  # Hz
      synchronization_threshold: 0.1
      model_version: "v1.0"
      
    # Asphalt heating system twins
    asphalt_heating:
      name: "Asphalt Heating System"
      description: "Digital twin for asphalt heating control"
      update_frequency: 5.0  # Hz
      synchronization_threshold: 0.05
      physics_parameters:
        thermal_conductivity: 0.5
        specific_heat: 920.0
        density: 2400.0
        ambient_temperature: 20.0
        heat_transfer_coefficient: 25.0
        emissivity: 0.95
        geometry_length: 10.0
        geometry_width: 5.0
        geometry_height: 0.1
        grid_size_x: 50
        grid_size_y: 25
        time_step: 0.1
      scada_tags:
        - "temperature_01"
        - "temperature_02"
        - "temperature_03"
        - "heat_flux_01"
        - "heat_flux_02"
        - "control_output_01"
        - "control_output_02"
        - "system_status"

# SCADA Integration Configuration
scada:
  # OPC UA Configuration
  opc_ua:
    enabled: true
    endpoint_url: "opc.tcp://localhost:4840"
    username: null
    password: null
    security_policy: "None"
    security_mode: "None"
    session_timeout: 60000
    connection_timeout: 10000
    keep_alive_interval: 5000
    
    tags:
      - name: "temperature_01"
        node_id: "ns=2;s=Temperature.Zone1"
        data_type: "float"
        description: "Temperature sensor zone 1"
        scaling_factor: 1.0
        offset: 0.0
        alarm_high: 200.0
        alarm_low: 0.0
        
      - name: "temperature_02"
        node_id: "ns=2;s=Temperature.Zone2"
        data_type: "float"
        description: "Temperature sensor zone 2"
        scaling_factor: 1.0
        offset: 0.0
        alarm_high: 200.0
        alarm_low: 0.0
        
      - name: "temperature_03"
        node_id: "ns=2;s=Temperature.Zone3"
        data_type: "float"
        description: "Temperature sensor zone 3"
        scaling_factor: 1.0
        offset: 0.0
        alarm_high: 200.0
        alarm_low: 0.0
        
      - name: "heat_flux_01"
        node_id: "ns=2;s=HeatFlux.Zone1"
        data_type: "float"
        description: "Heat flux sensor zone 1"
        scaling_factor: 1.0
        offset: 0.0
        
      - name: "heat_flux_02"
        node_id: "ns=2;s=HeatFlux.Zone2"
        data_type: "float"
        description: "Heat flux sensor zone 2"
        scaling_factor: 1.0
        offset: 0.0
        
      - name: "control_output_01"
        node_id: "ns=2;s=Control.Heater1"
        data_type: "float"
        description: "Heater 1 control output"
        scaling_factor: 1.0
        offset: 0.0
        
      - name: "control_output_02"
        node_id: "ns=2;s=Control.Heater2"
        data_type: "float"
        description: "Heater 2 control output"
        scaling_factor: 1.0
        offset: 0.0
        
      - name: "system_status"
        node_id: "ns=2;s=System.Status"
        data_type: "bool"
        description: "System operational status"
  
  # Modbus Configuration
  modbus:
    enabled: false
    protocol: "tcp"
    host: "localhost"
    port: 502
    timeout: 3.0
    
    tags:
      - name: "temperature_mb_01"
        slave_id: 1
        function_code: "READ_INPUT_REGISTERS"
        address: 0
        data_type: "FLOAT32"
        count: 2
        description: "Modbus temperature sensor 1"
        
      - name: "control_output_mb_01"
        slave_id: 1
        function_code: "WRITE_SINGLE_REGISTER"
        address: 100
        data_type: "UINT16"
        count: 1
        description: "Modbus control output 1"

# Physics Simulation Configuration
simulation:
  physics_engine:
    solver_type: "finite_difference"
    real_time_factor: 1.0
    max_simulation_time: 3600.0
    output_frequency: 10
    enable_checkpointing: true
    checkpoint_frequency: 100
    
  physics_parameters:
    thermal_conductivity: 0.5
    specific_heat: 920.0
    density: 2400.0
    ambient_temperature: 20.0
    heat_transfer_coefficient: 25.0
    emissivity: 0.95
    stefan_boltzmann: 5.67e-8
    geometry_length: 10.0
    geometry_width: 5.0
    geometry_height: 0.1
    grid_size_x: 50
    grid_size_y: 25
    time_step: 0.1
    
  boundary_conditions:
    left_bc_type: "neumann"
    left_bc_value: 0.0
    right_bc_type: "neumann"
    right_bc_value: 0.0
    top_bc_type: "convection"
    top_bc_value: 25.0
    bottom_bc_type: "dirichlet"
    bottom_bc_value: 20.0
    
  convergence:
    tolerance: 1e-6
    max_iterations: 1000

# Data Processing Configuration
data_processing:
  kafka:
    enabled: false
    bootstrap_servers: "localhost:9092"
    topics:
      - "digital_twin_data"
      - "scada_measurements"
      - "simulation_outputs"
      - "control_commands"
    
  redis:
    enabled: false
    host: "localhost"
    port: 6379
    db: 0
    password: null
    
  influxdb:
    enabled: false
    host: "localhost"
    port: 8086
    database: "digital_twin"
    username: null
    password: null
    
  # Data quality validation
  validation:
    enabled: true
    temperature_range: [-50, 300]  # °C
    heat_flux_range: [0, 10000]    # W/m²
    max_change_rate: 10.0          # °C/s
    outlier_threshold: 3.0         # standard deviations

# API Configuration
api:
  host: "0.0.0.0"
  port: 8000
  debug: false
  cors_enabled: true
  cors_origins: ["*"]
  
  # Authentication
  auth:
    enabled: false
    secret_key: "your-secret-key"
    algorithm: "HS256"
    token_expire_hours: 24
    
  # Rate limiting
  rate_limit:
    enabled: true
    requests_per_minute: 100
    
  # API endpoints
  endpoints:
    twins: "/api/v1/twins"
    scada: "/api/v1/scada"
    simulation: "/api/v1/simulation"
    data: "/api/v1/data"

# Monitoring Configuration
monitoring:
  prometheus:
    enabled: false
    port: 8001
    metrics_path: "/metrics"
    
  grafana:
    enabled: false
    dashboard_path: "./dashboards"
    
  logging:
    level: "INFO"
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: "digital_twin.log"
    max_size: "10MB"
    backup_count: 5
    
  alerts:
    enabled: true
    channels:
      - type: "email"
        recipients: ["<EMAIL>"]
      - type: "slack"
        webhook_url: "https://hooks.slack.com/services/..."
        
    rules:
      - name: "high_temperature"
        condition: "temperature > 180"
        severity: "warning"
        
      - name: "system_offline"
        condition: "system_status == false"
        severity: "critical"
        
      - name: "twin_desync"
        condition: "deviation > 5.0"
        severity: "warning"

# Deployment Configuration
deployment:
  mode: "development"  # development, production, testing
  
  docker:
    enabled: false
    registry: "localhost:5000"
    image_tag: "latest"
    
  kubernetes:
    enabled: false
    namespace: "digital-twin"
    replicas: 3
    resources:
      requests:
        cpu: "500m"
        memory: "1Gi"
      limits:
        cpu: "2000m"
        memory: "4Gi"
        
  scaling:
    auto_scaling: false
    min_replicas: 1
    max_replicas: 10
    target_cpu_utilization: 70
    target_memory_utilization: 80