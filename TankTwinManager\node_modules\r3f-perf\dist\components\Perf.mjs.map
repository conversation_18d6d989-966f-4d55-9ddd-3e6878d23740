{"version": 3, "file": "Perf.mjs", "sources": ["../../src/components/Perf.tsx"], "sourcesContent": null, "names": [], "mappings": ";;;;;;;;;AA+Ba,MAAA,cAAc,CAAC,eAAoC;AAC9D,QAAM,SAAiB;AAAA,IACrB,WAAW;AAAA,IACX,KAAK,aAAa,kBAAkB;AAAA,IACpC,KAAK,aAAa,iBAAiB;AAAA,IACnC,KAAK,aAAa,gBAAgB;AAAA,IAClC,QAAQ,aAAa,eAAe;AAAA,EAAA;AAE/B,SAAA;AACT;AAEA,MAAM,gBAAkC,CAAC,EAAE,WAAW,iBAAiB;AACrE,QAAM,kBAAkB,QAAQ,CAAC,MAAM,EAAE,eAAe;AACxD,QAAM,WAAW,QAAQ,CAAC,MAAM,EAAE,QAAQ;AAGxC,SAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,OACE,YACI;AAAA,QACE,OAAO,kBACH,YAAY,UAAU,EAAE,UAAU,aAClC,OAAO,YAAY,UAAU,EAAE,GAAG;AAAA,MAAA,IAExC,CAAC;AAAA,MACL,UAAA;AAAA,QAAA;AAAA,QACG,kBAAkB,GAAG,QAAQ,OAAO;AAAA,MAAA;AAAA,IAAA;AAAA,EAAA;AAG/C;AAEA,MAAM,YAA8B,CAAC,EAAE,WAAW,YAAY,YAAY,cAAc;AACtF,QAAM,KAAK,QAAQ,CAAC,UAAU,MAAM,EAAE;AAE/B,SAAA,0BACJ,gBACC,EAAA,UAAA;AAAA,IAAA,qBAAC,OACC,EAAA,UAAA;AAAA,MAAA,oBAAC,mBAAkB,EAAA;AAAA,MACnB;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,OACE,YACI;AAAA,YACE,OAAO,OAAO,YAAY,UAAU,EAAE,IAAI,SAAU,CAAA;AAAA,UAAA,IAEtD,CAAC;AAAA,UACL,UAAA;AAAA,QAAA;AAAA,MAEJ;AAAA,MACA,oBAAC,cAAW,UAAE,KAAA,CAAA;AAAA,IAAA,GAChB;AAAA,yBACC,OACC,EAAA,UAAA;AAAA,MAAA,oBAAC,qBAAoB,EAAA;AAAA,MACrB;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,OACE,YACI;AAAA,YACE,OAAO,OAAO,YAAY,UAAU,EAAE,IAAI,SAAU,CAAA;AAAA,UAAA,IAEtD,CAAC;AAAA,UACL,UAAA;AAAA,QAAA;AAAA,MAEJ;AAAA,MACA,oBAAC,cAAW,UAAE,KAAA,CAAA;AAAA,IAAA,GAChB;AAAA,yBAYC,OACC,EAAA,UAAA;AAAA,MAAA,oBAAC,cAAa,EAAA;AAAA,MACd,oBAAC,eAAc,EAAA,WAAsB,WAAwB,CAAA;AAAA,IAAA,GAC/D;AAAA,IACC,CAAC,WAAW,MACX,qBAAC,OACC,EAAA,UAAA;AAAA,MAAA,oBAAC,sBAAqB,EAAA;AAAA,MAEtB,oBAAC,SAAO,UAAG,GAAA,KAAK,OAAO,UAAU,IAAI,SAAS,QAAQ,CAAA;AAAA,IAAA,GACxD;AAAA,IAED,CAAC,WAAW,MACX,qBAAC,OACC,EAAA,UAAA;AAAA,MAAA,oBAAC,gBAAe,EAAA;AAAA,MAChB,oBAAC,SAAM,UAAS,YAAA,CAAA;AAAA,IAAA,GAClB;AAAA,IAED,mCACE,OACC,EAAA,UAAA;AAAA,MAAA,oBAAC,cAAa,EAAA;AAAA,0BACb,OAAM,EAAA,OAAO,YAAY,EAAE,OAAO,OAAO,YAAY,UAAU,EAAE,MAAM,IAAI,IAAI,CAAC,GAAI,qBAAW,MAAK;AAAA,MACpG,WAAW,QAAS,oBAAA,YAAA,EAAY,qBAAW,MAAK;AAAA,IAAA,GACnD;AAAA,EAAA,EAEJ,CAAA,IACE;AACN;AAEA,MAAM,SAA2B,CAAC;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,SAEI,qBAAA,UAAA,EAAA,UAAA;AAAA,IAAA,oBAAC,WAAU,EAAA,WAAsB,YAAwB,YAAwB,SAAkB;AAAA,IAClG,CAAC,WACA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IACF;AAAA,EAEJ,EAAA,CAAA;AAEJ;AAEA,MAAM,SAA2B,CAAC,EAAE,mBAAmB;AACrD,8BACG,OACC,EAAA,UAAA;AAAA,IAAA,qBAAC,OACC,EAAA,UAAA;AAAA,MAAA,oBAAC,YAAW,EAAA;AAAA,MACZ,oBAAC,SAAM,UAAU,aAAA,CAAA;AAAA,IAAA,GACnB;AAAA,yBACC,OACC,EAAA,UAAA;AAAA,MAAA,oBAAC,WAAU,EAAA;AAAA,MACX,oBAAC,SAAM,UAAQ,WAAA,CAAA;AAAA,IAAA,GACjB;AAAA,yBACC,OACC,EAAA,UAAA;AAAA,MAAA,oBAAC,iBAAgB,EAAA;AAAA,MACjB,oBAAC,SAAM,UAAO,UAAA,CAAA;AAAA,IAAA,GAChB;AAAA,yBACC,OACC,EAAA,UAAA;AAAA,MAAA,oBAAC,WAAU,EAAA;AAAA,MACX,oBAAC,SAAM,UAAK,QAAA,CAAA;AAAA,IAAA,GACd;AAAA,yBACC,OACC,EAAA,UAAA;AAAA,MAAA,oBAAC,SAAQ,EAAA;AAAA,MACT,oBAAC,SAAM,UAAM,SAAA,CAAA;AAAA,IAAA,GACf;AAAA,IACC,qCACE,OACC,EAAA,UAAA;AAAA,MAAA,oBAAC,kBAAiB,EAAA;AAAA,MAClB,oBAAC,SAAM,UAAQ,WAAA,CAAA;AAAA,IAAA,GACjB;AAAA,EAEJ,EAAA,CAAA;AAEJ;AAEA,MAAM,WAAW,CAAC,EAAE,KAAK,OAAO,UAAe;AAC7C,QAAM,WAAW,QAAQ,CAAC,MAAoB,EAAE,GAAG;AAEjD,SAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAW,GAAG,aAAa,MAAM,8BAA8B,EAAE;AAAA,MACjE,SAAS,MAAM;AACb,YAAI,IAAI;AACA,gBAAA,EAAE,KAAU;AAAA,MACtB;AAAA,MACA,UAAA,oBAAC,UAAM,UAAM,MAAA,CAAA;AAAA,IAAA;AAAA,EAAA;AAGnB;AACA,MAAM,YAA8B,CAAC,EAAE,eAAe,WAAW,aAAa,mBAAmB;AAC/F,QAAM,CAAC,MAAM,GAAG,IAAI,MAAM,SAAS,aAAa;AAEhD,8BACG,QACC,EAAA,UAAA;AAAA,IAAC,oBAAA,eAAA,EAAc,MAAY,WAAsB,aAA4B,CAAA;AAAA,IAC5E,iBAAiB,CAAC,cAAc,OAC9B,qBAAA,iBAAA,EAAgB,WAAW,iBAEzB,UAAA;AAAA,MAAA,mCAAgB,UAAS,EAAA,KAAI,YAAW,OAAM,YAAW,KAAU;AAAA,MACnE,eAAgB,oBAAA,UAAA,EAAS,KAAI,SAAQ,OAAM,SAAQ,KAAU;AAAA,MAC9D;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,SAAS,MAAM;AACb,gBAAI,CAAC,IAAI;AAAA,UACX;AAAA,UACC,UAAA,4BACE,QACC,EAAA,UAAA;AAAA,YAAA,oBAAC,kBAAiB,EAAA;AAAA,YAAE;AAAA,UACtB,EAAA,CAAA,yBAEC,QACC,EAAA,UAAA;AAAA,YAAA,oBAAC,gBAAe,EAAA;AAAA,YAAE;AAAA,UAAA,GACpB;AAAA,QAAA;AAAA,MAEJ;AAAA,IAAA,GACF;AAAA,EAEJ,EAAA,CAAA;AAEJ;AAEA,MAAM,gBAAgB,CAAC,EAAE,MAAM,WAAW,mBAAwB;AAChE,QAAM,MAAM,QAAQ,CAAC,UAAU,MAAM,GAAG;AAExC,SAEI,qBAAA,UAAA,EAAA,UAAA;AAAA,IAAA,oBAAC,UAAO,cAA4B;AAAA,IACnC,QACE,oBAAA,OAAA,EACC,UAAC,oBAAA,iBAAA,EAAgB,OAAO,EAAE,WAAW,YAAY,SAAS,KACvD,UAAA,QAAQ,cAAe,oBAAA,YAAA,CAAA,CAAW,EACrC,CAAA,GACF;AAAA,EAEJ,EAAA,CAAA;AAEJ;AAIO,MAAM,OAAyB,CAAC;AAAA,EACrC,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA,cAAc;AAAA,EACd,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACE,QAAA,mBAAmB,OAAO,IAAI;AAEpC,SAEI,qBAAA,UAAA,EAAA,UAAA;AAAA,IAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IACF;AAAA,IACA,oBAAC,aAAY,EAAA,MAAK,YAChB,UAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,YACG,YAAY,IAAI,OAAO,SAAS,IAAI,OAAO,IAAI,WAAW,WAAW,EAAE,IAAI,UAAU,YAAY,EAAE;AAAA,QAEtG,OAAO,EAAE,WAAW,UAAU,SAAS,YAAY,UAAU,QAAQ,GAAG,MAAM;AAAA,QAC9E,KAAK;AAAA,QACL,UAAA;AAAA,UAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YAAA;AAAA,UACF;AAAA,UACA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YAAA;AAAA,UACF;AAAA,QAAA;AAAA,MAAA;AAAA,IAAA,GAEJ;AAAA,EACF,EAAA,CAAA;AAEJ;"}