{"version": 3, "file": "Perf.js", "sources": ["../../src/components/Perf.tsx"], "sourcesContent": null, "names": ["usePerf", "jsxs", "PerfB", "PerfIContainer", "PerfI", "jsx", "LightningBoltIcon", "PerfSmallI", "RulerHorizontalIcon", "LapTimerIcon", "TextAlignJustifyIcon", "VercelLogoIcon", "BarChartIcon", "Fragment", "MarginIcon", "ImageIcon", "ActivityLogIcon", "MinusIcon", "DotIcon", "DropdownMenuIcon", "Toggle", "set<PERSON>erf", "ToggleContainer", "TriangleDownIcon", "TriangleUpIcon", "ContainerScroll", "ProgramsUI", "useRef", "PerfHeadless", "HtmlMinimal", "PerfS", "ChartUI"], "mappings": ";;;;;;;;;;;AA+Ba,MAAA,cAAc,CAAC,eAAoC;AAC9D,QAAM,SAAiB;AAAA,IACrB,WAAW;AAAA,IACX,KAAK,aAAa,kBAAkB;AAAA,IACpC,KAAK,aAAa,iBAAiB;AAAA,IACnC,KAAK,aAAa,gBAAgB;AAAA,IAClC,QAAQ,aAAa,eAAe;AAAA,EAAA;AAE/B,SAAA;AACT;AAEA,MAAM,gBAAkC,CAAC,EAAE,WAAW,iBAAiB;AACrE,QAAM,kBAAkBA,MAAAA,QAAQ,CAAC,MAAM,EAAE,eAAe;AACxD,QAAM,WAAWA,MAAAA,QAAQ,CAAC,MAAM,EAAE,QAAQ;AAGxC,SAAAC,2BAAA;AAAA,IAACC,OAAA;AAAA,IAAA;AAAA,MACC,OACE,YACI;AAAA,QACE,OAAO,kBACH,YAAY,UAAU,EAAE,UAAU,aAClC,OAAO,YAAY,UAAU,EAAE,GAAG;AAAA,MAAA,IAExC,CAAC;AAAA,MACL,UAAA;AAAA,QAAA;AAAA,QACG,kBAAkB,GAAG,QAAQ,OAAO;AAAA,MAAA;AAAA,IAAA;AAAA,EAAA;AAG/C;AAEA,MAAM,YAA8B,CAAC,EAAE,WAAW,YAAY,YAAY,cAAc;AACtF,QAAM,KAAKF,MAAAA,QAAQ,CAAC,UAAU,MAAM,EAAE;AAE/B,SAAA,qCACJG,OACC,gBAAA,EAAA,UAAA;AAAA,IAAAF,gCAACG,OAAAA,OACC,EAAA,UAAA;AAAA,MAAAC,2BAAA,IAACC,WAAkB,mBAAA,EAAA;AAAA,MACnBD,2BAAA;AAAA,QAACH,OAAA;AAAA,QAAA;AAAA,UACC,OACE,YACI;AAAA,YACE,OAAO,OAAO,YAAY,UAAU,EAAE,IAAI,SAAU,CAAA;AAAA,UAAA,IAEtD,CAAC;AAAA,UACL,UAAA;AAAA,QAAA;AAAA,MAEJ;AAAA,MACAG,2BAAAA,IAACE,qBAAW,UAAE,KAAA,CAAA;AAAA,IAAA,GAChB;AAAA,oCACCH,OAAAA,OACC,EAAA,UAAA;AAAA,MAAAC,2BAAA,IAACG,WAAoB,qBAAA,EAAA;AAAA,MACrBH,2BAAA;AAAA,QAACH,OAAA;AAAA,QAAA;AAAA,UACC,OACE,YACI;AAAA,YACE,OAAO,OAAO,YAAY,UAAU,EAAE,IAAI,SAAU,CAAA;AAAA,UAAA,IAEtD,CAAC;AAAA,UACL,UAAA;AAAA,QAAA;AAAA,MAEJ;AAAA,MACAG,2BAAAA,IAACE,qBAAW,UAAE,KAAA,CAAA;AAAA,IAAA,GAChB;AAAA,oCAYCH,OAAAA,OACC,EAAA,UAAA;AAAA,MAAAC,2BAAA,IAACI,WAAa,cAAA,EAAA;AAAA,MACdJ,2BAAAA,IAAC,eAAc,EAAA,WAAsB,WAAwB,CAAA;AAAA,IAAA,GAC/D;AAAA,IACC,CAAC,WAAW,MACXJ,2BAAA,KAACG,OACC,OAAA,EAAA,UAAA;AAAA,MAAAC,2BAAA,IAACK,WAAqB,sBAAA,EAAA;AAAA,MAEtBL,2BAAAA,IAACH,gBAAO,UAAG,GAAA,KAAK,OAAO,UAAU,IAAI,SAAS,QAAQ,CAAA;AAAA,IAAA,GACxD;AAAA,IAED,CAAC,WAAW,MACXD,2BAAA,KAACG,OACC,OAAA,EAAA,UAAA;AAAA,MAAAC,2BAAA,IAACM,WAAe,gBAAA,EAAA;AAAA,MAChBN,2BAAAA,IAACH,gBAAM,UAAS,YAAA,CAAA;AAAA,IAAA,GAClB;AAAA,IAED,8CACEE,cACC,EAAA,UAAA;AAAA,MAAAC,2BAAA,IAACO,WAAa,cAAA,EAAA;AAAA,qCACbV,OAAM,OAAA,EAAA,OAAO,YAAY,EAAE,OAAO,OAAO,YAAY,UAAU,EAAE,MAAM,IAAI,IAAI,CAAC,GAAI,qBAAW,MAAK;AAAA,MACpG,WAAW,QAASG,+BAAAE,OAAAA,YAAA,EAAY,qBAAW,MAAK;AAAA,IAAA,GACnD;AAAA,EAAA,EAEJ,CAAA,IACE;AACN;AAEA,MAAM,SAA2B,CAAC;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,SAEIN,2BAAA,KAAAY,qBAAA,EAAA,UAAA;AAAA,IAAAR,2BAAA,IAAC,WAAU,EAAA,WAAsB,YAAwB,YAAwB,SAAkB;AAAA,IAClG,CAAC,WACAA,2BAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IACF;AAAA,EAEJ,EAAA,CAAA;AAEJ;AAEA,MAAM,SAA2B,CAAC,EAAE,mBAAmB;AACrD,yCACG,OACC,EAAA,UAAA;AAAA,IAAAJ,gCAACG,OAAAA,OACC,EAAA,UAAA;AAAA,MAAAC,2BAAA,IAACS,WAAW,YAAA,EAAA;AAAA,MACZT,2BAAAA,IAACH,gBAAM,UAAU,aAAA,CAAA;AAAA,IAAA,GACnB;AAAA,oCACCE,OAAAA,OACC,EAAA,UAAA;AAAA,MAAAC,2BAAA,IAACU,WAAU,WAAA,EAAA;AAAA,MACXV,2BAAAA,IAACH,gBAAM,UAAQ,WAAA,CAAA;AAAA,IAAA,GACjB;AAAA,oCACCE,OAAAA,OACC,EAAA,UAAA;AAAA,MAAAC,2BAAA,IAACW,WAAgB,iBAAA,EAAA;AAAA,MACjBX,2BAAAA,IAACH,gBAAM,UAAO,UAAA,CAAA;AAAA,IAAA,GAChB;AAAA,oCACCE,OAAAA,OACC,EAAA,UAAA;AAAA,MAAAC,2BAAA,IAACY,WAAU,WAAA,EAAA;AAAA,MACXZ,2BAAAA,IAACH,gBAAM,UAAK,QAAA,CAAA;AAAA,IAAA,GACd;AAAA,oCACCE,OAAAA,OACC,EAAA,UAAA;AAAA,MAAAC,2BAAA,IAACa,WAAQ,SAAA,EAAA;AAAA,MACTb,2BAAAA,IAACH,gBAAM,UAAM,SAAA,CAAA;AAAA,IAAA,GACf;AAAA,IACC,gDACEE,cACC,EAAA,UAAA;AAAA,MAAAC,2BAAA,IAACc,WAAiB,kBAAA,EAAA;AAAA,MAClBd,2BAAAA,IAACH,gBAAM,UAAQ,WAAA,CAAA;AAAA,IAAA,GACjB;AAAA,EAEJ,EAAA,CAAA;AAEJ;AAEA,MAAM,WAAW,CAAC,EAAE,KAAK,OAAO,UAAe;AAC7C,QAAM,WAAWF,MAAAA,QAAQ,CAAC,MAAoB,EAAE,GAAG;AAEjD,SAAAK,2BAAA;AAAA,IAACe,OAAA;AAAA,IAAA;AAAA,MACC,WAAW,GAAG,aAAa,MAAM,8BAA8B,EAAE;AAAA,MACjE,SAAS,MAAM;AACb,YAAI,IAAI;AACAC,sBAAA,EAAE,KAAU;AAAA,MACtB;AAAA,MACA,UAAAhB,2BAAAA,IAAC,UAAM,UAAM,MAAA,CAAA;AAAA,IAAA;AAAA,EAAA;AAGnB;AACA,MAAM,YAA8B,CAAC,EAAE,eAAe,WAAW,aAAa,mBAAmB;AAC/F,QAAM,CAAC,MAAM,GAAG,IAAI,MAAM,SAAS,aAAa;AAEhD,yCACG,QACC,EAAA,UAAA;AAAA,IAACA,2BAAAA,IAAA,eAAA,EAAc,MAAY,WAAsB,aAA4B,CAAA;AAAA,IAC5E,iBAAiB,CAAC,cAAc,OAC9BJ,2BAAAA,KAAAqB,OAAAA,iBAAA,EAAgB,WAAW,iBAEzB,UAAA;AAAA,MAAA,8CAAgB,UAAS,EAAA,KAAI,YAAW,OAAM,YAAW,KAAU;AAAA,MACnE,eAAgBjB,2BAAAA,IAAA,UAAA,EAAS,KAAI,SAAQ,OAAM,SAAQ,KAAU;AAAA,MAC9DA,2BAAA;AAAA,QAACe,OAAA;AAAA,QAAA;AAAA,UACC,SAAS,MAAM;AACb,gBAAI,CAAC,IAAI;AAAA,UACX;AAAA,UACC,UAAA,uCACE,QACC,EAAA,UAAA;AAAA,YAAAf,2BAAA,IAACkB,WAAiB,kBAAA,EAAA;AAAA,YAAE;AAAA,UACtB,EAAA,CAAA,oCAEC,QACC,EAAA,UAAA;AAAA,YAAAlB,2BAAA,IAACmB,WAAe,gBAAA,EAAA;AAAA,YAAE;AAAA,UAAA,GACpB;AAAA,QAAA;AAAA,MAEJ;AAAA,IAAA,GACF;AAAA,EAEJ,EAAA,CAAA;AAEJ;AAEA,MAAM,gBAAgB,CAAC,EAAE,MAAM,WAAW,mBAAwB;AAChE,QAAM,MAAMxB,MAAAA,QAAQ,CAAC,UAAU,MAAM,GAAG;AAExC,SAEIC,2BAAA,KAAAY,qBAAA,EAAA,UAAA;AAAA,IAAAR,+BAAC,UAAO,cAA4B;AAAA,IACnC,QACEA,2BAAAA,IAAA,OAAA,EACC,UAACA,2BAAA,IAAAoB,wBAAA,EAAgB,OAAO,EAAE,WAAW,YAAY,SAAS,KACvD,UAAA,QAAQ,cAAepB,2BAAA,IAAAqB,oBAAA,CAAA,CAAW,EACrC,CAAA,GACF;AAAA,EAEJ,EAAA,CAAA;AAEJ;AAIO,MAAM,OAAyB,CAAC;AAAA,EACrC,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA,cAAc;AAAA,EACd,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACE,QAAA,mBAAmBC,aAAO,IAAI;AAEpC,SAEI1B,2BAAA,KAAAY,qBAAA,EAAA,UAAA;AAAA,IAAAR,2BAAA;AAAA,MAACuB,aAAA;AAAA,MAAA;AAAA,QACC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IACF;AAAA,IACAvB,2BAAAA,IAACwB,YAAAA,aAAY,EAAA,MAAK,YAChB,UAAA5B,2BAAA;AAAA,MAAC6B,OAAA;AAAA,MAAA;AAAA,QACC,YACG,YAAY,IAAI,OAAO,SAAS,IAAI,OAAO,IAAI,WAAW,WAAW,EAAE,IAAI,UAAU,YAAY,EAAE;AAAA,QAEtG,OAAO,EAAE,WAAW,UAAU,SAAS,YAAY,UAAU,QAAQ,GAAG,MAAM;AAAA,QAC9E,KAAK;AAAA,QACL,UAAA;AAAA,UAAAzB,2BAAA;AAAA,YAAC0B,MAAA;AAAA,YAAA;AAAA,cACC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YAAA;AAAA,UACF;AAAA,UACA1B,2BAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YAAA;AAAA,UACF;AAAA,QAAA;AAAA,MAAA;AAAA,IAAA,GAEJ;AAAA,EACF,EAAA,CAAA;AAEJ;;;"}