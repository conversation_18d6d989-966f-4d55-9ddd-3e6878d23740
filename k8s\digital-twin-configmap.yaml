# ConfigMaps for MLOps Digital Twin Platform
apiVersion: v1
kind: ConfigMap
metadata:
  name: digital-twin-config
  namespace: digital-twin
  labels:
    app: digital-twin
    component: config
data:
  # Application configuration
  app.yaml: |
    environment: production
    debug: false
    log_level: INFO
    
    # Service endpoints
    api:
      host: "0.0.0.0"
      port: 8000
      workers: 4
      timeout: 300
      
    # Database configuration
    database:
      pool_size: 20
      max_overflow: 30
      pool_timeout: 30
      pool_recycle: 3600
      
    # Redis configuration
    redis:
      max_connections: 50
      retry_on_timeout: true
      socket_timeout: 30
      
    # InfluxDB configuration
    influxdb:
      timeout: 30
      retries: 3
      batch_size: 1000
      
    # MinIO configuration
    minio:
      timeout: 30
      retries: 3
      secure: false
      
    # ML configuration
    ml:
      model_cache_size: 100
      prediction_timeout: 30
      training_timeout: 3600
      
    # Monitoring configuration
    monitoring:
      metrics_enabled: true
      health_check_interval: 30
      alert_evaluation_interval: 60
      
    # Feature flags
    features:
      weather_integration: true
      anomaly_detection: true
      predictive_maintenance: true
      energy_optimization: true
      
  # Redis configuration
  redis.conf: |
    # Redis configuration for Kubernetes
    bind 0.0.0.0
    port 6379
    protected-mode no
    
    # Memory settings
    maxmemory 512mb
    maxmemory-policy allkeys-lru
    
    # Persistence
    save 900 1
    save 300 10
    save 60 10000
    
    # Append only file
    appendonly yes
    appendfsync everysec
    
    # Logging
    loglevel notice
    
    # Performance
    tcp-keepalive 300
    timeout 0
    
  # Logging configuration
  logging.yaml: |
    version: 1
    disable_existing_loggers: false
    
    formatters:
      standard:
        format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
      detailed:
        format: "%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s"
      json:
        format: "%(asctime)s"
        class: "pythonjsonlogger.jsonlogger.JsonFormatter"
        
    handlers:
      console:
        class: logging.StreamHandler
        level: INFO
        formatter: standard
        stream: ext://sys.stdout
        
      file:
        class: logging.handlers.RotatingFileHandler
        level: DEBUG
        formatter: detailed
        filename: /app/logs/application.log
        maxBytes: 10485760  # 10MB
        backupCount: 5
        
    loggers:
      digital_twin:
        level: DEBUG
        handlers: [console, file]
        propagate: false
        
      uvicorn:
        level: INFO
        handlers: [console]
        propagate: false
        
      sqlalchemy:
        level: WARNING
        handlers: [console]
        propagate: false
        
    root:
      level: INFO
      handlers: [console]

---
# Environment-specific configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: digital-twin-env-config
  namespace: digital-twin
  labels:
    app: digital-twin
    component: config
    environment: production
data:
  # Feature toggles
  WEATHER_INTEGRATION_ENABLED: "true"
  ANOMALY_DETECTION_ENABLED: "true"
  PREDICTIVE_MAINTENANCE_ENABLED: "true"
  ENERGY_OPTIMIZATION_ENABLED: "true"
  MLFLOW_ENABLED: "true"
  
  # Performance settings
  WORKER_PROCESSES: "4"
  MAX_CONCURRENT_REQUESTS: "100"
  REQUEST_TIMEOUT: "300"
  
  # ML settings
  MODEL_CACHE_TTL: "3600"
  PREDICTION_BATCH_SIZE: "100"
  TRAINING_CHECKPOINT_INTERVAL: "100"
  
  # Monitoring settings
  METRICS_COLLECTION_INTERVAL: "30"
  HEALTH_CHECK_TIMEOUT: "10"
  ALERT_COOLDOWN_PERIOD: "300"
  
  # Data retention
  TIME_SERIES_RETENTION_DAYS: "90"
  LOG_RETENTION_DAYS: "30"
  MODEL_RETENTION_DAYS: "365"
  
  # External service timeouts
  WEATHER_API_TIMEOUT: "30"
  SCADA_CONNECTION_TIMEOUT: "60"
  DATABASE_QUERY_TIMEOUT: "30"
  
  # Security settings
  CORS_ORIGINS: "*"
  API_RATE_LIMIT: "100/minute"
  
  # Kubernetes-specific settings
  KUBERNETES_NAMESPACE: "digital-twin"
  POD_NAME: ""  # Will be set by downward API
  NODE_NAME: ""  # Will be set by downward API