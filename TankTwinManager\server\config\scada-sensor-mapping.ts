/**
 * SCADA Sensor Mapping Configuration
 * Maps real SCADA sensors to digital twin components based on actual SCADA screen layout
 * Ensures data consistency and proper tag naming conventions
 */

export interface SCADASensor {
  tagName: string;
  description: string;
  sensorType: 'temperature' | 'level' | 'pressure' | 'flow' | 'status' | 'alarm';
  dataType: 'float' | 'integer' | 'boolean' | 'string';
  unit: string;
  range: {
    min: number;
    max: number;
  };
  precision: number; // decimal places
  scanRate: number; // milliseconds
  alarmLimits?: {
    highHigh?: number;
    high?: number;
    low?: number;
    lowLow?: number;
  };
  digitalTwinMapping: {
    componentType: 'tank' | 'boiler' | 'pump' | 'valve' | 'pipe' | 'loading_station';
    componentId: string;
    property: string;
  };
  communication: {
    protocol: 'modbus' | 'opc_ua' | 'ethernet_ip' | 'profinet';
    address: string;
    register?: number;
    nodeId?: string;
  };
  calibration: {
    lastCalibrated: Date;
    calibrationDue: Date;
    calibrationCertificate: string;
    accuracy: number; // % of full scale
  };
}

export interface SCADATagGroup {
  groupName: string;
  description: string;
  tags: SCADASensor[];
  updateRate: number; // milliseconds
  priority: 'critical' | 'high' | 'normal' | 'low';
}

export interface SCADAAlarmConfiguration {
  alarmId: string;
  tagName: string;
  alarmType: 'high' | 'low' | 'deviation' | 'rate_of_change' | 'digital';
  setpoint: number;
  deadband: number;
  delay: number; // seconds
  priority: 'critical' | 'high' | 'medium' | 'low';
  message: string;
  actions: string[];
  acknowledgmentRequired: boolean;
  autoReset: boolean;
}

// Tank sensor mappings based on SCADA screen layout
export const TANK_SENSOR_MAPPINGS: SCADATagGroup[] = [
  {
    groupName: 'TANK_TEMPERATURES',
    description: 'Tank temperature sensors for all asphalt storage tanks',
    updateRate: 1000, // 1 second
    priority: 'critical',
    tags: [
      {
        tagName: 'TT_ASP_01',
        description: 'Tank ASP-01 Temperature',
        sensorType: 'temperature',
        dataType: 'float',
        unit: '°C',
        range: { min: 0, max: 200 },
        precision: 1,
        scanRate: 1000,
        alarmLimits: {
          highHigh: 180,
          high: 170,
          low: 130,
          lowLow: 120
        },
        digitalTwinMapping: {
          componentType: 'tank',
          componentId: 'ASP-01',
          property: 'temperature'
        },
        communication: {
          protocol: 'modbus',
          address: '************',
          register: 40001
        },
        calibration: {
          lastCalibrated: new Date('2024-01-15'),
          calibrationDue: new Date('2025-01-15'),
          calibrationCertificate: 'CAL-TT-01-2024',
          accuracy: 0.5
        }
      },
      {
        tagName: 'TT_ASP_02',
        description: 'Tank ASP-02 Temperature',
        sensorType: 'temperature',
        dataType: 'float',
        unit: '°C',
        range: { min: 0, max: 200 },
        precision: 1,
        scanRate: 1000,
        alarmLimits: {
          highHigh: 180,
          high: 170,
          low: 130,
          lowLow: 120
        },
        digitalTwinMapping: {
          componentType: 'tank',
          componentId: 'ASP-02',
          property: 'temperature'
        },
        communication: {
          protocol: 'modbus',
          address: '************',
          register: 40002
        },
        calibration: {
          lastCalibrated: new Date('2024-01-15'),
          calibrationDue: new Date('2025-01-15'),
          calibrationCertificate: 'CAL-TT-02-2024',
          accuracy: 0.5
        }
      }
      // Additional tank temperature sensors would follow the same pattern
    ]
  },
  {
    groupName: 'TANK_LEVELS',
    description: 'Tank level sensors for all asphalt storage tanks',
    updateRate: 2000, // 2 seconds
    priority: 'high',
    tags: [
      {
        tagName: 'LT_ASP_01',
        description: 'Tank ASP-01 Level',
        sensorType: 'level',
        dataType: 'float',
        unit: '%',
        range: { min: 0, max: 100 },
        precision: 1,
        scanRate: 2000,
        alarmLimits: {
          highHigh: 95,
          high: 90,
          low: 20,
          lowLow: 10
        },
        digitalTwinMapping: {
          componentType: 'tank',
          componentId: 'ASP-01',
          property: 'currentLevel'
        },
        communication: {
          protocol: 'modbus',
          address: '************',
          register: 40011
        },
        calibration: {
          lastCalibrated: new Date('2024-01-15'),
          calibrationDue: new Date('2025-01-15'),
          calibrationCertificate: 'CAL-LT-01-2024',
          accuracy: 1.0
        }
      }
    ]
  },
  {
    groupName: 'TANK_PRESSURES',
    description: 'Tank pressure sensors for all asphalt storage tanks',
    updateRate: 1000, // 1 second
    priority: 'high',
    tags: [
      {
        tagName: 'PT_ASP_01',
        description: 'Tank ASP-01 Pressure',
        sensorType: 'pressure',
        dataType: 'float',
        unit: 'bar',
        range: { min: 0, max: 5 },
        precision: 2,
        scanRate: 1000,
        alarmLimits: {
          highHigh: 2.8,
          high: 2.5,
          low: 0.5,
          lowLow: 0.2
        },
        digitalTwinMapping: {
          componentType: 'tank',
          componentId: 'ASP-01',
          property: 'pressure'
        },
        communication: {
          protocol: 'modbus',
          address: '************',
          register: 40021
        },
        calibration: {
          lastCalibrated: new Date('2024-01-15'),
          calibrationDue: new Date('2025-01-15'),
          calibrationCertificate: 'CAL-PT-01-2024',
          accuracy: 0.25
        }
      }
    ]
  }
];

// Hot-oil system sensor mappings
export const HOT_OIL_SENSOR_MAPPINGS: SCADATagGroup[] = [
  {
    groupName: 'HOT_OIL_BOILER',
    description: 'Hot-oil boiler sensors and controls',
    updateRate: 500, // 0.5 seconds
    priority: 'critical',
    tags: [
      {
        tagName: 'TT_HOB_SUPPLY',
        description: 'Hot-Oil Boiler Supply Temperature',
        sensorType: 'temperature',
        dataType: 'float',
        unit: '°C',
        range: { min: 0, max: 350 },
        precision: 1,
        scanRate: 500,
        alarmLimits: {
          highHigh: 320,
          high: 300,
          low: 200,
          lowLow: 150
        },
        digitalTwinMapping: {
          componentType: 'boiler',
          componentId: 'HOB-01',
          property: 'currentTemperature'
        },
        communication: {
          protocol: 'modbus',
          address: '************',
          register: 40001
        },
        calibration: {
          lastCalibrated: new Date('2024-01-15'),
          calibrationDue: new Date('2025-01-15'),
          calibrationCertificate: 'CAL-HOB-TT-2024',
          accuracy: 0.5
        }
      },
      {
        tagName: 'FT_HOB_FUEL',
        description: 'Hot-Oil Boiler Fuel Flow',
        sensorType: 'flow',
        dataType: 'float',
        unit: 'm³/h',
        range: { min: 0, max: 300 },
        precision: 1,
        scanRate: 1000,
        alarmLimits: {
          highHigh: 280,
          high: 250,
          low: 10,
          lowLow: 0
        },
        digitalTwinMapping: {
          componentType: 'boiler',
          componentId: 'HOB-01',
          property: 'fuelFlow'
        },
        communication: {
          protocol: 'modbus',
          address: '************',
          register: 40002
        },
        calibration: {
          lastCalibrated: new Date('2024-01-15'),
          calibrationDue: new Date('2025-01-15'),
          calibrationCertificate: 'CAL-HOB-FT-2024',
          accuracy: 1.0
        }
      }
    ]
  },
  {
    groupName: 'HOT_OIL_CIRCULATION',
    description: 'Hot-oil circulation system sensors',
    updateRate: 1000, // 1 second
    priority: 'high',
    tags: [
      {
        tagName: 'FT_HO_MAIN_SUPPLY',
        description: 'Hot-Oil Main Supply Flow',
        sensorType: 'flow',
        dataType: 'float',
        unit: 'L/min',
        range: { min: 0, max: 1500 },
        precision: 1,
        scanRate: 1000,
        alarmLimits: {
          highHigh: 1400,
          high: 1200,
          low: 100,
          lowLow: 50
        },
        digitalTwinMapping: {
          componentType: 'pipe',
          componentId: 'HO-MAIN-SUPPLY',
          property: 'flowRate'
        },
        communication: {
          protocol: 'modbus',
          address: '************',
          register: 40001
        },
        calibration: {
          lastCalibrated: new Date('2024-01-15'),
          calibrationDue: new Date('2025-01-15'),
          calibrationCertificate: 'CAL-HO-FT-2024',
          accuracy: 1.0
        }
      }
    ]
  }
];

// Loading station sensor mappings
export const LOADING_STATION_SENSOR_MAPPINGS: SCADATagGroup[] = [
  {
    groupName: 'LOADING_STATION_01',
    description: 'Loading Station 01 sensors and controls',
    updateRate: 1000, // 1 second
    priority: 'high',
    tags: [
      {
        tagName: 'FT_LS01_FLOW',
        description: 'Loading Station 01 Flow Rate',
        sensorType: 'flow',
        dataType: 'float',
        unit: 'L/min',
        range: { min: 0, max: 1200 },
        precision: 1,
        scanRate: 1000,
        digitalTwinMapping: {
          componentType: 'loading_station',
          componentId: 'LS-01',
          property: 'currentFlowRate'
        },
        communication: {
          protocol: 'modbus',
          address: '************',
          register: 40001
        },
        calibration: {
          lastCalibrated: new Date('2024-01-15'),
          calibrationDue: new Date('2025-01-15'),
          calibrationCertificate: 'CAL-LS01-FT-2024',
          accuracy: 1.0
        }
      },
      {
        tagName: 'DI_LS01_TRUCK_PRESENT',
        description: 'Loading Station 01 Truck Present',
        sensorType: 'status',
        dataType: 'boolean',
        unit: '',
        range: { min: 0, max: 1 },
        precision: 0,
        scanRate: 500,
        digitalTwinMapping: {
          componentType: 'loading_station',
          componentId: 'LS-01',
          property: 'truckPresent'
        },
        communication: {
          protocol: 'modbus',
          address: '************',
          register: 10001
        },
        calibration: {
          lastCalibrated: new Date('2024-01-15'),
          calibrationDue: new Date('2025-01-15'),
          calibrationCertificate: 'CAL-LS01-DI-2024',
          accuracy: 100
        }
      }
    ]
  }
];

// Complete SCADA sensor mapping configuration
export const SCADA_SENSOR_CONFIG = {
  tagGroups: [
    ...TANK_SENSOR_MAPPINGS,
    ...HOT_OIL_SENSOR_MAPPINGS,
    ...LOADING_STATION_SENSOR_MAPPINGS
  ],
  communicationSettings: {
    modbusSettings: {
      baudRate: 9600,
      dataBits: 8,
      stopBits: 1,
      parity: 'none',
      timeout: 5000 // milliseconds
    },
    opcUaSettings: {
      endpointUrl: 'opc.tcp://*************:4840',
      securityPolicy: 'None',
      securityMode: 'None',
      sessionTimeout: 60000 // milliseconds
    }
  },
  dataLogging: {
    enabled: true,
    logInterval: 60000, // 1 minute
    retentionPeriod: 365, // days
    compressionEnabled: true
  },
  alarmSettings: {
    alarmDelay: 5, // seconds
    alarmRetryCount: 3,
    alarmAcknowledgmentTimeout: 300, // seconds
    alarmHistoryRetention: 90 // days
  }
};
