"""
Tank System REST API Endpoints
Provides comprehensive API for tank control, monitoring, and management
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Query
from fastapi.responses import StreamingResponse
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import json
import asyncio
import logging
from pydantic import BaseModel, Field
from enum import Enum

# Import tank system components
from digital_twin.core.tank_models import (
    TankConfiguration, TankStateData, TankSensor, TankActuator,
    TankState, SensorType, ActuatorType, TankControlParameters,
    TankAlarm, AlarmSeverity, ControlMode, TankGroupConfiguration,
    TankPerformanceMetrics
)
from digital_twin.core.tank_control_algorithms import (
    TankControllerManager, ControlAlgorithmType, ControllerConfiguration
)
from digital_twin.scada.tank_control_integration import (
    TankControlSystem, TankControlConfiguration
)
from digital_twin.simulation.tank_physics_engine import (
    TankPhysicsEngine, SimulationConfiguration
)

logger = logging.getLogger(__name__)

# Create API router
router = APIRouter(prefix="/api/v1/tanks", tags=["Tank System"])

# Global tank system instances (in production, use dependency injection)
tank_control_system: Optional[TankControlSystem] = None
tank_physics_engine: Optional[TankPhysicsEngine] = None
tank_control_manager: Optional[TankControllerManager] = None


# Pydantic models for API requests/responses

class TankCreateRequest(BaseModel):
    """Request model for creating a new tank"""
    tank_id: str = Field(..., description="Unique identifier for the tank")
    name: str = Field(..., description="Tank name")
    description: str = Field("", description="Tank description")
    tank_type: str = Field("heating", description="Tank type")
    capacity: float = Field(50000.0, description="Tank capacity in liters")
    diameter: float = Field(6.0, description="Tank diameter in meters")
    height: float = Field(8.0, description="Tank height in meters")
    
    # Sensors configuration
    sensors: List[Dict[str, Any]] = Field(default_factory=list)
    actuators: List[Dict[str, Any]] = Field(default_factory=list)
    
    # Control parameters
    temperature_setpoint: float = Field(160.0, description="Temperature setpoint in Celsius")
    pressure_setpoint: float = Field(1.0, description="Pressure setpoint in bar")
    level_setpoint: float = Field(50.0, description="Level setpoint in percent")
    
    # Physics parameters
    thermal_properties: Dict[str, float] = Field(default_factory=dict)
    fluid_properties: Dict[str, float] = Field(default_factory=dict)
    
    class Config:
        schema_extra = {
            "example": {
                "tank_id": "tank_001",
                "name": "Asphalt Storage Tank 1",
                "description": "Primary asphalt storage and heating tank",
                "capacity": 50000.0,
                "diameter": 6.0,
                "height": 8.0,
                "temperature_setpoint": 160.0,
                "sensors": [
                    {
                        "sensor_id": "temp_zone_1",
                        "sensor_type": "temperature",
                        "description": "Zone 1 temperature sensor",
                        "node_id": "ns=2;s=Tank001.Zone1.Temperature"
                    }
                ],
                "actuators": [
                    {
                        "actuator_id": "heater_zone_1",
                        "actuator_type": "heater",
                        "description": "Zone 1 heater",
                        "node_id": "ns=2;s=Tank001.Zone1.Heater"
                    }
                ]
            }
        }


class TankControlRequest(BaseModel):
    """Request model for tank control operations"""
    command: str = Field(..., description="Control command")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Command parameters")
    
    class Config:
        schema_extra = {
            "example": {
                "command": "set_temperature",
                "parameters": {
                    "zone": 1,
                    "temperature": 165.0
                }
            }
        }


class TankResponse(BaseModel):
    """Response model for tank operations"""
    success: bool = Field(..., description="Operation success")
    message: str = Field(..., description="Response message")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")
    timestamp: datetime = Field(default_factory=datetime.now)


class TankStateResponse(BaseModel):
    """Response model for tank state"""
    tank_id: str
    timestamp: datetime
    state: str
    temperature: Dict[str, float] = Field(default_factory=dict)
    pressure: Dict[str, float] = Field(default_factory=dict)
    level: Dict[str, float] = Field(default_factory=dict)
    flow: Dict[str, float] = Field(default_factory=dict)
    heater_output: Dict[str, float] = Field(default_factory=dict)
    pump_output: Dict[str, float] = Field(default_factory=dict)
    valve_position: Dict[str, float] = Field(default_factory=dict)
    volume_current: float = 0.0
    mass_current: float = 0.0
    energy_content: float = 0.0
    active_alarms: List[str] = Field(default_factory=list)


class TankMetricsResponse(BaseModel):
    """Response model for tank performance metrics"""
    tank_id: str
    timestamp: datetime
    energy_consumption: float = 0.0
    energy_efficiency: float = 0.0
    temperature_stability: float = 0.0
    control_accuracy: float = 0.0
    uptime: float = 0.0


# API Endpoints

@router.post("/", response_model=TankResponse)
async def create_tank(request: TankCreateRequest, background_tasks: BackgroundTasks):
    """Create a new tank in the system"""
    try:
        # Create tank configuration
        from digital_twin.core.tank_models import (
            TankConfiguration, TankGeometry, TankType, TankSensor, TankActuator,
            TankControlParameters, SensorType, ActuatorType
        )
        
        # Create geometry
        geometry = TankGeometry(
            tank_type=TankType.HEATING,
            capacity=request.capacity,
            diameter=request.diameter,
            height=request.height,
            volume=3.14159 * (request.diameter/2)**2 * request.height,
            surface_area=3.14159 * request.diameter * request.height + 2 * 3.14159 * (request.diameter/2)**2
        )
        
        # Create sensors
        sensors = []
        for sensor_data in request.sensors:
            sensor = TankSensor(
                sensor_id=sensor_data["sensor_id"],
                sensor_type=SensorType(sensor_data["sensor_type"]),
                description=sensor_data.get("description", ""),
                unit=sensor_data.get("unit", "°C"),
                min_value=sensor_data.get("min_value", 0.0),
                max_value=sensor_data.get("max_value", 300.0),
                accuracy=sensor_data.get("accuracy", 1.0),
                location=sensor_data.get("location", "tank"),
                node_id=sensor_data["node_id"]
            )
            sensors.append(sensor)
        
        # Create actuators
        actuators = []
        for actuator_data in request.actuators:
            actuator = TankActuator(
                actuator_id=actuator_data["actuator_id"],
                actuator_type=ActuatorType(actuator_data["actuator_type"]),
                description=actuator_data.get("description", ""),
                unit=actuator_data.get("unit", "%"),
                min_value=actuator_data.get("min_value", 0.0),
                max_value=actuator_data.get("max_value", 100.0),
                location=actuator_data.get("location", "tank"),
                node_id=actuator_data["node_id"]
            )
            actuators.append(actuator)
        
        # Create control parameters
        control_params = TankControlParameters(
            temperature_setpoint=request.temperature_setpoint,
            pressure_setpoint=request.pressure_setpoint,
            level_setpoint=request.level_setpoint
        )
        
        # Create tank configuration
        tank_config = TankConfiguration(
            tank_id=request.tank_id,
            name=request.name,
            description=request.description,
            geometry=geometry,
            sensors=sensors,
            actuators=actuators,
            control_parameters=control_params,
            alarms=[],
            thermal_properties=request.thermal_properties,
            fluid_properties=request.fluid_properties
        )
        
        # Add tank to control system
        global tank_control_system, tank_control_manager
        if tank_control_system:
            # Add to existing system
            pass  # Implementation depends on control system structure
        
        if tank_control_manager:
            await tank_control_manager.add_tank(tank_config)
        
        return TankResponse(
            success=True,
            message=f"Tank {request.tank_id} created successfully",
            data={"tank_id": request.tank_id}
        )
        
    except Exception as e:
        logger.error(f"Error creating tank: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/", response_model=List[Dict[str, Any]])
async def list_tanks():
    """Get list of all tanks in the system"""
    try:
        global tank_control_system
        
        if not tank_control_system:
            return []
        
        # Get tank list from control system
        tanks = []
        for tank_config in tank_control_system.config.tank_configs:
            tank_info = {
                "tank_id": tank_config.tank_id,
                "name": tank_config.name,
                "description": tank_config.description,
                "capacity": tank_config.geometry.capacity,
                "state": "unknown"  # Would get from actual state
            }
            tanks.append(tank_info)
        
        return tanks
        
    except Exception as e:
        logger.error(f"Error listing tanks: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{tank_id}/state", response_model=TankStateResponse)
async def get_tank_state(tank_id: str):
    """Get current state of a specific tank"""
    try:
        global tank_control_system
        
        if not tank_control_system:
            raise HTTPException(status_code=404, detail="Tank controller not initialized")
        
        # Get tank state
        tank_state = await tank_control_system.get_system_status()
        
        if not tank_state:
            raise HTTPException(status_code=404, detail=f"Tank {tank_id} not found")
        
        # Convert to response model
        response = TankStateResponse(
            tank_id=tank_state.tank_id,
            timestamp=tank_state.timestamp,
            state=tank_state.state.value,
            temperature=tank_state.temperature,
            pressure=tank_state.pressure,
            level=tank_state.level,
            flow=tank_state.flow,
            heater_output=tank_state.heater_output,
            pump_output=tank_state.pump_output,
            valve_position=tank_state.valve_position,
            volume_current=tank_state.volume_current,
            mass_current=tank_state.mass_current,
            energy_content=tank_state.energy_content,
            active_alarms=tank_state.active_alarms
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting tank state: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{tank_id}/control", response_model=TankResponse)
async def control_tank(tank_id: str, request: TankControlRequest):
    """Execute control command on a tank"""
    try:
        global tank_controller_engine
        
        if not tank_controller_engine:
            raise HTTPException(status_code=404, detail="Tank controller not initialized")
        
        # Execute command
        success = await tank_controller_engine.execute_tank_command(
            tank_id, request.command, request.parameters
        )
        
        if success:
            return TankResponse(
                success=True,
                message=f"Command '{request.command}' executed successfully on tank {tank_id}",
                data={"tank_id": tank_id, "command": request.command}
            )
        else:
            return TankResponse(
                success=False,
                message=f"Failed to execute command '{request.command}' on tank {tank_id}",
                data={"tank_id": tank_id, "command": request.command}
            )
        
    except Exception as e:
        logger.error(f"Error controlling tank: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{tank_id}/setpoint", response_model=TankResponse)
async def set_tank_setpoint(tank_id: str, parameter: str, value: float):
    """Set setpoint for a tank parameter"""
    try:
        global tank_controller_engine, tank_control_manager
        
        # Update setpoint in controller engine
        if tank_controller_engine:
            success = await tank_controller_engine.write_tank_setpoint(tank_id, parameter, value)
        else:
            success = False
        
        # Update setpoint in control manager
        if tank_control_manager:
            controller_id = f"{parameter}_control"
            await tank_control_manager.set_setpoint(tank_id, controller_id, value)
        
        if success:
            return TankResponse(
                success=True,
                message=f"Setpoint for {parameter} set to {value}",
                data={"tank_id": tank_id, "parameter": parameter, "value": value}
            )
        else:
            return TankResponse(
                success=False,
                message=f"Failed to set setpoint for {parameter}",
                data={"tank_id": tank_id, "parameter": parameter, "value": value}
            )
        
    except Exception as e:
        logger.error(f"Error setting setpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{tank_id}/alarms", response_model=List[Dict[str, Any]])
async def get_tank_alarms(tank_id: str):
    """Get active alarms for a tank"""
    try:
        global tank_controller_engine
        
        if not tank_controller_engine:
            raise HTTPException(status_code=404, detail="Tank controller not initialized")
        
        alarms = await tank_controller_engine.get_tank_alarms(tank_id)
        
        alarm_list = []
        for alarm in alarms:
            alarm_dict = {
                "alarm_id": alarm.alarm_id,
                "description": alarm.description,
                "severity": alarm.severity.value,
                "active": alarm.active,
                "acknowledged": alarm.acknowledged,
                "first_occurrence": alarm.first_occurrence.isoformat() if alarm.first_occurrence else None,
                "last_occurrence": alarm.last_occurrence.isoformat() if alarm.last_occurrence else None
            }
            alarm_list.append(alarm_dict)
        
        return alarm_list
        
    except Exception as e:
        logger.error(f"Error getting tank alarms: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{tank_id}/alarms/{alarm_id}/acknowledge", response_model=TankResponse)
async def acknowledge_alarm(tank_id: str, alarm_id: str, user: str = "api_user"):
    """Acknowledge a tank alarm"""
    try:
        global tank_controller_engine
        
        if not tank_controller_engine:
            raise HTTPException(status_code=404, detail="Tank controller not initialized")
        
        success = await tank_controller_engine.acknowledge_alarm(tank_id, alarm_id, user)
        
        if success:
            return TankResponse(
                success=True,
                message=f"Alarm {alarm_id} acknowledged by {user}",
                data={"tank_id": tank_id, "alarm_id": alarm_id, "user": user}
            )
        else:
            return TankResponse(
                success=False,
                message=f"Failed to acknowledge alarm {alarm_id}",
                data={"tank_id": tank_id, "alarm_id": alarm_id}
            )
        
    except Exception as e:
        logger.error(f"Error acknowledging alarm: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{tank_id}/metrics", response_model=TankMetricsResponse)
async def get_tank_metrics(tank_id: str):
    """Get performance metrics for a tank"""
    try:
        global tank_physics_engine
        
        if not tank_physics_engine:
            raise HTTPException(status_code=404, detail="Physics engine not initialized")
        
        metrics = await tank_physics_engine.get_performance_metrics(tank_id)
        
        if not metrics:
            raise HTTPException(status_code=404, detail=f"Metrics for tank {tank_id} not found")
        
        response = TankMetricsResponse(
            tank_id=metrics.tank_id,
            timestamp=metrics.timestamp,
            energy_consumption=metrics.energy_consumption,
            energy_efficiency=metrics.energy_efficiency,
            temperature_stability=metrics.temperature_stability,
            control_accuracy=metrics.control_accuracy,
            uptime=metrics.uptime
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting tank metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{tank_id}/history")
async def get_tank_history(
    tank_id: str,
    start_time: Optional[datetime] = Query(None),
    end_time: Optional[datetime] = Query(None),
    parameters: Optional[List[str]] = Query(None)
):
    """Get historical data for a tank"""
    try:
        # Implementation would query historical database
        # For now, return mock data structure
        
        if not start_time:
            start_time = datetime.now() - timedelta(hours=24)
        if not end_time:
            end_time = datetime.now()
        
        history_data = {
            "tank_id": tank_id,
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "parameters": parameters or ["temperature", "pressure", "level"],
            "data_points": []  # Would contain actual historical data
        }
        
        return history_data
        
    except Exception as e:
        logger.error(f"Error getting tank history: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{tank_id}/stream")
async def stream_tank_data(tank_id: str):
    """Stream real-time tank data"""
    try:
        async def generate_data():
            while True:
                try:
                    # Get current tank state
                    global tank_controller_engine
                    if tank_controller_engine:
                        tank_state = await tank_controller_engine.read_tank_data(tank_id)
                        if tank_state:
                            data = {
                                "timestamp": tank_state.timestamp.isoformat(),
                                "tank_id": tank_state.tank_id,
                                "temperature": tank_state.temperature,
                                "pressure": tank_state.pressure,
                                "level": tank_state.level,
                                "heater_output": tank_state.heater_output
                            }
                            yield f"data: {json.dumps(data)}\n\n"
                    
                    await asyncio.sleep(1.0)  # Stream every second
                    
                except Exception as e:
                    logger.error(f"Error in data stream: {e}")
                    break
        
        return StreamingResponse(
            generate_data(),
            media_type="text/plain",
            headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
        )
        
    except Exception as e:
        logger.error(f"Error starting data stream: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status")
async def get_system_status():
    """Get overall tank system status"""
    try:
        global tank_control_system, tank_physics_engine, tank_control_manager
        
        status = {
            "timestamp": datetime.now().isoformat(),
            "control_system": {
                "initialized": tank_control_system is not None,
                "status": tank_control_system.get_system_status() if tank_control_system else None
            },
            "physics_engine": {
                "initialized": tank_physics_engine is not None,
                "status": tank_physics_engine.get_simulation_status() if tank_physics_engine else None
            },
            "control_manager": {
                "initialized": tank_control_manager is not None
            }
        }
        
        return status
        
    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Initialize system components
async def initialize_tank_system():
    """Initialize tank system components"""
    global tank_control_system, tank_physics_engine, tank_control_manager
    
    try:
        # Initialize tank control manager
        tank_control_manager = TankControllerManager()
        
        logger.info("Tank system initialized successfully")
        
    except Exception as e:
        logger.error(f"Error initializing tank system: {e}")


# Include this router in the main FastAPI app
def get_tank_router() -> APIRouter:
    """Get the tank API router"""
    return router
