"""
Modbus Client for SCADA Integration
Handles Modbus TCP/RTU communication with industrial devices
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable, Union
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum

try:
    from pymodbus.client.tcp import AsyncModbusTcpClient
    from pymodbus.client.serial import AsyncModbusSerialClient
    from pymodbus.exceptions import ModbusException
    from pymodbus.constants import <PERSON>ian
    from pymodbus.payload import BinaryPayloadDecoder, BinaryPayloadBuilder
    MODBUS_AVAILABLE = True
except ImportError:
    MODBUS_AVAILABLE = False
    print("Warning: pymodbus not installed. Modbus functionality will be limited.")

logger = logging.getLogger(__name__)


class ModbusProtocol(Enum):
    TCP = "tcp"
    RTU = "rtu"


class ModbusDataType(Enum):
    BOOL = "bool"
    INT16 = "int16"
    UINT16 = "uint16"
    INT32 = "int32"
    UINT32 = "uint32"
    FLOAT32 = "float32"
    FLOAT64 = "float64"
    STRING = "string"


class ModbusFunction(Enum):
    READ_COILS = 1
    READ_DISCRETE_INPUTS = 2
    READ_HOLDING_REGISTERS = 3
    READ_INPUT_REGISTERS = 4
    WRITE_SINGLE_COIL = 5
    WRITE_SINGLE_REGISTER = 6
    WRITE_MULTIPLE_COILS = 15
    WRITE_MULTIPLE_REGISTERS = 16


@dataclass
class ModbusTag:
    """Modbus tag configuration"""
    name: str
    slave_id: int
    function_code: ModbusFunction
    address: int
    data_type: ModbusDataType
    count: int = 1
    description: str = ""
    scaling_factor: float = 1.0
    offset: float = 0.0
    byte_order: str = "big"  # big, little
    word_order: str = "big"  # big, little
    last_value: Optional[Any] = None
    last_update: Optional[datetime] = None
    quality: str = "GOOD"


@dataclass
class ModbusConfiguration:
    """Modbus client configuration"""
    protocol: ModbusProtocol
    host: str = "localhost"
    port: int = 502
    # For RTU
    serial_port: str = "/dev/ttyUSB0"
    baudrate: int = 9600
    parity: str = "N"  # N, E, O
    stopbits: int = 1
    bytesize: int = 8
    # Common
    timeout: float = 3.0
    retry_on_empty: bool = True
    retry_on_invalid: bool = True
    close_comm_on_error: bool = True
    strict: bool = True
    tags: List[ModbusTag] = field(default_factory=list)


class ModbusClient:
    """Modbus client for SCADA integration"""
    
    def __init__(self, config: ModbusConfiguration):
        self.config = config
        self.client: Optional[Union[AsyncModbusTcpClient, AsyncModbusSerialClient]] = None
        self.is_connected = False
        self.tags: Dict[str, ModbusTag] = {tag.name: tag for tag in config.tags}
        self.connection_callbacks: List[Callable] = []
        self.data_callbacks: List[Callable] = []
        self._polling_task: Optional[asyncio.Task] = None
        self._poll_interval = 1.0  # seconds
    
    async def connect(self) -> bool:
        """Connect to Modbus device"""
        if not MODBUS_AVAILABLE:
            logger.error("Modbus library not available")
            return False
        
        try:
            logger.info(f"Connecting to Modbus device: {self.config.protocol.value}")
            
            if self.config.protocol == ModbusProtocol.TCP:
                self.client = AsyncModbusTcpClient(
                    host=self.config.host,
                    port=self.config.port,
                    timeout=self.config.timeout,
                    retry_on_empty=self.config.retry_on_empty,
                    retry_on_invalid=self.config.retry_on_invalid,
                    close_comm_on_error=self.config.close_comm_on_error,
                    strict=self.config.strict
                )
            else:  # RTU
                self.client = AsyncModbusSerialClient(
                    port=self.config.serial_port,
                    baudrate=self.config.baudrate,
                    parity=self.config.parity,
                    stopbits=self.config.stopbits,
                    bytesize=self.config.bytesize,
                    timeout=self.config.timeout,
                    retry_on_empty=self.config.retry_on_empty,
                    retry_on_invalid=self.config.retry_on_invalid,
                    close_comm_on_error=self.config.close_comm_on_error,
                    strict=self.config.strict
                )
            
            await self.client.connect()
            self.is_connected = True
            logger.info("Successfully connected to Modbus device")
            
            # Notify connection callbacks
            await self._notify_connection_callbacks(True)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Modbus device: {e}")
            self.is_connected = False
            await self._notify_connection_callbacks(False)
            return False
    
    async def disconnect(self):
        """Disconnect from Modbus device"""
        try:
            if self._polling_task:
                self._polling_task.cancel()
                self._polling_task = None
            
            if self.client:
                self.client.close()
                self.client = None
            
            self.is_connected = False
            logger.info("Disconnected from Modbus device")
            
        except Exception as e:
            logger.error(f"Error during disconnect: {e}")
    
    async def read_tag(self, tag_name: str) -> Optional[Any]:
        """Read a single tag value"""
        if not self.is_connected or tag_name not in self.tags:
            return None
        
        tag = self.tags[tag_name]
        
        try:
            # Read based on function code
            if tag.function_code == ModbusFunction.READ_COILS:
                result = await self.client.read_coils(tag.address, tag.count, tag.slave_id)
            elif tag.function_code == ModbusFunction.READ_DISCRETE_INPUTS:
                result = await self.client.read_discrete_inputs(tag.address, tag.count, tag.slave_id)
            elif tag.function_code == ModbusFunction.READ_HOLDING_REGISTERS:
                result = await self.client.read_holding_registers(tag.address, tag.count, tag.slave_id)
            elif tag.function_code == ModbusFunction.READ_INPUT_REGISTERS:
                result = await self.client.read_input_registers(tag.address, tag.count, tag.slave_id)
            else:
                logger.error(f"Unsupported read function code: {tag.function_code}")
                return None
            
            if result.isError():
                logger.error(f"Modbus error reading tag {tag_name}: {result}")
                tag.quality = "BAD"
                return None
            
            # Convert result to appropriate data type
            value = self._convert_result_to_value(result, tag)
            
            # Apply scaling
            if isinstance(value, (int, float)):
                value = value * tag.scaling_factor + tag.offset
            
            # Update tag
            tag.last_value = value
            tag.last_update = datetime.now()
            tag.quality = "GOOD"
            
            return value
            
        except Exception as e:
            logger.error(f"Error reading tag {tag_name}: {e}")
            tag.quality = "BAD"
            return None
    
    async def write_tag(self, tag_name: str, value: Any) -> bool:
        """Write a value to a tag"""
        if not self.is_connected or tag_name not in self.tags:
            return False
        
        tag = self.tags[tag_name]
        
        try:
            # Apply reverse scaling
            if isinstance(value, (int, float)):
                value = (value - tag.offset) / tag.scaling_factor
            
            # Convert value to appropriate format
            modbus_value = self._convert_value_to_modbus(value, tag)
            
            # Write based on function code
            if tag.function_code == ModbusFunction.WRITE_SINGLE_COIL:
                result = await self.client.write_coil(tag.address, modbus_value, tag.slave_id)
            elif tag.function_code == ModbusFunction.WRITE_SINGLE_REGISTER:
                result = await self.client.write_register(tag.address, modbus_value, tag.slave_id)
            elif tag.function_code == ModbusFunction.WRITE_MULTIPLE_COILS:
                result = await self.client.write_coils(tag.address, modbus_value, tag.slave_id)
            elif tag.function_code == ModbusFunction.WRITE_MULTIPLE_REGISTERS:
                result = await self.client.write_registers(tag.address, modbus_value, tag.slave_id)
            else:
                logger.error(f"Unsupported write function code: {tag.function_code}")
                return False
            
            if result.isError():
                logger.error(f"Modbus error writing tag {tag_name}: {result}")
                return False
            
            logger.info(f"Successfully wrote value {value} to tag {tag_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error writing tag {tag_name}: {e}")
            return False
    
    async def read_multiple_tags(self, tag_names: List[str]) -> Dict[str, Any]:
        """Read multiple tags"""
        results = {}
        
        for tag_name in tag_names:
            value = await self.read_tag(tag_name)
            if value is not None:
                results[tag_name] = value
        
        return results
    
    async def read_all_tags(self) -> Dict[str, Any]:
        """Read all configured tags"""
        return await self.read_multiple_tags(list(self.tags.keys()))
    
    async def start_polling(self, interval: float = 1.0):
        """Start polling all tags at specified interval"""
        self._poll_interval = interval
        self._polling_task = asyncio.create_task(self._polling_loop())
        logger.info(f"Started polling with interval {interval} seconds")
    
    async def stop_polling(self):
        """Stop polling"""
        if self._polling_task:
            self._polling_task.cancel()
            self._polling_task = None
            logger.info("Stopped polling")
    
    def register_connection_callback(self, callback: Callable):
        """Register a callback for connection state changes"""
        self.connection_callbacks.append(callback)
    
    def register_data_callback(self, callback: Callable):
        """Register a callback for data changes"""
        self.data_callbacks.append(callback)
    
    def get_tag_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all tags"""
        status = {}
        for name, tag in self.tags.items():
            status[name] = {
                'value': tag.last_value,
                'quality': tag.quality,
                'last_update': tag.last_update.isoformat() if tag.last_update else None,
                'address': tag.address,
                'slave_id': tag.slave_id
            }
        return status
    
    def _convert_result_to_value(self, result, tag: ModbusTag) -> Any:
        """Convert Modbus result to appropriate value"""
        try:
            if tag.function_code in [ModbusFunction.READ_COILS, ModbusFunction.READ_DISCRETE_INPUTS]:
                # Boolean values
                if tag.data_type == ModbusDataType.BOOL:
                    return result.bits[0] if result.bits else False
                else:
                    return result.bits
            
            else:  # Register values
                if not result.registers:
                    return None
                
                # Create decoder
                endian = Endian.Big if tag.byte_order == "big" else Endian.Little
                word_endian = Endian.Big if tag.word_order == "big" else Endian.Little
                
                decoder = BinaryPayloadDecoder.fromRegisters(
                    result.registers,
                    byteorder=endian,
                    wordorder=word_endian
                )
                
                # Decode based on data type
                if tag.data_type == ModbusDataType.INT16:
                    return decoder.decode_16bit_int()
                elif tag.data_type == ModbusDataType.UINT16:
                    return decoder.decode_16bit_uint()
                elif tag.data_type == ModbusDataType.INT32:
                    return decoder.decode_32bit_int()
                elif tag.data_type == ModbusDataType.UINT32:
                    return decoder.decode_32bit_uint()
                elif tag.data_type == ModbusDataType.FLOAT32:
                    return decoder.decode_32bit_float()
                elif tag.data_type == ModbusDataType.FLOAT64:
                    return decoder.decode_64bit_float()
                elif tag.data_type == ModbusDataType.STRING:
                    return decoder.decode_string(tag.count * 2)
                else:
                    return result.registers[0]
        
        except Exception as e:
            logger.error(f"Error converting result for tag {tag.name}: {e}")
            return None
    
    def _convert_value_to_modbus(self, value: Any, tag: ModbusTag) -> Any:
        """Convert value to Modbus format"""
        try:
            if tag.function_code in [ModbusFunction.WRITE_SINGLE_COIL, ModbusFunction.WRITE_MULTIPLE_COILS]:
                # Boolean values
                if isinstance(value, bool):
                    return value
                elif isinstance(value, (int, float)):
                    return bool(value)
                else:
                    return False
            
            else:  # Register values
                if tag.data_type == ModbusDataType.INT16:
                    return int(value) & 0xFFFF
                elif tag.data_type == ModbusDataType.UINT16:
                    return int(value) & 0xFFFF
                elif tag.data_type in [ModbusDataType.INT32, ModbusDataType.UINT32, ModbusDataType.FLOAT32]:
                    # Create builder
                    endian = Endian.Big if tag.byte_order == "big" else Endian.Little
                    word_endian = Endian.Big if tag.word_order == "big" else Endian.Little
                    
                    builder = BinaryPayloadBuilder(byteorder=endian, wordorder=word_endian)
                    
                    if tag.data_type == ModbusDataType.INT32:
                        builder.add_32bit_int(int(value))
                    elif tag.data_type == ModbusDataType.UINT32:
                        builder.add_32bit_uint(int(value))
                    elif tag.data_type == ModbusDataType.FLOAT32:
                        builder.add_32bit_float(float(value))
                    
                    return builder.to_registers()
                
                else:
                    return int(value)
        
        except Exception as e:
            logger.error(f"Error converting value for tag {tag.name}: {e}")
            return value
    
    async def _polling_loop(self):
        """Main polling loop"""
        while self.is_connected:
            try:
                # Read all tags
                data = await self.read_all_tags()
                
                # Notify data callbacks
                for callback in self.data_callbacks:
                    await callback(data)
                
                await asyncio.sleep(self._poll_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in polling loop: {e}")
                await asyncio.sleep(self._poll_interval)
    
    async def _notify_connection_callbacks(self, connected: bool):
        """Notify connection callbacks"""
        for callback in self.connection_callbacks:
            try:
                await callback(connected)
            except Exception as e:
                logger.error(f"Error in connection callback: {e}")


class ModbusTagBuilder:
    """Helper class to build Modbus tags"""
    
    @staticmethod
    def create_temperature_tag(name: str, slave_id: int, address: int) -> ModbusTag:
        """Create a temperature tag"""
        return ModbusTag(
            name=name,
            slave_id=slave_id,
            function_code=ModbusFunction.READ_INPUT_REGISTERS,
            address=address,
            data_type=ModbusDataType.FLOAT32,
            count=2,
            description=f"Temperature sensor {name}",
            scaling_factor=1.0,
            offset=0.0
        )
    
    @staticmethod
    def create_control_output_tag(name: str, slave_id: int, address: int) -> ModbusTag:
        """Create a control output tag"""
        return ModbusTag(
            name=name,
            slave_id=slave_id,
            function_code=ModbusFunction.WRITE_SINGLE_REGISTER,
            address=address,
            data_type=ModbusDataType.UINT16,
            count=1,
            description=f"Control output {name}",
            scaling_factor=1.0,
            offset=0.0
        )
    
    @staticmethod
    def create_status_tag(name: str, slave_id: int, address: int) -> ModbusTag:
        """Create a status tag"""
        return ModbusTag(
            name=name,
            slave_id=slave_id,
            function_code=ModbusFunction.READ_DISCRETE_INPUTS,
            address=address,
            data_type=ModbusDataType.BOOL,
            count=1,
            description=f"Status {name}"
        )