"""
SCADA Data Ingestion Pipeline
Real-time ingestion of tank control data into time-series database
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import json

from ...digital_twin.scada.tank_control_integration import TankControlSystem, TankControlConfiguration
from ..storage.influxdb_client import InfluxDBTimeSeriesClient, TankDataPoint, InfluxDBDataIngestionPipeline

logger = logging.getLogger(__name__)


@dataclass
class IngestionConfig:
    """Configuration for data ingestion pipeline"""
    tank_ids: List[str] = field(default_factory=list)
    ingestion_frequency: float = 1.0  # Hz
    batch_size: int = 100
    buffer_size: int = 1000
    enable_data_validation: bool = True
    enable_data_filtering: bool = True
    enable_data_transformation: bool = True
    data_retention_days: int = 90
    
    # Data quality thresholds
    temperature_range: tuple = (0.0, 300.0)  # °C
    power_range: tuple = (0.0, 500000.0)  # Watts
    efficiency_range: tuple = (0.0, 1.0)  # 0-100%


class SCADADataIngestionPipeline:
    """Pipeline for ingesting SCADA data from tank control systems"""
    
    def __init__(self, config: IngestionConfig, 
                 influx_client: InfluxDBTimeSeriesClient,
                 tank_control_systems: Dict[str, TankControlSystem]):
        self.config = config
        self.influx_client = influx_client
        self.tank_control_systems = tank_control_systems
        self.ingestion_pipeline = InfluxDBDataIngestionPipeline(influx_client)
        
        # Data buffers
        self.data_buffer: Dict[str, List[Dict[str, Any]]] = {}
        self.processed_count = 0
        self.error_count = 0
        self.validation_errors = 0
        
        # Pipeline state
        self.is_running = False
        self._ingestion_task: Optional[asyncio.Task] = None
        self._monitoring_task: Optional[asyncio.Task] = None
        self._cleanup_task: Optional[asyncio.Task] = None
        
        # Callbacks
        self.data_callbacks: List[Callable] = []
        self.error_callbacks: List[Callable] = []
        
        # Initialize buffers
        for tank_id in self.config.tank_ids:
            self.data_buffer[tank_id] = []
    
    async def start(self) -> bool:
        """Start the ingestion pipeline"""
        try:
            logger.info("Starting SCADA data ingestion pipeline")
            
            # Start InfluxDB ingestion pipeline
            await self.ingestion_pipeline.start_ingestion()
            
            # Register callbacks with tank control systems
            for tank_id, control_system in self.tank_control_systems.items():
                control_system.register_data_callback(
                    lambda tank_id=tank_id: self._create_data_callback(tank_id)
                )
            
            # Start pipeline tasks
            self._ingestion_task = asyncio.create_task(self._ingestion_loop())
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            
            self.is_running = True
            logger.info("SCADA data ingestion pipeline started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start ingestion pipeline: {e}")
            return False
    
    async def stop(self):
        """Stop the ingestion pipeline"""
        try:
            logger.info("Stopping SCADA data ingestion pipeline")
            
            self.is_running = False
            
            # Cancel tasks
            if self._ingestion_task:
                self._ingestion_task.cancel()
            if self._monitoring_task:
                self._monitoring_task.cancel()
            if self._cleanup_task:
                self._cleanup_task.cancel()
            
            # Flush remaining data
            await self._flush_all_buffers()
            
            # Stop InfluxDB pipeline
            await self.ingestion_pipeline.stop_ingestion()
            
            logger.info("SCADA data ingestion pipeline stopped")
            
        except Exception as e:
            logger.error(f"Error stopping ingestion pipeline: {e}")
    
    def _create_data_callback(self, tank_id: str) -> Callable:
        """Create data callback for specific tank"""
        async def callback(tag_name: str, value: Any, timestamp: datetime):
            await self._handle_scada_data(tank_id, tag_name, value, timestamp)
        return callback
    
    async def _handle_scada_data(self, tank_id: str, tag_name: str, value: Any, timestamp: datetime):
        """Handle incoming SCADA data"""
        try:
            # Create data entry
            data_entry = {
                'tank_id': tank_id,
                'tag_name': tag_name,
                'value': value,
                'timestamp': timestamp,
                'quality': 'good'
            }
            
            # Validate data if enabled
            if self.config.enable_data_validation:
                if not self._validate_data_entry(data_entry):
                    self.validation_errors += 1
                    return
            
            # Filter data if enabled
            if self.config.enable_data_filtering:
                if not self._filter_data_entry(data_entry):
                    return
            
            # Transform data if enabled
            if self.config.enable_data_transformation:
                data_entry = self._transform_data_entry(data_entry)
            
            # Add to buffer
            if tank_id in self.data_buffer:
                self.data_buffer[tank_id].append(data_entry)
                
                # Limit buffer size
                if len(self.data_buffer[tank_id]) > self.config.buffer_size:
                    self.data_buffer[tank_id] = self.data_buffer[tank_id][-self.config.buffer_size//2:]\n                    logger.warning(f\"Buffer overflow for tank {tank_id}, truncated to {len(self.data_buffer[tank_id])} entries\")\n            \n            # Notify callbacks\n            for callback in self.data_callbacks:\n                try:\n                    await callback(data_entry)\n                except Exception as e:\n                    logger.error(f\"Error in data callback: {e}\")\n        \n        except Exception as e:\n            logger.error(f\"Error handling SCADA data: {e}\")\n            self.error_count += 1\n            \n            # Notify error callbacks\n            for callback in self.error_callbacks:\n                try:\n                    await callback(e, data_entry if 'data_entry' in locals() else None)\n                except Exception as callback_error:\n                    logger.error(f\"Error in error callback: {callback_error}\")\n    \n    def _validate_data_entry(self, data_entry: Dict[str, Any]) -> bool:\n        \"\"\"Validate incoming data entry\"\"\"\n        try:\n            value = data_entry['value']\n            tag_name = data_entry['tag_name']\n            \n            # Check for None values\n            if value is None:\n                return False\n            \n            # Validate temperature values\n            if 'temperature' in tag_name.lower():\n                if not (self.config.temperature_range[0] <= value <= self.config.temperature_range[1]):\n                    logger.warning(f\"Temperature {value} out of range for {tag_name}\")\n                    return False\n            \n            # Validate power values\n            if 'power' in tag_name.lower() or 'heating' in tag_name.lower():\n                if not (self.config.power_range[0] <= value <= self.config.power_range[1]):\n                    logger.warning(f\"Power {value} out of range for {tag_name}\")\n                    return False\n            \n            # Validate efficiency values\n            if 'efficiency' in tag_name.lower():\n                if not (self.config.efficiency_range[0] <= value <= self.config.efficiency_range[1]):\n                    logger.warning(f\"Efficiency {value} out of range for {tag_name}\")\n                    return False\n            \n            return True\n            \n        except Exception as e:\n            logger.error(f\"Error validating data entry: {e}\")\n            return False\n    \n    def _filter_data_entry(self, data_entry: Dict[str, Any]) -> bool:\n        \"\"\"Filter data entries based on rules\"\"\"\n        try:\n            # Skip duplicate consecutive values (simple change detection)\n            # This would require maintaining state of last values\n            \n            # Skip data that's too old\n            max_age = timedelta(minutes=5)\n            if datetime.now() - data_entry['timestamp'] > max_age:\n                return False\n            \n            return True\n            \n        except Exception as e:\n            logger.error(f\"Error filtering data entry: {e}\")\n            return True  # Default to accepting data on error\n    \n    def _transform_data_entry(self, data_entry: Dict[str, Any]) -> Dict[str, Any]:\n        \"\"\"Transform data entry\"\"\"\n        try:\n            # Add derived fields\n            data_entry['hour_of_day'] = data_entry['timestamp'].hour\n            data_entry['day_of_week'] = data_entry['timestamp'].weekday()\n            \n            # Unit conversions if needed\n            if 'temperature' in data_entry['tag_name'].lower():\n                # Ensure temperature is in Celsius\n                # Add any needed conversions here\n                pass\n            \n            # Add data quality indicators\n            data_entry['data_age_seconds'] = (datetime.now() - data_entry['timestamp']).total_seconds()\n            \n            return data_entry\n            \n        except Exception as e:\n            logger.error(f\"Error transforming data entry: {e}\")\n            return data_entry\n    \n    async def _ingestion_loop(self):\n        \"\"\"Main ingestion loop\"\"\"\n        while self.is_running:\n            try:\n                # Process buffered data for each tank\n                for tank_id in self.config.tank_ids:\n                    await self._process_tank_buffer(tank_id)\n                \n                # Wait for next iteration\n                await asyncio.sleep(1.0 / self.config.ingestion_frequency)\n                \n            except asyncio.CancelledError:\n                break\n            except Exception as e:\n                logger.error(f\"Error in ingestion loop: {e}\")\n                await asyncio.sleep(1.0)\n    \n    async def _process_tank_buffer(self, tank_id: str):\n        \"\"\"Process buffered data for a specific tank\"\"\"\n        try:\n            if tank_id not in self.data_buffer or not self.data_buffer[tank_id]:\n                return\n            \n            # Get batch of data\n            batch_size = min(self.config.batch_size, len(self.data_buffer[tank_id]))\n            batch_data = self.data_buffer[tank_id][:batch_size]\n            self.data_buffer[tank_id] = self.data_buffer[tank_id][batch_size:]\n            \n            # Convert to TankDataPoint\n            data_point = await self._convert_to_tank_data_point(tank_id, batch_data)\n            \n            # Ingest to InfluxDB\n            if data_point:\n                await self.ingestion_pipeline.ingest_tank_data(data_point)\n                self.processed_count += len(batch_data)\n            \n        except Exception as e:\n            logger.error(f\"Error processing tank buffer for {tank_id}: {e}\")\n    \n    async def _convert_to_tank_data_point(self, tank_id: str, batch_data: List[Dict[str, Any]]) -> Optional[TankDataPoint]:\n        \"\"\"Convert batch data to TankDataPoint\"\"\"\n        try:\n            if not batch_data:\n                return None\n            \n            # Use latest timestamp\n            latest_timestamp = max(entry['timestamp'] for entry in batch_data)\n            \n            # Initialize data point\n            data_point = TankDataPoint(\n                tank_id=tank_id,\n                timestamp=latest_timestamp\n            )\n            \n            # Parse data entries\n            for entry in batch_data:\n                tag_name = entry['tag_name']\n                value = entry['value']\n                \n                # Parse zone temperatures\n                if 'temperature' in tag_name.lower() and 'zone' in tag_name.lower():\n                    zone_match = self._extract_zone_number(tag_name)\n                    if zone_match is not None:\n                        data_point.zone_temperatures[zone_match] = float(value)\n                \n                # Parse heating powers\n                elif 'heating' in tag_name.lower() and 'power' in tag_name.lower():\n                    zone_match = self._extract_zone_number(tag_name)\n                    if zone_match is not None:\n                        data_point.heating_powers[zone_match] = float(value)\n                \n                # Parse setpoints\n                elif 'setpoint' in tag_name.lower():\n                    zone_match = self._extract_zone_number(tag_name)\n                    if zone_match is not None:\n                        data_point.setpoints[zone_match] = float(value)\n                \n                # Parse system-level data\n                elif 'ambient' in tag_name.lower() and 'temperature' in tag_name.lower():\n                    data_point.ambient_temperature = float(value)\n                elif 'energy' in tag_name.lower() and 'consumption' in tag_name.lower():\n                    data_point.energy_consumption = float(value)\n                elif 'energy' in tag_name.lower() and 'cost' in tag_name.lower():\n                    data_point.energy_cost = float(value)\n                elif 'power' in tag_name.lower() and 'total' in tag_name.lower():\n                    data_point.total_power = float(value)\n                elif 'efficiency' in tag_name.lower():\n                    data_point.efficiency = float(value)\n                elif 'alarm' in tag_name.lower():\n                    if value:  # Only add active alarms\n                        data_point.alarms.append(tag_name)\n                elif 'mode' in tag_name.lower():\n                    data_point.control_mode = str(value)\n                else:\n                    # Store as additional tag\n                    data_point.tags[tag_name] = value\n            \n            return data_point\n            \n        except Exception as e:\n            logger.error(f\"Error converting to TankDataPoint: {e}\")\n            return None\n    \n    def _extract_zone_number(self, tag_name: str) -> Optional[int]:\n        \"\"\"Extract zone number from tag name\"\"\"\n        import re\n        \n        # Look for patterns like Zone1, zone_2, Zone.3, etc.\n        patterns = [\n            r'zone[\\._]?(\\d+)',\n            r'zone(\\d+)',\n            r'z(\\d+)',\n            r'heating[\\._]?(\\d+)',\n            r'temp[\\._]?(\\d+)'\n        ]\n        \n        for pattern in patterns:\n            match = re.search(pattern, tag_name.lower())\n            if match:\n                return int(match.group(1))\n        \n        return None\n    \n    async def _monitoring_loop(self):\n        \"\"\"Monitor pipeline health and performance\"\"\"\n        while self.is_running:\n            try:\n                # Log statistics\n                total_buffered = sum(len(buffer) for buffer in self.data_buffer.values())\n                \n                logger.info(\n                    f\"Pipeline stats: Processed: {self.processed_count}, \"\n                    f\"Errors: {self.error_count}, Validation errors: {self.validation_errors}, \"\n                    f\"Buffered: {total_buffered}\"\n                )\n                \n                # Check for buffer overflows\n                for tank_id, buffer in self.data_buffer.items():\n                    if len(buffer) > self.config.buffer_size * 0.8:\n                        logger.warning(f\"Buffer for tank {tank_id} is {len(buffer)}/{self.config.buffer_size} (80%+ full)\")\n                \n                # Reset counters periodically\n                if self.processed_count > 10000:\n                    self.processed_count = 0\n                    self.error_count = 0\n                    self.validation_errors = 0\n                \n                await asyncio.sleep(60.0)  # Monitor every minute\n                \n            except asyncio.CancelledError:\n                break\n            except Exception as e:\n                logger.error(f\"Error in monitoring loop: {e}\")\n                await asyncio.sleep(60.0)\n    \n    async def _cleanup_loop(self):\n        \"\"\"Periodic cleanup of old data\"\"\"\n        while self.is_running:\n            try:\n                # Clean up old data in InfluxDB\n                await self.influx_client.delete_old_data(self.config.data_retention_days)\n                \n                # Wait 24 hours before next cleanup\n                await asyncio.sleep(24 * 3600)\n                \n            except asyncio.CancelledError:\n                break\n            except Exception as e:\n                logger.error(f\"Error in cleanup loop: {e}\")\n                await asyncio.sleep(3600)  # Retry in 1 hour\n    \n    async def _flush_all_buffers(self):\n        \"\"\"Flush all data buffers\"\"\"\n        try:\n            for tank_id in self.config.tank_ids:\n                await self._process_tank_buffer(tank_id)\n            \n            logger.info(\"All buffers flushed\")\n            \n        except Exception as e:\n            logger.error(f\"Error flushing buffers: {e}\")\n    \n    def get_pipeline_statistics(self) -> Dict[str, Any]:\n        \"\"\"Get pipeline performance statistics\"\"\"\n        total_buffered = sum(len(buffer) for buffer in self.data_buffer.values())\n        \n        return {\n            'is_running': self.is_running,\n            'processed_count': self.processed_count,\n            'error_count': self.error_count,\n            'validation_errors': self.validation_errors,\n            'total_buffered': total_buffered,\n            'buffer_details': {tank_id: len(buffer) for tank_id, buffer in self.data_buffer.items()},\n            'config': {\n                'tank_ids': self.config.tank_ids,\n                'ingestion_frequency': self.config.ingestion_frequency,\n                'batch_size': self.config.batch_size,\n                'buffer_size': self.config.buffer_size\n            }\n        }\n    \n    def register_data_callback(self, callback: Callable):\n        \"\"\"Register callback for processed data\"\"\"\n        self.data_callbacks.append(callback)\n    \n    def register_error_callback(self, callback: Callable):\n        \"\"\"Register callback for errors\"\"\"\n        self.error_callbacks.append(callback)\n\n\nclass SCADAIngestionFactory:\n    \"\"\"Factory for creating SCADA ingestion pipelines\"\"\"\n    \n    @staticmethod\n    def create_pipeline(tank_control_systems: Dict[str, TankControlSystem],\n                       influx_client: InfluxDBTimeSeriesClient,\n                       tank_ids: Optional[List[str]] = None) -> SCADADataIngestionPipeline:\n        \"\"\"Create SCADA ingestion pipeline\"\"\"\n        \n        if tank_ids is None:\n            tank_ids = list(tank_control_systems.keys())\n        \n        config = IngestionConfig(\n            tank_ids=tank_ids,\n            ingestion_frequency=1.0,\n            batch_size=50,\n            buffer_size=1000,\n            enable_data_validation=True,\n            enable_data_filtering=True,\n            enable_data_transformation=True,\n            data_retention_days=90\n        )\n        \n        return SCADADataIngestionPipeline(\n            config=config,\n            influx_client=influx_client,\n            tank_control_systems=tank_control_systems\n        )\n\n\n# Example usage\nif __name__ == \"__main__\":\n    import asyncio\n    from ..storage.influxdb_client import create_influxdb_client, InfluxDBConfig\n    \n    async def test_ingestion_pipeline():\n        \"\"\"Test the SCADA ingestion pipeline\"\"\"\n        \n        # Create InfluxDB client\n        influx_config = InfluxDBConfig(\n            url=\"http://localhost:8086\",\n            token=\"your-token-here\",\n            org=\"asphalt_plant\",\n            bucket=\"tank_data\"\n        )\n        \n        influx_client = create_influxdb_client(influx_config)\n        \n        # Connect to InfluxDB\n        if not await influx_client.connect():\n            print(\"Failed to connect to InfluxDB\")\n            return\n        \n        # Create mock tank control systems (in real usage, these would be actual systems)\n        tank_control_systems = {}\n        \n        # Create ingestion pipeline\n        pipeline = SCADAIngestionFactory.create_pipeline(\n            tank_control_systems=tank_control_systems,\n            influx_client=influx_client,\n            tank_ids=[\"tank_001\", \"tank_002\"]\n        )\n        \n        # Start pipeline\n        success = await pipeline.start()\n        if success:\n            print(\"Ingestion pipeline started successfully\")\n            \n            # Run for a while\n            await asyncio.sleep(10)\n            \n            # Get statistics\n            stats = pipeline.get_pipeline_statistics()\n            print(f\"Pipeline statistics: {stats}\")\n            \n            # Stop pipeline\n            await pipeline.stop()\n        \n        # Disconnect from InfluxDB\n        await influx_client.disconnect()\n        print(\"Test completed\")\n    \n    # Run test\n    asyncio.run(test_ingestion_pipeline())"