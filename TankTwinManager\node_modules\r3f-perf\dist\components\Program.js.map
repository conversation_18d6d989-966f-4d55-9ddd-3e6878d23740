{"version": 3, "file": "Program.js", "sources": ["../../src/components/Program.tsx"], "sourcesContent": null, "names": ["usePerf", "useState", "useEffect", "data", "jsx", "ProgramsUL", "jsxs", "Fragment", "ProgramConsole", "el", "PerfI", "VercelLogoIcon", "ActivityLogIcon", "PerfB", "ProgramGeo", "ProgramHeader", "Toggle", "TriangleDownIcon", "TriangleUpIcon", "ProgramTitle", "LayersIcon", "ImageIcon", "RocketIcon", "ToggleVisible", "EyeOpenIcon", "EyeNoneIcon", "ProgramsULHeader", "ButtonIcon", "CubeIcon", "ProgramsGeoLi", "estimateBytesUsed", "ProgramsContainer"], "mappings": ";;;;;;;;AAqBA,MAAM,qBAAqB,CAAC,IAAY,YAAiB;AACjD,QAAA,aAAa,CAAC,SAAiB;AACnC,YAAQ,MAAM;AAAA,MACZ,KAAK;AACI,eAAA;AAAA,MACT,KAAK;AACI,eAAA;AAAA,MACT,KAAK;AACI,eAAA;AAAA,MACT;AACS,eAAA;AAAA,IACX;AAAA,EAAA;AAGI,QAAA,eAAe,CAAC,aAAqB;AACzC,YAAQ,UAAU;AAAA,MAChB,KAAK;AACI,eAAA;AAAA,MACT,KAAK;AACI,eAAA;AAAA,MACT,KAAK;AACI,eAAA;AAAA,MACT,KAAK;AACI,eAAA;AAAA,MACT,KAAK;AACI,eAAA;AAAA,MACT,KAAK;AACI,eAAA;AAAA,MACT,KAAK;AACI,eAAA;AAAA,MACT,KAAK;AACI,eAAA;AAAA,MACT;AACS,eAAA;AAAA,IACX;AAAA,EAAA;AAEK,SAAA;AAAA,IACL,MAAM;AAAA,IACN,KAAK,QAAQ,MAAM;AAAA,IACnB,UAAU,aAAa,QAAQ,QAAQ;AAAA,IACvC,OAAO,WAAW,QAAQ,KAAK;AAAA,IAC/B,OAAO,QAAQ,MAAM,SAAS;AAAA,EAAA;AAElC;AAEA,MAAM,aAAa,CAAC,EAAE,SAAS,UAAU,mBAAwB;AAC/D,QAAM,KAAKA,MAAAA,QAAQ,CAAC,UAAU,MAAM,EAAE;AACtC,QAAM,CAAC,UAAU,GAAG,IAAIC,eAAqB,IAAI;AAEjDC,QAAAA,UAAU,MAAM;AACd,QAAI,IAAI;AACA,YAAA,OAAY,mCAAS;AAC3B,UAAI,WAAW;AACT,YAAA,6BAAkB;AAEnB,WAAA,IAAI,QAAQ,CAAC,MAAW;AAC3B,YACE,CAAC,EAAE,GAAG,SAAS,SAAS,KACxB,EAAE,OAAO,oBACT,EAAE,OAAO,iBACT,EAAE,OAAO,gBACT,EAAE,OAAO,sBACT,EAAE,OAAO,gBACT,EAAE,OAAO,kBACT,EAAE,OAAO,iBACT,EAAE,OAAO,mBACT;AACA,cAAI,SAAc,CAAA;AAClB,cAAIC,QAAY;AAAA,YACd,MAAM,EAAE;AAAA,UAAA;AAEV,cAAI,EAAE,OAAO;AACT,cAAA,MAAM,QAAQ,CAAC,MAAW;AACtB,kBAAA,OAAO,MAAM,aAAa;AAC5B,uBAAO,KAAK,EAAE,SAAA,EAAW,UAAU,GAAG,CAAC,CAAC;AAAA,cAC1C;AAAA,YAAA,CACD;AACDA,kBAAK,QAAQ,OAAO;AAChB,gBAAA,SAAS,EAAE,EAAE,KAAK,SAAS,EAAE,EAAE,EAAE,OAAO;AAC1C,kBAAI,SAAS,EAAE,EAAE,EAAE,OAAO;AACxB;AACAA,sBAAK,QAAQ,mBAAmB,EAAE,IAAI,SAAS,EAAE,EAAE,CAAC;AAAA,cACtD;AAAA,YACF;AACI,gBAAA,CAACA,MAAK,OAAO;AACfA,oBAAK,QAAQ;AAAA,YACf;AACO,mBAAA,IAAI,EAAE,IAAIA,KAAI;AAAA,UACvB;AAAA,QACF;AAAA,MAAA,CACD;AAED,UAAI,SAAS,UAAU;AACrB,eAAO,KAAK,SAAS,QAAQ,EAAE,QAAQ,CAAC,QAAa;AAC7C,gBAAA,UAAU,SAAS,SAAS,GAAG;AACrC,cAAI,QAAQ,OAAO;AACX,kBAAA,EAAE,MAAU,IAAA;AAClB,gBAAIA,QAAY;AAAA,cACd,MAAM;AAAA,YAAA;AAEJ,gBAAA,IAAI,SAAS,SAAS,GAAG;AAC3B;AAAA,YACF;AACA,gBAAI,MAAM,WAAW;AACnB;AACAA,oBAAK,QAAQ,mBAAmB,KAAK,KAAK;AAAA,YAAA,OACrC;AACD,kBAAA,KAAK,KAAK,UAAU,KAAK;AACzB,kBAAA;AACG,qBAAA,KAAK,UAAU,KAAK;AAAA,uBAClB,MAAM;AACb,qBAAK,MAAM;cACb;AACAA,oBAAK,QAAQ;AAAA,YACf;AACO,mBAAA,IAAI,KAAKA,KAAI;AAAA,UACtB;AAAA,QAAA,CACD;AAAA,MACH;AAEA,UAAI,WAAW,GAAG;AAChB,qBAAa,QAAQ;AAAA,MACvB;AACA,UAAI,MAAM;AAAA,IACZ;AAAA,EACF,GAAG,CAAE,CAAA;AAGH,SAAAC,2BAAA,IAACC,OACE,YAAA,EAAA,UAAA,YACC,MAAM,KAAK,SAAS,OAAQ,CAAA,EAAE,IAAI,CAAC,YAAiB;AAEhD,WAAAD,2BAAAA,IAAC,UACE,UAAO,OAAA,QAAQ,UAAU,WACxBA,2BAAAA,IAAC,MACC,EAAA,UAAAE,2BAAA,KAAC,QACE,EAAA,UAAA;AAAA,MAAQ,QAAA;AAAA,MAAK;AAAA,MAAG;AAAA,sCAChB,KACE,EAAA,UAAA;AAAA,QAAQ,QAAA,MAAM,UAAU,GAAG,EAAE;AAAA,QAC7B,QAAQ,MAAM,SAAS,KAAK,QAAQ;AAAA,MAAA,GACvC;AAAA,IACF,EAAA,CAAA,EAAA,CACF,IAGEA,2BAAA,KAAAC,WAAA,UAAA,EAAA,UAAA;AAAA,MAACH,2BAAA,IAAA,MAAA,EACC,0CAAC,KAAG,EAAA,UAAA;AAAA,QAAA,QAAQ,MAAM;AAAA,QAAK;AAAA,MAAA,EAAA,CAAC,EAC1B,CAAA;AAAA,sCACC,OACE,EAAA,UAAA;AAAA,QAAA,OAAO,KAAK,QAAQ,KAAK,EAAE,IAAI,CAAC,QAAQ;AAChC,iBAAA,QAAQ,SACbA,2BAAAA,IAAC,OACE,EAAA,UAAA,QAAQ,QACNA,2BAAA,IAAA,KAAA,EAAE,MAAM,QAAQ,MAAM,GAAG,GAAG,QAAO,UAClC,UAACA,2BAAA,IAAA,OAAA,EAAI,KAAK,QAAQ,MAAM,GAAG,EAAG,CAAA,EAAA,CAChC,IAEAE,2BAAA,KAAC,MACE,EAAA,UAAA;AAAA,YAAA;AAAA,YAAI;AAAA,YAAGF,2BAAA,IAAA,KAAA,EAAG,UAAQ,QAAA,MAAM,GAAG,GAAE;AAAA,UAChC,EAAA,CAAA,EAAA,GARM,GAUV,IACE;AAAA,QAAA,CACL;AAAA,QACDE,2BAAA;AAAA,UAACE,OAAA;AAAA,UAAA;AAAA,YACC,SAAS,MAAM;;AACL,sBAAA;AAAA,gBACN,SAAS,QAAQ,MAAM,IAAI,OACzB,0CAAU,SAAS,QAAQ,MAAM,UAAjC,mBAAwC;AAAA,cAAA;AAAA,YAE9C;AAAA,YACD,UAAA;AAAA,cAAA;AAAA,cACe,QAAQ,MAAM;AAAA,cAAK;AAAA,YAAA;AAAA,UAAA;AAAA,QACnC;AAAA,MAAA,GACF;AAAA,IACF,EAAA,CAAA,EAAA,GA3CO,QAAQ,IA6CnB;AAAA,EAEH,CAAA,EACL,CAAA;AAEJ;AAKA,MAAM,sBAAsB,CAAC,EAAE,SAAc;AACnCR,QAAAA,QAAA,CAAC,UAAU,MAAM,GAAG;AAC5B,QAAM,KAAUA,MAAAA,QAAQ,CAAC,UAAU,MAAM,EAAE;AAErC,QAAA,SAAS,CAACS,QAAY;AACtB,QAAA,CAAC,GAAW,QAAA;AAEhB,UAAM,MACJ,KAAK;AAAA,MACFA,IAAG,WAAW,SACZ,GAAG,KAAK,OAAO,YACd,GAAG,KAAK,OAAO,QACf,GAAG,KAAK,OAAO,UACjB,MACA;AAAA,IACA,IAAA;AACE,WAAA,SAAS,GAAG,KAAK,OAAQ;AAAA,EAAA;AAEnC,SAEKL,2BAAA,IAAAG,WAAA,UAAA,EAAA,UAAA,GAAG,WAAW,QAAQ,KACpBD,gCAAAI,OAAAA,OAAA,EAAM,OAAO,EAAE,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QACpD,GAAA,UAAA;AAAA,IAAA,GAAG,WAAW,SAAS,aACrBN,2BAAA,IAAAO,2BAAA,EAAe,OAAO,EAAE,KAAK,OAAO,GAAG,IAEvCP,2BAAA,IAAAQ,4BAAA,EAAgB,OAAO,EAAE,KAAK,UAAU;AAAA,IAE1C,GAAG,WAAW;AAAA,oCACd,SAAO,EAAA,UAAA;AAAA,MAAA,GAAG,WAAW;AAAA,MAAK;AAAA,IAAA,GAAC;AAAA,IAC3B,MACCN,2BAAA;AAAA,MAACO,OAAA;AAAA,MAAA;AAAA,QACC,OAAO,EAAE,QAAQ,SAAS,OAAO,QAAQ,YAAY,OAAO;AAAA,QAE3D,UAAA;AAAA,UAAA,GAAG,WAAW,CAAC,GAAG,SAAS,YAAY,OAAO,EAAE,IAAI;AAAA,UAAE;AAAA,QAAA;AAAA,MAAA;AAAA,IACzD;AAAA,EAAA,EAEJ,CAAA,EAEJ,CAAA;AAEJ;AACA,MAAM,YAAgC,CAAC,EAAE,SAAS;AAChD,QAAM,CAAC,aAAa,cAAc,IAAIZ,MAAAA,SAAS,GAAG,OAAO;AAEzD,QAAM,CAAC,eAAe,GAAG,IAAIA,MAAAA,SAAS,GAAG,MAAM;AAC/C,QAAM,CAAC,WAAW,YAAY,IAAIA,eAAS,CAAC;AAC5C,QAAM,EAAE,QAAQ,SAAS,SAAA,IAAkB;AAE3C,yCACGa,mBACC,EAAA,UAAA;AAAA,IAAAR,2BAAA;AAAA,MAACS,OAAA;AAAA,MAAA;AAAA,QACC,SAAS,MAAM;AACb,aAAG,SAAS,CAAC;AAEb,iBAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,QAAQ;AAC7B,kBAAA,OAAO,OAAO,GAAG;AAEvB,iBAAK,SAAS,YAAY;AAAA,UAAA,CAC3B;AAED,cAAI,CAAC,aAAa;AAAA,QACpB;AAAA,QAEA,UAAA;AAAA,UAAAX,2BAAAA,IAACY,iBAAO,OAAO,EAAE,aAAa,SAC3B,0BACEZ,+BAAA,QAAA,EACC,UAACA,+BAAAa,WAAAA,kBAAA,CAAiB,CAAA,GACpB,IAEAb,2BAAA,IAAC,UACC,UAACA,2BAAA,IAAAc,WAAA,gBAAA,EAAe,GAClB,EAEJ,CAAA;AAAA,UACC,2CACE,QACC,EAAA,UAAA;AAAA,YAACd,2BAAAA,IAAAe,OAAAA,cAAA,EAAc,kBAAQ,KAAK,CAAA;AAAA,YAE5Bb,2BAAAA,KAACI,OAAM,OAAA,EAAA,OAAO,EAAE,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAA,GACrD,UAAA;AAAA,cAAAN,2BAAA,IAACgB,WAAW,YAAA,EAAA,OAAO,EAAE,KAAK,UAAU;AAAA,cACnC,OAAO,KAAK,MAAM,EAAE;AAAA,cACrBhB,2BAAAA,IAAC,WAAO,UAAO,OAAA,KAAK,MAAM,EAAE,SAAS,IAAI,UAAU,OAAO,CAAA;AAAA,YAAA,GAC5D;AAAA,YACC,YAAY,KACVE,2BAAAA,KAAAI,OAAAA,OAAA,EAAM,OAAO,EAAE,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAA,GACpD,UAAA;AAAA,cAAA,YAAY,IACXN,+BAACiB,WAAAA,WAAU,EAAA,OAAO,EAAE,KAAK,OAAA,EAAU,CAAA,mCAElCA,WAAAA,WAAU,EAAA,OAAO,EAAE,KAAK,UAAU;AAAA,cAEpC;AAAA,cACDjB,2BAAAA,IAAC,WAAM,UAAG,MAAA,CAAA;AAAA,YAAA,GACZ;AAAA,YAEFA,+BAAC,uBAAoB,IAAQ;AAAA,YAC5B,SAAS,gBAAgB,YACxBE,2BAAA,KAACI,OAAM,OAAA,EAAA,OAAO,EAAE,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,WACrD,UAAA;AAAA,cAAAN,2BAAA,IAACkB,WAAW,YAAA,EAAA,OAAO,EAAE,KAAK,UAAU;AAAA,cAAE;AAAA,cAEtClB,2BAAAA,IAAC,WAAM,UAAE,KAAA,CAAA;AAAA,cACTA,2BAAAA,IAACS,gBAAM,OAAO,EAAE,QAAQ,SAAS,OAAO,OAAO,GAAG,UAAI,OAAA,CAAA;AAAA,YAAA,GACxD;AAAA,UAAA,GAEJ;AAAA,UAEFT,2BAAA;AAAA,YAACmB,OAAA;AAAA,YAAA;AAAA,cACC,gBAAgB,MAAM;AACpB,uBAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,QAAQ;AAC7B,wBAAA,OAAO,OAAO,GAAG;AACvB,uBAAK,SAAS,YAAY;AAAA,gBAAA,CAC3B;AAAA,cACH;AAAA,cACA,gBAAgB,MAAM;AACpB,uBAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,QAAQ;AAC7B,wBAAA,OAAO,OAAO,GAAG;AACvB,uBAAK,SAAS,YAAY;AAAA,gBAAA,CAC3B;AAAA,cACH;AAAA,cACA,SAAS,CAAC,MAAW;AACnB,kBAAE,gBAAgB;AAElB,uBAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,QAAQ;AAC7B,wBAAA,OAAO,OAAO,GAAG;AACvB,wBAAM,SAAS,CAAC;AAChB,uBAAK,UAAU;AACf,qBAAG,UAAU;AACb,iCAAe,MAAM;AAAA,gBAAA,CACtB;AAAA,cACH;AAAA,cAEC,UAAc,cAAAnB,2BAAAA,IAACoB,WAAY,aAAA,CAAA,CAAA,mCAAMC,WAAY,aAAA,EAAA;AAAA,YAAA;AAAA,UAChD;AAAA,QAAA;AAAA,MAAA;AAAA,IACF;AAAA,IACAnB,2BAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,OAAO,EAAE,WAAW,gBAAgB,WAAW,GAAG,UAAU,SAAS;AAAA,QAErE,UAAA;AAAA,UAAAA,gCAACoB,OAAAA,kBACC,EAAA,UAAA;AAAA,YAAAtB,2BAAA,IAACuB,WAAW,YAAA,EAAA;AAAA,YAAE;AAAA,UAAA,GAChB;AAAA,UACAvB,2BAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC;AAAA,cACA;AAAA,cACA;AAAA,YAAA;AAAA,UACF;AAAA,0CACCsB,OAAAA,kBACC,EAAA,UAAA;AAAA,YAAAtB,2BAAA,IAACwB,WAAS,UAAA,EAAA;AAAA,YAAE;AAAA,UAAA,GACd;AAAA,yCAECvB,OAAAA,YACE,EAAA,UAAA,UACC,OAAO,KAAK,MAAM,EAAE;AAAA,YAClB,CAAC,QACC,OAAO,GAAG,KACV,OAAO,GAAG,EAAE,YACVC,2BAAAA,KAACuB,OACC,eAAA,EAAA,UAAA;AAAA,cAAAvB,gCAAC,QAAM,EAAA,UAAA;AAAA,gBAAO,OAAA,GAAG,EAAE,SAAS;AAAA,gBAAK;AAAA,cAAA,GAAE;AAAA,cAClC,OAAO,GAAG,EAAE,YAAY,OAAO,GAAG,EAAE,SAAS,aAC5CA,2BAAAA,KAAC,KACC,EAAA,UAAA;AAAA,gBAAAA,gCAAC,OACE,EAAA,UAAA;AAAA,kBAAO,OAAA,GAAG,EAAE,SAAS,UAAU;AAAA,kDAC/B,SAAM,EAAA,UAAA;AAAA,oBAAA;AAAA,oBAAE,OAAO,GAAG,EAAE,SAAS,UAAU;AAAA,oBAAK;AAAA,kBAAA,GAAC;AAAA,gBAAA,GAChD;AAAA,+CACC,MAAG,EAAA;AAAA,gDACH,OACE,EAAA,UAAA;AAAA,kBAAK,KAAA;AAAA,oBACHwB,oCAAkB,OAAO,GAAG,EAAE,QAAQ,IAAI,OACzC;AAAA,kBAAA,IACA;AAAA,kBAAK;AAAA,kBAET1B,2BAAAA,IAAC,WAAM,UAAY,eAAA,CAAA;AAAA,gBAAA,GACrB;AAAA,cAAA,GACF;AAAA,YAAA,EAAA,GAjBgB,GAmBpB;AAAA,UAAA,GAGV;AAAA,UACAE,2BAAA;AAAA,YAACE,OAAA;AAAA,YAAA;AAAA,cACC,SAAS,MAAM;AACb,wBAAQ,KAAK,QAAQ;AAAA,cACvB;AAAA,cACD,UAAA;AAAA,gBAAA;AAAA,gBACe,SAAS;AAAA,gBAAK;AAAA,cAAA;AAAA,YAAA;AAAA,UAC9B;AAAA,QAAA;AAAA,MAAA;AAAA,IACF;AAAA,EACF,EAAA,CAAA;AAEJ;AAEO,MAAM,aAA4B,MAAM;AACrCR,QAAAA,QAAA,CAAC,UAAU,MAAM,qBAAqB;AAC9C,QAAM,WAAeA,MAAAA,QAAQ,CAAC,UAAU,MAAM,QAAQ;AAEpD,SAAAI,2BAAA,IAAC2B,OACE,mBAAA,EAAA,UAAA,YACC,MAAM,KAAK,SAAS,OAAQ,CAAA,EAAE,IAAI,CAAC,OAAY;AAC7C,QAAI,CAAC,IAAI;AACA,aAAA;AAAA,IACT;AACA,WAAO,KAAM3B,+BAAA,WAAA,EAAiC,MAAlB,GAAG,SAAS,IAAc,IAAK;AAAA,EAC5D,CAAA,EACL,CAAA;AAEJ;;"}