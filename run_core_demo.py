#!/usr/bin/env python3
"""
Digital Twin Core System Demo
Demonstrates core functionality without external dependencies
"""

import asyncio
import logging
import json
import sys
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def demo_twin_lifecycle():
    """Demonstrate digital twin lifecycle management"""
    from digital_twin.core import DigitalTwinEngine, TwinConfiguration
    
    logger.info("🏗️ Digital Twin Lifecycle Demo")
    logger.info("-" * 40)
    
    # Create engine
    engine = DigitalTwinEngine()
    
    # Create multiple twins for different heating zones
    twins_config = [
        {
            'twin_id': 'heating_zone_1',
            'name': 'Heating Zone 1',
            'description': 'Primary asphalt heating zone',
            'asset_id': 'heater_zone_1'
        },
        {
            'twin_id': 'heating_zone_2', 
            'name': 'Heating Zone 2',
            'description': 'Secondary asphalt heating zone',
            'asset_id': 'heater_zone_2'
        },
        {
            'twin_id': 'mixing_station',
            'name': 'Asphalt Mixing Station',
            'description': 'Central mixing and control station',
            'asset_id': 'mixer_station_1'
        }
    ]
    
    # Create and start twins
    for twin_info in twins_config:
        config = TwinConfiguration(
            twin_id=twin_info['twin_id'],
            name=twin_info['name'],
            description=twin_info['description'],
            physical_asset_id=twin_info['asset_id'],
            model_version="v1.0",
            update_frequency=3.0,
            synchronization_threshold=0.08,
            physics_parameters={
                'thermal_conductivity': 0.5,
                'specific_heat': 920.0,
                'density': 2400.0,
                'ambient_temperature': 20.0,
                'geometry_length': 8.0,
                'geometry_width': 4.0
            },
            scada_tags=[
                f'{twin_info["twin_id"]}_temp_01',
                f'{twin_info["twin_id"]}_temp_02', 
                f'{twin_info["twin_id"]}_heat_flux',
                f'{twin_info["twin_id"]}_control_out'
            ]
        )
        
        twin = await engine.create_twin(config)
        await engine.start_twin(config.twin_id)
        logger.info(f"✅ Created and started: {config.name}")
    
    logger.info(f"📊 Total twins created: {len(engine.twins)}")
    return engine

async def demo_real_time_simulation():
    """Demonstrate real-time physics simulation"""
    from digital_twin.simulation import (
        RealTimePhysicsEngine, 
        SimulationConfig, 
        PhysicsParameters,
        BoundaryConditions
    )
    
    logger.info("\n🔬 Real-time Physics Simulation Demo")
    logger.info("-" * 40)
    
    # Create simulation for different scenarios
    scenarios = [
        {
            'name': 'High Temperature Scenario',
            'thermal_conductivity': 0.7,
            'ambient_temp': 35.0,
            'real_time_factor': 20.0  # 20x faster for demo
        },
        {
            'name': 'Standard Operating Conditions',
            'thermal_conductivity': 0.5,
            'ambient_temp': 20.0,
            'real_time_factor': 15.0
        }
    ]
    
    simulation_results = {}
    
    for scenario in scenarios:
        logger.info(f"🎯 Running: {scenario['name']}")
        
        config = SimulationConfig(
            physics_params=PhysicsParameters(
                thermal_conductivity=scenario['thermal_conductivity'],
                specific_heat=920.0,
                density=2400.0,
                ambient_temperature=scenario['ambient_temp'],
                grid_size_x=30,
                grid_size_y=15,
                time_step=0.05
            ),
            boundary_conditions=BoundaryConditions(),
            real_time_factor=scenario['real_time_factor'],
            max_simulation_time=3.0,  # 3 seconds of simulation
            output_frequency=5
        )
        
        engine = RealTimePhysicsEngine(config)
        await engine.initialize()
        await engine.start()
        
        # Let it run
        await asyncio.sleep(1.5)  # Real time
        
        state = await engine.get_current_state()
        simulation_results[scenario['name']] = {
            'sim_time': state['simulation_time'],
            'steps': state['step_count'],
            'real_time': state['real_time']
        }
        
        await engine.stop()
        logger.info(f"   ⏱️  Simulated {state['simulation_time']:.2f}s in {state['real_time']:.2f}s real time")
        logger.info(f"   🔢 Completed {state['step_count']} simulation steps")
    
    return simulation_results

async def demo_state_synchronization():
    """Demonstrate twin state synchronization with mock measurements"""
    logger.info("\n🔄 State Synchronization Demo")
    logger.info("-" * 40)
    
    from digital_twin.core import DigitalTwinEngine, TwinConfiguration
    
    engine = DigitalTwinEngine()
    
    # Create a twin for synchronization demo
    config = TwinConfiguration(
        twin_id="sync_demo_twin",
        name="Synchronization Demo Twin",
        description="Twin for demonstrating state synchronization",
        physical_asset_id="sync_heater_001",
        model_version="v1.0",
        update_frequency=5.0,
        synchronization_threshold=0.05
    )
    
    twin = await engine.create_twin(config)
    await engine.start_twin(config.twin_id)
    
    # Simulate measurement updates
    measurement_scenarios = [
        {
            'step': 1,
            'measurements': {
                'temperature_01': 75.5,
                'temperature_02': 73.2,
                'heat_flux_01': 1180.0,
                'control_output': 60.0
            }
        },
        {
            'step': 2,
            'measurements': {
                'temperature_01': 87.3,
                'temperature_02': 85.1,
                'heat_flux_01': 1420.0,
                'control_output': 75.0
            }
        },
        {
            'step': 3,
            'measurements': {
                'temperature_01': 92.1,
                'temperature_02': 89.8,
                'heat_flux_01': 1580.0,
                'control_output': 85.0
            }
        }
    ]
    
    for scenario in measurement_scenarios:
        logger.info(f"📡 Step {scenario['step']}: Updating with measurements...")
        
        # Update twin state
        await engine.update_twin_state(config.twin_id, scenario['measurements'])
        
        # Get updated state
        state = await engine.get_twin_state(config.twin_id)
        
        if state:
            logger.info(f"   🎯 Model confidence: {state.model_confidence:.3f}")
            logger.info(f"   📊 Deviation metrics: {state.deviation_metrics}")
            logger.info(f"   ⏱️  Last update: {state.timestamp.strftime('%H:%M:%S')}")
        
        await asyncio.sleep(0.5)
    
    await engine.stop_twin(config.twin_id)
    return True

async def demo_system_monitoring():
    """Demonstrate system monitoring and health checks"""
    logger.info("\n📊 System Monitoring Demo")
    logger.info("-" * 40)
    
    from digital_twin.core import DigitalTwinEngine, TwinConfiguration
    
    engine = DigitalTwinEngine()
    
    # Create monitoring twin
    config = TwinConfiguration(
        twin_id="monitoring_twin",
        name="System Monitoring Twin",
        description="Twin for system health monitoring",
        physical_asset_id="monitor_001",
        model_version="v1.0"
    )
    
    twin = await engine.create_twin(config)
    await engine.start_twin(config.twin_id)
    
    # Simulate system health data
    health_checks = [
        {'cpu_usage': 45.2, 'memory_usage': 62.1, 'network_latency': 12.5},
        {'cpu_usage': 52.8, 'memory_usage': 58.9, 'network_latency': 15.2},
        {'cpu_usage': 38.1, 'memory_usage': 71.3, 'network_latency': 8.7}
    ]
    
    for i, health in enumerate(health_checks, 1):
        logger.info(f"🔍 Health check {i}:")
        logger.info(f"   💻 CPU Usage: {health['cpu_usage']:.1f}%")
        logger.info(f"   🧠 Memory Usage: {health['memory_usage']:.1f}%") 
        logger.info(f"   🌐 Network Latency: {health['network_latency']:.1f}ms")
        
        # Update twin with health data
        await engine.update_twin_state(config.twin_id, health)
        
        await asyncio.sleep(0.3)
    
    # Get final twin status
    twin_obj = engine.twins[config.twin_id]
    twin_dict = twin_obj.to_dict()
    
    logger.info("📋 Final Twin Status:")
    logger.info(f"   State: {twin_dict['state']}")
    logger.info(f"   Simulation Steps: {twin_dict['simulation_step']}")
    logger.info(f"   Last Sync: {twin_dict['last_sync_time']}")
    
    await engine.stop_twin(config.twin_id)
    return twin_dict

async def main():
    """Run comprehensive digital twin system demo"""
    logger.info("🚀 Digital Twin System Comprehensive Demo")
    logger.info("=" * 60)
    logger.info(f"🕐 Demo started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("")
    
    try:
        # Run all demo scenarios
        demos = [
            ("Twin Lifecycle Management", demo_twin_lifecycle),
            ("Real-time Physics Simulation", demo_real_time_simulation),
            ("State Synchronization", demo_state_synchronization),
            ("System Monitoring", demo_system_monitoring)
        ]
        
        results = {}
        
        for demo_name, demo_func in demos:
            logger.info(f"🎬 Starting {demo_name}...")
            try:
                result = await demo_func()
                results[demo_name] = {"status": "success", "data": result}
                logger.info(f"✅ {demo_name} completed successfully")
            except Exception as e:
                logger.error(f"❌ {demo_name} failed: {e}")
                results[demo_name] = {"status": "failed", "error": str(e)}
            
            logger.info("")  # Spacing between demos
        
        # Summary
        logger.info("🎯 DEMO SUMMARY")
        logger.info("=" * 60)
        
        success_count = 0
        total_count = len(demos)
        
        for demo_name, result in results.items():
            status = "✅ PASS" if result["status"] == "success" else "❌ FAIL"
            logger.info(f"  {demo_name}: {status}")
            if result["status"] == "success":
                success_count += 1
        
        logger.info("")
        logger.info(f"📊 Results: {success_count}/{total_count} demos successful")
        
        if success_count == total_count:
            logger.info("🎉 All demos completed successfully!")
            logger.info("🚀 Digital Twin System is fully operational!")
        else:
            logger.info("⚠️  Some demos had issues. Check logs above.")
        
        logger.info("")
        logger.info("🔗 Next Steps:")
        logger.info("  • Run with Docker: docker-compose -f docker-compose.simple.yml up")
        logger.info("  • Start API server: python3 run_api_demo.py")
        logger.info("  • View documentation: README.md")
        logger.info("  • Check configuration: config/digital_twin_config.yaml")
        
        return success_count == total_count
        
    except Exception as e:
        logger.error(f"💥 Demo system error: {e}")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Demo interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}")
        sys.exit(1)